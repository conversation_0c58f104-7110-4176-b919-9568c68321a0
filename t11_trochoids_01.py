import numpy as np
import bpy
import shapely
from shapely.geometry import LineString, Point


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d

# --- Bezier and Arc functions (keep as in the previous 'revised' version) ---
def cubic_bezier(t, p0, p1, p2, p3):
    p0, p1, p2, p3 = map(np.array, [p0, p1, p2, p3])
    t = np.clip(t, 0.0, 1.0)
    return ((1 - t)**3 * p0 +
            3 * (1 - t)**2 * t * p1 +
            3 * (1 - t) * t**2 * p2 +
            t**3 * p3)

def generate_bezier_transition(p0, t0, p3, t3, num_points=20,
                               default_alpha_beta_factor=0.5,
                               alpha=None, beta=None):
    p0, p3 = np.array(p0), np.array(p3)
    t0_norm = np.linalg.norm(t0)
    t3_norm = np.linalg.norm(t3)

    _t0 = np.array(t0) / t0_norm if t0_norm > 1e-9 else np.array([1.0, 0.0])
    _t3 = np.array(t3) / t3_norm if t3_norm > 1e-9 else np.array([1.0, 0.0])
    if t0_norm <= 1e-9: print("Warning: Zero tangent T0.")
    if t3_norm <= 1e-9: print("Warning: Zero tangent T3.")

    if alpha is None or beta is None:
        dist = np.linalg.norm(p3 - p0)
        # Prevent zero distance issues if P0 and P3 are identical
        if dist < 1e-9:
             alpha_calc = 0.1 # Small default values
             beta_calc = 0.1
        else:
             alpha_calc = dist * default_alpha_beta_factor
             beta_calc = dist * default_alpha_beta_factor
        _alpha = alpha if alpha is not None else alpha_calc
        _beta = beta if beta is not None else beta_calc
    else:
        _alpha = alpha
        _beta = beta

    # Ensure alpha/beta are non-negative
    _alpha = max(0, _alpha)
    _beta = max(0, _beta)

    p1 = p0 + _alpha * _t0
    p2 = p3 - _beta * _t3

    if num_points < 2: return np.array([p0]) if num_points==1 else np.empty((0,2))
    t_values = np.linspace(0, 1, num_points)
    points = np.array([cubic_bezier(t, p0, p1, p2, p3) for t in t_values])
    return points

def generate_arc_points_tangents(center, radius, angle_start_rad, angle_end_rad, tangent_start_vec, tangent_end_vec, num_points=30):
    center = np.array(center)
    if radius < 1e-9 or num_points < 2:
       if num_points >= 1:
           start_point = center + radius * np.array([np.cos(angle_start_rad), np.sin(angle_start_rad)])
           return np.array([start_point]), tangent_start_vec, tangent_end_vec
       else:
           return np.empty((0, 2)), tangent_start_vec, tangent_end_vec

    angles = np.linspace(angle_start_rad, angle_end_rad, num_points)
    points = center + radius * np.array([np.cos(angles), np.sin(angles)]).T
    # Return the provided tangents, assuming they are correct for the context where the arc is used
    return points, tangent_start_vec, tangent_end_vec




def main():    

    # --- Toolpath Generation Parameters (TUNABLE) ---
    stepover = 0.2
    points_per_arc = 25
    points_per_transition = 20
    corner_alpha_beta_factor = 0.5 # Factor for CORNERS (distance-based)
    intra_step_alpha_beta_factor = 2.5 # Factor for INTRA-SEGMENT (stepover-based) - Start tuning here!

    # --- Input MAT Data ---
    # mat_skeleton_coords = [
    #     (-3.71, 1.43), (-0.46, 1.28), (1.38, 1.49),
    #     (3.65, 2.09), (4.84, 2.58), (6.41, 3.47)
    # ]
    mat_skeleton_coords = [
        (0.0, 0.0), (2.0, 0.0) 
    ]
    mat_radii = [3.0, 3.0]

    # --- Setup ---
    mat_points = np.array(mat_skeleton_coords)
    mat_radii = np.array(mat_radii)
    if len(mat_points) != len(mat_radii): raise ValueError("Points/radii mismatch.")
    if len(mat_points) < 2: raise ValueError("Need >= 2 MAT points.")
    full_toolpath = []
    last_pos = None
    last_tangent_out = None # Tangent leaving the last computed point

    # --- Toolpath Generation Loop ---
    for i in range(len(mat_points) - 1):
        p_i, p_i_plus_1 = mat_points[i], mat_points[i+1]
        r_i, r_i_plus_1 = mat_radii[i], mat_radii[i+1]
        segment_vec = p_i_plus_1 - p_i
        segment_len = np.linalg.norm(segment_vec)
        if segment_len < 1e-6: continue
        dir_vec = segment_vec / segment_len
        normal_vec = np.array([-dir_vec[1], dir_vec[0]])
        try:
            radius_interpolator = interp1d([0, segment_len], [r_i, r_i_plus_1], kind='linear', fill_value="extrapolate")
        except ValueError:
            radius_interpolator = lambda x: r_i

        # --- Corner Transition ---
        if last_pos is not None:
            first_arc_dist = min(stepover / 2.0, segment_len / 2.0)
            first_arc_center = p_i + first_arc_dist * dir_vec
            first_arc_radius = max(1e-6, float(radius_interpolator(first_arc_dist)))
            first_arc_start_pos = first_arc_center + first_arc_radius * normal_vec
            tangent_into_first_arc = dir_vec # Tangent INTO the start of the first arc

            corner_transition_points = generate_bezier_transition(
                last_pos,               # P0 = End of previous segment's path
                last_tangent_out,       # T0 = Tangent OUT of previous path
                first_arc_start_pos,    # P3 = Start of first arc on this segment
                tangent_into_first_arc, # T3 = Tangent INTO this first arc
                num_points=points_per_transition,
                default_alpha_beta_factor=corner_alpha_beta_factor
            )
            if corner_transition_points.shape[0] > 1:
                full_toolpath.extend(corner_transition_points[1:])

            # State for the start of the segment's loops
            last_pos = first_arc_start_pos
            last_tangent_out = tangent_into_first_arc # After transition, we are heading into the arc start tangent dir
            current_dist_on_segment = first_arc_dist
        else:
            # Initialization for the very first segment
            current_dist_on_segment = min(stepover / 2.0, segment_len / 2.0)
            first_arc_center = p_i + current_dist_on_segment * dir_vec
            first_arc_radius = max(1e-6, float(radius_interpolator(current_dist_on_segment)))
            last_pos = first_arc_center + first_arc_radius * normal_vec
            last_tangent_out = dir_vec # Assume starting tangent is along segment
            full_toolpath.append(last_pos)

        # --- Intra-Segment Loop ---
        while current_dist_on_segment < segment_len:
            center_arc = p_i + current_dist_on_segment * dir_vec
            radius_arc = max(1e-6, float(radius_interpolator(current_dist_on_segment)))
            arc_start_pos = center_arc + radius_arc * normal_vec
            arc_end_pos = center_arc - radius_arc * normal_vec
            tangent_into_arc = dir_vec # Tangent INTO arc start
            # *** TANGENT OUT OF ARC END (Clockwise Arc) ***
            tangent_out_of_arc = -dir_vec

            angle_dir = np.arctan2(dir_vec[1], dir_vec[0])
            angle_start = angle_dir + np.pi / 2.0
            angle_end = angle_dir - np.pi / 2.0

            # Ensure we are at arc_start_pos (add tiny transition if needed)
            if np.linalg.norm(last_pos - arc_start_pos) > 1e-6:
                trans_to_arc_start = generate_bezier_transition(
                    last_pos, last_tangent_out, arc_start_pos, tangent_into_arc,
                    num_points=max(2, points_per_transition // 2), # Need at least 2 pts
                    default_alpha_beta_factor=corner_alpha_beta_factor
                )
                if trans_to_arc_start.shape[0] > 1:
                    full_toolpath.extend(trans_to_arc_start[1:])
                last_pos = arc_start_pos # Ensure we are exactly at the start

            # Generate Arc points
            arc_points, _, _ = generate_arc_points_tangents(
                center_arc, radius_arc, angle_start, angle_end,
                tangent_into_arc, tangent_out_of_arc, # Pass contextual tangents (though not used by func itself)
                points_per_arc
            )
            if arc_points.shape[0] > 1:
                full_toolpath.extend(arc_points[1:]) # Skip first point (arc_start_pos)

            # Update position and the tangent *leaving* this arc
            last_pos = arc_end_pos
            last_tangent_out = tangent_out_of_arc # Use the CORRECT tangent out of the arc end

            # --- Prepare for next step (Transition to next arc start) ---
            next_dist_on_segment = current_dist_on_segment + stepover
            if next_dist_on_segment < segment_len:
                center_next_arc = p_i + next_dist_on_segment * dir_vec
                radius_next_arc = max(1e-6, float(radius_interpolator(next_dist_on_segment)))
                next_arc_start_pos = center_next_arc + radius_next_arc * normal_vec
                tangent_into_next_arc = dir_vec # Tangent INTO the start of the next arc

                # Calculate alpha/beta based on stepover
                intra_alpha = stepover * intra_step_alpha_beta_factor
                intra_beta = stepover * intra_step_alpha_beta_factor

                # Generate transition using CORRECT tangent OUT of previous arc
                transition_points = generate_bezier_transition(
                    last_pos,                # P0 = Arc end
                    last_tangent_out,        # T0 = Tangent OUT (-dir_vec) <-- Corrected
                    next_arc_start_pos,      # P3 = Next arc start
                    tangent_into_next_arc,   # T3 = Tangent IN (dir_vec) <-- Correct
                    num_points=points_per_transition,
                    alpha=intra_alpha,
                    beta=intra_beta
                )
                if transition_points.shape[0] > 1:
                    full_toolpath.extend(transition_points[1:])

                # Update state for start of next arc
                last_pos = next_arc_start_pos
                # The tangent leaving the *transition* and going *into* the next arc start
                last_tangent_out = tangent_into_next_arc
                current_dist_on_segment = next_dist_on_segment
            else:
                # Reached end of segment steps. last_pos and last_tangent_out are now set
                # correctly for the corner transition calculation at start of NEXT segment.
                current_dist_on_segment = segment_len # Exit condition

    # --- Convert path and Plot ---
    full_toolpath = np.array(full_toolpath)

    create_line_object(full_toolpath, "AAAAAAAAtrochoid")


if __name__ == "__main__":
    main()