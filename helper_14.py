import numpy as np
import shapely
import shapely.vectorized
import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time


def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=72, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.
    
    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def find_farthest_outside_point(inner_ellipse: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.               
    """    

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]
    
    # Filter points outside ellipse B    
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b
    
    outside_points_geom = shapely.points(inner_ellipse[outside_mask])
    
    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)
    
    # Find maximum
    return np.max(distances)


def _find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, start_angle_rad=0.0, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation']
    outer_center = outer_ellipse_data['center']

    t_arc = _find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)
    
    # 2. Apply the angular offset to get the effective parameter
    t_effective = t_arc + start_angle_rad
    
    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_effective), outer_semi_minor_axis * np.sin(t_effective)])
    point_vector = rotate_vector(point_vector, outer_rotation)
    point_vector += outer_center
    
    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_effective), outer_semi_major_axis * np.sin(t_effective)])
    normal_vector = rotate_vector(normal_vector, outer_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])
    
    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])
            
    return point_vector, normal_angle


def get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, local_pos):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.
    
    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries
    
    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse
        
    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
    new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
        outer_ellipse_data = outer_ellipse_data,
        fraction=local_pos
        )
    
    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=new_anchor,
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=new_rotation,
        anchor_on_perimeter=True
        )
    
    # create_line_object(ellipse_geometry, "ellipse_new_anchor", color=(0, 1, 0, 1))
            
    # Calculate distances to sides
    ellipse = shapely.geometry.LineString(ellipse_geometry)
    distances = []
    # create_line_object(ellipse_geometry, "ellipse_new_anchor", color=(0, 1, 0, 1))
    # create_line_object(sides_data['poly'].coords, "sides_data['poly']", color=(1, 0, 0, 1))
    # poly = shapely.geometry.Polygon(sides_data['poly'].coords)
    poly = shapely.geometry.Polygon(shapely.get_coordinates(sides_data['poly']))
    shapely.prepare(poly)

    for side_line in sides_data['lines']:   
        if ellipse.intersects(side_line):
            ## ellipse part as a result of the intersection            
            dist = find_farthest_outside_point(ellipse_geometry, poly) 
            distances.append(-dist) # negative, because of intersection
        else:
            distances.append(side_line.distance(ellipse))
      
    return distances, new_anchor, new_rotation, ellipse_center


def intersection_span_along_line(outer_data, inner_data, poly):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        
        distances = []
        front_fractions = None
        for i, ellipse_data in enumerate([outer_data, inner_data]):
            ellipse_geometry, _ = create_ellipse(
                anchor_point=ellipse_data['center'],
                semi_major_axis=ellipse_data['major'],
                semi_minor_axis=ellipse_data['minor'],
                rotation=ellipse_data['rotation']
                )
            create_line_object(ellipse_geometry, f"ellipse_{i}", color=(1, 1, 0, 1))
            linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
            
            inter = linear_ring.intersection(poly)
            for geom in inter.geoms:
                create_line_object(geom.coords, f"inter_{i}", color=(1, 0, 1, 1))
            
            if inter.is_empty:
                print('intersection is empty (intersection_span_along_line())')
                return None
            projected_distances = [linear_ring.project(d, normalized=True) for d in inter.geoms]
            if i == 0: # outer ellipse
                front_fractions = [min(projected_distances), max(projected_distances)]
            distances.append(
                inter.geoms[np.argmin(projected_distances)].distance(inter.geoms[np.argmax(projected_distances)])
                )

        return distances, front_fractions


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None



def get_ellipse_data(polygon, medial_axis):
        center = medial_axis.coords[0]
        center_point = shapely.geometry.Point(center)
        semi_axis_length = center_point.distance(polygon.boundary)

        ellipse_data = {
            'major': semi_axis_length,
            'minor': semi_axis_length,
            'center': center,            
            'length': 2 * np.pi * semi_axis_length
        }
        
        anchor = None
        for i, _ in enumerate(medial_axis.coords):
            if i > 0:
                line_start = np.array(medial_axis.coords[i-1])
                line_end = np.array(medial_axis.coords[i])
                anchor = intersect_line_ellipse(line_start, line_end, ellipse_data)
                if anchor is not None:
                    ellipse_data['anchor'] = anchor
                    break

        if anchor is None:            
            print("Problem with anchor")
            return None
        
        ### Initial rotation of the ellipse in radians ###        
        rotation_vector = anchor - np.array(center)
        ellipse_data['rotation'] = np.arctan2(rotation_vector[1], rotation_vector[0])

        return ellipse_data


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    distance_result : (m) float
      Distances along curve which result in
      nicely spaced circles.
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
    return pairs


def create_circle_points(center, radius, start_angle, resolution=72, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring
    p1 = np.array(reference_ls.coords[0])
    # Get the end point of the reference linestring
    p2 = np.array(reference_ls.coords[-1]) # Using last point for overall direction
                                            # or reference_ls.coords[1] for first segment

    # Use the first point of the target linestring as its representative point
    q = np.array(target_ls.coords[0])

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear


def calculate_ellipse_metrics(major, minor, position, inner_ellipse_base, outer_ellipse_data, sides_data, outer_ellipse, maximum_right_dist):
    """Pure function to calculate all metrics for given ellipse parameters"""
    test_inner_data = {**inner_ellipse_base, 'major': major, 'minor': minor}
    
    # Get ellipse positioning and distances
    distances, new_anchor, new_rotation, ellipse_center = get_ellipse_distances(
        test_inner_data, outer_ellipse_data, sides_data, position
    )
    
    # Calculate ellipse geometry for outer boundary check
    ellipse_geometry, _ = create_ellipse(
        anchor_point=ellipse_center,
        semi_major_axis=major,
        semi_minor_axis=minor,
        rotation=new_rotation
    )
    
    # Check if ellipse fits within outer boundary
    boundary_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)
    if boundary_distance < maximum_right_dist:
        boundary_distance = -boundary_distance
    elif boundary_distance == 0.0:
        boundary_distance = -1e100
    
    return {
        'distances': distances,
        'new_anchor': new_anchor,
        'new_rotation': new_rotation,
        'ellipse_center': ellipse_center,
        'boundary_distance': boundary_distance,
        'side_balance': distances[0] - distances[1],  # How balanced the side distances are
        'max_side_distance': max(distances, key=abs)  # Maximum absolute distance from sides
    }


def find_optimal_ellipse_position(major, minor, inner_ellipse_base, outer_ellipse_data, sides_data, front_fractions, tolerance=1e-4):
    """Find optimal position for given major/minor axes"""
    def position_objective(position):
        position = position if position > 0 else 1 + position
        metrics = calculate_ellipse_metrics(
            major, minor, position, inner_ellipse_base, outer_ellipse_data, sides_data, None, 0
        )
        return metrics['side_balance']
    
    try:
        return scipy.optimize.brentq(
            position_objective, 
            a=front_fractions[0], 
            b=front_fractions[1]-1, 
            xtol=tolerance
        )
    except ValueError as e:
        print(f"Could not find optimal position for major={major}, minor={minor}: {e}")
        return front_fractions[0]  # Return fallback position
    

def solve_ellipse_params(inner_ellipse_data, outer_ellipse_data, sides_data, minimum_major, maximum_major, outer_ellipse, min_radius_of_curvature, maximum_right_dist, front_fractions, tolerance=1e-4):
        def objective_function(major_minor_position):            
            softness = 10
            major, minor, position = major_minor_position
            inner_ellipse_data.update({'major': major, 'minor': minor})

            if position < 1-front_fractions[1]:
                position_translated = front_fractions[1] + position
            else:
                position_translated = position - (1-front_fractions[1])
            
            distances, _, new_rotation, new_center = get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, position_translated)
     
            ellipse_geometry, _ = create_ellipse(
                    anchor_point=new_center,
                    semi_major_axis=inner_ellipse_data['major'],
                    semi_minor_axis=inner_ellipse_data['minor'],
                    rotation=new_rotation                    
                    )

            minor_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)
            new_minor = abs(maximum_right_dist - minor_distance) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(minor_distance))
            new_major = np.sum(np.abs(distances)) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(distances))
            # new_position = np.abs(distances[0]-distances[1]) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(distances[0]-distances[1]))
            new_position = 0.0
            res = new_major + new_minor
            print(f'major: {major}, distances: {distances}, new_major: {new_major}, new_position: {new_position}, new_minor: {new_minor}, res: {res}')
            return res
        
        min_position = 0.0
        max_position = front_fractions[0] + (1 - front_fractions[1])     
        init_position = max_position / 2
        # init_position = 0.12

        minimum_minor = np.sqrt(min_radius_of_curvature * minimum_major)        
        initial_guess = np.array([minimum_major, minimum_minor, init_position])
        bounds = [
            (minimum_major, maximum_major), # Major axis
            (minimum_minor, minimum_major), # Minor axis
            (min_position, max_position) # possible correction of the position
        ]

        print(f'bounds: {bounds}')

        result = scipy.optimize.minimize(
            objective_function,
            initial_guess,
            method='Nelder-Mead', # Or 'Powell', 'COBYLA'            
            bounds=bounds,
            options={'xatol': 1e-3, 'fatol': 1e-3, 'adaptive': False, 'disp': True} # 'xatol' is the tolerance
        )
        return result.x


def fit_ellipse(outer_ellipse_data, sides_data, cutter_data, min_radius_of_curvature=15.0):
    ### Inital scale down ellipse of the cutter ###     
    maximum_right_dist: float = 0.3
    minor_correction_factor = 0.95 ##TODO: its a quick fix for boolean operations.

    inner_major_axis = outer_ellipse_data['major'] * (1 - (cutter_data['radius']*minor_correction_factor/outer_ellipse_data['major']))
    inner_minor_axis = outer_ellipse_data['minor'] * (1 - (cutter_data['radius']*minor_correction_factor/outer_ellipse_data['minor']))

    inner_ellipse_data = {
        'major': inner_major_axis,
        'minor': inner_minor_axis,
        'center': outer_ellipse_data['center'],
        'anchor': outer_ellipse_data['anchor'],
        'rotation': outer_ellipse_data['rotation'],
    }

    ## outer ellipse geometry
    outer_ellipse_geometry, _ = create_ellipse(
        anchor_point=outer_ellipse_data['center'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation']
        )
    outer_ellipse = shapely.geometry.Polygon(outer_ellipse_geometry)
    shapely.prepare(outer_ellipse)
    
    ### Helper data ###
    # [0]-outer, [1]-inner
    distances, front_fractions = intersection_span_along_line(outer_ellipse_data, inner_ellipse_data, sides_data['poly'])
    # print(f'distances: {distances}')
    if distances[0] < distances[1]: # ellipse will by smaller then previous
        minimum_major = distances[0]/2
        maximum_major = distances[1]/2
    else: # ellipse will by bigger
        print('ellipse will by bigger then previous')
        return
    
    print(f'minimum_major: {minimum_major}, maximum_major: {maximum_major}')
    # return
    
    res = solve_ellipse_params(
        inner_ellipse_data,
        outer_ellipse_data,
        sides_data,        
        minimum_major,
        maximum_major,
        outer_ellipse,
        min_radius_of_curvature,
        maximum_right_dist,
        front_fractions
        )
    
    print(res)
    
    position = res[2]
    if position < 1-front_fractions[1]:
        position_translated = front_fractions[1] + position
    else:
        position_translated = position - (1-front_fractions[1])
    
    new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
        outer_ellipse_data = outer_ellipse_data,
        fraction=position_translated
        )
    
    inner_ellipse_data.update({
        'major': res[0],
        'minor': res[1],
        'anchor': new_anchor,
        'rotation': new_rotation
        })
    
    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=inner_ellipse_data['anchor'],
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=inner_ellipse_data['rotation'],
        anchor_on_perimeter=True
        )
    create_line_object(ellipse_geometry, "ellipse_inner", color=(0, 1, 0.1, 1))
    
    inner_ellipse_data.update({'center': ellipse_center})
    
    ### Lenght of the outer ellipse ###
    # Eccentricity of the ellipse
    e = np.sqrt(1 - ((inner_ellipse_data['minor']+cutter_data['radius'])**2 / (inner_ellipse_data['major']+cutter_data['radius'])**2))
    # Exact perimeter calculation using numerical integration
    perimeter = 4 * (inner_ellipse_data['major']+cutter_data['radius']) * scipy.special.ellipe(e)

    new_anchor = rotate_vector(np.array([cutter_data['radius'], 0]), inner_ellipse_data['rotation'])    
    
    outer_ellipse_data = {            
        'major': inner_ellipse_data['major']+cutter_data['radius'],
        'minor': inner_ellipse_data['minor']+cutter_data['radius'],
        'center': ellipse_center,
        'anchor': inner_ellipse_data['anchor']+new_anchor,
        'rotation': inner_ellipse_data['rotation'],
        'length': perimeter
    }

    return outer_ellipse_data


def main():
    cutter_dim = 20 # in mm

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_dim / 2
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])    
    medial_middle = medial_axis.interpolate(0.5, normalized=True)
    
    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]
        

    tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]
    for geom in active_geoms:
        create_line_object(geom.coords, 'active_geom', color=(1, 0, 0, 1))

    ## Sides per path
    trochos_distances = advancing_front(geometry[1], polygon, 10) # 5mm between trochoids
    trochos = []
    for point, distance in trochos_distances:
        trochos.append(shapely.geometry.Polygon(create_circle_points(point, distance, 0, 16)))

    trochos[0] = shapely.geometry.Polygon(create_circle_points(trochos_distances[0][0], trochos_distances[0][1], 0, 72))
    trochos[-1] = shapely.geometry.Polygon(create_circle_points(trochos_distances[-1][0], trochos_distances[-1][1], 0, 72))
    trochos_poly = shapely.unary_union(trochos)
    sides = []
    for geom in active_geoms:
        if geom.intersects(trochos_poly):
            sides.append(geom.intersection(trochos_poly))

    if len(sides) == 2:
        ### Determine sides, the first side is the one that is on the "left" of the medial axis
        if get_side_of_medial_axis(sides[0], medial_axis) == -1:
            sides.reverse() 
        # sides = shapely.geometry.MultiLineString(sides)
        for side in sides:
            shapely.prepare(side)
    else:
        print("Problem with sides")
        print(len(sides))
        return
    
    combined_sides = list(sides[0].coords) + list(sides[1].coords)   
    if not shapely.geometry.LinearRing(combined_sides).is_ccw:        
        combined_sides = list(reversed(combined_sides))
    poly = shapely.geometry.LinearRing(combined_sides)
    shapely.prepare(poly)
    # create_line_object(poly.coords, "side_poly", color=(1, 0, 0, 1))

    sides_polys = []
    for line in sides:
        new_line = line.parallel_offset(50.0, 'left')
        combined_lines = list(line.coords) + list(reversed(new_line.coords))
        sides_polys.append(shapely.geometry.Polygon(combined_lines))
    sides_polys = shapely.geometry.MultiPolygon(sides_polys)
    shapely.prepare(sides_polys)
    sides_data = {
        'poly': poly,
        'polys': sides_polys,
        'lines': sides
    }
    
    # for side in sides_polys.geoms:
        # create_line_object(side.exterior.coords, "side", color=(1, 0, 0, 1))
    # return
    ellipse_data = get_ellipse_data(polygon, medial_axis)

    # for geom in active_geoms:
    #     create_line_object(geom.coords, 'poly')
    time1 = time.time()

    min_radius_of_curvature = 5.0    
    for i in range(1):
        print(f'iteration: {i}')
        ellipse_data = fit_ellipse(ellipse_data, sides_data, cutter_data, min_radius_of_curvature)
        
    time2 = time.time()
    print(f'Time: {time2-time1}')

if __name__ == "__main__":
    main()

