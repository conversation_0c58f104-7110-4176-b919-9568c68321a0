import numpy as np

tolerance = 0.01

outer_ellipse_data = {
    'major': 10,
    'minor': 10,
    'center': (0, 0),
    'perimeter': 2 * np.pi * 10
}

sides = [
    shapely.geometry.LineString(...),
    shapely.geometry.LineString(...)
]

ellipse = get_ellipse(0, outer_ellipse_data)
distances_init = get_ellipse_distances(ellipse, sides)

distance_diff_init = distances_init[0]-distances_init[1] #left - right distance, positive value = if left is further
move_distance_init = abs(distance_diff_init) / outer_ellipse_data['perimeter']
## if left distance is bigger, we need move in space 0-1, else in space 1-0. space are circular, so 0=1.
move_to_init = move_distance_init if distance_diff_init > 0 else 1-move_distance_init #calc direction

ellipse = get_ellipse(move_to_init, outer_ellipse_data)
distances = get_ellipse_distances(ellipse, sides)
distance_diff = distances[0]-distances[1] #left - right distance, positive value = if left is further

while distance_diff > tolerance:
    move_distance = abs(distance_diff) / outer_ellipse_data['perimeter']
    correction_factor = ... # calc correction factor based on relation between distance_diff_init and distance_diff
    move_to = move_distance * correction_factor if distance_diff > 0 else 1-move_distance * correction_factor #calc direction

    ellipse = get_ellipse(move_to, outer_ellipse_data)
    distances = get_ellipse_distances(ellipse, sides)
    distance_diff = distances[0]-distances[1]





