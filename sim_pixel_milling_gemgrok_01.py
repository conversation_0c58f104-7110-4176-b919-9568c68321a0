from __future__ import annotations
import numpy as np
import math
import time

import numpy as np
import pandas as pd

# --- Simulation Parameters ---
# Tool
tool_diameter = 10.0  # mm (D from Fig 6)
tool_radius = tool_diameter / 2.0
num_teeth = 2      # z (from Fig 6)

# Process
axial_depth_of_cut = 5.0 # mm (ap from Fig 6)
spindle_speed_rpm = 24000 # RPM (n from Fig 6)
feed_rate_vf = 800 # mm/min (Example, use calculated if preferred)

# Simulation Grid
pixel_size = 0.1  # mm per pixel (Delta in paper discussion)
workpiece_width_mm = 100.0
workpiece_height_mm = 50.0
padding_mm = tool_diameter
sim_width_mm = workpiece_width_mm + 2 * padding_mm
sim_height_mm = workpiece_height_mm + 2 * padding_mm

# Simulation Steps
sim_step_distance = 0.2 # mm (ds - how far tool moves each sim step)
# <<< Add check for step distance >>>
if sim_step_distance <= 1e-9:
    raise ValueError("Simulation step distance must be positive.")
steps_per_mm = 1.0 / sim_step_distance
steps_per_min = feed_rate_vf / sim_step_distance
# vf_check = axial_depth_of_cut * feed_rate_vf # No longer needed for ae calculation

# --- Helper Functions ---
# (Keep helper functions world_to_pixel, create_tool_matrix,
#  create_workpiece_matrix, generate_tool_path_straight,
#  generate_tool_path_corner as they were)
def world_to_pixel(xy_mm, origin_xy_mm, p_size):
    """Converts world coordinates (mm) to pixel indices."""
    px = int(round((xy_mm[0] - origin_xy_mm[0]) / p_size))
    py = int(round((xy_mm[1] - origin_xy_mm[1]) / p_size))
    return px, py

def create_tool_matrix(diameter, p_size):
    """Creates a binary matrix representing the circular tool."""
    radius = diameter / 2.0
    tool_matrix_size_pixels = int(math.ceil(diameter / p_size))
    if tool_matrix_size_pixels % 2 == 0:
        tool_matrix_size_pixels += 1
    center_pixel = tool_matrix_size_pixels // 2
    tool_matrix = np.zeros((tool_matrix_size_pixels, tool_matrix_size_pixels), dtype=np.uint8)
    radius_pixels_sq = (radius / p_size) ** 2
    for r in range(tool_matrix_size_pixels):
        for c in range(tool_matrix_size_pixels):
            dist_sq = (r - center_pixel)**2 + (c - center_pixel)**2
            if dist_sq <= radius_pixels_sq:
                tool_matrix[r, c] = 1
    return tool_matrix

def create_workpiece_matrix(sim_w_mm, sim_h_mm, wp_w_mm, wp_h_mm, pad_mm, p_size):
    """Creates the simulation grid with the workpiece material marked."""
    sim_width_pixels = int(round(sim_w_mm / p_size))
    sim_height_pixels = int(round(sim_h_mm / p_size))
    workpiece_matrix = np.zeros((sim_height_pixels, sim_width_pixels), dtype=np.uint8)
    wp_origin_mm = (pad_mm, pad_mm)
    wp_start_px, wp_start_py = world_to_pixel((wp_origin_mm[0], wp_origin_mm[1]), (0,0), p_size)
    wp_end_px = wp_start_px + int(round(wp_w_mm / p_size))
    wp_end_py = wp_start_py + int(round(wp_h_mm / p_size))
    workpiece_matrix[wp_start_py:wp_end_py, wp_start_px:wp_end_px] = 1
    sim_origin_mm = (0.0, 0.0)
    return workpiece_matrix, sim_origin_mm

def generate_tool_path_straight(start_xy, end_xy, step_ds):
    """Generates points for a straight line tool path."""
    path = []
    start_vec = np.array(start_xy); end_vec = np.array(end_xy)
    total_dist = np.linalg.norm(end_vec - start_vec)
    direction = (end_vec - start_vec) / total_dist if total_dist > 0 else np.array([0,0])
    current_dist = 0.0
    while current_dist <= total_dist:
        path.append(tuple(start_vec + direction * current_dist))
        current_dist += step_ds
    if not np.allclose(path[-1], end_xy): path.append(tuple(end_xy))
    return path

def generate_tool_path_corner(start_xy, corner_xy, end_xy, step_ds):
     """Generates points for a path with one corner."""
     path1 = generate_tool_path_straight(start_xy, corner_xy, step_ds)
     path2 = generate_tool_path_straight(corner_xy, end_xy, step_ds)
     return path1[:-1] + path2
# --- Simulation Core ---

print("Creating Tool Matrix...")
tool_matrix = create_tool_matrix(tool_diameter, pixel_size)
tool_matrix_center_offset = tool_matrix.shape[0] // 2
print(f"Tool matrix shape: {tool_matrix.shape}")

print("Creating Workpiece Matrix...")
workpiece, sim_origin = create_workpiece_matrix(
    sim_width_mm, sim_height_mm,
    workpiece_width_mm, workpiece_height_mm,
    padding_mm, pixel_size
)
initial_workpiece = workpiece.copy() # Keep initial state for reference
sim_height_pixels, sim_width_pixels = workpiece.shape
print(f"Workpiece matrix shape: {workpiece.shape}")

# --- Define Tool Path (Example: Corner Turn - Inside corner cut) ---
path_start = (padding_mm + workpiece_width_mm / 2.0, padding_mm - tool_radius) # Start below workpiece
path_corner = (padding_mm + workpiece_width_mm / 2.0, padding_mm + workpiece_height_mm / 2.0) # Move up to center
path_end = (padding_mm + workpiece_width_mm + tool_radius, padding_mm + workpiece_height_mm / 2.0) # Move right out of workpiece
tool_path_mm = generate_tool_path_corner(path_start, path_corner, path_end, sim_step_distance)

print(f"Generated tool path with {len(tool_path_mm)} steps.")

# --- Run Simulation ---
mrr_results = []
theta_results = [] # Store theta values
ae_results = []    # <<< Store ae values for clarity >>>
path_length_results = []
current_path_length = 0.0

start_time = time.time()

for i, tool_center_mm in enumerate(tool_path_mm):

    # Calculate path length for plotting
    if i > 0:
        dist_moved = np.linalg.norm(np.array(tool_center_mm) - np.array(tool_path_mm[i-1]))
        # Ensure dist_moved is consistent with sim_step_distance for MRR/ae calculation
        # In practice, small variations might occur at the very end of path segments
        step_dist_actual = dist_moved if dist_moved > 1e-9 else sim_step_distance
        current_path_length += dist_moved
    else:
        step_dist_actual = sim_step_distance # Assume first step uses nominal distance

    path_length_results.append(current_path_length)

    # Convert tool center mm to pixel coordinates
    tool_center_px, tool_center_py = world_to_pixel(tool_center_mm, sim_origin, pixel_size)

    # Determine the slice of the workpiece the tool overlaps
    y_start = tool_center_py - tool_matrix_center_offset
    y_end = y_start + tool_matrix.shape[0]
    x_start = tool_center_px - tool_matrix_center_offset
    x_end = x_start + tool_matrix.shape[1]

    # Boundary checks
    y_start_clip = max(0, y_start); y_end_clip = min(sim_height_pixels, y_end)
    x_start_clip = max(0, x_start); x_end_clip = min(sim_width_pixels, x_end)

    # Get corresponding slices
    workpiece_slice = workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip]
    tool_y_start_offset = y_start_clip - y_start
    tool_y_end_offset = tool_matrix.shape[0] - (y_end - y_end_clip)
    tool_x_start_offset = x_start_clip - x_start
    tool_x_end_offset = tool_matrix.shape[1] - (x_end - x_end_clip)
    tool_slice = tool_matrix[tool_y_start_offset:tool_y_end_offset,
                             tool_x_start_offset:tool_x_end_offset]

    mrr_inst = 0.0
    area_removed_inst_mm2 = 0.0
    ae_estimated = 0.0
    overlap_pixels = 0

    if workpiece_slice.shape == tool_slice.shape and workpiece_slice.size > 0:
        overlap = workpiece_slice & tool_slice
        overlap_pixels = np.sum(overlap)

        if overlap_pixels > 0:
            # Calculate instantaneous material removal area PER STEP (mm^2)
            area_removed_inst_mm2 = overlap_pixels * (pixel_size ** 2)

            # --- Corrected MRR Calculation ---
            mrr_inst = area_removed_inst_mm2 * axial_depth_of_cut * feed_rate_vf / step_dist_actual

            # --- Corrected ae Calculation ---
            ae_estimated = area_removed_inst_mm2 / step_dist_actual

            # Update workpiece
            workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip][overlap == 1] = 0

    mrr_results.append(mrr_inst)
    ae_results.append(ae_estimated) # Store ae

    # --- Calculate Theta from corrected ae ---
    theta_deg = 0.0
    # Clamp ae: Must be between 0 and tool diameter (2*R)
    # Allow slightly over D due to pixelation/step approximation before clamping arg
    ae_clamped_for_theta = max(0.0, min(ae_estimated, tool_diameter * 1.01)) # Allow slight tolerance

    if ae_clamped_for_theta > 1e-9 and tool_radius > 1e-9: # Check if cutting and radius valid
        # Calculate argument for acos based on (Eq. 14)
        arg = (tool_radius - ae_clamped_for_theta) / tool_radius
        # Clamp argument to [-1, 1] for acos
        arg_clamped = max(-1.0, min(arg, 1.0))
        # Calculate theta in radians and convert to degrees
        theta_rad = math.acos(arg_clamped)
        theta_deg = math.degrees(theta_rad)

    theta_results.append(theta_deg)


end_time = time.time()
print(f"\nSimulation finished in {end_time - start_time:.2f} seconds.")
print(theta_results[151])



# ----------------------------------------------------------------------
# 0. Helper – tool bitmap (unchanged)
# ----------------------------------------------------------------------
def tool_matrix(D: float, p: int) -> np.ndarray:
    """
    Binary mask of a ØD flat end‑mill, p×p pixels.
    """
    Δ = D / p
    idx = np.arange(p) - p/2 + 0.5
    x = idx * Δ
    y = x.copy()
    T = ((x[None, :]**2 + y[:, None]**2) <= (D/2)**2)
    return T.astype(np.uint8)      # 1 = material


# ----------------------------------------------------------------------
# 1. Work‑piece bitmap (simple rectangle)
# ----------------------------------------------------------------------
def build_workpiece(
        bbox: tuple[float, float, float, float],
        Δ: float
) -> tuple[np.ndarray, dict]:
    """
    bbox = (xmin, xmax, ymin, ymax)  [mm]

    Returns
    -------
    W     : bool ndarray (True = material)
    meta  : {Δ, xmin, ymin}  for later index ↔ physical coordinate conversion
    """
    xmin, xmax, ymin, ymax = bbox
    nx = int(np.ceil((xmax - xmin)/Δ))
    ny = int(np.ceil((ymax - ymin)/Δ))
    W = np.ones((ny, nx), dtype=bool)     # full stock
    meta = dict(Δ=Δ, xmin=xmin, ymin=ymin, shape=W.shape)
    return W, meta


# ----------------------------------------------------------------------
# 2. One step – overlap, remove, area
# ----------------------------------------------------------------------
def _remove_and_area(
        W: np.ndarray,
        T: np.ndarray,
        cx: float, cy: float, meta: dict
) -> float:
    """
    Overlap tool bitmap *T* centred at (cx, cy) [mm] with workpiece *W*,
    knock the overlapped pixels out, and return removed AREA  [mm²].
    """
    Δ = meta['Δ']
    r0 = int(round((cy - meta['ymin'])/Δ))        # row index in W
    c0 = int(round((cx - meta['xmin'])/Δ))        # col index in W
    p = T.shape[0]                                # tool size (square)

    # top‑left corner of tool bitmap within W
    rs = r0 - p//2
    cs = c0 - p//2

    # clip to work‑piece border
    rW0, rW1 = max(rs, 0), min(rs + p, W.shape[0])
    cW0, cW1 = max(cs, 0), min(cs + p, W.shape[1])
    if rW0 >= rW1 or cW0 >= cW1:
        return 0.0                                # completely outside

    # corresponding slice inside tool bitmap
    rT0, rT1 = rW0 - rs, rW1 - rs
    cT0, cT1 = cW0 - cs, cW1 - cs

    # Boolean ‘and’
    overlap = T[rT0:rT1, cT0:cT1] & W[rW0:rW1, cW0:cW1]
    removed_px = overlap.sum(dtype=np.int64)
    W[rW0:rW1, cW0:cW1][overlap] = False          # update stock

    return removed_px * Δ * Δ                     # [mm²]


# ----------------------------------------------------------------------
# 3. Main simulation loop
# ----------------------------------------------------------------------
def simulate(
        tool_D: float,
        p: int,
        bbox: tuple[float, float, float, float],
        path: np.ndarray,
        ap: float,             # axial depth [mm]
        fz: float,             # feed per tooth [mm]
        z: int,                # teeth
        n_rpm: float           # spindle speed [rev/min]
) -> pd.DataFrame:
    """
    Parameters
    ----------
    path  : (N,2) ndarray – list of cutter‑centre positions [mm]
            Should be sampled at *fz* increments!  →  one entry = one tooth pass.
    Returns
    -------
    pandas DataFrame with columns:
        area  [mm²]   A_fz
        V_fz [mm³]
        MRR  [mm³/min]
        a_e  [mm]
        theta[deg]
    """
    Δ = tool_D / p                       # keep pixel size identical to tool discret.
    W, meta = build_workpiece(bbox, Δ)
    T = tool_matrix(tool_D, p)
    R = tool_D / 2.0
    vf = n_rpm * z * fz                  # programmed feed [mm/min]

    data = []
    for step, (cx, cy) in enumerate(path):
        A_fz = _remove_and_area(W, T, cx, cy, meta)  # mm²
        V_fz = A_fz * ap                             # mm³
        MRR  = V_fz * z * n_rpm / 1000.0             # → cm³/min if you want mm³/min keep /1
        a_e  = A_fz / fz if fz > 0 else 0.0          # Eq. (12) A_fz = a_e * fz
        # safer fall‑backs
        a_e  = min(max(a_e, 0.0), 2*R)
        theta = np.degrees(np.arccos((R - a_e)/R)) if a_e <= R else 180.0

        data.append(dict(step=step, X=cx, Y=cy,
                         area=A_fz, V_fz=V_fz, MRR=MRR,
                         a_e=a_e, theta=theta))

    return pd.DataFrame(data)


# --- tool & process -------------------------------------------------
D  = 10.0        # mm
p  = 101          # pixels per diameter
ap = 5.0         # axial depth
fz = 0.05        # mm
z  = 2
n  = 24000.0      # rpm

start_time = time.time()

# --- work‑piece 50 × 30 mm -----------------------------------------
bbox = ( -10, 110, -10, 60 )          # little clearance around (0,0)→(50,30)    

df = simulate(D, p, bbox, tool_path_mm, ap, fz, z, n)

end_time = time.time()
print(f"\nSimulation finished in {end_time - start_time:.2f} seconds.")
print(df['theta'][151])
print("\nPeak MRR  = {:.2f} mm³/min".format(df['MRR'].max()))
print("Peak θ    = {:.1f} °".format(df['theta'].max()))