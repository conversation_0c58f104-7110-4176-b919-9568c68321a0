import networkx as nx
import numpy as np
from scipy.optimize import linear_sum_assignment

def solve_chinese_postman(G, start_node=None):
    """
    Solves the Chinese Postman Problem for an undirected, unweighted, connected graph G.
    Returns the Eulerian circuit starting at `start_node` (or an arbitrary node if unspecified).
    """
    # Check if the graph is connected
    if not nx.is_connected(G):
        raise ValueError("Graph must be connected for the Chinese Postman Problem.")

    # Check if the graph is already Eulerian
    if nx.is_eulerian(G):
        return list(nx.eulerian_circuit(G, source=start_node))

    # Validate start_node if provided
    if start_node is not None and start_node not in G:
        raise ValueError(f"Start node {start_node} not in the graph.")

    # Step 1: Find all nodes with odd degrees
    odd_nodes = [n for n, d in G.degree() if d % 2 != 0]

    # Step 2: Compute all-pairs shortest path lengths (unweighted)
    dist = dict(nx.all_pairs_shortest_path_length(G))

    # Step 3: Create the cost matrix for odd nodes
    n = len(odd_nodes)
    cost_matrix = np.zeros((n, n))
    for i in range(n):
        for j in range(n):
            if i != j:
                cost_matrix[i, j] = dist[odd_nodes[i]][odd_nodes[j]]
            else:
                cost_matrix[i, j] = np.inf  # Avoid self-pairing

    # Step 4: Solve minimum weight matching (Hungarian algorithm)
    row_ind, col_ind = linear_sum_assignment(cost_matrix)

    # Step 5: Augment the graph with duplicated edges
    MG = nx.MultiGraph(G)  # Copy original edges

    pairs = []
    for i in range(n):
        j = col_ind[i]
        if i < j:  # Process each pair once
            pairs.append((odd_nodes[i], odd_nodes[j]))

    # Add edges along shortest paths between pairs
    for u, v in pairs:
        path = nx.shortest_path(G, u, v)  # Unweighted BFS-based path
        for i in range(len(path) - 1):
            a, b = path[i], path[i+1]
            MG.add_edge(a, b)  # Duplicate edges along the path

    # Step 6: Find the Eulerian circuit starting at `start_node`
    return list(nx.eulerian_circuit(MG, source=start_node))

# Example usage:
if __name__ == "__main__":
    # Create an unweighted, non-Eulerian graph (path graph)
    G = nx.Graph()    
    G.add_edge('A', 'B')
    G.add_edge('B', 'C')
    G.add_edge('C', 'D')
    G.add_edge('D', 'A')
    G.add_edge('B', 'D')    

    start_node = 'A'  # Specify the starting node
    circuit = solve_chinese_postman(G, start_node=start_node)

    print("Eulerian Circuit starting at node", start_node)
    print(circuit)