import bpy
from mathutils import Vector
import numpy as np
import trimesh
import shapely.geometry
from pyclothoids import Clothoid
import time
from shapely.geometry import <PERSON>y<PERSON>, Point, LineString
from shapely.strtree import STRtree
import math

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []
    
    geometry_list = []
    for obj in selected_objects:
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    edges = [(i, i + 1) for i in range(len(vertices) - 1)]
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def expand_by_x(indices, x, total_elements):
  """
  Expands a sequence of circular indices by x elements at the beginning
  and x elements at the end.

  Args:
    indices: A NumPy array or list of integers representing the indices.
    x: The number of elements to expand by on each side (must be non-negative).
    total_elements: The total number of elements in the circle (default is 72).

  Returns:
    A NumPy array with the expanded sequence of indices.
  """
  if not isinstance(indices, np.ndarray):
    indices = np.array(indices)

  if indices.size == 0 or x < 0:
    return indices
      
  if x == 0:
    return indices

  # Calculate preceding and succeeding indices
  first = indices[0]
  last = indices[-1]
  
  # Create ranges and handle wrapping with modulo
  preceding = (first - np.arange(x, 0, -1)) % total_elements
  succeeding = (last + np.arange(1, x + 1)) % total_elements
  
  # Combine all indices
  return np.concatenate((preceding, indices, succeeding))


def max_front_engagement(sample_distances, radii, tool_radius, num_theta=36):
    """
    Compute maximum engagement angles for all pairs of consecutive circles using vectorized operations.
    
    Parameters:
    - sample_distances: Array of cumulative distances (shape: (N+1,))
    - radii: Array of circle radii (shape: (N+1,))
    - tool_radius: Radius of the tool (scalar)
    - num_theta: Number of angles to sample for each circle (default: 100)
    
    Returns:
    - Array of maximum engagement angles in radians (shape: (N,))
    """
    # Number of segments
    N = len(radii) - 1
    
    # Generate angle array for sampling
    theta = np.linspace(0, 2 * np.pi, num_theta, endpoint=False)
    
    # Compute segment lengths and radii for consecutive pairs
    # l = sample_distances[1:] - sample_distances[:-1]  # shape: (N,)
    l = sample_distances[1:]
    r1 = np.full_like(l, radii[0])  # shape: (N,)
    r2 = radii[1:]   # shape: (N,)
    
    # Offset radii by tool radius
    R1 = r1 + tool_radius  # shape: (N,)
    R2 = r2 + tool_radius  # shape: (N,)
    
    # Tool center coordinates (C) for all pairs and angles
    C_x = l[:, None] + r2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    C_y = r2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    C = np.stack((C_x, C_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # Tangent points (N) on offset second circle
    N_x = l[:, None] + R2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    N_y = R2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    N = np.stack((N_x, N_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # First circle center (O1) at origin for all pairs
    num_segments = len(r1)  # Use integer value instead of N array
    O1 = np.zeros((num_segments, 1, 2))  # shape: (num_segments, 1, 2)
    
    # Distance from O1 to C
    d = np.sqrt(np.sum((C - O1)**2, axis=-1))  # shape: (N, num_theta)
    
    # Intersection condition
    mask = (d >= np.abs(R1[:, None] - tool_radius)) & (d <= (R1[:, None] + tool_radius))
    
    # Compute gamma (angle for intersection points)
    gamma = np.arccos(np.clip((d**2 + R1[:, None]**2 - tool_radius**2) / (2 * d * R1[:, None]), -1.0, 1.0))
    gamma = np.nan_to_num(gamma, nan=0.0)  # shape: (N, num_theta)
    
    # Unit vector from O1 to C
    u = (C - O1) / d[..., None]  # shape: (N, num_theta, 2)
    u = np.nan_to_num(u, nan=0.0)
    
    # Perpendicular vector (rotated 90 degrees)
    v = np.stack((-u[..., 1], u[..., 0]), axis=-1)  # shape: (N, num_theta, 2)
    
    # Intersection points D1 and D2
    cos_gamma = np.cos(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    sin_gamma = np.sin(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    D1 = O1 + R1[:, None, None] * (u * cos_gamma + v * sin_gamma)  # shape: (N, num_theta, 2)
    D2 = O1 + R1[:, None, None] * (u * cos_gamma - v * sin_gamma)  # shape: (N, num_theta, 2)
    
    # Distances from N to D1 and D2
    DN1 = np.sqrt(np.sum((D1 - N)**2, axis=-1))  # shape: (N, num_theta)
    DN2 = np.sqrt(np.sum((D2 - N)**2, axis=-1))  # shape: (N, num_theta)
    
    # Engagement angles
    alpha1 = np.arccos(np.clip(1 - (DN1**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    alpha2 = np.arccos(np.clip(1 - (DN2**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    
    # Apply mask and compute maximum
    alpha1 = np.where(mask, alpha1, 0.0)
    alpha2 = np.where(mask, alpha2, 0.0)
    max_alpha = np.max(np.maximum(alpha1, alpha2), axis=1)  # shape: (N,)
    
    # Convert to degrees
    max_alpha = np.rad2deg(max_alpha)
    
    return max_alpha


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def angle_engagement(center: tuple[float, float], p_center: tuple[float, float], r: float, stock_poly: Polygon, arc_resolution=12) -> float | None:
    # (Using the robust version from the previous step)
    if not isinstance(stock_poly, (Polygon, shapely.geometry.MultiPolygon)) or not stock_poly.is_valid: return None
    if center == p_center: return 0.0
    vec = [center[0] - p_center[0], center[1] - p_center[1]]
    vec_mag = math.sqrt(vec[0]**2 + vec[1]**2)
    if vec_mag < 1e-9: return 0.0
    movement_angle = np.arctan2(vec[1], vec[0])
    start_angle = movement_angle + np.pi / 2
    end_angle = movement_angle - np.pi / 2
    arc_points = [(center[0] + r * np.cos(t), center[1] + r * np.sin(t))
                  for t in np.linspace(end_angle, start_angle, arc_resolution)]
    if len(arc_points) < 2: return None
    arc = shapely.LineString(arc_points)
    if not arc.is_valid: return None
    try:
        intersection_geom = shapely.intersection(arc, stock_poly)
        if not intersection_geom or intersection_geom.is_empty: return 0.0
        total_length = 0.0
        if intersection_geom.geom_type == "LineString": total_length = intersection_geom.length
        elif intersection_geom.geom_type == "MultiLineString": total_length = sum(line.length for line in intersection_geom.geoms)
        elif intersection_geom.geom_type == "GeometryCollection":
             for geom in intersection_geom.geoms:
                  if geom.geom_type == "LineString": total_length += geom.length
                  elif geom.geom_type == "MultiLineString": total_length += sum(line.length for line in geom.geoms)
        engagement_angle = total_length / r
        return max(0.0, min(engagement_angle, np.pi))
    except Exception: return None


def advancing_front_ccwe(path, polygon, R, CWE, min_radius=5):
    """Constant cutter work-piece engagement."""
    """Generate advancing front points along path."""
    samples_per_milimeter = 10

    path = np.asarray(path)
    if path.shape[0] <= 1:
        return np.array([]), np.array([])

    # Sample path
    sampler = trimesh.path.traversal.PathSample(path)    
    sample_step = 1.0 / samples_per_milimeter
    
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)
    points = sampler.sample(sample_distances)
    radii_full = boundary_distance(polygon, points)
    radii = radii_full

    # Find first index where radius drops below min_radius
    min_radius_idx = np.argmax(radii < min_radius) if np.any(radii < min_radius) else None    
    if min_radius_idx is not None and min_radius_idx > 0:
        # We found a point where radius drops below min_radius        
        sample_distances = sample_distances[:min_radius_idx]        
        radii = radii[:min_radius_idx]    
    
    closest_indices = []
    cycle_offset = 0
    distance_offset = 0   
       
    while True:    
        slice_start = cycle_offset                
            
        # Calculate engagement for current slice
        slice_distances = sample_distances[slice_start:] - distance_offset
        slice_radii = radii[slice_start:]
        mfe = max_front_engagement(sample_distances=slice_distances, radii=slice_radii, tool_radius=R)       
        
        # Find indices where values are greater than CWE
        indices = np.where(mfe > CWE)[0]

        # Get the first index if any exist
        if len(indices) > 0:
            local_idx = indices[0]-1
        else:
            local_idx = np.argmin(np.abs(mfe - CWE))
        
        global_idx = slice_start + local_idx
        
        # Update tracking variables
        cycle_offset += local_idx
        distance_offset += slice_distances[local_idx]

        if closest_indices and closest_indices[-1] == global_idx:            
            break

        closest_indices.append(global_idx)
        if np.linalg.norm(points[-1] - points[global_idx]) < (radii[-1] + radii[global_idx]+(R*2)):            
            break
        
    # Create buffer circles for remaining points
    last_idx = closest_indices[-1]
    
    # Create union of all circles along remaining path
    remaining_points = points[last_idx:]
    remaining_radii = radii[last_idx:]
    circles = [Point(p[0], p[1]).buffer(r+R) for p, r in zip(remaining_points, remaining_radii)]
    circles_all = shapely.ops.unary_union(circles)
    
    # Create special buffer circles for start and end points
    buffer_factor = 1.05
    circle_start = Point(*points[last_idx]).buffer(radii[last_idx] + (R * buffer_factor))
    circle_end = Point(*points[-1]).buffer(radii[-1] + (R * buffer_factor))
    circle_ends = shapely.union(circle_start, circle_end)
    
    # Get the difference between all circles and the end circles
    stack_rest = shapely.difference(circles_all, circle_ends)
    current_stock = stack_rest.geoms[1]

    # for geom in stack_rest.geoms:
        # create_ring_object(geom.exterior.coords, "rest")

    circle_resolution = 72
    angles = []

    for c, r in zip(points[last_idx::10], radii[last_idx::10]):       
        # Create circle points using numpy        
        theta = np.linspace(0, 2*np.pi, circle_resolution)
        circle_points = np.column_stack([
            c[0] + r * np.cos(theta),
            c[1] + r * np.sin(theta)
        ])
        p_dist = boundary_distance(current_stock, circle_points)        
        p_inside = np.array([current_stock.contains(Point(p)) for p in circle_points])
                        
        # Find points that satisfy both distance and containment criteria        
        valid_indices = np.union1d(np.where(p_inside), np.where(p_dist < R))
        valid_indices = expand_by_x(valid_indices, 1, circle_resolution)
        valid_points = circle_points[valid_indices]

        if len(valid_points) == 0: continue        
        angles = []
        loop_stack = current_stock

        for i in range(len(valid_points)-1):
            point = valid_points[i]
            next_point = valid_points[i+1]
            tool_circle = Point(*next_point).buffer(R)
            angles.append(angle_engagement(tuple(next_point), tuple(point), R, loop_stack, arc_resolution=24))

            # --- Update Stock ---
            try:
                if loop_stack.intersects(tool_circle):
                    # Performance: If stock is MultiPolygon, process each part
                    if loop_stack.geom_type == 'MultiPolygon':
                        new_parts = []
                        for part in loop_stack.geoms:
                            if part.intersects(tool_circle):
                                diff = shapely.difference(part, tool_circle)
                                if not diff.is_empty:
                                        new_parts.append(diff)
                            else:
                                new_parts.append(part)
                        new_stock = shapely.ops.unary_union(new_parts) # Reassemble
                    else: # Single Polygon
                        new_stock = shapely.difference(loop_stack, tool_circle)

                    # Validate and potentially fix new stock
                    if not new_stock.is_valid:
                        new_stock = new_stock.buffer(0)
                        if not new_stock.is_valid:
                            print(f"Error: Stock remains invalid after buffer(0) at step {i+1}. Stopping.")
                            break
                    loop_stack = new_stock
                    if loop_stack.is_empty:
                        print(f"Stopping toolpath generation at step {i+1}: Stock depleted.")
                        break
            except Exception as e:
                print(f"Error during stock update at step {i+1}: {e}")
                break # Safer to stop
        
        if len(valid_points) > 0 and np.max(np.rad2deg(angles)) >= CWE:
            current_stock = shapely.difference(current_stock, shapely.LineString(valid_points).buffer(R))
            create_line_object(valid_points, "valid_points")
            # break
    

    # Store results
    result_distances = sample_distances[closest_indices]
    result_radii = radii_full[closest_indices]     

    return result_distances, result_radii


def advancing_front(path, polygon, step, step_treshold=0.33, min_radius=5):
    """Generate advancing front points along path."""
    path = np.asarray(path)
    if path.shape[0] <= 1:
        return np.array([]), np.array([])

    # Sample path
    sampler = trimesh.path.traversal.PathSample(path)
    sample_step = step / 25.0
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)
    points = sampler.sample(sample_distances)
    radii = boundary_distance(polygon, points)

    # Find end index where radius drops below min_radius
    decreasing_end = np.where(np.diff(radii) >= 0)[0]
    end_idx = decreasing_end[0] + 1 if decreasing_end.size > 0 else len(radii)
    drops_below = np.where(radii[:end_idx] < min_radius)[0]
    end_index = drops_below[0] if drops_below.size > 0 else len(radii) - 1
    end_distance = sample_distances[end_index]

    # Store results
    result_distances = [0.0]
    result_radii = [radii[0]]
    last_idx = 0

    for i in range(1, len(points)):
        if sample_distances[i] >= end_distance:
            vector = points[end_index] - points[last_idx]
            front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[end_index]            
            if front_distance >= (step_treshold * step):               
                result_distances.append(end_distance)
                result_radii.append(radii[end_index])
            break

        vector = points[i] - points[last_idx]
        front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[i]
        if front_distance >= step:
            result_distances.append(sample_distances[i])
            result_radii.append(radii[i])
            last_idx = i

    return np.array(result_distances), np.array(result_radii)


def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, edge_length):
    """Generate points along an arc in counter-clockwise direction."""
    center = np.array(center)
    radius = max(1e-9, radius)
        
    arc_length = radius * (angle_start_rad -angle_end_rad)
    num_points = max(2, int(np.ceil(arc_length / edge_length)) + 1)
    angles = np.linspace(angle_end_rad, angle_start_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculate arc angles based on current and next centers/radii."""
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)
    angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if segment_len > 1e-9 else 0.0

    start_angle = angle_dir + np.pi / 2.0
    end_angle = angle_dir - np.pi / 2.0

    if R_next > R_current and segment_len > 1e-9:
        delta_angle = np.arcsin(np.clip((R_next - R_current) / segment_len, 0.0, 1.0))
        start_angle += delta_angle
        end_angle -= delta_angle

    return start_angle, end_angle


def find_nearby_edges(polygon: Polygon, point: Point, max_distance: float) -> list[LineString]:
    # Extract coordinates and create edges
    coords = list(polygon.exterior.coords)
    edges = [LineString([coords[i], coords[i + 1]]) 
            for i in range(len(coords) - 1)]
    
    # Create spatial index
    tree = STRtree(edges)
    
    # Create a search buffer around the point
    search_area = point.buffer(max_distance)
    
    # Query the spatial index for potential edges
    potential_edges = tree.query(search_area)
    
    # Final precise distance check
    nearby_edges = [
        edges[idx] for idx in potential_edges 
        if edges[idx].distance(point) <= max_distance
    ]
    
    return nearby_edges


def main():
    # Get geometry and validate
    geometry = get_geometry()
    if len(geometry) < 2:
        print("Please select exactly two objects: a boundary polygon (active) and a path.")
        return
    
    if len(geometry) > 2:
        # Create holes from all geometries except first (exterior) and last (path)
        holes = [geom for geom in geometry[1:-1]]
        # Create polygon with exterior and holes
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)
    else:
        polygon = shapely.geometry.Polygon(shell=geometry[0])

    path_shapely = shapely.geometry.LineString(geometry[-1])

    # Setup parameters
    # edge_step = 12
    # edge_step_treshold = 0.4  # small value for debugging
    min_path_radius = 5
    edge_length = 2  
    R = 10.0 #tool radius
    buffer_dist = -R
    CWE = 90.0 #in degrees 

    path = np.array(path_shapely.coords)
    polygon_boundary = polygon.buffer(buffer_dist)
    # create_ring_object(polygon_boundary.exterior.coords, "polygon_boundary")
    if isinstance(polygon_boundary, shapely.geometry.MultiPolygon):
        polygon_boundary = max(polygon_boundary.geoms, key=lambda p: p.area)

    # Generate points    
    result_distances, result_radii = advancing_front_ccwe(path, polygon_boundary, R, CWE, min_path_radius)
    # print(f'result_distances: {result_distances}, result_radii: {result_radii}')
    # return

    if len(result_distances) < 2:
        print("Not enough points generated.")
        return

    path_sampler = trimesh.path.traversal.PathSample(path)
    selected_centers = path_sampler.sample(result_distances)

    # Generate final path
    final_path = []
    all_arc_data = {}

    # Process arcs and transitions
    for i in range(len(selected_centers)):
        C_i, R_i = selected_centers[i], max(1e-6, result_radii[i])
        
        if i < len(selected_centers) - 1:
            C_next, R_next = selected_centers[i+1], max(1e-6, result_radii[i+1])            
            start_angle, end_angle = calculate_arc_angles(C_i, R_i, C_next, R_next)            
        else: # Last arc - use previous arc for direction
            C_prev, R_prev = selected_centers[i-1], max(1e-6, result_radii[i-1])
            segment_vec = C_i - C_prev
            angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if np.linalg.norm(segment_vec) > 1e-9 else 0.0
            start_angle = angle_dir + np.pi/2
            end_angle = angle_dir - np.pi/2

            if R_i > R_prev and np.linalg.norm(segment_vec) > 1e-9:
                delta_angle = np.arcsin(np.clip((R_i - R_prev) / np.linalg.norm(segment_vec), 0.0, 1.0))
                start_angle += delta_angle
                end_angle -= delta_angle

        # Store arc data
        all_arc_data[i] = {
            "center": C_i,
            "radius": R_i,
            "start_angle": start_angle + np.pi * 2,
            "end_angle": end_angle + np.pi * 2
        }

        
        # Generate arc points
        arc_points = generate_arc_points(C_i, R_i, 
                                       all_arc_data[i]["start_angle"],
                                       all_arc_data[i]["end_angle"], 
                                       edge_length)

        # create_line_object(arc_points, f"arc_{i}")

        if i == 0:
            final_path.extend(arc_points)
        else:
            # Generate clothoid transition
            prev_arc = all_arc_data[i-1]
            x0 = float(prev_arc["center"][0] + prev_arc["radius"] * np.cos(prev_arc["start_angle"]))
            y0 = float(prev_arc["center"][1] + prev_arc["radius"] * np.sin(prev_arc["start_angle"]))            
            theta0 = float(prev_arc["start_angle"] + np.pi/2)
            
            x1 = float(C_i[0] + R_i * np.cos(all_arc_data[i]["end_angle"]))
            y1 = float(C_i[1] + R_i * np.sin(all_arc_data[i]["end_angle"]))
            theta1 = float(all_arc_data[i]["end_angle"] + np.pi/2)
            
            clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)
            num_points = max(2, int(np.ceil(clothoid.length / edge_length)) + 1)
            clothoid_points = np.array(clothoid.SampleXY(num_points)).T
            
            final_path.extend(clothoid_points[1:])
            final_path.extend(arc_points[1:])

        if i == len(selected_centers) - 1:
            # print('last arc')
            # print(f'center: {C_i}')
            # dis = polygon_boundary.boundary.distance(shapely.geometry.Point(C_i))
            # print(f'radius: {dis}')
            # nearby_edges = find_nearby_edges(polygon_boundary, C_i, dis)
            # print(nearby_edges)
            ...


    # create_line_object(np.array(final_path), "merged_path")
    # print(f'length: {shapely.LineString(final_path).length}')    


if __name__ == "__main__":    
    main()    
