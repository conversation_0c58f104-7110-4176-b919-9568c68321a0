import numpy as np
from scipy.special import hankel2
import matplotlib.pyplot as plt

# Step 1: Define the Pocket Boundary (e.g., a square)
def define_pocket_boundary():
    # Square pocket with vertices at (0,0), (1,0), (1,1), (0,1)
    boundary = np.array([[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]])  # Closed polygon
    return boundary

# Step 2: Compute the Simplified Medial Axis (SMA) Tree
def compute_sma_tree(boundary):
    # For a square, the medial axis is the diagonals intersecting at the center
    # Here, we approximate SMA segments manually as segments from corners to center
    center = np.array([0.5, 0.5])
    sma_segments = [
        (np.array([0, 0]), center),    # Bottom-left to center
        (np.array([1, 0]), center),    # Bottom-right to center
        (np.array([1, 1]), center),    # Top-right to center
        (np.array([0, 1]), center)     # Top-left to center
    ]
    return sma_segments

# Step 3: Compute Propagation Direction for Each SMA Segment
def compute_propagation_direction(sma_segment, boundary):
    p1, p2 = sma_segment
    d_sb = (p2 - p1) / np.linalg.norm(p2 - p1)  # Direction of SMA segment
    
    # Find the associated boundary segment (closest to midpoint of SMA segment)
    midpoint = (p1 + p2) / 2
    min_dist = float('inf')
    closest_segment = None
    for i in range(len(boundary) - 1):
        b1, b2 = boundary[i], boundary[i + 1]
        dist = np.linalg.norm(np.cross(b2 - b1, b1 - midpoint)) / np.linalg.norm(b2 - b1)
        if dist < min_dist:
            min_dist = dist
            closest_segment = (b1, b2)
    
    # Direction of the associated boundary segment
    b1, b2 = closest_segment
    d_pd = (b2 - b1) / np.linalg.norm(b2 - b1)
    
    # Compute angle between d_pd and d_sb
    cos_theta = np.clip(np.dot(d_pd, d_sb), -1.0, 1.0)
    angle = np.arccos(cos_theta)
    
    # Propagation direction angle in local coordinates
    theta_L = np.pi / 2 - angle
    
    # Local coordinate system: x_l along d_sb, y_l perpendicular
    d_sb_perp = np.array([-d_sb[1], d_sb[0]])  # Rotate 90 degrees counterclockwise
    
    # Propagation direction in global coordinates
    k_pd = np.cos(theta_L) * d_sb + np.sin(theta_L) * d_sb_perp
    return k_pd

# Step 4: Synthesize the Sound Field
def synthesize_sound_field(sma_segments, boundary, stepover=0.1, grid_size=100):
    # Sound field parameters
    v = 1.0  # Speed of sound (arbitrary units)
    f = v / (2 * stepover)  # Frequency based on step-over
    k = 2 * np.pi * f / v  # Wavenumber
    
    # Create a grid over the pocket area
    x = np.linspace(-0.1, 1.1, grid_size)
    y = np.linspace(-0.1, 1.1, grid_size)
    X, Y = np.meshgrid(x, y)
    S_p = np.zeros_like(X, dtype=complex)
    
    # Discretize each SMA segment into point sources
    num_points = 10  # Number of point sources per segment
    for p1, p2 in sma_segments:
        k_pd = compute_propagation_direction((p1, p2), boundary)
        segment_length = np.linalg.norm(p2 - p1)
        points = [p1 + t * (p2 - p1) / (num_points - 1) for t in range(num_points)]
        
        for x_0 in points:
            # Driving function for plane wave synthesis
            D = np.exp(1j * k * np.dot(k_pd, x_0))
            # Distance from grid point to source
            r = np.sqrt((X - x_0[0])**2 + (Y - x_0[1])**2)
            # Avoid singularity at r=0
            r = np.where(r < 1e-6, 1e-6, r)
            # Sound field contribution using 2D Green's function (Hankel function)
            S_p += D * hankel2(0, k * r)
    
    return X, Y, S_p

# Step 5: Extract Tool Paths
def extract_tool_paths(X, Y, S_p):
    # Compute real part of the sound field
    S_p_real = np.real(S_p)
    
    # Plot the sound field and extract contours
    plt.figure(figsize=(8, 8))
    plt.contour(X, Y, S_p_real, levels=np.linspace(-1, 1, 10), cmap='viridis')
    plt.plot(boundary[:, 0], boundary[:, 1], 'k-', linewidth=2, label='Pocket Boundary')
    plt.title('Generated Tool Paths')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.legend()
    plt.axis('equal')
    plt.savefig('generated_tool_paths.jpg')
    plt.show()
    
    # Note: In a real application, extract contour coordinates for machining
    # Here, we just visualize them

# Main Execution
if __name__ == "__main__":
    # Define pocket
    boundary = define_pocket_boundary()
    
    # Compute SMA tree
    sma_segments = compute_sma_tree(boundary)
    
    # Desired step-over distance
    stepover = 0.4
    
    # Synthesize sound field
    X, Y, S_p = synthesize_sound_field(sma_segments, boundary, stepover)
    
    # Extract and visualize tool paths
    extract_tool_paths(X, Y, S_p)
