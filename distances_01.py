import numpy as np

def point_to_segment_distance(p, a, b):
    """
    Calculates the shortest distance from a point p to a line segment (a, b).
    p, a, b are 2D numpy arrays (points).
    """
    # If the segment is just a point (a == b)
    if np.array_equal(a, b):
        return np.linalg.norm(p - a)

    # Vector from a to b (segment vector)
    ab = b - a
    # Vector from a to p
    ap = p - a

    # Project ap onto ab, scaled by the length of ab
    # t is the projection parameter.
    # If t < 0, closest point is a.
    # If t > 1, closest point is b.
    # Otherwise, closest point is on the segment.
    ab_squared_length = np.dot(ab, ab)
    if ab_squared_length == 0: # Should be caught by array_equal, but defensive
        return np.linalg.norm(p - a)

    t = np.dot(ap, ab) / ab_squared_length

    if t < 0.0:
        closest_point = a  # Closest to endpoint a
    elif t > 1.0:
        closest_point = b  # Closest to endpoint b
    else:
        closest_point = a + t * ab  # Projection falls on the segment

    return np.linalg.norm(p - closest_point)


def distances_from_linestring_a_to_linestring_b(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        # Or raise an error, or return array of NaNs/Infs
        return np.full(num_points_a, np.nan)

    all_min_distances = np.full(num_points_a, np.inf)

    for i, point_a in enumerate(ls_a):
        min_dist_for_point_a = np.inf

        if num_points_b == 1: # Linestring B is just a single point
            dist_to_b_point = np.linalg.norm(point_a - ls_b[0])
            min_dist_for_point_a = dist_to_b_point
        else:
            # Iterate through segments of linestring B
            for j in range(num_points_b - 1):
                seg_start = ls_b[j]
                seg_end = ls_b[j+1]
                dist_to_segment = point_to_segment_distance(point_a, seg_start, seg_end)
                if dist_to_segment < min_dist_for_point_a:
                    min_dist_for_point_a = dist_to_segment
        
        all_min_distances[i] = min_dist_for_point_a

    return all_min_distances


import numpy as np

def distances_from_linestring_a_to_linestring_b_vectorized(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b using vectorized operations.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        return np.full(num_points_a, np.nan)

    # If linestring B is a single point
    if num_points_b == 1:
        # Distances from all points in ls_a to the single point in ls_b
        # ls_a is (N, 2), ls_b[0] is (2,), ls_b[0][np.newaxis, :] is (1,2)
        # Broadcasting (N,2) - (1,2) -> (N,2)
        diff = ls_a - ls_b[0] # or ls_a - ls_b[0][np.newaxis, :]
        distances = np.linalg.norm(diff, axis=1)
        return distances

    # Prepare segments of linestring B
    # b_starts are points p1 of segments, b_ends are points p2
    b_starts = ls_b[:-1]  # Shape (M-1, 2)
    b_ends = ls_b[1:]    # Shape (M-1, 2)
    
    num_segments_b = b_starts.shape[0]
    if num_segments_b == 0: # Should be caught by num_points_b == 1, but good check
        # This case implies ls_b had only one point, handled above.
        # If somehow reached, it means ls_b was malformed or empty.
        # For safety, if ls_b had points but no segments (e.g., ls_b = [[0,0]]),
        # we should have handled it with num_points_b == 1.
        # If ls_b was truly empty, already handled.
        # This path is unlikely if prior checks are robust.
        return np.full(num_points_a, np.nan)


    # Expand dimensions for broadcasting:
    # ls_a: (N, 1, 2) - for each point in A, we consider all segments of B
    # b_starts, b_ends: (1, M, 2) - for each segment in B, we consider all points of A
    p = ls_a[:, np.newaxis, :]     # (N, 1, 2)
    s1 = b_starts[np.newaxis, :, :] # (1, M, 2)
    s2 = b_ends[np.newaxis, :, :]   # (1, M, 2)

    # Vectors for segments and from segment start to points in ls_a
    seg_vectors = s2 - s1      # (1, M, 2), segment vectors (s1_s2)
    ap_vectors = p - s1        # (N, M, 2), vectors from s1 to p

    # Length squared of segments. Add epsilon to avoid division by zero for zero-length segments.
    # seg_lens_sq will be (1, M)
    seg_lens_sq = np.sum(seg_vectors**2, axis=2) + 1e-9 # Add epsilon for stability

    # Project ap_vectors onto seg_vectors
    # dot_product will be (N, M)
    dot_product = np.sum(ap_vectors * seg_vectors, axis=2)
    
    # t is the projection parameter
    # t will be (N, M)
    t = dot_product / seg_lens_sq

    # Clip t to be between 0 and 1.
    # If t < 0, closest point is s1.
    # If t > 1, closest point is s2.
    # Else, closest point is s1 + t * seg_vectors.
    t_clipped = np.clip(t, 0, 1)

    # Calculate the closest points on the segments (or their extensions if not clipped)
    # to each point in ls_a.
    # closest_points_on_lines will be (N, M, 2)
    # t_clipped needs to be (N, M, 1) for broadcasting with seg_vectors (1, M, 2)
    # or seg_vectors needs to be (N,M,2) (already is after broadcasting rules apply)
    closest_points_on_segments = s1 + t_clipped[:, :, np.newaxis] * seg_vectors

    # Calculate distances from points in p to these closest points
    # distances_to_segments will be (N, M)
    distances_to_segments = np.linalg.norm(p - closest_points_on_segments, axis=2)
    
    # For each point in ls_a, find the minimum distance to any segment in ls_b
    # min_distances will be (N,)
    min_distances = np.min(distances_to_segments, axis=1)
    
    return min_distances


if __name__ == '__main__':
    # Linestring A: A simple line
    linestring_a = np.array([
        [0, 0],
        [1, 1],
        [2, 0]
    ])

    # Linestring B: A 'U' shape
    linestring_b = np.array([
        [3, 2],
        [4, 2],
        [4, 0],
        [3, 0],
        [3, -1] # Let's add a point that might be closest to (2,0)
    ])
    
    # --- Test Case 1: Standard linestrings ---
    print("--- Test Case 1 ---")
    distances = distances_from_linestring_a_to_linestring_b(linestring_a, linestring_b)
    distances2 = distances_from_linestring_a_to_linestring_b_vectorized(linestring_a, linestring_b)
    print(f"Linestring A:\n{linestring_a}")
    print(f"Linestring B:\n{linestring_b}")
    print(f"Distances from points in A to linestring B: {distances}")    
    print(f"Distances from points in A to linestring B: {distances2}")
