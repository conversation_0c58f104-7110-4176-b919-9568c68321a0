import numpy as np
import time

def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots

def intersect_line_ellipse(line_p1, line_p2, ellipse_center, semi_major_a, semi_minor_b):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """
    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center
    a = semi_major_a
    b = semi_minor_b

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    intersection_points = []
    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            intersection_points.append(np.array([ix, iy]))

    return intersection_points

# --- Example Usage ---
if __name__ == "__main__":
    # Define the line by two points
    line_start = np.array([-40.0, 5.0])
    line_end = np.array([0.0, 0.0])  # Horizontal line through center

    # Define the ellipse
    ellipse_center = np.array([0.0, 0.0])
    semi_major_a = 25.0
    semi_minor_b = 15.0

    # Find intersections
    # time1 = time.time()
    # for i in range(1000):
    #     intersections = intersect_line_ellipse(line_start, line_end, ellipse_center, semi_major_a, semi_minor_b)
    # time2 = time.time()
    # print(f'Time: {time2-time1}')
    intersections = intersect_line_ellipse(line_start, line_end, ellipse_center, semi_major_a, semi_minor_b)

    print(f"Line from {line_start} to {line_end}")
    print(f"Ellipse centered at {ellipse_center} with a={semi_major_a}, b={semi_minor_b}")
    print("Intersection Points:", intersections)
    # Expected: [array([-5.,  0.]), array([5., 0.])]
