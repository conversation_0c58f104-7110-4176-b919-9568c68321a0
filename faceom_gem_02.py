import numpy as np
import math
import matplotlib.pyplot as plt

# --- Existing Helper Functions (from previous implementation) ---

def normalize(v):
    """Normalizes a vector."""
    norm = np.linalg.norm(v)
    if norm == 0:
       return v
    return v / norm

def rotate_vector(v, angle_rad):
    """Rotates a 2D vector by a given angle in radians."""
    rotation_matrix = np.array([
        [math.cos(angle_rad), -math.sin(angle_rad)],
        [math.sin(angle_rad), math.cos(angle_rad)]
    ])
    return rotation_matrix @ v

def line_circle_intersection(line_start, line_dir_vec, circle_center, circle_radius):
    """
    Finds intersection points of a ray (line_start, line_dir_vec) and a circle.
    Returns the intersection point closest to line_start along the ray direction,
    or None if no intersection exists on the ray.
    (Code unchanged from previous version)
    """
    line_dir_vec = normalize(line_dir_vec)
    d = line_start - circle_center
    v = line_dir_vec
    a = 1.0
    b = 2 * d.dot(v)
    c = d.dot(d) - circle_radius**2
    discriminant = b**2 - 4*a*c
    if discriminant < 0:
        return None
    else:
        sqrt_discriminant = math.sqrt(discriminant)
        k1 = (-b + sqrt_discriminant) / (2*a)
        k2 = (-b - sqrt_discriminant) / (2*a)
        valid_k = []
        if k1 >= -1e-9: valid_k.append(k1)
        if k2 >= -1e-9: valid_k.append(k2)
        if not valid_k:
            return None
        else:
            chosen_k = min(valid_k)
            intersection_point = line_start + chosen_k * line_dir_vec
            return intersection_point

# --- New Helper Functions for Discrete Contours ---

def get_tangent_at_index(contour_points, index):
    """
    Approximates the tangent vector at a given index on the polyline.
    Uses central difference if possible, otherwise forward/backward difference.
    """
    n_points = len(contour_points)
    if n_points < 2:
        return np.array([1.0, 0.0]) # Default tangent for single point

    if index == 0:
        # Forward difference
        tangent = contour_points[1] - contour_points[0]
    elif index == n_points - 1:
        # Backward difference
        tangent = contour_points[index] - contour_points[index - 1]
    else:
        # Central difference (preferred)
        tangent = contour_points[index + 1] - contour_points[index - 1]

    return normalize(tangent)

def find_index_at_arc_length(contour_points, start_index, target_step_length):
    """
    Finds the index on the polyline that is approximately target_step_length
    away from the start_index, moving forward along the segments.
    Returns the index of the *endpoint* of the segment containing the target point.
    """
    n_points = len(contour_points)
    if start_index >= n_points - 1 or target_step_length <= 0:
        return start_index # Cannot move forward or zero step

    current_length = 0.0
    current_index = start_index

    while current_index < n_points - 1:
        p1 = contour_points[current_index]
        p2 = contour_points[current_index + 1]
        segment_length = np.linalg.norm(p2 - p1)

        if current_length + segment_length >= target_step_length:
            # Target length falls within this segment or at its end
            return current_index + 1
        else:
            current_length += segment_length
            current_index += 1

    # If target_step_length is longer than remaining contour, return the last index
    return n_points - 1

def find_closest_vertex_index(contour_points, start_search_index, query_point):
    """
    Finds the index of the vertex closest to query_point, searching forward
    from start_search_index.
    """
    n_points = len(contour_points)
    if start_search_index >= n_points:
        return n_points - 1

    min_dist_sq = float('inf')
    closest_index = start_search_index

    # Search forward from the starting index
    for i in range(start_search_index, n_points):
        dist_sq = np.sum((contour_points[i] - query_point)**2) # Squared distance is faster
        if dist_sq < min_dist_sq:
            min_dist_sq = dist_sq
            closest_index = i
        # Optimization: If distance starts increasing significantly, we might stop early
        # This requires careful implementation, omitted for simplicity here.

    return closest_index

# --- FACEOM Algorithm for Discrete Contours ---

def faceom_discrete(contour_points, start_index, beta_deg, theta_deg, r_tool, arc_length_step, max_steps=10000):
    """
    Implements FACEOM using a discrete NumPy array for the contour.

    Args:
        contour_points: NumPy array of shape (N, 2) representing the contour.
        start_index: The index in contour_points to start from.
        beta_deg: Initial angle (degrees) between tool path tangent and contour tangent.
        theta_deg: Desired constant cutter engagement angle (degrees).
        r_tool: Tool radius.
        arc_length_step: The target distance to advance along the contour polyline
                         in each step (approximates dt). Controls step density.
        max_steps: Maximum number of iterations.

    Returns:
        List of tool path points (np.array).
        List of corresponding contact points on the contour (indices).
    """
    n_points = len(contour_points)
    if n_points < 2:
        raise ValueError("Contour must have at least 2 points.")
    if start_index < 0 or start_index >= n_points:
         raise ValueError("start_index out of bounds.")
    if r_tool <= 0:
        raise ValueError("Tool radius must be positive.")
    if arc_length_step <= 0:
         raise ValueError("arc_length_step must be positive.")

    # Convert angles to radians
    theta_rad = math.radians(theta_deg)
    beta_rad = math.radians(beta_deg)
    alpha_rad = math.radians(90.0) - theta_rad # Angle for rotating PC vector

    # --- Initialization (Similar to parametric, but using index) ---
    i = 0
    current_contour_index = start_index
    tool_path_points = []
    contact_indices = [] # Store indices of contact points

    # Initial contour point C0
    c0 = contour_points[current_contour_index]

    # Initial contour tangent c'(t0) approximated at the index
    c_prime_0 = get_tangent_at_index(contour_points, current_contour_index)
    if np.linalg.norm(c_prime_0) < 1e-9:
         raise ValueError(f"Contour tangent is near zero at index {current_contour_index}. Cannot initialize.")

    # Initial rotation angle omega (Fig 5)
    omega_rad = beta_rad + theta_rad + math.radians(90.0)

    # Rotate tangent vector C'(t0) by omega
    direction_c0_to_p0 = -rotate_vector(c_prime_0, omega_rad)

    # Initial tool center P0
    p0 = c0 + r_tool * normalize(direction_c0_to_p0)
    tool_path_points.append(p0)
    contact_indices.append(current_contour_index)

    # Initial tool path tangent v0 (Fig 2 / Fig 4 logic)
    vec_p0_c0 = c0 - p0
    if np.linalg.norm(vec_p0_c0) < 1e-9:
         raise ValueError("Initial P0 and C0 are coincident. Cannot calculate initial v0.")
    v0 = rotate_vector(vec_p0_c0, alpha_rad)
    vi = normalize(v0) # Current tool path direction vector

    # --- Main Loop ---
    last_contour_index_processed = current_contour_index
    while last_contour_index_processed < n_points - 1 and i < max_steps:
        pi = tool_path_points[-1] # Current tool center position

        # Step 1 (Adaptation): Find the *target* contour point C_target
        # Find the index on the polyline approx. arc_length_step away
        target_contour_index = find_index_at_arc_length(
            contour_points, last_contour_index_processed, arc_length_step
        )
        if target_contour_index <= last_contour_index_processed and target_contour_index < n_points -1 :
             # If step size is too small to even reach the next vertex, force advance
             target_contour_index = last_contour_index_processed + 1
             # print(f"Warning: Arc length step too small at step {i+1}. Forcing advance to index {target_contour_index}.")


        c_target = contour_points[target_contour_index]

        # Step 2: Find intersection P(i+1) between ray (Pi, vi) and circle(C_target, r_tool)
        p_next = line_circle_intersection(pi, vi, c_target, r_tool)

        if p_next is None:
            print(f"Warning: No intersection found at step {i+1} targeting index {target_contour_index}. Stopping.")
            # Add more debug info if needed
            # dist_pi_ctarget = np.linalg.norm(c_target - pi)
            # print(f"  Distance Pi to C_target = {dist_pi_ctarget}, r_tool = {r_tool}")
            break

        # Step 3 (Adaptation): Find *actual* contact point C_actual_next (closest vertex)
        # and calculate next tool path tangent v(i+1)
        # Search forward from the *last processed* index to find the closest vertex to P_next
        actual_contact_index = find_closest_vertex_index(
            contour_points, last_contour_index_processed, p_next
        )
        c_actual_next = contour_points[actual_contact_index]

        vec_p_next_c_actual_next = c_actual_next - p_next

        # Ensure the distance is roughly r_tool (sanity check)
        actual_dist = np.linalg.norm(vec_p_next_c_actual_next)
        if abs(actual_dist - r_tool) > r_tool * 0.5: # Allow some tolerance
             print(f"Warning: Distance P(i+1) to C_actual_next ({actual_dist:.3f}) "
                   f"differs significantly from r_tool ({r_tool:.3f}) at step {i+1} "
                   f"(contact index {actual_contact_index}).")
             # This might happen with coarse contours or large steps. Could break or continue.

        if actual_dist < 1e-9:
            print(f"Warning: P(i+1) and C_actual_next are coincident at step {i+1}. Using previous direction.")
            v_next = vi # Maintain direction as fallback
        else:
            # Rotate P(i+1)C(i+1) by alpha around P(i+1)
            v_next_unnormalized = rotate_vector(vec_p_next_c_actual_next, alpha_rad)
            v_next = normalize(v_next_unnormalized)

        # Step 4: Update state
        tool_path_points.append(p_next)
        contact_indices.append(actual_contact_index)
        vi = v_next
        last_contour_index_processed = actual_contact_index # Update based on actual contact
        i += 1

        # Break if we reached the end of the contour
        if last_contour_index_processed >= n_points - 1:
             # print("Reached end of contour.")
             break


    if i == max_steps:
        print(f"Warning: Reached maximum steps ({max_steps}).")

    return np.array(tool_path_points), np.array(contact_indices)


# --- Example Usage ---

# Create a sample 2D NumPy contour (e.g., a rectangle with rounded corners)
def create_rounded_rect(width, height, radius, num_points_corner=10):
    w = width / 2.0 - radius
    h = height / 2.0 - radius
    points = []

    # Top right corner
    center = (w, h)
    for i in range(num_points_corner + 1):
        angle = math.pi / 2.0 * (i / num_points_corner)
        points.append((center[0] + radius * math.cos(angle), center[1] + radius * math.sin(angle)))

    # Top left corner
    center = (-w, h)
    for i in range(num_points_corner + 1):
        angle = math.pi / 2.0 + math.pi / 2.0 * (i / num_points_corner)
        points.append((center[0] + radius * math.cos(angle), center[1] + radius * math.sin(angle)))

    # Bottom left corner
    center = (-w, -h)
    for i in range(num_points_corner + 1):
        angle = math.pi + math.pi / 2.0 * (i / num_points_corner)
        points.append((center[0] + radius * math.cos(angle), center[1] + radius * math.sin(angle)))

    # Bottom right corner
    center = (w, -h)
    for i in range(num_points_corner + 1):
        angle = 3.0 * math.pi / 2.0 + math.pi / 2.0 * (i / num_points_corner)
        points.append((center[0] + radius * math.cos(angle), center[1] + radius * math.sin(angle)))

    # Close the loop (optional, FACEOM currently handles open contours)
    # points.append(points[0])
    return np.array(points)

# Parameters for example
contour_data = create_rounded_rect(width=4, height=3, radius=0.5, num_points_corner=15)
start_idx = 0
r_tool_discrete = 0.25
theta_deg_discrete = 75.0
beta_deg_discrete = 90.0 # Start perpendicular
step_discrete = 0.5 # Target arc length step along contour

print("\nGenerating tool path for Discrete Contour...")
tool_path_discrete, contact_idxs_discrete = faceom_discrete(
    contour_data,
    start_idx,
    beta_deg_discrete, theta_deg_discrete,
    r_tool_discrete, step_discrete
)
print(f"Generated {len(tool_path_discrete)} points.")

# --- Visualization ---
plt.style.use('seaborn-v0_8-whitegrid')

plt.figure(figsize=(10, 8))
# Plot original contour
plt.plot(contour_data[:, 0], contour_data[:, 1], 'b.-', label='Original Discrete Contour', markersize=3, linewidth=1)

# Plot generated tool path
if len(tool_path_discrete) > 0:
    plt.plot(tool_path_discrete[:, 0], tool_path_discrete[:, 1], 'r.-', label='FACEOM Tool Path P(t)')
    # Plot tool circles at intervals
    interval = max(1, len(tool_path_discrete) // 25) # Show ~25 circles
    for i in range(0, len(tool_path_discrete), interval):
        p_tool = tool_path_discrete[i]
        contact_idx = contact_idxs_discrete[i]
        c_contact = contour_data[contact_idx]

        circle = plt.Circle(p_tool, r_tool_discrete, color='grey', fill=False, alpha=0.3)
        plt.gca().add_patch(circle)
        # Plot connection line P_i C_i (for verification)
        plt.plot([p_tool[0], c_contact[0]], [p_tool[1], c_contact[1]], 'g--', alpha=0.4)
        # Mark contact points
        plt.plot(c_contact[0], c_contact[1], 'go', markersize=4, alpha=0.6)


plt.title(f'FACEOM Example: Discrete Contour (theta={theta_deg_discrete} deg)')
plt.xlabel('X [mm]')
plt.ylabel('Y [mm]')
plt.legend()
plt.grid(True)
plt.axis('equal')
plt.show()