import numpy as np

def _wrap(angle):
    """Wrap angle to the interval (-π, π]."""
    # Using modulo arithmetic to map angle to the desired range
    return (angle + np.pi) % (2 * np.pi) - np.pi

def _interval_overlap_length(start1, end1, start2, end2):
    """Calculates the length of overlap between [start1, end1] and [start2, end2]."""
    # Find the latest start time and earliest end time
    overlap_start = np.maximum(start1, start2)
    overlap_end = np.minimum(end1, end2)
    # Overlap length is positive only if overlap_end > overlap_start
    return np.maximum(0.0, overlap_end - overlap_start)

def _front_side_engagement(beta, theta, feed_dir):
    """
    Simplified vectorized calculation of front-side engagement angle.

    Calculates the angular length of the "front" semicircle (relative to
    feed direction) that is *not* covered by the cleared wedge defined
    by beta and theta.

    Args:
        beta: Angle(s) from current cutter center to previous loop center.
        theta: Half-angle(s) of the cleared wedge from the previous loop.
        feed_dir: Angle(s) representing the current feed direction (tangent).

    Returns:
        Front-side engagement angle(s) in radians, between 0 and π.
    """
    # Define the "front" interval relative to the feed direction (angle 0)
    front_start, front_end = -np.pi / 2, np.pi / 2
    total_front_angle = np.pi # Length of the front interval

    # Express the center of the cleared wedge relative to the feed direction
    beta_rel = _wrap(beta - feed_dir)

    # Calculate the absolute start and end angles of the cleared wedge
    # Note: These angles can be outside the [-pi, pi] range initially
    wedge_start = beta_rel - theta
    wedge_end = beta_rel + theta

    # Calculate the total length of the cleared wedge that falls within the
    # conceptual "front" interval by checking overlaps with the main front
    # interval and its periodic images shifted by +/- 2*pi. This handles wrapping.
    cleared_in_front = (
        _interval_overlap_length(wedge_start, wedge_end, front_start, front_end) +
        _interval_overlap_length(wedge_start, wedge_end, front_start - 2 * np.pi, front_end - 2 * np.pi) +
        _interval_overlap_length(wedge_start, wedge_end, front_start + 2 * np.pi, front_end + 2 * np.pi)
    )

    # Ensure the calculated cleared length does not exceed the total front angle
    # (e.g., due to large theta or floating point inaccuracies).
    cleared_in_front = np.minimum(cleared_in_front, total_front_angle)

    # The engagement is the portion of the front interval *not* cleared.
    engagement = total_front_angle - cleared_in_front

    # Ensure engagement is non-negative due to potential float precision issues.
    return np.maximum(0.0, engagement)


def _calculate_single_loop_max_engagement(R, r_prev, r_next, d_off, nsamples=360):
    """
    Internal helper: Calculate max front-side engagement for ONE transition.

    Args:
        R: Cutter radius [mm].
        r_prev: Radius of the previous trochoid loop [mm].
        r_next: Radius of the current trochoid loop [mm].
        d_off: Distance between the centers of the previous and current loops [mm].
               MUST be positive.
        nsamples: Number of points to sample along the current loop's circumference.

    Returns:
        Maximum front-side engagement angle in degrees (0 to 180).
        Returns NaN if d_off is not positive or other issues occur.
    """
    # Basic input validation
    if d_off <= 0:
        # print(f"Warning: Non-positive d_off ({d_off}) encountered. Returning NaN.")
        return np.nan
    if R <= 0 or r_prev < 0 or r_next < 0:
        # print(f"Warning: Non-positive radius (R={R}, r_prev={r_prev}, r_next={r_next}). Returning NaN.")
        return np.nan

    # Effective radius of the clearance circle left by the previous loop pass
    R_prev_eff = R + r_prev

    # Sample angles around the current trochoid loop path
    psi = np.linspace(0, 2 * np.pi, nsamples, endpoint=False)

    # Calculate cutter center coordinates (x, y) relative to *previous* loop center
    # Previous loop center @ (0,0). Current loop center @ (d_off, 0).
    # Point on current loop path (cutter center):
    cx = d_off + r_next * np.cos(psi)
    cy = r_next * np.sin(psi)

    # Distance 'd' from the point (cx, cy) to the previous loop center (0,0)
    d = np.hypot(cx, cy)
    # Avoid division by zero if d is exactly zero (cutter center at prev center)
    d = np.maximum(d, 1e-12) # Add small epsilon

    # Calculate the feed direction (tangent to the current loop)
    tx = -r_next * np.sin(psi)
    ty = r_next * np.cos(psi)
    feed_dir = np.arctan2(ty, tx) # Angle of the tangent vector

    # Angle 'beta' from the current cutter center (cx, cy) back to the
    # previous loop center (0, 0). This is atan2(0-cy, 0-cx).
    beta = np.arctan2(-cy, -cx)

    # --- Determine Engagement State for each sample point ---
    # Mask for cases where the cutter circle is entirely outside the
    # previous effective clearance circle (no intersection).
    no_intersection_mask = d >= R + R_prev_eff

    # Mask for cases where one circle is completely inside the other.
    inside_clearance_mask = d <= abs(R_prev_eff - R)

    # Initialize engagement angles (default to 0)
    engaged_angle = np.zeros_like(d)

    # Case 1: No intersection -> Tool is fully engaged with new material
    # across its front half. Front engagement is pi (180 degrees).
    engaged_angle[no_intersection_mask] = np.pi

    # Case 2: One circle inside the other: engagement is effectively zero
    # relative to the previous pass in the context of *front-side* calculation.
    # If R_prev_eff is tiny and inside R, front engagement would be pi, but
    # the _front_side_engagement handles the geometry.
    # Initialize to zero, main calc below handles partial overlap correctly.

    # Case 3: Circles intersect -> Calculate partial engagement.
    calc_mask = ~(no_intersection_mask | inside_clearance_mask)

    if np.any(calc_mask):
        d_calc = d[calc_mask] # Distances for intersecting cases
        beta_calc = beta[calc_mask]
        feed_dir_calc = feed_dir[calc_mask]

        # Avoid numerical issues near R_prev_eff == R and d_calc == 0
        # This check is conceptually important but d_calc > 0 enforced by d_off > 0
        # and the small epsilon added to d.

        # Calculate the half-angle 'theta' of the circular segment cut into
        # the current cutter circle by the previous effective clearance circle.
        # cos(theta) = (d_calc^2 + R^2 - R_prev_eff^2) / (2 * d_calc * R)
        # Denominator check: 2 * d_calc * R should not be zero
        denominator = 2 * d_calc * R
        # If R=0 this will be zero, handled by initial checks
        # If d_calc is zero (or epsilon), this could be near zero.
        safe_denom_mask = denominator > 1e-12
        
        if not np.all(safe_denom_mask):
             # This case shouldn't happen with d_off > 0 and R > 0, but as safety:
             print(f"Warning: Near-zero denominator in arccos calculation. Setting theta=pi for affected points.")
             theta = np.full_like(d_calc, np.pi) # Default to full clearance if calculation fails
        else:
            cos_theta_arg = (d_calc**2 + R**2 - R_prev_eff**2) / denominator
            # Clamp argument for arccos to avoid NaN due to float precision near +/-1
            cos_theta_arg = np.clip(cos_theta_arg, -1.0, 1.0)
            theta = np.arccos(cos_theta_arg) # Half-angle of the cleared wedge

            # Calculate the front-side engagement using the simplified function
            calculated_engagement = _front_side_engagement(
                beta_calc, theta, feed_dir_calc
            )
            engaged_angle[calc_mask] = calculated_engagement


    # Find the maximum engagement angle across all sampled points
    # Check if engaged_angle contains any NaNs before finding max
    if np.isnan(engaged_angle).any():
        # print(f"Warning: NaN values encountered in engagement angle calculation for R={R}, r_prev={r_prev}, r_next={r_next}, d_off={d_off}. Returning NaN.")
        return np.nan

    phi_max_rad = np.max(engaged_angle)
    phi_max_deg = np.degrees(phi_max_rad)

    return phi_max_deg


def calculate_max_engagement_series(R, radii, sample_distances, nsamples=360):
    """
    Calculates the maximum front-side engagement angle for a series of
    trochoid loops defined by their radii and center distances.

    Args:
        R: Cutter radius [mm] (scalar, must be > 0).
        radii: NumPy array of trochoid loop radii [mm]. Each element >= 0.
        sample_distances: NumPy array of cumulative distances of loop centers [mm].
                          Must be the same length as radii and monotonically increasing.
        nsamples: Number of points to sample along each loop's circumference.

    Returns:
        NumPy array of maximum front-side engagement angles in degrees (0 to 180).
        The length is len(radii) - 1. Contains NaN for any transitions where
        input parameters are invalid (e.g., non-increasing distances).
    """
    if not isinstance(radii, np.ndarray):
        radii = np.array(radii)
    if not isinstance(sample_distances, np.ndarray):
        sample_distances = np.array(sample_distances)

    if len(radii) != len(sample_distances):
        raise ValueError("radii and sample_distances must have the same length.")
    if R <= 0:
        raise ValueError("Cutter radius R must be positive.")
    if np.any(radii < 0):
        raise ValueError("All loop radii must be non-negative.")
    if len(radii) < 2:
        return np.array([]) # No transitions to calculate

    num_transitions = len(radii) - 1
    max_engagement_results = np.zeros(num_transitions) * np.nan # Initialize with NaN

    # Calculate offsets between consecutive loop centers
    dist_offsets = np.diff(sample_distances)

    # Check if all distances are strictly increasing
    if np.any(dist_offsets <= 0):
        print(f"Warning: sample_distances are not strictly increasing. "
              f"Calculations for non-positive offsets will result in NaN.")

    for i in range(num_transitions):
        r_prev = radii[i]
        r_next = radii[i+1]
        d_off = dist_offsets[i]

        # d_off <= 0 check is handled implicitly by _calculate_single_loop_max_engagement returning NaN
        # Call the single-loop calculation logic
        max_engagement_results[i] = _calculate_single_loop_max_engagement(
            R, r_prev, r_next, d_off, nsamples
        )

    return max_engagement_results


# --- Example Usage ---
R_cutter = 5.0   # cutter radius [mm]

# Define loop radii and their center positions (cumulative distance)
# loop_radii = np.array([10.0, 10.5, 10.8, 11.0, 10.8, 10.5, 10.0]) # Example radii
loop_radii = np.array([10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0]) # Example radii
# Center positions along a path
center_distances = np.array([0.0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0]) # Example distances

# Calculate the max engagement for each transition
max_engagements_deg = calculate_max_engagement_series(
    R_cutter, loop_radii, center_distances)
print(max_engagements_deg)
