import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Polygon
from shapely.affinity import affine_transform

# Function to generate ellipse points
def generate_ellipse_points(center, a, b, theta, num_points=100):
    """Generate points for an ellipse given center, axes lengths, and rotation."""
    t = np.linspace(0, 2 * np.pi, num_points)
    x = center[0] + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
    y = center[1] + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)
    return np.column_stack((x, y))

# Function to rotate the red ellipse around the touching point
def rotate_ellipse(c_red, a_red, b_red, θ_red, θ_green, p0):
    """Rotate red ellipse around touching point p0 to align with green ellipse."""
    # Generate red ellipse points
    red_points = generate_ellipse_points(c_red, a_red, b_red, θ_red)
    red_polygon = Polygon(red_points)
    
    # Compute rotation angle to align with green ellipse
    Δθ = θ_green - θ_red
    cos_Δθ = np.cos(Δθ)
    sin_Δθ = np.sin(Δθ)
    x0, y0 = p0
    
    # Affine transformation matrix for rotation around p0
    matrix = [
        cos_Δθ, -sin_Δθ, x0 - x0 * cos_Δθ + y0 * sin_Δθ,
        sin_Δθ, cos_Δθ, y0 - x0 * sin_Δθ - y0 * cos_Δθ
    ]
    
    # Apply rotation
    rotated_red_polygon = affine_transform(red_polygon, matrix)
    return rotated_red_polygon

# Function to plot both ellipses
def plot_ellipses(c_green, a_green, b_green, θ_green, c_red, a_red, b_red, θ_red, p0):
    """Plot the green ellipse and the rotated red ellipse with touching point."""
    # Generate points for green ellipse
    green_points = generate_ellipse_points(c_green, a_green, b_green, θ_green)
    
    # Rotate red ellipse around p0
    rotated_red_polygon = rotate_ellipse(c_red, a_red, b_red, θ_red, θ_green, p0)
    rotated_red_points = np.array(rotated_red_polygon.exterior.coords)
    
    # Create the plot
    plt.figure(figsize=(8, 8))
    plt.plot(green_points[:, 0], green_points[:, 1], color='green', label='Green Ellipse')
    plt.plot(rotated_red_points[:, 0], rotated_red_points[:, 1], color='red', label='Rotated Red Ellipse')
    plt.scatter(p0[0], p0[1], color='black', label='Touching Point (p0)')
    
    # Ensure equal aspect ratio for undistorted ellipses
    plt.gca().set_aspect('equal', adjustable='box')
    
    # Add labels, title, and legend
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.title('Aligned Ellipses with Touching Point')
    plt.legend()
    
    # Add a grid and display the plot
    plt.grid(True)
    plt.show()

# Example parameters
c_green = (0, 0)      # Green ellipse center
a_green = 5           # Green major axis
b_green = 3           # Green minor axis
θ_green = np.pi / 4   # Green rotation (45 degrees)

c_red = (1, 1)        # Red ellipse center
a_red = 2             # Red major axis
b_red = 1             # Red minor axis
θ_red = np.pi / 6     # Red rotation (30 degrees)
p0 = (3, 2)           # Touching point

# Run the plotting function
plot_ellipses(c_green, a_green, b_green, θ_green, c_red, a_red, b_red, θ_red, p0)