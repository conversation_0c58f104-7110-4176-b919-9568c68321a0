"""
sim_pixel_milling.py
====================
Pixel‑based 2.5‑D milling simulation (flat‐end mill).

Implements:
    * tool_matrix(D, p)           – from previous version
    * build_workpiece(bbox, Δ)    – rectangular blank
    * simulate(tool, wp, path, …) – loop along tool path
      ↳ returns a pandas DataFrame with A_fz, V_fz, MRR, a_e, θ …

Author: 2025‑04  (o3 High / GPT‑4)
"""
from __future__ import annotations
import numpy as np
import pandas as pd



# ----------------------------------------------------------------------
# 0. Helper – tool bitmap (unchanged)
# ----------------------------------------------------------------------
def tool_matrix(D: float, p: int) -> np.ndarray:
    """
    Binary mask of a ØD flat end‑mill, p×p pixels.
    """
    Δ = D / p
    idx = np.arange(p) - p/2 + 0.5
    x = idx * Δ
    y = x.copy()
    T = ((x[None, :]**2 + y[:, None]**2) <= (D/2)**2)
    return T.astype(np.uint8)      # 1 = material


# ----------------------------------------------------------------------
# 1. Work‑piece bitmap (simple rectangle)
# ----------------------------------------------------------------------
def build_workpiece(
        bbox: tuple[float, float, float, float],
        Δ: float
) -> tuple[np.ndarray, dict]:
    """
    bbox = (xmin, xmax, ymin, ymax)  [mm]

    Returns
    -------
    W     : bool ndarray (True = material)
    meta  : {Δ, xmin, ymin}  for later index ↔ physical coordinate conversion
    """
    xmin, xmax, ymin, ymax = bbox
    nx = int(np.ceil((xmax - xmin)/Δ))
    ny = int(np.ceil((ymax - ymin)/Δ))
    W = np.ones((ny, nx), dtype=bool)     # full stock
    meta = dict(Δ=Δ, xmin=xmin, ymin=ymin, shape=W.shape)
    return W, meta


# ----------------------------------------------------------------------
# 2. One step – overlap, remove, area
# ----------------------------------------------------------------------
def _remove_and_area(
        W: np.ndarray,
        T: np.ndarray,
        cx: float, cy: float, meta: dict
) -> float:
    """
    Overlap tool bitmap *T* centred at (cx, cy) [mm] with workpiece *W*,
    knock the overlapped pixels out, and return removed AREA  [mm²].
    """
    Δ = meta['Δ']
    r0 = int(round((cy - meta['ymin'])/Δ))        # row index in W
    c0 = int(round((cx - meta['xmin'])/Δ))        # col index in W
    p = T.shape[0]                                # tool size (square)

    # top‑left corner of tool bitmap within W
    rs = r0 - p//2
    cs = c0 - p//2

    # clip to work‑piece border
    rW0, rW1 = max(rs, 0), min(rs + p, W.shape[0])
    cW0, cW1 = max(cs, 0), min(cs + p, W.shape[1])
    if rW0 >= rW1 or cW0 >= cW1:
        return 0.0                                # completely outside

    # corresponding slice inside tool bitmap
    rT0, rT1 = rW0 - rs, rW1 - rs
    cT0, cT1 = cW0 - cs, cW1 - cs

    # Boolean ‘and’
    overlap = T[rT0:rT1, cT0:cT1] & W[rW0:rW1, cW0:cW1]
    removed_px = overlap.sum(dtype=np.int64)
    W[rW0:rW1, cW0:cW1][overlap] = False          # update stock

    return removed_px * Δ * Δ                     # [mm²]


# ----------------------------------------------------------------------
# 3. Main simulation loop
# ----------------------------------------------------------------------
def simulate(
        tool_D: float,
        p: int,
        bbox: tuple[float, float, float, float],
        path: np.ndarray,
        ap: float,             # axial depth [mm]
        fz: float,             # feed per tooth [mm]
        z: int,                # teeth
        n_rpm: float           # spindle speed [rev/min]
) -> pd.DataFrame:
    """
    Parameters
    ----------
    path  : (N,2) ndarray – list of cutter‑centre positions [mm]
            Should be sampled at *fz* increments!  →  one entry = one tooth pass.
    Returns
    -------
    pandas DataFrame with columns:
        area  [mm²]   A_fz
        V_fz [mm³]
        MRR  [mm³/min]
        a_e  [mm]
        theta[deg]
    """
    Δ = tool_D / p                       # keep pixel size identical to tool discret.
    W, meta = build_workpiece(bbox, Δ)
    T = tool_matrix(tool_D, p)
    R = tool_D / 2.0
    vf = n_rpm * z * fz                  # programmed feed [mm/min]

    data = []
    for step, (cx, cy) in enumerate(path):
        A_fz = _remove_and_area(W, T, cx, cy, meta)  # mm²
        V_fz = A_fz * ap                             # mm³
        MRR  = V_fz * z * n_rpm / 1000.0             # → cm³/min if you want mm³/min keep /1
        a_e  = A_fz / fz if fz > 0 else 0.0          # Eq. (12) A_fz = a_e * fz
        # safer fall‑backs
        a_e  = min(max(a_e, 0.0), 2*R)
        theta = np.degrees(np.arccos((R - a_e)/R)) if a_e <= R else 180.0

        data.append(dict(step=step, X=cx, Y=cy,
                         area=A_fz, V_fz=V_fz, MRR=MRR,
                         a_e=a_e, theta=theta))

    return pd.DataFrame(data)


def resample_path(P, ds):
    """Linear resampling of a poly‑line at spacing ds."""
    out = [P[0]]
    for p0, p1 in zip(P[:-1], P[1:]):
        seg = p1 - p0
        L = np.linalg.norm(seg)
        n = max(int(np.floor(L/ds)), 1)
        for k in range(1, n+1):
            out.append(p0 + seg * (k*ds/L))
    return np.asarray(out)

# ----------------------------------------------------------------------
# 4. Quick demo
# ----------------------------------------------------------------------
if __name__ == "__main__":
    # --- tool & process -------------------------------------------------
    D  = 10.0        # mm
    p  = 32          # pixels per diameter
    ap = 5.0         # axial depth
    fz = 0.05        # mm
    z  = 4
    n  = 4000.0      # rpm

    # --- work‑piece 50 × 30 mm -----------------------------------------
    bbox = ( -5, 55, -5, 35 )          # little clearance around (0,0)→(50,30)    
    
    path = np.array([[10.0, 10.0],                     
                     [10.0005, 10.0],
                     [10.006, 10.0],
                     [10.01, 10.0]])
    
    # dense = resample_path(np.array([[10,10],[15,10],[10,10]]), fz)  # ds = fz

    df = simulate(D, p, bbox, path, ap, fz, z, n)

    print(df.head())
    print("\nPeak MRR  = {:.2f} mm³/min".format(df['MRR'].max()))
    print("Peak θ    = {:.1f} °".format(df['theta'].max()))