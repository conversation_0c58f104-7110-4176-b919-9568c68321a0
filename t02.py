import networkx as nx
from shapely import Polygon, MultiPolygon

def build_buffer_containment_graph(original_polygon, buffer_depths):
    """
    Builds a directed graph representing containment relationships between polygons generated through sequential buffering.
    
    Args:
        original_polygon (shapely.Polygon): The original polygon to buffer.
        buffer_depths (list of float): List of negative buffer values to apply sequentially.
        
    Returns:
        networkx.DiGraph: Directed graph where edges point from containing polygon to contained polygon.
    """
    G = nx.DiGraph()
    
    # Use WKT to handle polygon uniqueness
    original_wkt = original_polygon.wkt
    G.add_node(original_wkt, polygon=original_polygon)
    node_data = {original_wkt: original_polygon}
    
    queue = [original_wkt]
    print(queue)
    
    for depth in buffer_depths:
        next_queue = []
        for parent_wkt in queue:
            parent_poly = node_data[parent_wkt]
            buffered = parent_poly.buffer(depth)
            
            if buffered.is_empty:
                continue
            
            # Handle MultiPolygon by exploding into individual Polygons
            children = []
            if isinstance(buffered, MultiPolygon):
                children = list(buffered.geoms)
            elif isinstance(buffered, Polygon):
                children = [buffered]
            
            for child in children:
                if child.is_empty:
                    continue
                
                # Check if parent actually contains the child
                if not parent_poly.contains(child):
                    continue
                
                child_wkt = child.wkt
                
                # Add child to graph if not already present
                if child_wkt not in node_data:
                    node_data[child_wkt] = child
                    G.add_node(child_wkt, polygon=child)
                
                # Add edge from parent to child
                G.add_edge(parent_wkt, child_wkt)
                next_queue.append(child_wkt)
        
        queue = next_queue
        if not queue:
            break  # No more polygons to process
    
    return G

# Example usage:
if __name__ == "__main__":
    # Example original polygon (a square)
    original = Polygon([(0, 0), (10, 0), (10, 10), (0, 10)])
    buffer_depths = [-0.2, -0.3, -0.4, -0.5, -0.6]  # Example buffer values
    
    containment_graph = build_buffer_containment_graph(original, buffer_depths)
    
    # To access the polygon from a node:
    # node_data = nx.get_node_attributes(containment_graph, 'polygon')
    # polygon = node_data[node_wkt]
    
    #print("Containment Graph Nodes:", containment_graph.number_of_nodes())
    #print("Containment Graph Edges:", containment_graph.number_of_edges())
    print(containment_graph.edges())