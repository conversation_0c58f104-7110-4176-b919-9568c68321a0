import networkx as nx
import numpy as np
import matplotlib.pyplot as plt

def create_graph_with_constraints(n_nodes, required_edges, start_node, seed=42):
    # Validate start node constraints
    for u, v in required_edges:
        if start_node == v:
            raise ValueError(f"Start node {start_node} cannot be the end of required edge {u}→{v}")
    if start_node not in range(n_nodes):
        raise ValueError(f"Invalid start node {start_node} for {n_nodes} nodes")

    np.random.seed(seed)
    coordinates = np.random.rand(n_nodes, 2)
    
    G = nx.Graph()
    for i in range(n_nodes):
        G.add_node(i, pos=coordinates[i])
    
    # Process required edges into super nodes
    remaining_nodes = set(G.nodes())
    super_nodes = []
    for u, v in required_edges:
        if u in remaining_nodes and v in remaining_nodes:
            super_nodes.append((u, v))
            remaining_nodes.remove(u)
            remaining_nodes.remove(v)
    
    modified_nodes = list(remaining_nodes) + super_nodes
    
    # Build distance matrix for modified nodes
    def get_distance(a, b):
        if isinstance(a, int) and isinstance(b, int):
            return np.linalg.norm(G.nodes[a]['pos'] - G.nodes[b]['pos'])
        elif isinstance(a, tuple):
            a_start, a_end = a
            if isinstance(b, int):
                return np.linalg.norm(G.nodes[a_end]['pos'] - G.nodes[b]['pos'])
            else:
                b_start, b_end = b
                return np.linalg.norm(G.nodes[a_end]['pos'] - G.nodes[b_start]['pos'])
        else:
            b_start, b_end = b
            return np.linalg.norm(G.nodes[a]['pos'] - G.nodes[b_start]['pos'])
    
    n_mod = len(modified_nodes)
    distance_matrix = np.zeros((n_mod, n_mod))
    for i in range(n_mod):
        for j in range(n_mod):
            distance_matrix[i, j] = get_distance(modified_nodes[i], modified_nodes[j])
    
    # Create TSP graph with modified nodes
    G_mod = nx.Graph()
    for i in range(n_mod):
        for j in range(i + 1, n_mod):
            G_mod.add_edge(i, j, weight=distance_matrix[i, j])
    
    # Solve TSP
    tsp_path_indices = nx.approximation.traveling_salesman_problem(G_mod, cycle=True, weight='weight')
    
    # Expand super nodes in path
    expanded_path = []
    for idx in tsp_path_indices:
        node = modified_nodes[idx]
        if isinstance(node, tuple):
            expanded_path.extend(node)
        else:
            expanded_path.append(node)
    
    # Remove duplicate end node if needed
    if expanded_path[0] == expanded_path[-1]:
        expanded_path = expanded_path[:-1]
    
    # Rotate path to start with specified node
    try:
        start_idx = expanded_path.index(start_node)
    except ValueError:
        raise ValueError(f"Start node {start_node} not found in TSP path")
    
    rotated_path = expanded_path[start_idx:] + expanded_path[:start_idx]
    
    # Verify all nodes are visited
    assert len(set(rotated_path)) == n_nodes, "Not all nodes visited"
    
    # Create ordered path with required edges
    final_path = []
    visited = set()
    for node in rotated_path:
        if node not in visited:
            final_path.append(node)
            visited.add(node)
    
    return final_path, G

# Example usage
n_nodes = 20
required_edges = [(4, 5), (8, 2)]
start_node = 0  # Change this to your desired start node

# Generate constrained TSP path
tsp_path, G = create_graph_with_constraints(n_nodes, required_edges, start_node)

# Visualize the result
pos = nx.get_node_attributes(G, 'pos')
plt.figure(figsize=(12, 8))
nx.draw(G, pos, with_labels=True, node_size=300)
path_edges = list(zip(tsp_path[:-1], tsp_path[1:]))
nx.draw_networkx_edges(G, pos, edgelist=path_edges, edge_color='r', width=2)
plt.title(f"Constrained TSP Path Starting at Node {start_node}")
plt.show()

print("TSP Path with Constraints:")
print(" -> ".join(map(str, tsp_path)))