import bpy
from mathutils import Vector
import math

import numpy as np
import trimesh # Assuming trimesh is used for PathSample
import shapely.geometry # Assuming shapely is used for Polygon

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) == 2:
        contour = shapely.geometry.Polygon(shell=geometry[0])
        path = shapely.geometry.LineString(geometry[1])
    else:
        print("Please select a valid mesh object.")
        return []
    return contour, path


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def create_circle_object(center, radius, name):
    """Create a circle object in Blender to visualize a maximally inscribed disc.

    Args:
        center: [x, y] coordinates of the circle center
        radius: Radius of the circle
        name: Name for the created circle object

    Returns:
        The created Blender object
    """
    # Create a mesh for the circle
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Number of vertices in the circle
    vertices_count = 32

    # Generate vertices around the circle
    verts = []
    for i in range(vertices_count):
        angle = 2.0 * math.pi * i / vertices_count
        x = center[0] + radius * math.cos(angle)
        y = center[1] + radius * math.sin(angle)
        verts.append(Vector((x, y, 0)))

    # Create edges around the circle (connect vertices to form a loop)
    edges = [(i, (i+1) % vertices_count) for i in range(vertices_count)]

    # Create the mesh from vertices and edges (no faces)
    mesh.from_pydata(verts, edges, [])
    mesh.update()

    # Set display properties
    mat = bpy.data.materials.new(name=f"{name}_material")
    mat.diffuse_color = (0.2, 0.8, 0.2, 1.0)  # Solid green for better visibility as a line
    obj.data.materials.append(mat)

    return obj


def boundary_distance(polygon, points):
    """
    Find the distance between a polygon's boundary and an
    array of points.

    Parameters
    -------------
    polygon : shapely.geometry.Polygon
      Polygon to query
    points : (n, 2) float
      2D points

    Returns
    ------------
    distance : (n,) float
      Minimum distance from each point to polygon boundary
    """     
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front(path, polygon, step, min_radius=5):
    path = np.asanyarray(path)
    if not trimesh.util.is_shape(path, (-1, 2)):
         raise ValueError("Path should be N Gons x 2 dimensions")
    if not isinstance(polygon, shapely.geometry.Polygon):
         raise ValueError("Polygon must be a shapely Polygon object")
    if not path.shape[0] > 1:
         raise ValueError("Path must have more than one point")

    # create a sampler object which can sample the path by distance
    sampler = trimesh.path.traversal.PathSample(path)
    # sample finely relative to the desired step size
    sample_step = step / 25.0
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)

    # get the points and boundary distance (radius) for each sample
    points = sampler.sample(sample_distances)
    radii = boundary_distance(polygon=polygon, points=points)    

    # store results: distance along path, and radius
    # always include the first point
    result_distances = [0.0]
    result_radii = [radii[0]]
    # store the index of the last point added
    last_idx = 0

    # loop through sampled points
    for i in range(1, len(points)):
        if radii[i] < min_radius:
            break
        # vector between candidate point and last point added
        vector = points[i] - points[last_idx]
        # calculate the edge-to-edge distance between circles
        front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[i]
        # if the distance exceeds threshold add point to results
        if front_distance >= step:
            result_distances.append(sample_distances[i])
            result_radii.append(radii[i])
            last_idx = i

    # check to make sure the last point isn't identical to the second-to-last
    # this can happen on tiny paths if points are duplicated
    if (len(result_distances) > 1 and
            result_distances[-1] - result_distances[-2] < 1e-8):
        result_distances.pop()
        result_radii.pop()

    return np.array(result_distances), np.array(result_radii)


# --- Arc Generation Function ---
def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, num_points=30):
    center = np.array(center)
    radius = max(1e-9, radius) # Ensure positive radius
    if num_points < 2:
       # Return just the start point if not enough points for a line
       return center + radius * np.array([np.cos(angle_start_rad), np.sin(angle_start_rad)])
    # Linspace handles angle wrapping correctly for sweeps > 180 or < -180
    angles = np.linspace(angle_start_rad, angle_end_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


# --- Helper to Calculate Arc Angles (Simplified Usage) ---
def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculates potentially extended start/end angles for the current arc
       based ONLY on the immediate next step.
    """
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)

    if segment_len < 1e-9: # Handle coincident points
        # Cannot determine direction, default to horizontal sweep
        angle_dir = 0.0
    else:
        dir_vec = segment_vec / segment_len
        angle_dir = np.arctan2(dir_vec[1], dir_vec[0])

    Std_Start_Angle = angle_dir + np.pi / 2.0
    Std_End_Angle = angle_dir - np.pi / 2.0
    delta_angle = 0.0

    # Extend only if next radius is larger AND distance allows calculation
    if R_next > R_current and segment_len > 1e-9:
        delta_R = R_next - R_current
        ratio = delta_R / segment_len
        # Use arcsin, clipped to handle floating point / geometry issues
        delta_angle = np.arcsin(np.clip(ratio, 0.0, 1.0))

    Actual_Start_Angle = Std_Start_Angle + delta_angle
    Actual_End_Angle = Std_End_Angle - delta_angle
    return Actual_Start_Angle, Actual_End_Angle


def cubic_bezier(t, p0, p1, p2, p3):
    p0, p1, p2, p3 = map(np.array, [p0, p1, p2, p3])
    t = np.clip(t, 0.0, 1.0)
    return ((1 - t)**3 * p0 + 3 * (1 - t)**2 * t * p1 + 3 * (1 - t) * t**2 * p2 + t**3 * p3)


def generate_bezier_transition(p0, t0, p3, t3, num_points=20, default_alpha_beta_factor=0.5, alpha=None, beta=None):
    p0, p3 = np.array(p0), np.array(p3)
    t0_norm, t3_norm = np.linalg.norm(t0), np.linalg.norm(t3)
    _t0 = np.array(t0) / t0_norm if t0_norm > 1e-9 else np.array([1.0, 0.0])
    _t3 = np.array(t3) / t3_norm if t3_norm > 1e-9 else np.array([1.0, 0.0])
    # Optional warnings removed for brevity
    if alpha is None or beta is None:
        dist = np.linalg.norm(p3 - p0)
        alpha_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        beta_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        _alpha = alpha if alpha is not None else alpha_calc
        _beta = beta if beta is not None else beta_calc
    else: _alpha, _beta = alpha, beta
    _alpha, _beta = max(0, _alpha), max(0, _beta)
    p1, p2 = p0 + _alpha * _t0, p3 - _beta * _t3
    if num_points < 2: return np.array([p0]) if num_points == 1 else np.empty((0, 2))
    t_values = np.linspace(0, 1, num_points)
    return np.array([cubic_bezier(t, p0, p1, p2, p3) for t in t_values])


def main():
    geometry = get_geometry()
    islands = None

    if len(geometry) == 1:
        polygon = geometry_to_polygon(geometry)
        print("Warning: Only one boundary selected. Advancing front requires a path.")
        path = np.array(polygon.exterior.coords, dtype=np.float64) # Use boundary as path?
    elif len(geometry) == 2:
        polygon, path_shapely = geometry_to_trocho_path(geometry) # Use specific func
        path = np.array(path_shapely.coords, dtype=np.float64)
    else:
        print("Please select exactly two objects: a boundary polygon (active) and a path.")
        return

    # --- User Parameters ---
    buffer_dist = -5          # Inward buffer distance
    edge_step = 2             # Desired distance between circle FRONTS
    min_path_radius = 5       # Minimum radius of trochoid circes
    target_arc_segment_length = 3.0 # Desired length of arc segments    
    points_per_transition = 15      # Resolution of the Bezier transitions
    transition_alpha_beta_factor = 0.5 # Smoothness factor for transitions (Tunable)
    # --- End User Parameters ---

    # Create buffered boundary object
    try:
        polygon_boundary = polygon.buffer(buffer_dist)
        if not polygon_boundary.is_valid or polygon_boundary.is_empty:
             print(f"Warning: Buffer resulted in invalid/empty polygon. Using original.")
             polygon_boundary = polygon
        if isinstance(polygon_boundary, shapely.geometry.MultiPolygon):
             polygon_boundary = max(polygon_boundary.geoms, key=lambda p: p.area)
             print("Warning: Buffer resulted in MultiPolygon, using largest.")
        if isinstance(polygon_boundary, shapely.geometry.Polygon):
             create_ring_object(polygon_boundary.exterior.coords, f"polygon_boundary")
        else:
             print("Error: Could not create valid polygon boundary after buffer.")
             return
    except Exception as e:
        print(f"Error buffering polygon: {e}")
        polygon_boundary = polygon


    # --- Generate Loop Centers and Radii ---
    print("Running advancing_front...")
    try:
        result_distances, result_radii = advancing_front(path, polygon_boundary, edge_step, min_path_radius)
        print(f"Found {len(result_distances)} loop points.")
        path_sampler = trimesh.path.traversal.PathSample(path)
        selected_centers = path_sampler.sample(result_distances)
        if len(selected_centers) < 2: raise ValueError("Advancing front needs >= 2 points.")
    except Exception as e:
        print(f"Error during advancing_front or sampling: {e}")
        selected_centers = np.empty((0,2))
        return

    # --- Step 1: Generate List of Arcs (as before) ---
    all_arc_coords = []
    if len(selected_centers) >= 2:
        for i in range(len(selected_centers) - 1):
            C_i, R_i = selected_centers[i], max(1e-6, result_radii[i])
            C_iplus1, R_iplus1 = selected_centers[i+1], max(1e-6, result_radii[i+1])
            start_angle, end_angle = calculate_arc_angles(C_i, R_i, C_iplus1, R_iplus1)
            angle_span = abs(start_angle - end_angle)
            arc_length = R_i * angle_span
            num_points_i = max(2, math.ceil(arc_length / target_arc_segment_length) + 1)
            arc_i_points = generate_arc_points(C_i, R_i, start_angle, end_angle, num_points_i)
            all_arc_coords.append(arc_i_points)

        # Last arc
        C_last, R_last = selected_centers[-1], max(1e-6, result_radii[-1])
        C_prev, R_prev = selected_centers[-2], max(1e-6, result_radii[-2])
        segment_vec_last = C_last - C_prev
        segment_len_last = np.linalg.norm(segment_vec_last)
        if segment_len_last > 1e-9: dir_vec_last = segment_vec_last / segment_len_last
        else: dir_vec_last = np.array([1.0, 0.0])
        angle_dir_last = np.arctan2(dir_vec_last[1], dir_vec_last[0])
        Std_Start_Angle_Last = angle_dir_last + np.pi / 2.0
        Std_End_Angle_Last = angle_dir_last - np.pi / 2.0
        delta_angle_last = 0.0
        if R_last > R_prev and segment_len_last > 1e-9:
            delta_R_last = R_last - R_prev
            ratio_last = delta_R_last / segment_len_last
            delta_angle_last = np.arcsin(np.clip(ratio_last, 0.0, 1.0))
        Actual_Start_Angle_Last = Std_Start_Angle_Last + delta_angle_last
        Actual_End_Angle_Last = Std_End_Angle_Last - delta_angle_last
        angle_span_last = abs(Actual_Start_Angle_Last - Actual_End_Angle_Last)
        arc_length_last = R_last * angle_span_last
        num_points_last = max(2, math.ceil(arc_length_last / target_arc_segment_length) + 1)
        arc_last_points = generate_arc_points(C_last, R_last, Actual_Start_Angle_Last, Actual_End_Angle_Last, num_points_last)
        all_arc_coords.append(arc_last_points)

        # --- Step 2: Generate Transitions and Combine Path ---
    full_connected_path = [] # Use a standard Python list for efficient appending
    if not all_arc_coords:
        print("No arcs were generated.")
        return

    # Add the first arc's points initially
    if len(all_arc_coords[0]) > 0:
        full_connected_path.extend(all_arc_coords[0].tolist()) # Convert to list for extend
    else:
        print("Warning: First arc has no points.")
        # Handle empty first arc if necessary

    # Iterate through arcs to generate transitions *and add the subsequent arc*
    for i in range(len(all_arc_coords) - 1):
        arc_i_points = all_arc_coords[i]
        arc_iplus1_points = all_arc_coords[i+1]

        # --- Generate Transition (check points first) ---
        if len(arc_i_points) < 2 or len(arc_iplus1_points) < 2:
            print(f"Warning: Skipping transition {i}. Adding arc {i+1} directly.")
            # If skipping transition, still add the next arc's points
            if len(arc_iplus1_points) > 0:
                 # Check if last point added matches the start of arc_iplus1
                 if full_connected_path and np.linalg.norm(np.array(full_connected_path[-1]) - arc_iplus1_points[0]) < 1e-6:
                      full_connected_path.extend(arc_iplus1_points[1:].tolist()) # Skip first if connected
                 else:
                      full_connected_path.extend(arc_iplus1_points.tolist()) # Add all if gap
            continue # Move to next iteration

        P0 = arc_i_points[-1]
        P3 = arc_iplus1_points[0]

        # Approx Tangents with Fallback
        fallback_dir_vec = P3 - P0
        fallback_dir_norm = np.linalg.norm(fallback_dir_vec)
        fallback_dir = fallback_dir_vec / fallback_dir_norm if fallback_dir_norm > 1e-9 else np.array([1.0, 0.0])
        T0_approx = P0 - arc_i_points[-2]
        T0_norm = np.linalg.norm(T0_approx)
        T0 = T0_approx / T0_norm if T0_norm > 1e-9 else fallback_dir
        T3_approx = arc_iplus1_points[1] - P3
        T3_norm = np.linalg.norm(T3_approx)
        T3 = T3_approx / T3_norm if T3_norm > 1e-9 else fallback_dir

        # Bezier Params
        dist_p0_p3 = np.linalg.norm(P3 - P0)
        alpha = dist_p0_p3 * transition_alpha_beta_factor
        beta = dist_p0_p3 * transition_alpha_beta_factor

        # Generate Bezier transition points
        transition_points = generate_bezier_transition(
            P0, T0, P3, T3,
            num_points=points_per_transition, alpha=alpha, beta=beta
        )
        # --- End Transition Generation ---

        # Add transition points (skip P0 as it's already end of arc i)
        if transition_points.shape[0] > 1:
            full_connected_path.extend(transition_points[1:].tolist()) # Convert to list

        # --- *** ADD THE NEXT ARC'S POINTS *** ---
        # The transition ended at P3, which is arc_iplus1_points[0].
        # Add the rest of the points from arc i+1.
        if len(arc_iplus1_points) > 1:
            full_connected_path.extend(arc_iplus1_points[1:].tolist()) # Convert to list
        # --- End Adding Next Arc ---

    # Convert final path list back to numpy array for object creation
    if full_connected_path:
        final_path_coords = np.array(full_connected_path)
    else: # Handle case where no points were added at all
        final_path_coords = np.empty((0, 2))


    # Create a single Blender object for the final path
    if final_path_coords.shape[0] > 1: # Need at least 2 points for a line
        create_line_object(final_path_coords, "trochoid_connected_path")
    else:
        print("Failed to generate connected path (less than 2 points).")

    # for arc in all_arc_coords:
    #     create_line_object(arc, "trochoid_arc")


if __name__ == "__main__":
    main()