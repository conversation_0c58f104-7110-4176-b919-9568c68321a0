import numpy as np

def quintic_hermite(p0, v0, a0, p1, v1, a1):
    f = p0
    e = v0
    d = a0 / 2
    M = np.array([[1, 1, 1], [5, 4, 3], [20, 12, 6]])
    B = np.array([p1 - d - e - f, v1 - 2*d - e, a1 - 2*d])
    a, b, c = np.linalg.solve(M, B)
    return np.array([a, b, c, d, e, f])  # t^5 to t^0

import numpy as np
import matplotlib.pyplot as plt

# Endpoint conditions
P1 = np.array([0, 1])
T1 = np.array([-1, 0])
A1 = np.array([0, -1])
P2 = np.array([3, 0])
T2 = np.array([0, 1])
A2 = np.array([-1, 0])

# Compute coefficients
coeffs_x = quintic_hermite(P1[0], T1[0], A1[0], P2[0], T2[0], A2[0])
coeffs_y = quintic_hermite(P1[1], T1[1], A1[1], P2[1], T2[1], A2[1])

# Evaluate polynomial
def eval_poly(coeffs, t):
    return np.polyval(coeffs, t)

t_fine = np.linspace(0, 1, 100)
x = eval_poly(coeffs_x, t_fine)
y = eval_poly(coeffs_y, t_fine)

# Define arcs for visualization
theta = np.linspace(0, np.pi/2, 100)
arc1_x = np.cos(theta)
arc1_y = np.sin(theta)
arc2_x = 2 + np.cos(theta)
arc2_y = np.sin(theta)

# Plot
plt.figure(figsize=(8, 6))
plt.plot(arc1_x, arc1_y, 'b-', label='Arc a1')
plt.plot(x, y, 'g-', label='Connecting curve')
plt.plot(arc2_x, arc2_y, 'r-', label='Arc a2')
plt.plot(P1[0], P1[1], 'bo')
plt.plot(P2[0], P2[1], 'ro')
plt.axis('equal')
plt.legend()
plt.xlabel('x')
plt.ylabel('y')
plt.title('G2 Continuous Curve from a1 to a2')
plt.grid(True)
plt.show()