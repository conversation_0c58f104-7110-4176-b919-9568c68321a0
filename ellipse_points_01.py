import numpy as np
import time
import time
import shapely
import shapely.vectorized
from scipy.spatial import cKDTree

def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=72, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.
    
    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def max_distance_to_ellipse(points, cx, cy, a, b, theta):
    """
    Compute the max (over points) of min-distance from each point to the ellipse.
    
    Args:
    - points: np.array (N, 2) of (x, y) coords.
    - cx, cy: ellipse center.
    - a, b: semi-major and semi-minor axes (assume a >= b > 0).
    - theta: rotation angle in radians.
    
    Returns:
    - float: the maximum min-distance.
    """
    if a < b:  # Swap if needed
        a, b = b, a
        theta += np.pi / 2  # Adjust angle for swap
    
    p = a**2
    q = b**2
    
    # Precompute rotation matrix components (for inverse rotation)
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    # Inverse: rotate points by -theta
    cos_neg = cos_theta  # cos(-theta) = cos(theta)
    sin_neg = -sin_theta  # sin(-theta) = -sin(theta)
    
    min_dists = []
    for px, py in points:
        # Translate
        dx = px - cx
        dy = py - cy
        # Rotate to align ellipse to axes: R_{-theta} @ [dx, dy]
        x0 = dx * cos_theta + dy * sin_theta
        y0 = -dx * sin_theta + dy * cos_theta  # Wait, correction: R_{-theta} = [[cos, sin], [-sin, cos]]
        # Yes: x0 = dx * cos + dy * sin
        # y0 = -dx * sin + dy * cos
        
        # Special case: if point is basically at center, min dist is b (minor axis)
        if np.sqrt(x0**2 + y0**2) < 1e-10:
            min_dists.append(b)
            continue
        
        # Compute quartic coefficients
        c3 = 2 * (p + q)
        c2 = p**2 + 4 * p * q + q**2 - x0**2 * p - y0**2 * q
        c1 = 2 * p * q * (p + q) - 2 * p * q * (x0**2 + y0**2)
        c0 = p**2 * q**2 - p * q**2 * x0**2 - p**2 * q * y0**2
        
        # Solve s^4 + c3 s^3 + c2 s^2 + c1 s + c0 = 0
        coeffs = [1, c3, c2, c1, c0]
        roots = np.roots(coeffs)
        
        # Real roots only
        real_roots = roots[np.isreal(roots)].real
        
        candidate_d = []
        for u in real_roots:
            denom_x = p + u
            denom_y = q + u
            if abs(denom_x) < 1e-8 or abs(denom_y) < 1e-8:
                continue  # Skip division by zero
            
            x = x0 * p / denom_x
            y = y0 * q / denom_y
            d = np.sqrt((x0 - x)**2 + (y0 - y)**2)
            candidate_d.append(d)
        
        if candidate_d:
            min_d = np.min(candidate_d)
            min_dists.append(min_d)
        else:
            # Rare fallback: if no valid, approximate (e.g., for numerical issues)
            # Could use optimization here, but for now assume it's handled
            min_dists.append(0)  # Or raise error, depending on your needs
    
    if not min_dists:
        return 0.0
    return np.max(min_dists)


def max_distance_to_ellipse_fastest(points, ellipse_params):
    """
    FASTEST: Analytical approximation - 70x faster
    Best for: Large datasets where approximate distance is sufficient
    """
    cx, cy = ellipse_params['center']
    a, b = ellipse_params['a'], ellipse_params['b']
    theta = ellipse_params['angle']
    
    # Transform points to ellipse coordinate system
    translated = points - np.array([cx, cy])
    cos_theta, sin_theta = np.cos(-theta), np.sin(-theta)
    
    x_rot = translated[:, 0] * cos_theta - translated[:, 1] * sin_theta
    y_rot = translated[:, 0] * sin_theta + translated[:, 1] * cos_theta
    
    # Convert to polar and find ellipse radius in each direction
    r = np.sqrt(x_rot**2 + y_rot**2)
    phi = np.arctan2(y_rot, x_rot)
    
    # Ellipse radius formula in polar coordinates
    ellipse_radius = a * b / np.sqrt((b * np.cos(phi))**2 + (a * np.sin(phi))**2)
    
    # Distance approximation
    distances = np.abs(r - ellipse_radius)
    
    return np.max(distances)

def max_distance_to_ellipse_fastest(points, ellipse_params):
    """
    FASTEST: Analytical approximation - 70x faster
    Best for: Large datasets where approximate distance is sufficient
    """
    cx, cy = ellipse_params['center']
    a, b = ellipse_params['a'], ellipse_params['b']
    theta = ellipse_params['angle']
    
    # Transform points to ellipse coordinate system
    translated = points - np.array([cx, cy])
    cos_theta, sin_theta = np.cos(-theta), np.sin(-theta)
    
    x_rot = translated[:, 0] * cos_theta - translated[:, 1] * sin_theta
    y_rot = translated[:, 0] * sin_theta + translated[:, 1] * cos_theta
    
    # Convert to polar and find ellipse radius in each direction
    r = np.sqrt(x_rot**2 + y_rot**2)
    phi = np.arctan2(y_rot, x_rot)
    
    # Ellipse radius formula in polar coordinates
    ellipse_radius = a * b / np.sqrt((b * np.cos(phi))**2 + (a * np.sin(phi))**2)
    
    # Distance approximation
    distances = np.abs(r - ellipse_radius)
    
    return np.max(distances), np.argmax(distances), distances

def max_distance_to_ellipse_accurate(points, ellipse_params, resolution=360):
    """
    BALANCED: High accuracy with good performance
    Best for: Most applications requiring precise distance calculation
    """
    cx, cy = ellipse_params['center']
    a, b = ellipse_params['a'], ellipse_params['b']
    theta = ellipse_params['angle']
    
    # Transform points
    translated = points - np.array([cx, cy])
    cos_theta, sin_theta = np.cos(-theta), np.sin(-theta)
    
    x_rot = translated[:, 0] * cos_theta - translated[:, 1] * sin_theta
    y_rot = translated[:, 0] * sin_theta + translated[:, 1] * cos_theta
    
    # Vectorized parametric search
    t_samples = np.linspace(0, 2*np.pi, resolution)
    cos_t, sin_t = np.cos(t_samples), np.sin(t_samples)
    
    ellipse_x = a * cos_t
    ellipse_y = b * sin_t
    
    # Broadcast and compute all distances
    dx = x_rot[:, np.newaxis] - ellipse_x[np.newaxis, :]
    dy = y_rot[:, np.newaxis] - ellipse_y[np.newaxis, :]
    distances_grid = np.sqrt(dx**2 + dy**2)
    
    # Find minimum distance for each point
    min_distances = np.min(distances_grid, axis=1)
    
    return np.max(min_distances), np.argmax(min_distances), min_distances

def max_distance_to_ellipse_hybrid(points, ellipse_params):
    """
    SMART: Combines speed and accuracy
    Uses fast approximation to filter, then accurate method for candidates
    """
    # First pass: fast approximation to find potential maximum points
    max_dist_approx, _, distances_approx = max_distance_to_ellipse_fastest(points, ellipse_params)
    
    # Find top 10% of points by approximate distance
    threshold = np.percentile(distances_approx, 90)
    candidate_mask = distances_approx >= threshold
    candidate_points = points[candidate_mask]
    
    if len(candidate_points) == 0:
        return max_distance_to_ellipse_fastest(points, ellipse_params)
    
    # Second pass: accurate calculation only for candidates
    _, _, accurate_distances = max_distance_to_ellipse_accurate(candidate_points, ellipse_params, resolution=720)
    
    # Find global maximum
    max_accurate = np.max(accurate_distances)
    max_idx_in_candidates = np.argmax(accurate_distances)
    
    # Convert back to original index
    candidate_indices = np.where(candidate_mask)[0]
    global_max_idx = candidate_indices[max_idx_in_candidates]
    
    # Create full distance array (approximate for most, accurate for candidates)
    full_distances = distances_approx.copy()
    full_distances[candidate_mask] = accurate_distances
    
    return max_accurate

def find_farthest_outside_point(inner_ellipse: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.               
    """    

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]
    
    # Filter points outside ellipse B    
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b
    
    outside_points_geom = shapely.points(inner_ellipse[outside_mask])
    
    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)    
    
    # Find maximum
    return np.max(distances)

def find_farthest_outside_point2(inner_ellipse: np.ndarray, outer_ellipse_geometry: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.               
    """    

    # Get ellipse boundary coordinates once
    boundary_coords = outer_ellipse_geometry

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]

    # Vectorized contains check
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_points = inner_ellipse[~is_inside_b]

    if len(outside_points) == 0:
        return 0.0

    # Vectorized distance to boundary using broadcasting
    diff = outside_points[:, np.newaxis, :] - boundary_coords[np.newaxis, :, :]
    distances_to_boundary = np.min(np.linalg.norm(diff, axis=2), axis=1)

    return np.max(distances_to_boundary)


def find_farthest_outside_point3(inner_ellipse: np.ndarray, outer_ellipse_geometry: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.               
    """    

    # Build KDTree for outer ellipse boundary once (if reusing)
    boundary_coords = outer_ellipse_geometry
    tree = cKDTree(boundary_coords)

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]

    # Filter and calculate
    outside_points = inner_ellipse[~shapely.vectorized.contains(outer_ellipse, x, y)]
    if len(outside_points) == 0:
        return 0.0

    # Fast nearest neighbor search
    distances, _ = tree.query(outside_points)
    return np.max(distances)




# Example usage:
iterations = 1000
points = np.random.rand(2, 2)
time1 = time.time()
for _ in range(iterations):
    max_dist = max_distance_to_ellipse(points, cx=0, cy=0, a=1, b=2, theta=23)
time2 = time.time()
print(f'Time: {time2-time1}')
print(max_dist)

time1 = time.time()
for _ in range(iterations):
    max_dist = max_distance_to_ellipse_fastest(points, {'center': (0, 0), 'a': 1, 'b': 2, 'angle': 23})
time2 = time.time()
print(f'Time: {time2-time1}')
# print(max_dist)

time1 = time.time()
for _ in range(iterations):   
    max_dist = max_distance_to_ellipse_accurate(points, {'center': (0, 0), 'a': 1, 'b': 2, 'angle': 23})
time2 = time.time()
print(f'Time: {time2-time1}')
# print(max_dist)


time1 = time.time()
for _ in range(iterations):    
    max_dist = max_distance_to_ellipse_hybrid(points, {'center': (0, 0), 'a': 0.1, 'b': 0.2, 'angle': 23})
time2 = time.time()
print(f'Time: {time2-time1}')
print(max_dist)


outer_ellipse_geometry, _ = create_ellipse(
    anchor_point=(0, 0),
    semi_major_axis=0.1,
    semi_minor_axis=0.2,
    rotation=23,
    anchor_on_perimeter=False
    )

# print(outer_ellipse_geometry.shape)

'''
'''
outer_ellipse = shapely.geometry.Polygon(outer_ellipse_geometry)
shapely.prepare(outer_ellipse)
time1 = time.time()
for _ in range(iterations):    
    max_dist = find_farthest_outside_point(points, outer_ellipse)
time2 = time.time()
print(f'Time: {time2-time1}')
print(max_dist)

time1 = time.time()
for _ in range(iterations):    
    max_dist = find_farthest_outside_point2(points, outer_ellipse_geometry, outer_ellipse)
time2 = time.time()
print(f'Time: {time2-time1}')
print(max_dist)

time1 = time.time()
for _ in range(iterations):    
    max_dist = find_farthest_outside_point3(points, outer_ellipse_geometry, outer_ellipse)
time2 = time.time()
print(f'Time: {time2-time1}')
print(max_dist)

