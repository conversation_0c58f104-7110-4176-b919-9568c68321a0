import numpy as np

class EllipseFitter:
    def fit(self, points):
        """
        Fit an ellipse to 5 points and return geometric parameters.
        
        Parameters:
        points : numpy.ndarray
            A 5x2 array of points [x, y].
            
        Returns:
        dict
            Dictionary with keys: 'center' (tuple (x0, y0)), 'a', 'b', 'theta' (rotation angle in radians).
        """
        if points.shape != (5, 2):
            raise ValueError("Exactly 5 points are required.")
        
        # Step 1: Construct the design matrix
        M = np.zeros((5, 6))
        for i in range(5):
            x, y = points[i]
            M[i] = [x*x, x*y, y*y, x, y, 1]
        
        # Step 2: Solve using SVD
        U, S, Vh = np.linalg.svd(M)
        v = Vh[-1, :]
        A, B, C, D, E, F = v
        
        # Step 3: Ensure ellipse condition B² - 4AC < 0
        if B*B - 4*A*C >= 0:
            A, B, C, D, E, F = -A, -B, -C, -D, -E, -F
        
        # Step 4: Compute the center (x0, y0)
        M_center = np.array([[A, B/2], [B/2, C]])
        rhs = np.array([-D/2, -E/2])
        try:
            center = np.linalg.solve(M_center, rhs)
        except np.linalg.LinAlgError:
            raise RuntimeError("Failed to compute ellipse center: singular matrix.")
        x0, y0 = center
        
        # Step 5: Compute F0 and adjust if positive
        F0 = A*x0*x0 + B*x0*y0 + C*y0*y0 + D*x0 + E*y0 + F
        if F0 > 0:
            A, B, C, D, E, F = -A, -B, -C, -D, -E, -F
            F0 = -F0
        
        # Step 6: Compute eigenvalues and eigenvectors of the quadratic form matrix
        M_quad = np.array([[A, B/2], [B/2, C]])
        eigenvalues, eigenvectors = np.linalg.eig(M_quad)
        idx = np.argsort(eigenvalues)
        lambda_min = eigenvalues[idx[0]]
        lambda_max = eigenvalues[idx[1]]
        v_min = eigenvectors[:, idx[0]]
        
        # Step 7: Compute semi-axes
        a = np.sqrt(-F0 / lambda_min)  # semi-major axis
        b = np.sqrt(-F0 / lambda_max)  # semi-minor axis
        
        # Step 8: Compute rotation angle (theta) from the eigenvector
        theta = np.arctan2(v_min[1], v_min[0]) % np.pi
        
        return {
            'center': (x0, y0),
            'a': a,
            'b': b,
            'theta': theta
        }



# Example usage
if __name__ == "__main__":
    # Your example points
    points = np.array([[-18107.85742188,  -9668.421875  ],
                       [-18109.07421875,  -9649.95117188],
                       [-18133.55859375,  -9622.34765625],
                       [-18161.0234375,   -9615.94433594],
                       [-18180.34570312,  -9623.63476562]], dtype=np.float64)
    
    # Fit ellipse
    fitter = EllipseFitter()
    fitter.fit(points)
    
    # Get parameters
    params = fitter.fit(points)
    print(params)