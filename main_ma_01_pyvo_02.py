import numpy as np
from numpy.linalg import cond
import bpy
import networkx as nx
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point, MultiLineString
from shapely.ops import nearest_points, linemerge
import math

# Use Shapely for efficient nearest point calculation


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def create_circle_object(center, radius, name):
    """Create a circle object in Blender to visualize a maximally inscribed disc.

    Args:
        center: [x, y] coordinates of the circle center
        radius: Radius of the circle
        name: Name for the created circle object

    Returns:
        The created Blender object
    """
    # Create a mesh for the circle
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Number of vertices in the circle
    vertices_count = 32

    # Generate vertices around the circle
    verts = []
    for i in range(vertices_count):
        angle = 2.0 * math.pi * i / vertices_count
        x = center[0] + radius * math.cos(angle)
        y = center[1] + radius * math.sin(angle)
        verts.append(Vector((x, y, 0)))

    # Create edges around the circle (connect vertices to form a loop)
    edges = [(i, (i+1) % vertices_count) for i in range(vertices_count)]

    # Create the mesh from vertices and edges (no faces)
    mesh.from_pydata(verts, edges, [])
    mesh.update()

    # Set display properties
    mat = bpy.data.materials.new(name=f"{name}_material")
    mat.diffuse_color = (0.2, 0.8, 0.2, 1.0)  # Solid green for better visibility as a line
    obj.data.materials.append(mat)

    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects


def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.

    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects

    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []

    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))

    return blender_objects


def decompose_to_polygons(geom):
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_vertices_by_angles_and_short_edges(vertices, min_edge_length=0.1):
    """
    Classify vertices of a polygon based on their angles and identify vertices
    connected to short edges.

    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    min_edge_length: float, minimum edge length threshold

    Returns:
    tuple of three arrays containing:
    - indices of convex angles
    - indices of concave angles
    - indices of vertices connected to edges shorter than min_edge_length
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)

    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices

    # Calculate edge lengths
    prev_edge_lengths = np.linalg.norm(v1, axis=1)
    next_edge_lengths = np.linalg.norm(v2, axis=1)

    # Find vertices connected to short edges
    short_edges_mask = (prev_edge_lengths < min_edge_length) | (next_edge_lengths < min_edge_length)
    short_edges_indices = np.where(short_edges_mask)[0]

    # Normalize vectors
    v1_norm = v1 / prev_edge_lengths[:, np.newaxis]
    v2_norm = v2 / next_edge_lengths[:, np.newaxis]

    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)
    dot_products = np.clip(dot_products, -1, 1)

    # Calculate angles in radians
    angles = np.arccos(dot_products)

    # Calculate 2D cross product
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]

    # Flip angles where cross product is negative
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)

    # Classify angles (from inside perspective)
    concave_mask = angles < np.pi
    convex_mask = angles > np.pi

    return (np.where(convex_mask)[0],
            np.where(concave_mask)[0],
            short_edges_indices)



def minimum_distance_to_boundary(point, contour, islands=None):
    """
    Calculate the minimum distance from a point to the boundary of the shape,
    considering both contour and islands.

    Parameters:
    -----------
    point : array-like
        Coordinates of the point [x, y]
    contour : shapely.geometry.Polygon
        Exterior boundary of the shape
    islands : shapely.geometry.MultiPolygon, optional
        Interior islands/holes

    Returns:
    --------
    float : Minimum distance to any part of the boundary
    tuple : Coordinates of the closest boundary point
    """
    # Convert point to shapely Point
    point_geom = Point(point)

    # Get distance to exterior boundary
    min_dist = point_geom.distance(contour.exterior)
    closest_point = nearest_points(point_geom, contour.exterior)[1].coords[0]

    # Check islands if they exist
    if islands and not islands.is_empty:
        # For each island, check if distance is smaller
        for island in islands.geoms:
            island_dist = point_geom.distance(island.exterior)
            if island_dist < min_dist:
                min_dist = island_dist
                closest_point = nearest_points(point_geom, island.exterior)[1].coords[0]

    return min_dist, closest_point


def hash_line(line, tolerance=1e-6):
    """Hashes a LineString for duplicate detection, considering direction."""
    coords = np.array(line.coords)
    rounded_coords = np.round(coords, int(-np.log10(tolerance)))
    # Sort coordinates to make hash direction-independent
    return hash(tuple(sorted(map(tuple, rounded_coords))))


def hash_edge(edge, tolerance=1e-6):
    """Hashes an edge (start, end) for duplicate detection."""
    start, end = edge
    rounded_start = tuple(np.round(start, int(-np.log10(tolerance))))
    rounded_end = tuple(np.round(end, int(-np.log10(tolerance))))
    return hash((rounded_start, rounded_end))


def remove_duplicate_edges(edges, tolerance=1e-6):
    unique_edges = []
    seen_hashes = set()

    for edge in edges:
        edge_hash = hash_edge(edge, tolerance)
        if edge_hash not in seen_hashes:
            unique_edges.append(edge)
            seen_hashes.add(edge_hash)

    return unique_edges


def voronoi(contour, islands=None, min_edge_length=0.001): #min_edge_length units: 1 = 1 millimeter
    pv = pyvoronoi.Pyvoronoi(100)

    # Process the contour (exterior polygon)
    coords = list(contour.exterior.coords)
    print(f"Adding contour with {len(coords)} points")

    # Add segments for each edge of the contour (excluding the last point which is same as first)
    for i in range(len(coords) - 1):
        start = coords[i]
        end = coords[i + 1]
        pv.AddSegment([start, end])

    # Process islands (holes) if provided
    if islands and not islands.is_empty:
        # If islands is a MultiPolygon, iterate through its polygons
        for island in islands.geoms:
            hole_coords = list(island.exterior.coords)
            print(f"Adding island with {len(hole_coords)} points")

            # Add segments for each edge of the island
            for i in range(len(hole_coords) - 1):
                start = hole_coords[i]
                end = hole_coords[i + 1]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    print("Cell Count: {0}".format(len(cells)))

    # Collect all edges data first
    linear_edges = []
    curved_edges = []

    # Use sets for efficient duplicate detection
    seen_linear_hashes = set()
    seen_curved_hashes = set()

    for cIndex, cell in enumerate(cells):
        for i, edge_idx in enumerate(cell.edges):
            e = edges[edge_idx]
            # Skip edges with invalid vertices
            if e.start == -1 or e.end == -1:
                continue

            startVertex = vertices[e.start]
            endVertex = vertices[e.end]

            # Store edge data with identifier for naming
            if e.is_linear:
                # Create tuple form of start and end points for hashing
                start_point = (startVertex.X, startVertex.Y)
                end_point = (endVertex.X, endVertex.Y)

                # create_line_object([start_point, end_point], f"{cIndex}_{i}")

                # Create hash from the edge points (sorted for direction independence)
                edge_tuple = tuple(sorted([start_point, end_point]))
                edge_hash = hash(edge_tuple)

                # Only add if not seen before
                if edge_hash not in seen_linear_hashes:
                    linear_edges.append({
                        'points': [list(start_point), list(end_point)],
                        'name': f"linear_edge_{cIndex}_{i}"
                    })
                    seen_linear_hashes.add(edge_hash)
            else:
                # Calculate distance only for curved edges
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                         (startVertex.Y - endVertex.Y)**2) / 10
                points = pv.DiscretizeCurvedEdge(edge_idx, max_distance)
                points_array = np.array([[p[0], p[1]] for p in points])
                
                # For curved edges, hash the first and last point
                # Use the points_array we already created
                if len(points_array) >= 2:
                    # Convert array points to tuples for hashing
                    start_point = tuple(points_array[0])
                    end_point = tuple(points_array[-1])
                    start_end_tuple = tuple(sorted([start_point, end_point]))
                    curve_hash = hash(start_end_tuple)

                    # Only add if not seen before
                    if curve_hash not in seen_curved_hashes:
                        curved_edges.append({
                            'points': points_array,
                            'name': f"curved_edge_{cIndex}_{i}"
                        })
                        seen_curved_hashes.add(curve_hash)

    # Extract medial axis - these are the interior edges of the Voronoi diagram
    medial_edges = []

    # Calculate convex and concave indices for the contour
    contour_coords = np.array(contour.exterior.coords[:-1])  # Exclude last point which is same as first
    convex_indices, concave_indices, short_edges = classify_vertices_by_angles_and_short_edges(contour_coords, min_edge_length=min_edge_length)

    # Dictionary to store all convex and concave vertices by polygon
    convex_concave_vertices = {}
    convex_concave_vertices['contour'] = {
        'convex': convex_indices,
        'concave': concave_indices,
        'short_edges': short_edges
    }

    # If islands exist, calculate convex and concave indices for each island (since islands have reversed orientation)
    if islands and not islands.is_empty:
        convex_concave_vertices['islands'] = {}
        for i, island in enumerate(islands.geoms):
            island_coords = np.array(island.exterior.coords[:-1])
            convex_indices, concave_indices, short_edges = classify_vertices_by_angles_and_short_edges(island_coords, min_edge_length=min_edge_length)
            convex_concave_vertices['islands'][i] = {
                'convex': convex_indices,
                'concave': concave_indices,
                'short_edges': short_edges
            }

    # We'll use the polygon to determine which edges are inside
    for edge in linear_edges + curved_edges:
        points = np.array(edge['points'])
        # Skip processing if no points
        if len(points) == 0:
            continue

        # Check if midpoint of the edge is inside the contour but outside any islands
        midpoint = points.mean(axis=0) if len(points) > 1 else points[0]
        midpoint_point = Point(midpoint)

        # Edge is part of medial axis if it's inside contour but outside all islands
        is_inside = contour.contains(midpoint_point)

        # Check if point is in any island
        is_in_island = False
        if islands and not islands.is_empty:
            is_in_island = islands.contains(midpoint_point)

        # Skip if edge is not inside or is in an island
        if not (is_inside and not is_in_island):
            continue

        # Check if this edge is connected to a convex vertex in contour or islands
        # Find closest contour vertex to first and last point of edge
        start_point = points[0]
        end_point = points[-1] if len(points) > 1 else points[0]

        # Check connection to contour convex vertices
        contour_convex = convex_concave_vertices['contour']['convex']
        contour_short_edges = convex_concave_vertices['contour']['short_edges']
        # convex_short = np.concatenate((contour_convex, contour_short_edges))
        convex_short = contour_convex
        if len(convex_short) > 0:
            # Calculate distances to all contour vertices
            start_dists = np.sqrt(np.sum((contour_coords - start_point)**2, axis=1))
            end_dists = np.sqrt(np.sum((contour_coords - end_point)**2, axis=1))

            # Find closest vertex indices
            closest_to_start = np.argmin(start_dists)
            closest_to_end = np.argmin(end_dists)

            # Skip if either end is too close to a convex vertex or connected to a short edge
            # Use a small threshold distance to determine if points are "connected"
            threshold = 0.01

            if closest_to_start in convex_short and start_dists[closest_to_start] < threshold:
                continue

            if closest_to_end in convex_short and end_dists[closest_to_end] < threshold:
                continue

        # Check connection to island convex vertices (since islands have reversed orientation)
        skip_edge = False
        if islands and not islands.is_empty and 'islands' in convex_concave_vertices:
            # Check each island for convex vertices
            for i, island in enumerate(islands.geoms):
                if i not in convex_concave_vertices['islands'] or len(convex_concave_vertices['islands'][i]) == 0:
                    continue

                island_coords = np.array(island.exterior.coords[:-1])
                island_convex = convex_concave_vertices['islands'][i]['convex']  # These are now convex indices

                # Calculate distances to all island vertices
                start_dists = np.sqrt(np.sum((island_coords - start_point)**2, axis=1))
                end_dists = np.sqrt(np.sum((island_coords - end_point)**2, axis=1))

                # Find closest vertex indices
                closest_to_start = np.argmin(start_dists)
                closest_to_end = np.argmin(end_dists)

                threshold = 0.01

                # If either endpoint is too close to a convex vertex, skip this edge entirely
                if (closest_to_start in island_convex and start_dists[closest_to_start] < threshold) or \
                   (closest_to_end in island_convex and end_dists[closest_to_end] < threshold):
                    skip_edge = True
                    break  # Stop checking other islands

        # Skip this edge if it's connected to any island convex vertex
        if skip_edge:
            continue

        # Since we already filtered duplicates during initial collection,
        # we can directly calculate radius and add to medial edges
        # Calculate radius (distance to boundary) for each point on this edge
        radii = []
        boundary_points = []

        for point in points:
            radius, boundary_pt = minimum_distance_to_boundary(point, contour, islands)
            # radius, boundary_pt = (0,0)
            radii.append(radius)
            boundary_points.append(boundary_pt)

        # Add to medial edges with radius information
        medial_edges.append({
            'points': points,
            'name': edge['name'].replace('edge', 'medial'),
            'radii': radii,
            'boundary_points': boundary_points
        })

    print(f"Found {len(medial_edges)} medial axis edges")
    return medial_edges


def boundary_distance(polygon, points):
    """
    Find the distance between a polygon's boundary and an
    array of points.

    Parameters
    -------------
    polygon : shapely.geometry.Polygon
      Polygon to query
    points : (n, 2) float
      2D points

    Returns
    ------------
    distance : (n,) float
      Minimum distance from each point to polygon boundary
    """     
    boundary = polygon.boundary
    return np.array([boundary.distance(Point(p)) for p in points])


def find_accurate_cut_point(p1, p2, r1, r2, min_radius, sample_step=0.1):
    """
    Find cut point by sampling the segment.
    
    Args:
        p1, p2: Start and end points of the segment
        r1, r2: Radii at start and end points
        min_radius: Minimum allowed radius
        sample_step: Distance between samples along the segment
    """
    # Calculate segment length and required number of samples
    segment_length = np.linalg.norm(p2 - p1)
    num_samples = max(int(segment_length / sample_step), 2)  # At least 2 samples
    
    # Generate sample points
    t_range = np.linspace(0, 1, num_samples)
    sample_points = p1[None, :] + t_range[:, None] * (p2 - p1)[None, :]
    
    # Get distances to contour
    contour_distances = boundary_distance(polygon, sample_points)
    
    # If islands exist, check distances to them
    if islands and not islands.is_empty:
        # Calculate distances to all islands
        island_distances = np.array([boundary_distance(island, sample_points) for island in islands.geoms])
        # Take minimum distance to any island at each point
        island_min_distances = np.min(island_distances, axis=0)
        # Take minimum between contour and island distances
        sample_radii = np.minimum(contour_distances, island_min_distances)
    else:
        sample_radii = contour_distances
    
    # Find where radius crosses min_radius
    crossings = np.where(np.diff(sample_radii >= min_radius))[0]
    if len(crossings) > 0:        
        # Use the first crossing
        idx = crossings[0]
        t = t_range[idx] + (min_radius - sample_radii[idx]) / (sample_radii[idx + 1] - sample_radii[idx]) * (t_range[idx + 1] - t_range[idx])
        return p1 + t * (p2 - p1)
    
    return None  # No crossing found


def separate_edges_by_radius(edges: list, minimum_radius: float) -> tuple[list, list]:
    """
    Separates edges into two groups based on radius criteria and processes edges that need splitting.
    Uses accurate cut point finding by sampling the segment.
    
    Args:
        edges: List of edge dictionaries containing 'points' and 'radii'
        minimum_radius: Threshold value for separating edges
        
    Returns:
        tuple containing:
        - list of edges with radius >= minimum_radius (normal_edges + shortened_edges_bigger)
        - list of edges with radius < minimum_radius (all_small_radius + shortened_edges_smaller)
    """
    # Stage 1: Initial separation
    stage1_filtered = []  # edges where at least one point has radius >= minimum_radius
    all_small_radius = []  # edges where ALL points have radius < minimum_radius
    
    for edge in edges:
        if any(radius >= minimum_radius for radius in edge['radii']):
            stage1_filtered.append(edge)
        else:
            all_small_radius.append(edge)
    
    # Stage 2: Separate edges that need no splitting from those that do
    small_radius_edges = [
        edge for edge in stage1_filtered
        if any(radius < minimum_radius for radius in edge['radii'])
    ]
    
    normal_edges = [
        edge for edge in stage1_filtered
        if all(radius >= minimum_radius for radius in edge['radii'])
    ]
    
    # Process edges that need splitting
    shortened_edges_bigger = []
    shortened_edges_smaller = []
    
    for edge in small_radius_edges:
        points = edge['points']
        radii = edge['radii']
        
        # Handle edges point by point
        current_segment_points = []
        current_segment_radii = []
        
        for i in range(len(points) - 1):
            p1, p2 = points[i], points[i + 1]
            r1, r2 = radii[i], radii[i + 1]
            
            # Start new segment with first point
            if not current_segment_points:
                current_segment_points.append(p1)
                current_segment_radii.append(r1)
            
            cut_point = find_accurate_cut_point(p1, p2, r1, r2, minimum_radius, sample_step=0.05)
            
            if cut_point is not None:
                # Add cut point to current segment and create new edge
                if r1 >= minimum_radius:
                    # Finish bigger segment
                    current_segment_points.append(cut_point)
                    current_segment_radii.append(minimum_radius)
                    
                    new_edge = edge.copy()
                    new_edge['points'] = np.array(current_segment_points)
                    new_edge['radii'] = np.array(current_segment_radii)
                    shortened_edges_bigger.append(new_edge)
                    
                    # Start smaller segment
                    current_segment_points = [cut_point, p2]
                    current_segment_radii = [minimum_radius, r2]
                    
                else:
                    # Finish smaller segment
                    current_segment_points.append(cut_point)
                    current_segment_radii.append(minimum_radius)
                    
                    new_edge = edge.copy()
                    new_edge['points'] = np.array(current_segment_points)
                    new_edge['radii'] = np.array(current_segment_radii)
                    shortened_edges_smaller.append(new_edge)
                    
                    # Start bigger segment
                    current_segment_points = [cut_point, p2]
                    current_segment_radii = [minimum_radius, r2]
            else:
                # No cut point, continue current segment
                current_segment_points.append(p2)
                current_segment_radii.append(r2)
            
        # Add final segment if points exist
        if current_segment_points:
            new_edge = edge.copy()
            new_edge['points'] = np.array(current_segment_points)
            new_edge['radii'] = np.array(current_segment_radii)
            
            # Determine which list to append to based on segment radii
            if all(r >= minimum_radius for r in current_segment_radii):
                shortened_edges_bigger.append(new_edge)
            else:
                shortened_edges_smaller.append(new_edge)
    
    # Combine results
    bigger_radius_edges = normal_edges + shortened_edges_bigger
    smaller_radius_edges = all_small_radius + shortened_edges_smaller
    
    return bigger_radius_edges, smaller_radius_edges
    

def find_max_radius_point(edges: list) -> tuple[np.ndarray, float]:
    """
    Find the point with the maximum radius from a list of edges.
    
    Args:
        edges: List of edge dictionaries containing 'points' and 'radii'
        
    Returns:
        tuple containing:
        - np.ndarray: Coordinates of the point with the maximum radius
        - float: Maximum radius value
    """
    max_radius = float('-inf')
    max_point = None
    
    for edge in edges:
        max_radius_edge = max(edge['radii'])
        if max_radius_edge > max_radius:
            max_radius = max_radius_edge
            max_point = edge['points'][np.argmax(edge['radii'])]
    
    return max_point, max_radius


if __name__ == "__main__":    

    geometry = get_geometry()
    polygon = geometry_to_polygon(geometry)
    contour, islands = geometry_to_shapely(geometry)
    
    medial_edges = voronoi(contour, islands, min_edge_length)
    bigger_radius_edges, smaller_radius_edges = separate_edges_by_radius(medial_edges, 15)
    
    # # Process bigger radius edges
    # if bigger_radius_edges:
    #     max_point, max_radius = find_max_radius_point(bigger_radius_edges)
    #     print("\nBigger radius edges - absolute maximum point:")
    #     print(f"  Max radius point coords: {max_point}")
    #     print(f"  Max radius value: {max_radius}")
    
    # Create visualization
    for edge in bigger_radius_edges:
        create_line_object(edge['points'], edge['name'])
    
    # for edge in smaller_radius_edges:
    #     create_line_object(edge['points'], edge['name'])
