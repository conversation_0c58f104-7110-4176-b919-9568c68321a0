import numpy as np
from shapely.geometry import LineString

def max_distance_right_side(l1, l2):
    """
    Find maximum distance from points of l2 that are on the right side of l1.
    Returns: (max_distance, count_on_right, distances_array, on_right_mask)
    """
    l1_coords = np.array(l1.coords)
    l2_coords = np.array(l2.coords)
    
    n_l2 = len(l2_coords)    
    
    # Broadcast arrays for vectorized operations
    p_broadcast = l2_coords[:, np.newaxis, :]
    a_broadcast = l1_coords[:-1][np.newaxis, :, :]
    b_broadcast = l1_coords[1:][np.newaxis, :, :]
    
    # Calculate projections for all point-segment pairs
    ab = b_broadcast - a_broadcast
    ap = p_broadcast - a_broadcast
    
    # Dot products
    ab_dot_ab = np.sum(ab * ab, axis=2)
    ap_dot_ab = np.sum(ap * ab, axis=2)
    
    # Clamp t values
    t = np.clip(ap_dot_ab / ab_dot_ab, 0, 1)
    
    # Calculate closest points
    closest = a_broadcast + t[:, :, np.newaxis] * ab
    
    # Calculate distances
    distances = np.linalg.norm(p_broadcast - closest, axis=2)
    
    # Find closest segment for each point
    closest_seg_idx = np.argmin(distances, axis=1)
    min_distances = np.min(distances, axis=1)
    
    # Calculate cross products for closest segments
    on_right = np.zeros(n_l2, dtype=bool)
    for i in range(n_l2):
        seg_idx = closest_seg_idx[i]
        a = l1_coords[seg_idx]
        b = l1_coords[seg_idx + 1]
        p = l2_coords[i]
        
        cross_z = (b[0] - a[0]) * (p[1] - a[1]) - (b[1] - a[1]) * (p[0] - a[0])
        on_right[i] = cross_z < 0
    
    # Get distances for points on the right
    right_distances = min_distances[on_right]
    
    if len(right_distances) > 0:
        max_dist = np.max(right_distances)
    else:
        max_dist = None
    
    return max_dist

# Example: vertical line and S-shaped curve
# Create vertical line (l1)
l1_points = [(5, 0), (5, 10)]
l1 = LineString(l1_points)

# Create S-shaped curve (l2)
t = np.linspace(0, 4*np.pi, 50)
x = 5 + 3 * np.sin(t/2)
y = t * 10 / (4*np.pi)
l2_points = list(zip(x, y))
l2 = LineString(l2_points)

# Find max distance on right side
max_dist = max_distance_right_side(l1, l2)

# Print results
if max_dist is not None:
    print(f"Maximum distance from right-side points to l1: {max_dist:.4f}")
    # print(f"Number of points on right side: {count}/{len(l2_coords)}")
    # print(f"Distance range for right-side points: [{np.min(distances[on_right]):.4f}, {max_dist:.4f}]")
else:
    print("No points found on the right side of l1")
