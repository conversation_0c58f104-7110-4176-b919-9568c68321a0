import numpy as np
import math
import time
from numba import njit, prange # Import Numba

# --- Simulation Parameters ---
# Tool
tool_diameter = 10.0  # mm (D from Fig 6)
tool_radius = tool_diameter / 2.0
num_teeth = 2      # z (from Fig 6)

# Process
axial_depth_of_cut = 5.0 # mm (ap from Fig 6)
spindle_speed_rpm = 24000 # RPM (n from Fig 6)
feed_rate_vf = 800 # mm/min (Example, use calculated if preferred)

# Simulation Grid
pixel_size = 0.1  # mm per pixel (Delta in paper discussion)
workpiece_width_mm = 100.0
workpiece_height_mm = 50.0
padding_mm = tool_diameter
sim_width_mm = workpiece_width_mm + 2 * padding_mm
sim_height_mm = workpiece_height_mm + 2 * padding_mm

# Simulation Steps
sim_step_distance = 0.2 # mm (ds - how far tool moves each sim step)
if sim_step_distance <= 1e-9:
    raise ValueError("Simulation step distance must be positive.")
steps_per_mm = 1.0 / sim_step_distance
steps_per_min = feed_rate_vf / sim_step_distance

# Derived constants
pixel_area_mm2 = pixel_size ** 2
tool_radius_pixels_sq = (tool_radius / pixel_size) ** 2
feed_rate_vf_mm_per_s = feed_rate_vf / 60.0 # Convert to mm/s for MRR if needed, though original formula uses mm/min
# Note: MRR calculation using vf directly with area removed per *step* needs care
# MRR = Volume_removed_per_step / Time_per_step
# Volume_removed_per_step = area_removed_inst_mm2 * axial_depth_of_cut
# Time_per_step = step_dist_actual_mm / feed_rate_vf_mm_per_s (if vf is constant speed along path)
# MRR = (area_removed_inst_mm2 * ap) / (step_dist_actual_mm / (vf / 60.0))
# MRR = area_removed_inst_mm2 * ap * vf / (60.0 * step_dist_actual_mm) -> This matches the original formula if vf is in mm/min
# If vf is feed per *minute*, and step_dist_actual is mm per *step*, then
# Steps per minute = feed_rate_vf / step_dist_actual (approximately, if step_dist is constant)
# MRR [mm^3/min] = Volume_removed_per_step [mm^3] * Steps_per_minute [steps/min]
# MRR = (area_removed_inst_mm2 * axial_depth_of_cut) * (feed_rate_vf / step_dist_actual) - This seems correct

# --- Helper Functions ---
# Basic helpers remain mostly unchanged, but ensure Numba compatibility if needed inside core loop
# (world_to_pixel can be replaced by inline calculations within the Numba function)

def create_tool_matrix(diameter, p_size):
    """Creates a binary matrix representing the circular tool."""
    radius = diameter / 2.0
    tool_matrix_size_pixels = int(math.ceil(diameter / p_size))
    if tool_matrix_size_pixels % 2 == 0:
        tool_matrix_size_pixels += 1 # Ensure odd size for a definite center pixel
    center_pixel = tool_matrix_size_pixels // 2
    tool_matrix = np.zeros((tool_matrix_size_pixels, tool_matrix_size_pixels), dtype=np.uint8)
    radius_pixels_sq_local = (radius / p_size) ** 2 # Use local variable
    for r in range(tool_matrix_size_pixels):
        for c in range(tool_matrix_size_pixels):
            dist_sq = (r - center_pixel)**2 + (c - center_pixel)**2
            if dist_sq <= radius_pixels_sq_local:
                tool_matrix[r, c] = 1
    return tool_matrix

def create_workpiece_matrix(sim_w_mm, sim_h_mm, wp_w_mm, wp_h_mm, pad_mm, p_size):
    """Creates the simulation grid with the workpiece material marked."""
    sim_width_pixels = int(round(sim_w_mm / p_size))
    sim_height_pixels = int(round(sim_h_mm / p_size))
    workpiece_matrix = np.zeros((sim_height_pixels, sim_width_pixels), dtype=np.uint8)
    # Use floor for start, ceil for end for conservative pixel coverage
    wp_start_px = int(math.floor((pad_mm) / p_size))
    wp_start_py = int(math.floor((pad_mm) / p_size))
    wp_end_px = int(math.ceil((pad_mm + wp_w_mm) / p_size))
    wp_end_py = int(math.ceil((pad_mm + wp_h_mm) / p_size))
    # Ensure indices are within bounds
    wp_start_px = max(0, wp_start_px)
    wp_start_py = max(0, wp_start_py)
    wp_end_px = min(sim_width_pixels, wp_end_px)
    wp_end_py = min(sim_height_pixels, wp_end_py)

    workpiece_matrix[wp_start_py:wp_end_py, wp_start_px:wp_end_px] = 1
    sim_origin_mm = (0.0, 0.0) # Assuming origin is bottom-left
    return workpiece_matrix, sim_origin_mm

def generate_tool_path_straight(start_xy, end_xy, step_ds):
    """Generates points for a straight path with consistent step size."""
    start_vec = np.array(start_xy, dtype=np.float64)
    end_vec = np.array(end_xy, dtype=np.float64)
    total_dist = np.linalg.norm(end_vec - start_vec)

    path = [tuple(start_vec)]
    if total_dist <= 1e-9: # Use tolerance
        return np.array(path) # Return numpy array

    direction = (end_vec - start_vec) / total_dist
    num_steps = int(math.floor(total_dist / step_ds)) # Number of full steps

    for i in range(1, num_steps + 1):
        path.append(tuple(start_vec + direction * i * step_ds))

    # Add end point if it's significantly different from the last point
    if not np.allclose(path[-1], end_xy, atol=step_ds/2):
         path.append(tuple(end_xy))
    return np.array(path, dtype=np.float64) # Return numpy array


def generate_tool_path_corner(start_xy, corner_xy, end_xy, step_ds):
    """Generates points for a path with one corner."""
    path1 = generate_tool_path_straight(start_xy, corner_xy, step_ds)
    path2 = generate_tool_path_straight(corner_xy, end_xy, step_ds)
    # Combine using numpy, avoid duplicate corner
    if len(path1) > 0 and len(path2) > 0:
        if np.allclose(path1[-1], path2[0]):
             # Concatenate excluding the duplicate corner from path2
             return np.vstack((path1, path2[1:]))
        else:
             # Should not happen with how generate_tool_path_straight is written, but handle just in case
             return np.vstack((path1, path2))
    elif len(path1) > 0:
        return path1
    else:
        return path2

# --- Numba Optimized Simulation Core ---
#@njit(fastmath=True) # Enable fastmath for potential speedup, check precision if critical
def run_simulation_core(tool_path_mm, workpiece, tool_matrix,
                        sim_origin_x_mm, sim_origin_y_mm, p_size,
                        tool_matrix_center_offset, sim_height_pixels, sim_width_pixels,
                        axial_depth_of_cut, feed_rate_vf, default_sim_step_distance,
                        tool_radius_mm, tool_diameter_mm):
    """
    Performs the core pixel milling simulation using Numba for acceleration.

    Args:
        tool_path_mm (np.ndarray): Array of (x, y) tool center coordinates (N x 2).
        workpiece (np.ndarray): 2D uint8 array representing the workpiece (mutable).
        tool_matrix (np.ndarray): 2D uint8 array representing the tool shape.
        sim_origin_x_mm (float): X coordinate of the simulation grid origin.
        sim_origin_y_mm (float): Y coordinate of the simulation grid origin.
        p_size (float): Pixel size in mm.
        tool_matrix_center_offset (int): Offset from tool matrix edge to center pixel.
        sim_height_pixels (int): Height of the workpiece grid in pixels.
        sim_width_pixels (int): Width of the workpiece grid in pixels.
        axial_depth_of_cut (float): ap in mm.
        feed_rate_vf (float): vf in mm/min.
        default_sim_step_distance (float): Nominal step distance ds in mm.
        tool_radius_mm (float): Tool radius in mm.
        tool_diameter_mm (float): Tool diameter in mm.

    Returns:
        tuple: (mrr_results, theta_results, ae_results, path_length_results) numpy arrays.
    """
    n_steps = len(tool_path_mm)
    mrr_results = np.zeros(n_steps, dtype=np.float64)
    theta_results = np.zeros(n_steps, dtype=np.float64)
    ae_results = np.zeros(n_steps, dtype=np.float64)
    path_length_results = np.zeros(n_steps, dtype=np.float64)

    current_path_length = 0.0
    inv_p_size = 1.0 / p_size
    pixel_area_mm2_local = p_size * p_size # Numba prefers local simple variables
    tool_matrix_shape_0 = tool_matrix.shape[0]
    tool_matrix_shape_1 = tool_matrix.shape[1]

    last_tool_center_mm = tool_path_mm[0] # Initialize for first step distance calculation

    for i in range(n_steps):
        tool_center_mm = tool_path_mm[i]

        # Calculate path length
        if i > 0:
            # Calculate Euclidean distance using loops for Numba compatibility if np.linalg.norm is slow/unsupported initially
            # dist_moved = np.linalg.norm(tool_center_mm - last_tool_center_mm) # Numba supports this now
            dx = tool_center_mm[0] - last_tool_center_mm[0]
            dy = tool_center_mm[1] - last_tool_center_mm[1]
            dist_moved = math.sqrt(dx*dx + dy*dy)
            # Use max to avoid division by zero, ensure it's at least a very small positive number if dist is effectively zero
            step_dist_actual = max(dist_moved, 1e-9)
            current_path_length += dist_moved
        else:
            # For the very first step, we don't have a previous point,
            # assume nominal step distance for calculations like MRR rate.
            # Or, if the path starts exactly at the first point with no prior movement,
            # step_dist_actual could be considered conceptually infinite for rate calcs,
            # or just use the default step distance as a convention. Let's use default.
            step_dist_actual = max(default_sim_step_distance, 1e-9)
            current_path_length = 0.0 # Path length starts at 0

        path_length_results[i] = current_path_length

        # --- Convert tool center mm to pixel coordinates (Inline) ---
        # Using round().astype(int) which is common in Numba contexts
        tool_center_px = int(round((tool_center_mm[0] - sim_origin_x_mm) * inv_p_size))
        tool_center_py = int(round((tool_center_mm[1] - sim_origin_y_mm) * inv_p_size))

        # --- Calculate tool overlap region ---
        y_start = tool_center_py - tool_matrix_center_offset
        y_end = y_start + tool_matrix_shape_0
        x_start = tool_center_px - tool_matrix_center_offset
        x_end = x_start + tool_matrix_shape_1

        # --- Handle boundary conditions (Clipping) ---
        y_start_clip = max(0, y_start)
        y_end_clip = min(sim_height_pixels, y_end)
        x_start_clip = max(0, x_start)
        x_end_clip = min(sim_width_pixels, x_end)

        # --- Check if the clipped region has valid dimensions ---
        if y_start_clip >= y_end_clip or x_start_clip >= x_end_clip:
            # Tool is completely outside the workpiece grid or clipped region is empty
            mrr_results[i] = 0.0
            ae_results[i] = 0.0
            theta_results[i] = 0.0
            last_tool_center_mm = tool_center_mm # Update for next iteration
            continue # Skip to next path point

        # --- Get corresponding slice offsets for the tool matrix ---
        tool_y_start_offset = y_start_clip - y_start
        tool_y_end_offset = tool_matrix_shape_0 - (y_end - y_end_clip)
        tool_x_start_offset = x_start_clip - x_start
        tool_x_end_offset = tool_matrix_shape_1 - (x_end - x_end_clip)

        # --- Initialize metrics for this step ---
        overlap_pixels_count = 0

        # --- Iterate over the clipped overlap region ---
        # This explicit looping is often very fast in Numba
        for r_wp in range(y_start_clip, y_end_clip):
            for c_wp in range(x_start_clip, x_end_clip):
                # Check if workpiece has material
                if workpiece[r_wp, c_wp] == 1:
                    # Calculate corresponding tool matrix index
                    r_tool = r_wp - y_start_clip + tool_y_start_offset
                    c_tool = c_wp - x_start_clip + tool_x_start_offset

                    # Check if the tool covers this pixel
                    if tool_matrix[r_tool, c_tool] == 1:
                        # This pixel is removed
                        overlap_pixels_count += 1
                        workpiece[r_wp, c_wp] = 0 # Remove material

        # --- Calculate metrics based on overlap ---
        mrr_inst = 0.0
        ae_estimated = 0.0
        theta_deg = 0.0

        if overlap_pixels_count > 0:
            area_removed_inst_mm2 = overlap_pixels_count * pixel_area_mm2_local
            # MRR = Vol_per_step * Steps_per_min = (Area * ap) * (vf / step_dist)
            mrr_inst = area_removed_inst_mm2 * axial_depth_of_cut * feed_rate_vf / step_dist_actual
            ae_estimated = area_removed_inst_mm2 / step_dist_actual # Effective radial depth (width of cut band)

            # Calculate engagement angle (theta)
            if tool_radius_mm > 1e-9:
                 # Clamp ae_estimated: must be >= 0 and <= tool diameter
                 # Add small tolerance to diameter to handle floating point issues near full engagement
                ae_clamped = min(max(0.0, ae_estimated), tool_diameter_mm * 1.0001)

                # Argument for acos: (R-ae)/R = 1 - ae/R
                # Clamp argument to [-1, 1] to avoid math domain error
                arg = 1.0 - (ae_clamped / tool_radius_mm)
                arg_clamped = max(-1.0, min(arg, 1.0))
                theta_rad = math.acos(arg_clamped)
                theta_deg = math.degrees(theta_rad)
                # Note: This theta definition might differ. Often it's half the angle.
                # This formula gives the angle phi from the paper (if ae is radial depth).
                # If ae_estimated is chip thickness related area/step, interpretation needs care.
                # Assuming ae_estimated is effective radial depth of cut ae.
                # The formula acos((R-ae)/R) gives the *half* angle swept by the cut.
                # The total engagement angle theta_e is often 2 * this angle.
                # Let's stick to the formula used previously, assuming it calculates what's needed.
                # If ae is the *width* of the removed band, then ae = R*(1-cos(theta/2))? No.
                # If ae is radial depth:
                # cos(angle_entry) = (R-ae)/R -> angle_entry = acos(1-ae/R) - angle relative to feed direction
                # cos(angle_exit) = 1 -> angle_exit = 0
                # engagement = angle_entry - angle_exit = acos(1-ae/R) -> This matches the formula!
                # So the formula calculates the total engagement angle in radians.

        # Store results
        mrr_results[i] = mrr_inst
        ae_results[i] = ae_estimated
        theta_results[i] = theta_deg

        last_tool_center_mm = tool_center_mm # Update for next iteration's distance calc

    return mrr_results, theta_results, ae_results, path_length_results

# --- Setup ---
print("Creating Tool Matrix...")
tool_matrix = create_tool_matrix(tool_diameter, pixel_size)
tool_matrix_center_offset = tool_matrix.shape[0] // 2
print(f"Tool matrix shape: {tool_matrix.shape}")

print("Creating Workpiece Matrix...")
workpiece, sim_origin = create_workpiece_matrix(
    sim_width_mm, sim_height_mm,
    workpiece_width_mm, workpiece_height_mm,
    padding_mm, pixel_size
)
initial_workpiece = workpiece.copy() # Keep initial state for reference/visualization
sim_height_pixels, sim_width_pixels = workpiece.shape
print(f"Workpiece matrix shape: {workpiece.shape}")

# --- Define Tool Path (Example: Corner Turn - Inside corner cut) ---
# Ensure path points are float64 for Numba
path_start = (padding_mm + workpiece_width_mm / 2.0, padding_mm - tool_radius)
path_corner = (padding_mm + workpiece_width_mm / 2.0, padding_mm + workpiece_height_mm / 2.0)
path_end = (padding_mm + workpiece_width_mm + tool_radius, padding_mm + workpiece_height_mm / 2.0)
tool_path_mm = generate_tool_path_corner(path_start, path_corner, path_end, sim_step_distance)

# Convert path list to numpy array if not already
if not isinstance(tool_path_mm, np.ndarray):
    tool_path_mm = np.array(tool_path_mm, dtype=np.float64)
elif tool_path_mm.dtype != np.float64:
     tool_path_mm = tool_path_mm.astype(np.float64)


print(f"Generated tool path with {len(tool_path_mm)} steps.")

# --- Run Simulation ---
print("Running Numba Optimized Simulation...")
start_time = time.time()

# Make a copy of the workpiece if you want to run the simulation multiple times
# without regenerating it
workpiece_sim = workpiece.copy()

# Call the Numba-compiled function
mrr_results, theta_results, ae_results, path_length_results = run_simulation_core(
    tool_path_mm, workpiece_sim, tool_matrix,
    sim_origin[0], sim_origin[1], pixel_size,
    tool_matrix_center_offset, sim_height_pixels, sim_width_pixels,
    axial_depth_of_cut, feed_rate_vf, sim_step_distance,
    tool_radius, tool_diameter
)

# --- First run includes compilation time ---
# Optional: Run once to compile before timing
# _, _, _, _ = run_simulation_core(
#     tool_path_mm[:2], workpiece.copy(), tool_matrix,
#     sim_origin[0], sim_origin[1], pixel_size,
#     tool_matrix_center_offset, sim_height_pixels, sim_width_pixels,
#     axial_depth_of_cut, feed_rate_vf, sim_step_distance,
#     tool_radius, tool_diameter
# )
# print("Numba compilation finished (if first run). Running timed simulation...")
# start_time = time.time()
# workpiece_sim = workpiece.copy() # Reset workpiece
# mrr_results, theta_results, ae_results, path_length_results = run_simulation_core(
#     tool_path_mm, workpiece_sim, tool_matrix,
#     sim_origin[0], sim_origin[1], pixel_size,
#     tool_matrix_center_offset, sim_height_pixels, sim_width_pixels,
#     axial_depth_of_cut, feed_rate_vf, sim_step_distance,
#     tool_radius, tool_diameter
# )
# --- End Optional Compilation Run ---


end_time = time.time()
print(f"\nSimulation finished in {end_time - start_time:.4f} seconds.") # More precision for fast runs

# Store results in a dictionary if preferred for consistency
results = {
    'mrr': mrr_results,
    'theta': theta_results,
    'ae': ae_results,
    'path_length': path_length_results
}

# --- Verification (Optional) ---
print(f"Number of steps processed: {len(results['mrr'])}")
# Example: Check a value (adjust index if path changes)
check_index = min(151, len(results['theta']) - 1) # Prevent index error
if check_index >= 0:
    print(f"Theta at index {check_index}: {results['theta'][check_index]:.4f}")
else:
     print("Path too short to check index 151.")

# You might want to visualize the final workpiece_sim state
# import matplotlib.pyplot as plt
# plt.figure(figsize=(10, 5))
# plt.imshow(initial_workpiece, cmap='Greys', origin='lower', alpha=0.5)
# plt.imshow(workpiece_sim, cmap='Blues', origin='lower', alpha=0.5)
# plt.title("Final Workpiece State (Blue = Remaining)")
# plt.xlabel("X (pixels)")
# plt.ylabel("Y (pixels)")
# plt.show()