import numpy as np
from shapely.geometry import Point

class SpiralEngagementCalculator:
    """Calculator for determining optimal spiral offsets based on cutter engagement."""
    
    def __init__(self, cutter_radius, target_engagement_deg=75):
        """
        Initialize the calculator with cutter parameters.
        
        Args:
            cutter_radius: Radius of the cutting tool
            target_engagement_deg: Target engagement angle in degrees
        """
        self.cutter_radius = cutter_radius
        self.target_engagement_rad = np.deg2rad(target_engagement_deg)
        
    def calculate_engagement(self, spiral_radius, offset, inside=True):
        """
        Calculate the engagement angle between a spiral and a cutter.
        
        Args:
            spiral_radius: Radius of the spiral
            offset: Offset distance
            inside: Whether the cutter is inside the spiral
            
        Returns:
            Engagement angle in radians
        """
        if inside:
            effective_offset = offset + spiral_radius - self.cutter_radius
        else:
            new_offset = spiral_radius
            spiral_radius = spiral_radius - offset + self.cutter_radius
            effective_offset = new_offset

        # Create geometric representations
        spiral_circle = Point(0, 0).buffer(spiral_radius, resolution=128)
        cutter_circle = Point(effective_offset, 0).buffer(self.cutter_radius, resolution=128).boundary

        # Calculate engagement as arc length divided by radius
        engagement = (cutter_circle.difference(spiral_circle).length / 2) / self.cutter_radius
        return engagement
    
    def find_optimal_offset(self, spiral_radius, inside=True, precision=0.05):
        """
        Find the offset value that produces the desired cutter workpiece engagement.
        
        Args:
            spiral_radius: Radius of the spiral
            inside: Whether the cutter is inside the spiral
            precision: Step size for the search
            
        Returns:
            Optimal offset value
        """
        min_offset = 0.0
        max_offset = self.cutter_radius
        offset_candidates = np.arange(min_offset, max_offset, precision)
        
        best_offset = 0
        min_diff = float('inf')
        
        for offset in offset_candidates:
            engagement = self.calculate_engagement(spiral_radius, offset, inside)
            diff = abs(engagement - self.target_engagement_rad)
            
            if diff < min_diff:
                min_diff = diff
                best_offset = offset
                
                # Early exit if we're very close to target
                if diff < 0.001:
                    break
                    
        return best_offset
    
    def find_spiral_offsets(self, inner_radius, outer_radius):
        """
        Find optimal offsets for both inner and outer spirals.
        
        Args:
            inner_radius: Radius of the inner spiral
            outer_radius: Radius of the outer spiral
            
        Returns:
            Tuple of (inside_offset, outside_offset)
        """
        inside_offset = self.find_optimal_offset(inner_radius, inside=True)
        outside_offset = self.find_optimal_offset(outer_radius, inside=False)
        
        return inside_offset, outside_offset


def main():
    # Example usage
    cutter_radius = 10.0
    inner_radius = (40.0/2)# - cutter_radius
    outer_radius = (260/2) - cutter_radius
    target_engagement = 75  # degrees
    
    calculator = SpiralEngagementCalculator(cutter_radius, target_engagement)
    inside_offset, outside_offset = calculator.find_spiral_offsets(inner_radius, outer_radius)
    
    print(f"Inner spiral offset: {inside_offset:.3f}")
    print(f"Outer spiral offset: {outside_offset:.3f}")


if __name__ == '__main__':
    main()
