import numpy as np
import matplotlib.pyplot as plt

def find_ellipse_scale_factor(point, center, semi_axes, angle_rad):
    """
    Calculates the scale factor 's' required for an ellipse to touch an external point.

    The ellipse is scaled uniformly, meaning its new semi-axes will be (s*a, s*b).

    Parameters:
    ----------
    point : np.ndarray or tuple
        The (x, y) coordinates of the external point.
    center : np.ndarray or tuple
        The (cx, cy) coordinates of the ellipse's center.
    semi_axes : np.ndarray or tuple
        The (a, b) semi-axis lengths of the ellipse (a is along the x-axis
        before rotation, b is along the y-axis).
    angle_rad : float
        The rotation angle of the ellipse in radians.

    Returns:
    -------
    float
        The required scale factor 's'. The point is on the ellipse if s=1,
        inside if s<1, and outside if s>1.
    """
    # Ensure inputs are numpy arrays for vector operations
    point = np.asarray(point)
    center = np.asarray(center)
    semi_axes = np.asarray(semi_axes)
    a, b = semi_axes

    # 1. Translate the point to the ellipse's local origin
    point_translated = point - center

    # 2. Rotate the point backwards by the ellipse's angle
    c, s = np.cos(angle_rad), np.sin(angle_rad)
    
    # The inverse rotation matrix is the transpose of the forward rotation matrix
    # Forward rotation matrix: [[c, -s], [s, c]]
    # Inverse rotation matrix: [[c, s], [-s, c]]
    rot_matrix_inv = np.array([[c, s], 
                               [-s, c]])
    
    point_local = rot_matrix_inv @ point_translated
    x_local, y_local = point_local

    # 3. Calculate the scale factor in the simple, non-rotated system
    # We are solving s = sqrt( (x_local/a)^2 + (y_local/b)^2 )
    term1 = (x_local / a)**2
    term2 = (y_local / b)**2
    
    scale_factor = np.sqrt(term1 + term2)

    return scale_factor

# --- Example Usage and Visualization ---

# 1. Define the ellipse parameters
ellipse_center = np.array([5, 3])
ellipse_semi_axes = np.array([4, 2])  # a=4, b=2
# Angle in degrees, then convert to radians for numpy functions
angle_deg = 0
angle_rad = np.deg2rad(angle_deg)

# 2. Define an external point
external_point = np.array([12, 7])

# 3. Calculate the required scale factor
s = find_ellipse_scale_factor(
    point=external_point,
    center=ellipse_center,
    semi_axes=ellipse_semi_axes,
    angle_rad=angle_rad
)

print(f"Ellipse Parameters:")
print(f"  Center: {ellipse_center}")
print(f"  Semi-axes (a, b): {ellipse_semi_axes}")
print(f"  Rotation: {angle_deg} degrees\n")
print(f"External Point: {external_point}\n")
print(f"Required scale factor 's': {s:.4f}")

# 4. Visualization to verify the result
def plot_ellipse(ax, center, semi_axes, angle_rad, **kwargs):
    """Helper function to plot a rotated ellipse."""
    t = np.linspace(0, 2 * np.pi, 200)
    a, b = semi_axes
    
    # Points on the standard ellipse
    ellipse_points = np.array([a * np.cos(t), b * np.sin(t)])
    
    # Rotation matrix
    c, s = np.cos(angle_rad), np.sin(angle_rad)
    rot_matrix = np.array([[c, -s], [s, c]])
    
    # Rotate and translate points
    rotated_points = rot_matrix @ ellipse_points
    final_points = rotated_points + center[:, np.newaxis]
    
    ax.plot(final_points[0, :], final_points[1, :], **kwargs)

# Create plot
fig, ax = plt.subplots(figsize=(10, 8))

# Plot the original ellipse
plot_ellipse(ax, ellipse_center, ellipse_semi_axes, angle_rad,
             color='blue', linestyle='--', label='Original Ellipse')

# Plot the scaled ellipse
scaled_semi_axes = ellipse_semi_axes * s
plot_ellipse(ax, ellipse_center, scaled_semi_axes, angle_rad,
             color='red', linewidth=2, label=f'Scaled Ellipse (s={s:.2f})')

# Plot the center and the target point
ax.plot(*ellipse_center, 'ko', label='Center')
ax.plot(*external_point, 'r*', markersize=12, label='Target Point')

# Formatting
ax.set_title('Scaling an Ellipse to Touch a Point')
ax.set_xlabel('X-axis')
ax.set_ylabel('Y-axis')
ax.axhline(0, color='grey', lw=0.5)
ax.axvline(0, color='grey', lw=0.5)
ax.grid(True, linestyle=':')
ax.legend()
ax.set_aspect('equal', adjustable='box') # Crucial for correct visualization
plt.show()