import numpy as np
from scipy.optimize import differential_evolution
from shapely.geometry import Polygon, Point
import time

# 1. Define a fixed, complex polygon that our circle will intersect with.
#    This can be defined once, outside the objective function.
TARGET_POLYGON = Polygon([
    (0, 0), (1, 5), (6, 8), (10, 4), (8, 1), (3, 0)
])

# 2. Define the objective function.
#    This function will be called in parallel by the workers.
def objective_function(params):
    """
    Calculates the value to be minimized.
    `params` is a numpy array: [x, y, radius]
    """
    x, y, radius = params
    
    # Create the circle geometry inside the function.
    # This is good practice as it keeps each evaluation self-contained.
    circle = Point(x, y).buffer(radius, quad_segs=10000)
    
    # Calculate the intersection using Shapely.
    intersection = TARGET_POLYGON.intersection(circle)
    
    # differential_evolution MINIMIZES the objective function.
    # To MAXIMIZE the area, we return the NEGATIVE area.
    return -intersection.area

# 3. Define the search space (bounds) for the parameters [x, y, radius].
#    Let's constrain the circle's center to be within a 10x10 box
#    and its radius between 0.5 and 4.
bounds = [
    (0, 10),      # x-coordinate bounds
    (0, 10),      # y-coordinate bounds
    (0.5, 4.0)    # radius bounds
]

if __name__ == '__main__':
    # --- Run with parallelization ---
    print("Running with parallelization (workers=-1)...")
    start_time_parallel = time.time()
    
    result_parallel = differential_evolution(
        objective_function,
        bounds,
        strategy='best1bin',
        maxiter=100,
        popsize=30,
        tol=0.01,
        mutation=(0.5, 1),
        recombination=0.7,
        updating='deferred', # 'deferred' is required for workers > 1
        workers=32,          # Use all available CPU cores
        disp=True            # Display progress
    )
    
    end_time_parallel = time.time()
    print(f"Parallel execution time: {end_time_parallel - start_time_parallel:.2f} seconds")
    print("Parallel Result:", result_parallel.x)
    print("Max Intersection Area found (Parallel):", -result_parallel.fun)

    print("\n" + "="*40 + "\n")

    # --- Run serially for comparison ---
    print("Running serially (workers=1)...")
    start_time_serial = time.time()
    
    result_serial = differential_evolution(
        objective_function,
        bounds,
        strategy='best1bin',
        maxiter=100,
        popsize=15,
        tol=0.01,
        mutation=(0.5, 1),
        recombination=0.7,
        workers=1,           # Use only a single core
        disp=True
    )
    
    end_time_serial = time.time()
    print(f"Serial execution time: {end_time_serial - start_time_serial:.2f} seconds")
    print("Serial Result:", result_serial.x)
    print("Max Intersection Area found (Serial):", -result_serial.fun)