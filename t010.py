import numpy as np
from scipy.optimize import brentq, minimize_scalar

# ------------------------------------------------------------
# Ellipse support
# ------------------------------------------------------------
def build_ellipse(a, b, phi):
    """Return matrix M s.t. x^T M x = 1 is the ellipse."""
    ct, st = np.cos(phi), np.sin(phi)
    R = np.array([[ct, -st], [st,  ct]])
    D = np.diag([1/a**2, 1/b**2])
    return R @ D @ R.T

def ellipse_distance(M, point):
    """Algebraic distance from point to ellipse M."""
    return np.sqrt(point @ M @ point) - 1

# ------------------------------------------------------------
# Line–ellipse tangency helpers
# ------------------------------------------------------------
def contact_parameters(lines, c, w):
    """
    lines = list of (p,d) tuples
    returns α,β such that ellipse with centre c and axis vector w
    is tangent to all lines.
    """
    A = []
    rhs = []
    for p, d in lines:
        n = np.array([-d[1], d[0]])         # perpendicular
        n = n / np.linalg.norm(n)
        A.append([ (n[0]*w[0])**2,
                   (n[1]*w[1])**2 ])
        rhs.append((n @ (p - c))**2)
    A = np.asarray(A, dtype=float)
    rhs = np.asarray(rhs, dtype=float)
    x, *_ = np.linalg.lstsq(A, rhs, rcond=None)
    return np.sqrt(np.maximum(x, 0))

# ------------------------------------------------------------
# Main routine
# ------------------------------------------------------------
def maximal_inner_ellipse(a, b, phi,
                          poly0, poly1):
    """
    poly0, poly1: arrays shape (N,2) and (M,2)
                  each row is a vertex of the poly-line
    """
    M = build_ellipse(a, b, phi)

    # build segment list for each poly-line
    def to_segments(poly):
        return [(poly[i], poly[i+1]-poly[i])
                for i in range(len(poly)-1)]
    segs0 = to_segments(poly0)
    segs1 = to_segments(poly1)
    lines = segs0 + segs1

    # bisector direction (simple average of first and last normals)
    def normal(seg):
        p, d = seg
        n = np.array([-d[1], d[0]])
        return n / np.linalg.norm(n)
    n0 = normal(segs0[0])
    nL = normal(segs1[-1])
    u  = (n0 + nL)
    u  = u / np.linalg.norm(u)

    # scalar function for root finding
    def f(lam):
        c = lam * u
        w = np.ones(2)          # initial axis directions (will be refined)
        ab2 = contact_parameters(lines, c, w)
        alpha, beta = np.sqrt(ab2)
        dist_outer = ellipse_distance(M, c) + 1
        dist_line  = np.sqrt(alpha**2 * u[0]**2 + beta**2 * u[1]**2)
        return dist_outer - dist_line

    # admissible interval
    lam_max = min(np.linalg.norm(seg[0]) for seg in lines)
    lam = brentq(f, 1e-3, lam_max*0.99)

    # final ellipse
    c = lam * u
    ab2 = contact_parameters(lines, c, np.ones(2))
    alpha, beta = np.sqrt(ab2)

    return c, alpha, beta


def rot(phi):
    c, s = np.cos(phi), np.sin(phi)
    return np.array([[c, -s], [s, c]])


# ------------------------------------------------------------
# Demo
# ------------------------------------------------------------
if __name__ == "__main__":
    import matplotlib.pyplot as plt

    # Outer ellipse
    a, b, phi = 10.0, 6.0, np.pi/6

    # Two simple poly-lines (replace by any list of vertices)
    t = np.linspace(0, 2*np.pi, 100)
    outer = np.column_stack([a*np.cos(t), b*np.sin(t)])
    outer = (outer @ rot(phi) for phi in [np.array([[np.cos(phi),-np.sin(phi)],
                                                     [np.sin(phi), np.cos(phi)]])][0])

    poly0 = np.array([[ 9.5,  1.0],
                      [ 5.0,  4.0],
                      [ 0.0,  5.0]])
    poly1 = np.array([[-8.0, -3.0],
                      [-3.0, -4.0],
                      [ 0.0, -5.5]])

    c, alpha, beta = maximal_inner_ellipse(a, b, phi, poly0, poly1)

    # plot
    fig, ax = plt.subplots(); ax.set_aspect(1)
    ax.plot(*outer.T, label='outer ellipse')
    ax.plot(*poly0.T, lw=2, marker='o')
    ax.plot(*poly1.T, lw=2, marker='o')

    theta = np.linspace(0, 2*np.pi, 400)
    inner = np.column_stack([alpha*np.cos(theta), beta*np.sin(theta)])
    inner = (inner @ rot(phi) for phi in [np.array([[np.cos(phi),-np.sin(phi)],
                                                     [np.sin(phi), np.cos(phi)]])][0])
    inner += c
    ax.plot(*inner.T, label='inner ellipse')
    ax.scatter(*c, color='k')
    ax.legend(); plt.show()