import numpy as np
from scipy import integrate
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
import time

class EllipseProperties:
    """A simple class to hold ellipse parameters."""
    def __init__(self, a, b, center_x, center_y, angle_deg=0):
        self.a = a # semi-major axis
        self.b = b # semi-minor axis
        self.cx = center_x
        self.cy = center_y
        self.angle = angle_deg
        self.angle_rad = np.deg2rad(angle_deg)
        
    def area(self):
        """Calculates the total area of the ellipse."""
        return np.pi * self.a * self.b

    def is_inside(self, x, y):
        """
        Checks if a point (x, y) is inside the ellipse.
        Handles rotated ellipses.
        """
        # Translate point to origin
        x_trans = x - self.cx
        y_trans = y - self.cy

        # Rotate point
        cos_a = np.cos(-self.angle_rad)
        sin_a = np.sin(-self.angle_rad)
        x_rot = x_trans * cos_a - y_trans * sin_a
        y_rot = x_trans * sin_a + y_trans * cos_a

        # Check against the canonical ellipse equation
        return (x_rot**2 / self.a**2) + (y_rot**2 / self.b**2) <= 1


def calculate_stick_out_area(ellipse_A, ellipse_B):
    """
    Calculates the area of ellipse_B that is outside of ellipse_A
    using numerical double integration.
    """
    
    # Define the indicator function.
    # It returns 1 if a point is in B but not in A, otherwise 0.
    # Note: dblquad passes arguments as (y, x) to the function.
    def indicator_func(y, x):
        is_in_B = ellipse_B.is_inside(x, y)
        if not is_in_B:
            return 0.0
        
        is_in_A = ellipse_A.is_inside(x, y)
        
        return 1.0 if not is_in_A else 0.0

    # Integration bounds: We integrate over the bounding box of Ellipse B,
    # as no area can exist outside of it.
    # For simplicity, we use an axis-aligned bounding box.
    # A tighter bound would be the rotated bounding box, but this is sufficient.
    x_min = ellipse_B.cx - ellipse_B.a
    x_max = ellipse_B.cx + ellipse_B.a
    y_min = ellipse_B.cy - ellipse_B.b
    y_max = ellipse_B.cy + ellipse_B.b

    print("Starting numerical integration...")
    start_time = time.time()
    
    # Perform the double integral of the indicator function.
    # The result is the area of the region where the indicator is 1.
    area, error = integrate.dblquad(
        indicator_func,
        x_min, x_max,
        lambda x: y_min, lambda x: y_max,
        epsabs=1e-4, # Adjust tolerance for speed/accuracy trade-off
        epsrel=1e-4
    )
    
    end_time = time.time()
    print(f"Integration finished in {end_time - start_time:.4f} seconds.")
    print(f"Estimated error: {error:.4e}")
    
    return area


def plot_ellipses_and_area(ellipse_A, ellipse_B, stick_out_area):
    """Visualizes the two ellipses and shades the 'stick-out' area."""
    fig, ax = plt.subplots(figsize=(10, 6))

    # Create matplotlib patches for the ellipses
    e_A = Ellipse(xy=(ellipse_A.cx, ellipse_A.cy), width=2*ellipse_A.a, height=2*ellipse_A.b, 
                  angle=ellipse_A.angle, edgecolor='blue', fc='blue', alpha=0.3, label='Ellipse A')
    e_B = Ellipse(xy=(ellipse_B.cx, ellipse_B.cy), width=2*ellipse_B.a, height=2*ellipse_B.b, 
                  angle=ellipse_B.angle, edgecolor='red', fc='red', alpha=0.3, label='Ellipse B')

    ax.add_patch(e_A)
    ax.add_patch(e_B)

    # --- Shade the specific "stick-out" area ---
    # Create a grid of points around ellipse B
    x_coords = np.linspace(ellipse_B.cx - ellipse_B.a, ellipse_B.cx + ellipse_B.a, 200)
    y_coords = np.linspace(ellipse_B.cy - ellipse_B.b, ellipse_B.cy + ellipse_B.b, 200)
    X, Y = np.meshgrid(x_coords, y_coords)
    
    # Check each point on the grid
    Z = np.zeros_like(X)
    for i in range(X.shape[0]):
        for j in range(X.shape[1]):
            if ellipse_B.is_inside(X[i, j], Y[i, j]) and not ellipse_A.is_inside(X[i, j], Y[i, j]):
                Z[i, j] = 1

    # Use contourf to plot the shaded region
    ax.contourf(X, Y, Z, levels=[0.5, 1.5], colors=['green'], alpha=0.7)
    
    # Dummy plot for the legend
    ax.plot([], [], color='green', linewidth=10, alpha=0.7, 
            label=f'B outside A (Area ≈ {stick_out_area:.4f})')

    ax.set_title("Area of Ellipse B Sticking Out of Ellipse A")
    ax.legend()
    ax.set_aspect('equal', 'box')
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # Set plot limits to see both ellipses clearly
    all_x = [ellipse_A.cx - ellipse_A.a, ellipse_A.cx + ellipse_A.a, 
             ellipse_B.cx - ellipse_B.a, ellipse_B.cx + ellipse_B.a]
    all_y = [ellipse_A.cy - ellipse_A.b, ellipse_A.cy + ellipse_A.b,
             ellipse_B.cy - ellipse_B.b, ellipse_B.cy + ellipse_B.b]
    ax.set_xlim(min(all_x) - 1, max(all_x) + 1)
    ax.set_ylim(min(all_y) - 1, max(all_y) + 1)
    
    plt.show()

# --- Main Execution ---

# Case 1: Your original example (B is fully inside A)
print("--- Case 1: Original Example (No Overlap) ---")
A1 = EllipseProperties(a=10, b=5, center_x=0, center_y=0)
B1 = EllipseProperties(a=2, b=5, center_x=4, center_y=0)
area1 = calculate_stick_out_area(A1, B1)
print(f"\nArea of B outside A: {area1:.6f}")
print(f"Total area of B: {B1.area():.4f}")
plot_ellipses_and_area(A1, B1, area1)


# Case 2: A corrected, more interesting example where B sticks out
print("\n\n--- Case 2: Corrected Example (With Overlap) ---")
A2 = EllipseProperties(a=10, b=5, center_x=0, center_y=0)
B2 = EllipseProperties(a=4, b=3, center_x=8, center_y=0)
area2 = calculate_stick_out_area(A2, B2)
print(f"\nArea of B outside A: {area2:.6f}")
print(f"Total area of B: {B2.area():.4f}")
plot_ellipses_and_area(A2, B2, area2)

# Case 3: A rotated example
print("\n\n--- Case 3: Rotated Ellipse Example ---")
A3 = EllipseProperties(a=10, b=5, center_x=0, center_y=0)
B3 = EllipseProperties(a=5, b=2, center_x=8, center_y=2, angle_deg=30)
area3 = calculate_stick_out_area(A3, B3)
print(f"\nArea of B outside A: {area3:.6f}")
print(f"Total area of B: {B3.area():.4f}")
plot_ellipses_and_area(A3, B3, area3)