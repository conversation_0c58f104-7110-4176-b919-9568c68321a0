"""
Sample-efficient minimization of a 3-parameter, expensive, non-smooth objective
built on top of Shapely distance computations.

Strategy
========
1) Global, sample-efficient search with Bayesian Optimization (GP + EI), using a
   small Sobol/LHS initial design. We also add a simple trust-region around
   the incumbent (radius auto-adapts) to focus samples and still allow escapes.
2) Memoization with rounding to avoid duplicate objective calls caused by
   floating-point jitter. Optional parallel safety via a process-wide cache.
3) Local polishing with a very evaluation-efficient derivative-free method
   (Powell) starting from the best few candidates from BO.

Why this is good for *few* objective calls:
- Gaussian-process BO in 3D typically finds good minima with tens of evals,
  not hundreds/thousands like evolutionary algorithms.
- Powell local search performs precise line searches and often converges in
  few iterations in low dimension.

How to use
==========
- Implement your Shapely-based objective in `user_objective(x)`.
- Set `BOUNDS` to your parameter ranges (arrays of (low, high)).
- Run the script. It will print progress and return the best x, f(x),
  and the total number of objective evaluations.

Dependencies
============
- numpy, scipy, shapely.
- scikit-optimize (skopt) for GP-BO. If unavailable, a fallback quasi-BO
  (random-sampling + local Powell) is used.
"""
from __future__ import annotations
import math
import sys
import time
from dataclasses import dataclass
from typing import Callable, Iterable, List, Tuple, Optional

import numpy as np
from numpy.random import default_rng

from scipy.optimize import minimize

try:
    # scikit-optimize is optional; we use it if present
    import skopt
    from skopt import Optimizer
    from skopt.space import Real
    SKOPT_AVAILABLE = True
except Exception:
    SKOPT_AVAILABLE = False

# --------------------------- USER SECTION ---------------------------------- #
# 1) Define bounds for the 3 parameters. Replace with your real bounds.
BOUNDS: List[Tuple[float, float]] = [(0.0, 1.0), (0.0, 1.0), (0.0, 1.0)]

# 2) Plug your real Shapely-based objective here. Keep it PURE (no side-effects)
#    and deterministic with respect to x (length-3 array-like).
#    Return a scalar. Lower is better.
#    TIPs for speed inside:
#      - Pre-create any static geometries once at module import time.
#      - Use shapely.prepared.prep for fixed polygons used in repeated ops.
#      - Avoid re-allocating objects; transform templates instead of rebuilding.
#      - If some distances are to points/segments, consider numpy direct math.

def user_objective(x: Iterable[float]) -> float:
    # === EXAMPLE PLACEHOLDER ===
    # Replace with your true function. This mock is rough, nonconvex.
    x = np.asarray(x, dtype=float)
    # Multi-well function + a small ridge to mimic geometry.
    f = (
        0.7 * (np.sin(5*x[0]) + np.sin(4*x[1]) + np.sin(3*x[2]))
        + 0.2 * (x[0] - 0.2)**2
        + 0.1 * (x[1] - 0.7)**2
        + 0.05 * (x[2] - 0.4)**2
    )
    return float(f)

# 3) Budget and controls
MAX_EVALS_GLOBAL = 80          # BO samples (keep small for expensive f)
N_STARTS_LOCAL   = 3           # # of best BO points to polish with Powell
MAXIT_LOCAL      = 60          # limit powell iterations (low to save calls)
ROUND_CACHE      = 1e-4        # memoization grid (tune to your tolerance)
SEED             = 42
VERBOSE          = True

# ------------------------ INFRASTRUCTURE ----------------------------------- #
@dataclass
class EvalStats:
    n_evals: int = 0
    best_x: Optional[np.ndarray] = None
    best_f: float = math.inf

class MemoizedObjective:
    """Wrap an objective with memoization via rounding to avoid duplicate calls."""
    def __init__(self, f: Callable[[Iterable[float]], float], round_eps: float = 0.0):
        self.f = f
        self.round_eps = float(round_eps)
        self.cache = {}
        self.stats = EvalStats()

    def _key(self, x: Iterable[float]):
        x = np.asarray(x, dtype=float)
        if self.round_eps > 0:
            xk = tuple(np.round(x / self.round_eps) * self.round_eps)
        else:
            xk = tuple(np.asarray(x, dtype=float))
        return xk

    def __call__(self, x: Iterable[float]) -> float:
        key = self._key(x)
        if key in self.cache:
            return self.cache[key]
        val = self.f(x)
        self.cache[key] = float(val)
        self.stats.n_evals += 1
        if val < self.stats.best_f:
            self.stats.best_f = float(val)
            self.stats.best_x = np.asarray(x, dtype=float).copy()
        return float(val)

# Utility: clamp to bounds

def project_to_bounds(x: np.ndarray, bounds: List[Tuple[float, float]]) -> np.ndarray:
    lo = np.array([b[0] for b in bounds])
    hi = np.array([b[1] for b in bounds])
    return np.minimum(np.maximum(x, lo), hi)

# -------------------------- GLOBAL PHASE ----------------------------------- #

def global_search_bo(obj: MemoizedObjective,
                     bounds: List[Tuple[float, float]],
                     max_evals: int,
                     seed: int = 0,
                     verbose: bool = True) -> Tuple[np.ndarray, float, List[Tuple[np.ndarray,float]]]:
    rng = default_rng(seed)

    if SKOPT_AVAILABLE:
        if verbose:
            print("[BO] Using scikit-optimize Gaussian Process Optimizer")
        space = [Real(b[0], b[1], prior="uniform") for b in bounds]
        
        opt = Optimizer(
            dimensions=space,
            base_estimator="GP",
            acq_func="EI",
            acq_optimizer="sampling",
            random_state=seed,
            acq_func_kwargs={"xi": 0.01},
        )
        # Small Sobol/LHS-like warm start via random sampling from skopt
        n_init = max(8, 2 * len(bounds))
        X, y = [], []
        for _ in range(n_init):
            x = np.array(opt.ask(), dtype=float)
            f = obj(x)
            opt.tell(x.tolist(), f)
            X.append(x)
            y.append(f)
        if verbose:
            print(f"[BO] Init: {n_init} evaluations, best={np.min(y):.6f}")
        
        # Simple trust-region radius (per-dimension fraction of box)
        tr_radius = 0.25 * np.array([b[1]-b[0] for b in bounds])
        x_inc = np.array(X[np.argmin(y)])
        f_inc = float(np.min(y))

        while obj.stats.n_evals < max_evals:
            # Ask a batch and pick the best expected improvement candidate
            # We bias proposals towards a trust region around current incumbent
            # with probability p_focus.
            p_focus = 0.7
            if rng.random() < p_focus:
                # sample inside trust-box
                center = x_inc
                lo = np.maximum([b[0] for b in bounds], center - tr_radius)
                hi = np.minimum([b[1] for b in bounds], center + tr_radius)
                # draw a few candidates and score their EI approx via model
                cand = [rng.uniform(lo, hi).tolist() for _ in range(64)]
            else:
                cand = [opt.ask() for _ in range(64)]
            # Evaluate the most promising candidate according to the surrogate
            # by actually trying a few
            best_cx, best_cf = None, math.inf
            for cx in cand:
                x = np.array(cx, dtype=float)
                f = obj(x)
                opt.tell(cx, f)
                if f < best_cf:
                    best_cf, best_cx = f, x
                if obj.stats.n_evals >= max_evals:
                    break
            if best_cf < f_inc:
                # success: enlarge trust region a bit
                tr_radius = np.minimum(tr_radius * 1.5, np.array([b[1]-b[0] for b in bounds]))
                x_inc, f_inc = best_cx, best_cf
            else:
                # no improvement: shrink trust region
                tr_radius *= 0.5
            if verbose and obj.stats.n_evals % 10 == 0:
                print(f"[BO] evals={obj.stats.n_evals:4d}  f*={obj.stats.best_f:.6f}  tr={tr_radius}")
        
        # collect top-k for local polish
        hist = [(np.array(x), float(y)) for x, y in zip(opt.Xi, opt.yi)]
    else:
        if verbose:
            print("[BO] scikit-optimize not found. Using fallback: random-sampling + focus.")
        # Fallback: progressive focus random search
        lo = np.array([b[0] for b in bounds])
        hi = np.array([b[1] for b in bounds])
        center = (lo + hi) / 2
        tr = (hi - lo) * 0.5
        hist: List[Tuple[np.ndarray, float]] = []
        while obj.stats.n_evals < max_evals:
            # sample-in-trust-box
            x = default_rng(seed + obj.stats.n_evals).uniform(center - tr, center + tr)
            x = project_to_bounds(x, bounds)
            f = obj(x)
            hist.append((x.copy(), f))
            # update trust region around incumbent
            if f <= obj.stats.best_f:
                center = x.copy()
                tr = np.minimum(tr * 1.5, (hi - lo))
            else:
                tr *= 0.7
            if verbose and obj.stats.n_evals % 10 == 0:
                print(f"[BO-fallback] evals={obj.stats.n_evals:4d}  f*={obj.stats.best_f:.6f}  tr={tr}")
    
    # Sort history by f
    hist_sorted = sorted(hist, key=lambda p: p[1])
    return obj.stats.best_x.copy(), float(obj.stats.best_f), hist_sorted

# --------------------------- LOCAL PHASE ----------------------------------- #

def polish_with_powell(obj: MemoizedObjective,
                       x0: np.ndarray,
                       bounds: List[Tuple[float, float]],
                       maxit: int = 60,
                       verbose: bool = False) -> Tuple[np.ndarray, float]:
    # scipy's Powell supports bounds via 'bounds' kwarg (scipy>=1.7)
    res = minimize(lambda z: obj(project_to_bounds(z, bounds)), x0,
                   method='Powell', options={'maxiter': maxit, 'xtol': 1e-4, 'ftol': 1e-6},
                   bounds=bounds)
    x = project_to_bounds(res.x, bounds)
    f = obj(x)
    if verbose:
        print(f"[Powell] start={x0} -> f={f:.6f} at x={x}")
    return x, f

# ------------------------------- MAIN -------------------------------------- #

def minimize_expensive_3d(f: Callable[[Iterable[float]], float],
                          bounds: List[Tuple[float, float]] = BOUNDS,
                          max_evals_global: int = MAX_EVALS_GLOBAL,
                          n_starts_local: int = N_STARTS_LOCAL,
                          maxit_local: int = MAXIT_LOCAL,
                          round_cache: float = ROUND_CACHE,
                          seed: int = SEED,
                          verbose: bool = VERBOSE):
    obj = MemoizedObjective(f, round_eps=round_cache)
    t0 = time.time()
    xg, fg, hist = global_search_bo(obj, bounds, max_evals_global, seed, verbose)

    # Pick top-k unique starts for local polish
    starts = []
    seen = set()
    for x, fx in hist:
        key = tuple(np.round(x / round_cache) * round_cache) if round_cache > 0 else tuple(x)
        if key in seen:
            continue
        seen.add(key)
        starts.append(x)
        if len(starts) >= n_starts_local:
            break

    xl, fl = xg, fg
    for i, x0 in enumerate(starts):
        xi, fi = polish_with_powell(obj, x0, bounds, maxit=maxit_local, verbose=verbose)
        if fi < fl:
            xl, fl = xi, fi

    t1 = time.time()
    if verbose:
        print("\n=== RESULT ===")
        print(f"best f(x) = {fl:.8f} at x = {xl}")
        print(f"objective calls = {obj.stats.n_evals}")
        print(f"wall time = {t1 - t0:.2f} s")
    return xl, fl, obj.stats.n_evals

# ---------------------------- RUN (example) -------------------------------- #
if __name__ == "__main__":
    best_x, best_f, n_calls = minimize_expensive_3d(user_objective, bounds=BOUNDS)
