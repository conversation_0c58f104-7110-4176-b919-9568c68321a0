from __future__ import annotations
import numpy as np
import math
import time
import numba # Import Numba

# --- Simulation Parameters ---
tool_diameter = 10.0
tool_radius = tool_diameter / 2.0

pixel_size = 0.5
workpiece_width_mm = 300.0
workpiece_height_mm = 300.0
padding_mm = tool_diameter
sim_width_mm = workpiece_width_mm + 2 * padding_mm
sim_height_mm = workpiece_height_mm + 2 * padding_mm
sim_step_distance = 0.1
NUM_ANGLE_BINS = 360


print(f"Pixel Size (Delta): {pixel_size} mm")
print(f"Sim Step Distance (ds): {sim_step_distance} mm")
print(f"Precomputing for {NUM_ANGLE_BINS} angle bins (using Numba).")

# --- Numba-Optimized Helper Functions ---

@numba.njit(cache=True)
def _get_engageable_for_bin_njit(full_outline_indices_array, direction_vec, dot_product_threshold):
    """
    Numba-optimized function to find engageable pixels for ONE direction vector.

    Args:
        full_outline_indices_array: 2D NumPy array [[dr1, dc1], [dr2, dc2], ...]
        direction_vec: 1D NumPy array [dx, dy]
        dot_product_threshold: Float threshold

    Returns:
        2D NumPy array of engageable [dr, dc] indices for this bin.
    """
    num_outline_pixels = full_outline_indices_array.shape[0]
    # Pre-allocate a boolean mask (faster than list append in njit)
    engageable_mask = np.zeros(num_outline_pixels, dtype=numba.boolean)
    count = 0
    for i in range(num_outline_pixels):
        # dr = full_outline_indices_array[i, 0] # Row offset (y)
        # dc = full_outline_indices_array[i, 1] # Col offset (x)
        # Vector from tool center to outline pixel (x=dc, y=dr)
        # Note: Numba array creation inside loop can be slow, reuse vectors if possible
        # Or directly compute: dot = direction_vec[0] * dc + direction_vec[1] * dr
        dot_product = direction_vec[0] * full_outline_indices_array[i, 1] + \
                      direction_vec[1] * full_outline_indices_array[i, 0]

        if dot_product > dot_product_threshold:
            engageable_mask[i] = True
            count += 1

    # Create the result array using the mask
    engageable_indices = np.empty((count, 2), dtype=full_outline_indices_array.dtype)
    current_idx = 0
    for i in range(num_outline_pixels):
        if engageable_mask[i]:
            engageable_indices[current_idx, 0] = full_outline_indices_array[i, 0]
            engageable_indices[current_idx, 1] = full_outline_indices_array[i, 1]
            current_idx += 1

    return engageable_indices


@numba.njit(cache=True)
def _calculate_contact_njit(tool_center_px, tool_center_py,
                            engageable_indices_array, # Now expects 2D NumPy array
                            workpiece, sim_height_pixels, sim_width_pixels):
    """
    Numba-optimized function to count contacted outline pixels.
    """
    contacted_outline_pixels = 0
    num_engageable = engageable_indices_array.shape[0]

    for i in range(num_engageable):
        # dr, dc are relative offsets from the tool center
        dr = engageable_indices_array[i, 0]
        dc = engageable_indices_array[i, 1]

        # Absolute pixel coordinates
        px = tool_center_px + dc
        py = tool_center_py + dr

        # Bounds check inline
        if 0 <= py < sim_height_pixels and 0 <= px < sim_width_pixels:
            # Material check
            if workpiece[py, px] == 1:
                contacted_outline_pixels += 1

    return contacted_outline_pixels


# --- Standard Helper Functions (Mostly unchanged) ---
def world_to_pixel(xy_mm, origin_xy_mm, p_size):
    px = int(round((xy_mm[0] - origin_xy_mm[0]) / p_size))
    py = int(round((xy_mm[1] - origin_xy_mm[1]) / p_size))
    return px, py

def create_tool_matrices(diameter, p_size):
    # ... (logic as before, returns filled matrix, FULL outline indices as LIST of tuples, center offset) ...
    radius = diameter / 2.0
    radius_pixels = radius / p_size
    tool_matrix_size_pixels = int(math.ceil(diameter / p_size))
    if tool_matrix_size_pixels % 2 == 0: tool_matrix_size_pixels += 1
    center_pixel = tool_matrix_size_pixels // 2
    tool_matrix_filled = np.zeros((tool_matrix_size_pixels, tool_matrix_size_pixels), dtype=np.uint8)
    tool_full_outline_indices_relative_list = [] # Keep as list initially
    outline_tolerance = 0.6
    for r in range(tool_matrix_size_pixels):
        for c in range(tool_matrix_size_pixels):
            dr, dc = r - center_pixel, c - center_pixel
            dist = math.sqrt(dr**2 + dc**2)
            if dist <= radius_pixels + 1e-6: tool_matrix_filled[r, c] = 1
            if abs(dist - radius_pixels) < outline_tolerance:
                 tool_full_outline_indices_relative_list.append((dr, dc)) # List of tuples
    if not tool_full_outline_indices_relative_list:
         print("Warning: No outline pixels found.")
    # Return list, convert to array later before passing to Numba
    return tool_matrix_filled, tool_full_outline_indices_relative_list, center_pixel


def precompute_engageable_outlines(full_outline_indices_list, num_bins):
    """
    Precomputes engageable outline pixels using the Numba helper.
    Stores results as NumPy arrays in the dictionary.
    """
    print(f"Starting precomputation for {num_bins} angle bins (using Numba)...")
    precomputed_outlines = {}
    dot_product_threshold = 1e-6

    # Convert list of tuples to NumPy array ONCE for Numba function
    if not full_outline_indices_list:
        print("Warning: Empty outline list provided to precompute.")
        return {} # Return empty dict if no outline pixels

    full_outline_indices_array = np.array(full_outline_indices_list, dtype=np.int32)

    start_precomp_time = time.time()
    bin_angle_step = 360.0 / num_bins

    for i in range(num_bins):
        angle_deg = i * bin_angle_step
        angle_rad = math.radians(angle_deg)
        direction_vec = np.array([math.cos(angle_rad), math.sin(angle_rad)], dtype=np.float64)

        # Call the Numba JITted function
        engageable_indices_array = _get_engageable_for_bin_njit(
            full_outline_indices_array, direction_vec, dot_product_threshold
        )

        # Store the resulting NumPy array in the dictionary
        precomputed_outlines[i] = engageable_indices_array

    end_precomp_time = time.time()
    print(f"Precomputation finished in {end_precomp_time - start_precomp_time:.4f} seconds.")
    return precomputed_outlines

# --- create_workpiece_matrix, generate_tool_path_straight, generate_tool_path_slot remain the same ---
def create_workpiece_matrix(sim_w_mm, sim_h_mm, wp_w_mm, wp_h_mm, pad_mm, p_size):
    sim_width_pixels = int(round(sim_w_mm / p_size)); sim_height_pixels = int(round(sim_h_mm / p_size))
    workpiece_matrix = np.zeros((sim_height_pixels, sim_width_pixels), dtype=np.uint8)
    wp_x_min_mm=pad_mm; wp_x_max_mm=pad_mm+wp_w_mm; wp_y_min_mm=pad_mm; wp_y_max_mm=pad_mm+wp_h_mm
    wp_start_px, wp_start_py = world_to_pixel((wp_x_min_mm, wp_y_min_mm), (0,0), p_size)
    wp_end_px, wp_end_py = world_to_pixel((wp_x_max_mm, wp_y_max_mm), (0,0), p_size)
    wp_start_py=max(0, wp_start_py); wp_start_px=max(0, wp_start_px); wp_end_py=min(sim_height_pixels, wp_end_py); wp_end_px=min(sim_width_pixels, wp_end_px)
    if wp_end_py>wp_start_py and wp_end_px>wp_start_px: workpiece_matrix[wp_start_py:wp_end_py, wp_start_px:wp_end_px] = 1
    else: print("Warning: Workpiece dimensions resulted in zero area.")
    sim_origin_mm = (0.0, 0.0); return workpiece_matrix, sim_origin_mm

def generate_tool_path_straight(start_xy, end_xy, step_ds):
    start_vec=np.array(start_xy); end_vec=np.array(end_xy); path_vector=end_vec-start_vec
    total_dist=np.linalg.norm(path_vector); path=[tuple(start_vec)]
    if total_dist<=1e-9: return path
    direction=path_vector/total_dist; num_steps=int(math.floor(total_dist/step_ds))
    for i in range(1, num_steps+1): path.append(tuple(start_vec+direction*i*step_ds))
    if not np.allclose(path[-1], end_xy, atol=step_ds*0.1): path.append(tuple(end_xy))
    elif total_dist>0 and len(path)==1: path.append(tuple(end_xy))
    return path

def generate_tool_path_slot(wp_x_start_mm, wp_y_center_mm, wp_width_mm, tool_rad_mm, pad_mm, step_ds):
    start_x=wp_x_start_mm-pad_mm/2.0; end_x=wp_x_start_mm+wp_width_mm+pad_mm/2.0
    y_pos=wp_y_center_mm; start_point=(start_x, y_pos); end_point=(end_x, y_pos)
    return generate_tool_path_straight(start_point, end_point, step_ds)


# --- Setup ---
print("Creating Tool Matrices...")
tool_matrix_filled, full_outline_indices_list, tool_matrix_center_offset = create_tool_matrices(
    tool_diameter, pixel_size
)
print(f"Tool filled matrix shape: {tool_matrix_filled.shape}")
print(f"Total FULL Outline Pixels: {len(full_outline_indices_list)}")
if not full_outline_indices_list:
    raise ValueError("Cannot simulate with zero outline pixels.")

# --- Perform Precomputation ---
precomputed_engageable_arrays = precompute_engageable_outlines(full_outline_indices_list, NUM_ANGLE_BINS)
# Check if precomputation returned data
if not precomputed_engageable_arrays:
     raise ValueError("Precomputation failed or returned no data.")


print("\nCreating Workpiece Matrix...")
workpiece, sim_origin = create_workpiece_matrix(
    sim_width_mm, sim_height_mm,
    workpiece_width_mm, workpiece_height_mm,
    padding_mm, pixel_size
)
initial_workpiece = workpiece.copy()
sim_height_pixels, sim_width_pixels = workpiece.shape
print(f"Workpiece matrix shape: {workpiece.shape}")

# --- Define Tool Path ---
wp_center_y_mm = padding_mm + workpiece_height_mm / 2.0
tool_path_mm = generate_tool_path_slot(
    padding_mm, wp_center_y_mm, workpiece_width_mm,
    tool_radius, padding_mm, sim_step_distance
)
# Make path longer for more steps
# tool_path_mm = tool_path_mm * 10 # Repeat path 10 times
print(f"\nGenerated tool path with {len(tool_path_mm)} steps.")

# --- Run Simulation ---
results_engagement_angle = []
path_lengths = []
current_path_length = 0.0
last_valid_direction_vec = np.array([1.0, 0.0])

print("\nStarting simulation (using Numba)...")
# --- Numba Compile Step ---
# Run the JITted functions once with sample data to trigger compilation
print("Compiling Numba functions...")
compile_start = time.time()
_ = _calculate_contact_njit(0, 0, np.array([[0,0]], dtype=np.int32), workpiece, sim_height_pixels, sim_width_pixels)
_ = _get_engageable_for_bin_njit(np.array(full_outline_indices_list,dtype=np.int32), np.array([1.0,0.0]), 1e-6)
print(f"Numba compilation finished in {time.time() - compile_start:.4f} seconds.")


start_time = time.time() # Start timing after compilation

tool_path_mm = [(60, 60),                     
                (60, 61),
                # (60, 60),
                # (60, 60),                
                ]


n_steps = len(tool_path_mm)
for i, tool_center_mm in enumerate(tool_path_mm):

    # --- 1. Calculate Path Length ---
    # (Same as before)
    if i > 0:
        dist_moved = np.linalg.norm(np.array(tool_center_mm) - np.array(tool_path_mm[i-1]))
        current_path_length += dist_moved
    path_lengths.append(current_path_length)


    # --- 2. Determine Current Movement Direction ---
    # (Same as before)
    current_pos = np.array(tool_center_mm)
    direction_vec = np.array([0.0, 0.0])
    if i == 0:
        if n_steps > 1: move_vec = np.array(tool_path_mm[1]) - current_pos
        else: move_vec = np.array([0.0, 0.0])
    else:
        move_vec = current_pos - np.array(tool_path_mm[i-1])
    move_norm = np.linalg.norm(move_vec)
    if move_norm > 1e-9:
        direction_vec = move_vec / move_norm
        last_valid_direction_vec = direction_vec
    else:
        direction_vec = last_valid_direction_vec


    # --- 3. Calculate Angle using Precomputed Outline (Numba) ---
    tool_center_px, tool_center_py = world_to_pixel(tool_center_mm, sim_origin, pixel_size)

    # Calculate angle bin
    current_angle_rad = math.atan2(direction_vec[1], direction_vec[0])
    current_angle_deg = math.degrees(current_angle_rad)
    normalized_angle_deg = (current_angle_deg + 360) % 360
    angle_bin = int(round(normalized_angle_deg * NUM_ANGLE_BINS / 360.0)) % NUM_ANGLE_BINS

    # Retrieve the precomputed NumPy array for this bin
    engageable_indices_array = precomputed_engageable_arrays.get(angle_bin)
    # Fallback for safety, though should not happen if precomputation is complete
    if engageable_indices_array is None:
         print(f"Warning: No precomputed array found for angle bin {angle_bin}. Skipping step {i}.")
         engageable_indices_array = np.empty((0,2), dtype=np.int32) # Empty array


    # Call the Numba JITted function to count contacts
    contacted_outline_pixels = _calculate_contact_njit(
        tool_center_px, tool_center_py,
        engageable_indices_array,
        workpiece, sim_height_pixels, sim_width_pixels
    )

    total_engageable_outline_pixels_dynamic = engageable_indices_array.shape[0]

    # Calculate the angle (degrees)
    engagement_angle_deg = 0.0
    if total_engageable_outline_pixels_dynamic > 0:
        engagement_angle_deg = (contacted_outline_pixels / total_engageable_outline_pixels_dynamic) * 180.0

    results_engagement_angle.append(engagement_angle_deg)

    # --- 4. Update Workpiece State (Using Full Tool Area - NumPy) ---
    # (Same as before - keeping this in NumPy for now)
    y_start=tool_center_py - tool_matrix_center_offset; y_end=y_start + tool_matrix_filled.shape[0]
    x_start=tool_center_px - tool_matrix_center_offset; x_end=x_start + tool_matrix_filled.shape[1]
    y_start_clip=max(0, y_start); y_end_clip=min(sim_height_pixels, y_end)
    x_start_clip=max(0, x_start); x_end_clip=min(sim_width_pixels, x_end)
    tool_y_start_offset=y_start_clip - y_start; tool_y_end_offset = tool_matrix_filled.shape[0] - (y_end - y_end_clip)
    tool_x_start_offset=x_start_clip - x_start; tool_x_end_offset = tool_matrix_filled.shape[1] - (x_end - x_end_clip)
    workpiece_slice = workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip]
    tool_slice_filled = tool_matrix_filled[tool_y_start_offset:tool_y_end_offset, tool_x_start_offset:tool_x_end_offset]
    if workpiece_slice.shape == tool_slice_filled.shape and workpiece_slice.size > 0:
        overlap = workpiece_slice & tool_slice_filled
        # Numba *could* optimize this assignment if it was a simple loop,
        # but boolean indexing is often faster via NumPy for large arrays.
        workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip][overlap == 1] = 0

    # Optional: Print progress less frequently
    if (i + 1) % 500 == 0 or i == n_steps - 1:
         print(f"Step {i+1}/{n_steps}, Angle Bin: {angle_bin}, Angle: {engagement_angle_deg:.2f} deg")


end_time = time.time()
sim_duration = end_time - start_time
print(f"\nSimulation finished in {sim_duration:.4f} seconds.")
if n_steps > 0:
    print(f"Average time per step: {sim_duration / n_steps * 1000:.4f} ms")


# --- Output / Visualization (Same as before) ---
# (Plotting code remains the same)
# print("\nSample Engagement Angles:")
# sample_indices = np.linspace(0, len(results_engagement_angle)-1, 10, dtype=int)
# for idx in sample_indices:
#     print(f"Path Length {path_lengths[idx]:.2f} mm: Angle = {results_engagement_angle[idx]:.2f} deg")

print(results_engagement_angle)