import numpy as np
import time

def ellipse_to_conic(h, k, a, b, theta):
    # Convert params to conic coefficients [A, B, C, D, E, F]
    cos_t, sin_t = np.cos(theta), np.sin(theta)
    A = a**2 * sin_t**2 + b**2 * cos_t**2
    B = 2 * (b**2 - a**2) * cos_t * sin_t
    C = a**2 * cos_t**2 + b**2 * sin_t**2
    D = -2 * A * h - B * k
    E = -B * h - 2 * C * k
    F = A * h**2 + B * h * k + C * k**2 - a**2 * b**2
    return np.array([A, B, C, D, E, F]) / np.max([A, B, C])  # Normalize

def is_inside_ellipse(x, y, conic_B):
    A, B, C, D, E, F = conic_B
    return A*x**2 + B*x*y + C*y**2 + D*x + E*y + F <= 0

# To check if A is inside B: generate parametric points on A and test
def check_containment(params_A, params_B, num_points=72):
    h1, k1, a1, b1, theta1 = params_A
    h2, k2, a2, b2, theta2 = params_B  # Not used here, but for full check
    t = np.linspace(0, 2*np.pi, num_points)
    x = h1 + a1 * np.cos(t) * np.cos(theta1) - b1 * np.sin(t) * np.sin(theta1)
    y = k1 + a1 * np.cos(t) * np.sin(theta1) + b1 * np.sin(t) * np.cos(theta1)
    conic_B = ellipse_to_conic(h2, k2, a2, b2, theta2)
    inside = all(is_inside_ellipse(x[i], y[i], conic_B) for i in range(num_points))
    return inside  # True if all boundary points of A are inside B (approx)

params_A = [0, 0, 1, 0.5, 0]  # h,k,a,b,theta
params_B = [0, 0, 1.4, 1, 2]
time1 = time.time()
print(check_containment(params_A, params_B))
time2 = time.time()
print(f'Time: {time2-time1}')
