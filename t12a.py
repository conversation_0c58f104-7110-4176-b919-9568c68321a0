# First, install the library if you haven't already
# !pip install pyclothoids

import numpy as np
from pyclothoids import Clothoid

# Define start and end configurations
# Start at (0, 0) with direction 0 radians
x0, y0, theta0 = 0.0, 0.0, 0.0
# End at (5, 2) with direction π/2 radians (90 degrees)
x1, y1, theta1 = 5.0, 2.0, np.pi/2

# Solve for the clothoid connecting these configurations
clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)

# Sample points along the clothoid for plotting
s = np.linspace(0, clothoid.length, 100)
X, Y = clothoid.SampleXY(100)

print(X, Y)

