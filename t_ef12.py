import numpy as np
from scipy.optimize import least_squares
from typing import Tuple

# --------------------------------------------------------------
# 1️⃣  Residual function  (vectorised, pure NumPy)
# --------------------------------------------------------------
def _ellipse_residuals(params: np.ndarray,
                       pts: np.ndarray) -> np.ndarray:
    """
    Compute raw algebraic residuals for a set of points.

    Parameters
    ----------
    params : (5,) array_like
        [xc, yc, a, b, theta] (θ in radians)
    pts : (N,2) array
        Points (x, y) where the residual is evaluated.

    Returns
    -------
    res : (N,) array
        Residuals = 0 for a perfect ellipse.
    """
    xc, yc, a, b, theta = params

    # centre‑shifted points
    X = pts[:, 0] - xc
    Y = pts[:, 1] - yc

    # pre‑compute sin & cos once per evaluation (much faster)
    c = np.cos(theta)
    s = np.sin(theta)

    # rotated coordinate system (no matrix multiply!)
    #   X' =  X*c + Y*s
    #   Y' = -X*s + Y*c
    Xp =  X * c + Y * s
    Yp = -X * s + Y * c

    # algebraic distance (0 if exact)
    res = (Xp ** 2) / (a ** 2) + (Yp ** 2) / (b ** 2) - 1.0
    return res


def _ellipse_jacobian(params: np.ndarray,
                      pts: np.ndarray) -> np.ndarray:
    """
    Analytic Jacobian ∂r_i/∂(xc, yc, a, b, θ). Shape (N,5)
    """
    xc, yc, a, b, theta = params

    X = pts[:, 0] - xc
    Y = pts[:, 1] - yc

    c = np.cos(theta)
    s = np.sin(theta)

    # Rotated coordinates
    Xp =  X * c + Y * s
    Yp = -X * s + Y * c

    # Helper denominators
    a2 = a * a
    b2 = b * b
    inv_a2 = 1.0 / a2
    inv_b2 = 1.0 / b2

    # Derivatives
    dXc = -1.0                           # ∂X/∂xc = -1
    dYc = -1.0                           # ∂Y/∂yc = -1

    # partial derivatives of Xp and Yp
    dXp_dx =  c
    dXp_dy =  s
    dXp_dxc = -c
    dXp_dyc = -s

    dYp_dx = -s
    dYp_dy =  c
    dYp_dxc =  s
    dYp_dyc = -c

    # Residual r = (Xp^2)/a^2 + (Yp^2)/b^2 - 1  (scalar)
    # ∂r/∂xc = 2*Xp/a^2 * dXp_dxc + 2*Yp/b^2 * dYp_dxc  + 0
    # similarly for other params.
    dr_dxc = 2 * Xp * inv_a2 * dXp_dxc + 2 * Yp * inv_b2 * dYp_dxc
    dr_dyc = 2 * Xp * inv_a2 * dXp_dyc + 2 * Yp * inv_b2 * dYp_dyc
    dr_da  = -2 * Xp * Xp / (a**3)            # d/(da) (Xp^2/a^2) = -2 Xp^2 / a^3
    dr_db  = -2 * Yp * Yp / (b**3)            # similar for b
    # d/ dθ term (use product rule)
    dXp_dθ = -X * s + Y * c               # d/dθ (X*c+Y*s) = -X*s + Y*c
    dYp_dθ = -X * c - Y * s               # d/dθ (-X*s + Y*c) = -X*c - Y*s

    dr_dtheta = 2 * Xp * inv_a2 * dXp_dθ + 2 * Yp * inv_b2 * dYp_dθ

    # Assemble Jacobian (N x 5)
    J = np.empty((pts.shape[0], 5), dtype=np.float64)
    J[:, 0] = dr_dxc           # ∂r/∂xc
    J[:, 1] = dr_dyc           # ∂r/∂yc
    J[:, 2] = dr_da
    J[:, 3] = dr_db
    J[:, 4] = dr_dtheta
    return J


# --------------------------------------------------------------
# 2️⃣  Fit wrapper -------------------------------------------------
def fit_ellipse(points: np.ndarray,
                initial: np.ndarray,
                bounds: Tuple[np.ndarray, np.ndarray],
                *,
                max_nfev: int = 15,
                ftol: float = 1e-6,
                xtol: float = 1e-6,                
                verbose: int = 0) -> Tuple[np.ndarray, int]:
    """
    Fast 5‑point ellipse fitting using `scipy.optimize.least_squares`.

    Parameters
    ----------
    points : (N,2) array
        Points to be fitted (N≥5; any N works).
    initial : (5,) array_like
        Initial guess `[xc, yc, a, b, θ]`.
    bounds : (lower, upper) each of length 5
        Bounds on `(xc, yc, a, b, θ)`. Angles must be in radians.
    max_nfev : int, optional
        Maximum number of function evaluations.
    ftol, xtol : float, optional
        Convergence tolerances (default ~1e‑10, very strict).
    verbose : {0,1,2,3}
        Verbosity passed to `least_squares`.

    Returns
    -------
    params : (5,) array
        The fitted parameters.
    nfev : int
        Number of function evaluations used.
    """
    # Convert to float64 once, to avoid type‑promotion inside the loop
    pts = np.asarray(points, dtype=np.float64)

    # ----------------------------------------------------------------
    # Use the ‘trf’ algorithm because it supports bounds.
    # ----------------------------------------------------------------
    res = least_squares(
        fun=_ellipse_residuals,
        x0=np.asarray(initial, dtype=np.float64),
        args=(pts,),
        method='trf',                # Trust‑region reflective (= supports bounds)
        bounds=bounds,
        jac=_ellipse_jacobian,
        max_nfev=max_nfev,
        ftol=ftol,
        xtol=xtol,
        gtol=1e-6,
        # diff_step=1e-8,  # Smaller step for numerical derivatives
        verbose=verbose,
        # Slightly reduce overhead: store Jacobian numerically.
        # If you want a custom Jacobian, add `jac=_ellipse_jacobian`.
    )
    return res.x, res.nfev

# --------------------------------------------------------------
# 3️⃣  Example usage (the data you gave) -----------------------
if __name__ == '__main__':
    import numpy as np

    points = np.array([[-18107.85742188,  -9668.421875  ],
                       [-18109.07421875,  -9649.95117188],
                       [-18133.55859375,  -9622.34765625],
                       [-18161.0234375 ,  -9615.94433594],
                       [-18180.34570312, -9623.63476562]], dtype=np.float64)

    # bounds in the (xc, yc, a, b, θ) order – angle in rad.
    bounds = (np.array([-18170, -9679, 30, 20, np.deg2rad(320)], dtype=np.float64),
              np.array([-18130, -9625, 60, 40, np.deg2rad(340)], dtype=np.float64))

    # initial guess
    init = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)

    # -------------------------------------------------------------
    # Fit it
    # -------------------------------------------------------------

    import time
    time1 = time.time()
    params, nfev = fit_ellipse(points, init, bounds, verbose=2)
    time2 = time.time()
    print(f'Time: {time2-time1}')

    # -------------------------------------------------------------
    # Show results (float with only a few decimals)
    # -------------------------------------------------------------
    xc, yc, a, b, theta = params
    print('\nFitted ellipse:')
    print(f'  centre   : ({xc:.3f} , {yc:.3f})')
    print(f'  a = {a:.3f}   b = {b:.3f}')
    print(f'  θ (deg) = {np.rad2deg(theta):.3f}')
    print('  nfev      =', nfev)
