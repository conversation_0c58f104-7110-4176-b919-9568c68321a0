import numpy as np
from scipy.optimize import root_scalar

# Parameters from your example
a = 20
b = 10
px = -21.4249
py = -4.66661
pivot_y = -b  # -10
min_radius_of_curvature = 5

# Step 1: Find unconstrained solution (proportional k)
def f_unconstrained(k):
    sx = 1 + k * abs(px)
    sy = 1 + k * py
    if sy <= 0:
        return np.inf
    cy = pivot_y + (0 - pivot_y) * sy
    a_new = a * sx
    b_new = b * sy
    term_x = (px / a_new) ** 2
    term_y = ((py - cy) / b_new) ** 2
    return term_x + term_y - 1

# Bracket for k
k_low = 0
f_low = f_unconstrained(k_low)
k_high = 1e-3
f_high = f_unconstrained(k_high)
max_iter = 100
iter_count = 0
while (f_low * f_high > 0 or np.isinf(f_high)) and iter_count < max_iter:
    k_high *= 2
    f_high = f_unconstrained(k_high)
    iter_count += 1

if f_low * f_high > 0 or np.isinf(f_high):
    raise ValueError("No root found in reasonable bracket for unconstrained")

sol = root_scalar(f_unconstrained, bracket=[k_low, k_high], method='brentq')
k = sol.root

# Unconstrained values
sx_unc = 1 + k * abs(px)
sy_unc = 1 + k * py
a_new_unc = a * sx_unc
b_new_unc = b * sy_unc
cy_unc = pivot_y + (0 - pivot_y) * sy_unc
rho_unc = b_new_unc**2 / a_new_unc

# Check constraint
min_b = np.sqrt(min_radius_of_curvature * a_new_unc)
if b_new_unc >= min_b:
    print("Unconstrained solution satisfies constraint")
    print(f"k: {k}")
    print(f"sx: {sx_unc}")
    print(f"sy: {sy_unc}")
    print(f"new_center_y: {cy_unc}")
    print(f"rho: {rho_unc}")
else:
    # Constraint violated: Solve for smaller a_new with rho = min_rho
    print("Constraint violated - solving for smaller a_new with rho = min_rho")
    
    def g(a_new):
        if a_new <= 0:
            return np.inf
        b_new = np.sqrt(min_radius_of_curvature * a_new)
        sy = b_new / b
        cy = pivot_y + (0 - pivot_y) * sy
        term_x = (px / a_new) ** 2
        term_y = ((py - cy) / b_new) ** 2
        return term_x + term_y - 1

    # Bracket downward: from small a_low to a_new_unc
    a_low = 1e-3  # Small positive
    g_low = g(a_low)
    a_high = a_new_unc
    g_high = g(a_high)
    iter_count = 0
    # If no sign change, try to adjust a_low downward if needed, but since g_low likely >0, g_high <0 for these cases
    if g_low * g_high > 0:
        # If same sign, check if we need upward instead
        a_low_up = a_new_unc
        g_low_up = g(a_low_up)
        a_high_up = a_new_unc * 2
        g_high_up = g(a_high_up)
        while (g_low_up * g_high_up > 0 or np.isinf(g_high_up)) and iter_count < max_iter:
            a_high_up *= 2
            g_high_up = g(a_high_up)
            iter_count += 1
        if g_low_up * g_high_up <= 0 and not np.isinf(g_high_up):
            # Use upward bracket
            sol_con = root_scalar(g, bracket=[a_low_up, a_high_up], method='brentq')
        else:
            raise ValueError("No root found in either downward or upward bracket for constrained")
    else:
        # Use downward bracket
        sol_con = root_scalar(g, bracket=[a_low, a_high], method='brentq')

    a_new = sol_con.root
    b_new = np.sqrt(min_radius_of_curvature * a_new)
    sy = b_new / b
    sx = a_new / a
    cy_new = pivot_y + (0 - pivot_y) * sy
    rho = b_new**2 / a_new  # Should be exactly min_radius

    print(f"adjusted_sx: {sx}")
    print(f"adjusted_sy: {sy}")
    print(f"new_center_y: {cy_new}")
    print(f"rho: {rho}")