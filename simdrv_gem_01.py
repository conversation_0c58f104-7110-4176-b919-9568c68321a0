import numpy as np
import cv2
import shapely.geometry as sg
import shapely.affinity as sa
import math
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional

# --- Simulation Environment ---
class SimulationEnvironment:
    def __init__(self, pocket_polygon: sg.Polygon, tool_radius: float, resolution: int):
        """
        Initializes the simulation environment.

        Args:
            pocket_polygon: Shapely polygon representing the pocket boundary.
            tool_radius: Radius of the cutting tool.
            resolution: Size of the simulation grid (e.g., 512 for a 512x512 grid).
        """
        self.pocket_polygon_orig = pocket_polygon
        self.tool_radius = tool_radius
        self.resolution = resolution

        # --- Determine grid bounds and scaling ---
        minx, miny, maxx, maxy = pocket_polygon.bounds
        self.bounds = (minx, miny, maxx, maxy)
        self.width = maxx - minx
        self.height = maxy - miny
        self.scale = resolution / max(self.width, self.height)
        self.offset_x = -minx * self.scale
        self.offset_y = -miny * self.scale
        # Pad resolution slightly if aspect ratio isn't 1:1
        self.grid_width = int(self.width * self.scale) + 1
        self.grid_height = int(self.height * self.scale) + 1

        self.grid = np.zeros((self.grid_height, self.grid_width), dtype=np.uint8)
        self.tool_radius_px = int(self.tool_radius * self.scale)

        print(f"Simulation Grid: {self.grid_height}x{self.grid_width} px")
        print(f"Tool Radius: {self.tool_radius_px} px")

        # --- Render initial pocket ---
        self._render_polygon(self.pocket_polygon_orig, 255) # 255 = material

    def _world_to_pixel(self, point: Tuple[float, float]) -> Tuple[int, int]:
        """Converts world coordinates to pixel coordinates."""
        x, y = point
        px = int(x * self.scale + self.offset_x)
        py = int(y * self.scale + self.offset_y)
        # OpenCV uses (y, x) indexing for numpy arrays
        return (py, px)

    def _pixel_to_world(self, pixel: Tuple[int, int]) -> Tuple[float, float]:
         """Converts pixel coordinates to world coordinates."""
         py, px = pixel
         x = (px - self.offset_x) / self.scale
         y = (py - self.offset_y) / self.scale
         return (x, y)

    def _render_polygon(self, polygon: sg.Polygon, color: int):
        """Renders a polygon onto the grid."""
        if polygon.is_empty: return

        exterior_px = np.array([self._world_to_pixel(p) for p in polygon.exterior.coords], dtype=np.int32)
        cv2.fillPoly(self.grid, [exterior_px], color)

        for interior in polygon.interiors:
            interior_px = np.array([self._world_to_pixel(p) for p in interior.coords], dtype=np.int32)
            cv2.fillPoly(self.grid, [interior_px], 0) # Cut out islands

    def _render_tool(self, center_px: Tuple[int, int], target_grid: np.ndarray, color: int):
         """Renders a tool (circle) onto a target grid."""
         py, px = center_px
         cv2.circle(target_grid, (px, py), self.tool_radius_px, color, -1) # -1 thickness fills


    def check_engagement(self, next_pos_world: Tuple[float, float]) -> Tuple[int, bool]:
        """
        Calculates the number of material pixels the tool would remove at the next position.

        Args:
            next_pos_world: The potential next center position of the tool in world coordinates.

        Returns:
            A tuple (engagement_pixels, is_gouge).
            engagement_pixels: Number of material pixels removed.
            is_gouge: True if the tool center is outside the original pocket boundary.
                      Returns -1 engagement if gouge detected.
        """
        next_pos_px = self._world_to_pixel(next_pos_world)
        py, px = next_pos_px

        # --- Basic Gouge Check (Tool Center) ---
        # A more robust check would verify the entire tool disk
        if not self.pocket_polygon_orig.contains(sg.Point(next_pos_world)):
             # Check slightly expanded boundary to allow touching
             if not self.pocket_polygon_orig.buffer(self.tool_radius * 0.1).contains(sg.Point(next_pos_world)):
                # print(f"Gouge detected at {next_pos_world}")
                return -1, True # Indicate gouge with -1

        # --- Calculate Engagement ---
        # Create a mask where the tool will be
        tool_mask = np.zeros_like(self.grid)
        self._render_tool(next_pos_px, tool_mask, 1) # Use 1 for the mask value

        # Find where the tool mask overlaps with current material (255)
        engagement_pixels = np.sum((tool_mask == 1) & (self.grid == 255))

        return int(engagement_pixels), False


    def cut(self, center_world: Tuple[float, float]):
        """Permanently removes material by rendering the tool in black."""
        center_px = self._world_to_pixel(center_world)
        self._render_tool(center_px, self.grid, 0) # 0 = cut (black)

    def get_current_state(self) -> np.ndarray:
        """Returns a copy of the current simulation grid."""
        return self.grid.copy()

    def visualize(self, toolpath_world: Optional[List[Tuple[float, float]]] = None, title="Simulation State"):
        """Displays the current grid state."""
        plt.figure(figsize=(8, 8))
        plt.imshow(self.grid, cmap='gray', origin='lower',
                   extent=[self.bounds[0], self.bounds[2], self.bounds[1], self.bounds[3]]) # Use world bounds for extent

        if toolpath_world:
            path_x = [p[0] for p in toolpath_world]
            path_y = [p[1] for p in toolpath_world]
            plt.plot(path_x, path_y, 'r.-', markersize=2, linewidth=1, label="Toolpath")
            if len(path_x) > 0:
                 plt.plot(path_x[-1], path_y[-1], 'go', markersize=5, label="Current Pos") # Mark end

        # Plot original pocket outline for reference
        if self.pocket_polygon_orig.geom_type == 'Polygon':
            px, py = self.pocket_polygon_orig.exterior.xy
            plt.plot(px, py, 'b--', linewidth=0.8, label="Original Pocket")
            for interior in self.pocket_polygon_orig.interiors:
                ix, iy = interior.xy
                plt.plot(ix, iy, 'b--', linewidth=0.8)
        elif self.pocket_polygon_orig.geom_type == 'MultiPolygon':
             for poly in self.pocket_polygon_orig.geoms:
                 px, py = poly.exterior.xy
                 plt.plot(px, py, 'b--', linewidth=0.8, label="Original Pocket")
                 for interior in poly.interiors:
                    ix, iy = interior.xy
                    plt.plot(ix, iy, 'b--', linewidth=0.8)


        plt.title(title)
        plt.xlabel("X (world)")
        plt.ylabel("Y (world)")
        plt.legend(fontsize='small')
        plt.axis('equal')
        plt.show()

# --- Toolpath Generator ---
class ToolpathGenerator:
    def __init__(self, pocket_polygon: sg.Polygon, tool_radius: float,
                 step_size: float, target_engagement_factor: float, # Factor of tool diameter pixels
                 engagement_tolerance_factor: float, # Factor of target engagement
                 max_angle_dev_deg: float, sim_resolution: int):

        self.pocket_polygon = pocket_polygon
        self.tool_radius = tool_radius
        self.step_size = step_size # World units
        self.max_angle_dev_deg = max_angle_dev_deg
        self.sim_resolution = sim_resolution

        self.sim_env = SimulationEnvironment(pocket_polygon, tool_radius, sim_resolution)

        # --- Calculate Target Engagement in Pixels ---
        # Base on tool diameter pixels squared, roughly area
        tool_diameter_px = 2 * self.sim_env.tool_radius_px
        # Heuristic: Target a fraction of the new area the tool covers per step
        # A full step moves the circle, the new area is complex, approx rect width * step
        # Let's use a simpler target: a fraction of the tool's pixel area
        # Or better: target based on angular engagement (harder here)
        # Let's use the paper's idea: compare pixel counts before/after
        # We need a *target count*. What's reasonable?
        # A stepover of ~50% radius -> ~75% area overlap?
        # Target pixels should relate to step_size_px * tool_diameter_px
        step_size_px = step_size * self.sim_env.scale
        # Rough target based on a fraction of the rectangle swept by the diameter
        # This needs tuning based on desired stepover!
        # Let's target a fraction of the tool's *radius* in pixels as the engagement arc length proxy
        self.target_engagement_px = int(target_engagement_factor * self.sim_env.tool_radius_px)
        # self.target_engagement_px = 50 # DEBUG: Hardcode for now

        self.engagement_tolerance_px = int(engagement_tolerance_factor * self.target_engagement_px)
        # Ensure tolerance is at least 1 pixel
        self.engagement_tolerance_px = max(1, self.engagement_tolerance_px)

        print(f"Target Engagement: {self.target_engagement_px} px")
        print(f"Engagement Tolerance: +/- {self.engagement_tolerance_px} px")

        self.raw_toolpath_world: List[Tuple[float, float]] = []
        self.raw_engagements: List[int] = [] # Store engagement for each step


    def _calculate_start_point(self) -> Tuple[float, float]:
        """Finds a starting point inside the pocket."""
        # Simple centroid or representative point
        start_pt = self.pocket_polygon.representative_point()
        # Optional: Plunge hole slightly larger than tool
        # self.sim_env.cut(start_pt.coords[0]) # Simulate plunge
        print(f"Start point (world): {start_pt.coords[0]}")
        return start_pt.coords[0]

    # Corresponds roughly to GetEngagement in Algorithm 1 + checkEngagement
    def _get_engagement_for_angle(self, current_path: List[Tuple[float, float]],
                                 last_direction: Tuple[float, float],
                                 angle_deg: float) -> Tuple[Optional[Tuple[float, float]], int, bool]:
        """
        Calculates the next point and its engagement for a given angle deviation.

        Returns: (next_point_world | None, engagement_pixels, is_gouge)
                 Returns None for next_point if calculation fails.
        """
        if not current_path: return None, 0, False

        current_pos = current_path[-1]
        angle_rad = math.radians(angle_deg)

        # Rotate the last direction vector
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        dx, dy = last_direction
        next_dx = dx * cos_a - dy * sin_a
        next_dy = dx * sin_a + dy * cos_a

        # Normalize (important if last_direction wasn't normalized)
        norm = math.sqrt(next_dx**2 + next_dy**2)
        if norm < 1e-9: # Avoid division by zero if last direction was zero
             if angle_deg == 0: # Try moving along original direction if angle is 0
                  next_dx, next_dy = dx, dy
                  norm = math.sqrt(next_dx**2 + next_dy**2)
                  if norm < 1e-9: return None, 0, False # Still zero
             else:
                  # If direction is zero and angle isn't, we can't rotate. Maybe default?
                  # Default to horizontal if no prior direction
                  next_dx, next_dy = 1.0, 0.0
                  norm = 1.0


        next_dx /= norm
        next_dy /= norm

        # Calculate next position
        next_pos_world = (current_pos[0] + next_dx * self.step_size,
                          current_pos[1] + next_dy * self.step_size)

        engagement, is_gouge = self.sim_env.check_engagement(next_pos_world)

        return next_pos_world, engagement, is_gouge


    # Corresponds to Algorithm 3: FindBestMove
    def _find_best_spiral_move(self, current_path: List[Tuple[float, float]],
                              last_direction: Tuple[float, float]) -> Tuple[Optional[Tuple[float, float]], bool]:
        """
        Tries to find the next move that satisfies engagement criteria.

        Returns: (best_point_found | None, found_valid_move)
                 'found_valid_move' is True if a move with positive engagement was found,
                 even if not perfectly within tolerance (used by boundary follower logic).
        """
        best_pos: Optional[Tuple[float, float]] = None
        min_delta_engagement = abs(self.target_engagement_px) # Initialize high
        best_engagement = -1 # Track engagement of best move
        found_positive_engagement = False

        # Explore angles: 0, +1, -1, +2, -2, ...
        angles_to_check = [0] + [a * sgn for a in range(1, int(self.max_angle_dev_deg) + 1) for sgn in [1, -1]]

        for angle_deg in angles_to_check:
            next_pos, engagement, is_gouge = self._get_engagement_for_angle(current_path, last_direction, angle_deg)

            if next_pos is None or is_gouge:
                # print(f"Angle {angle_deg}: Gouge or Calc Error (Eng: {engagement})")
                continue # Skip gouged or invalid points

            if engagement > 0:
                found_positive_engagement = True

            delta_engagement = abs(engagement - self.target_engagement_px)

            # Algorithm 3 logic refinement:
            # 1. Check if within tolerance (early exit)
            if engagement > 0 and delta_engagement <= self.engagement_tolerance_px:
                 # print(f"Angle {angle_deg}: Found move within tolerance. Engagement: {engagement}")
                 return next_pos, True # Found ideal move

            # 2. If not ideal, track the best one seen so far (minimum delta)
            if delta_engagement < min_delta_engagement:
                 # print(f"Angle {angle_deg}: New best delta {delta_engagement}. Engagement: {engagement}")
                 min_delta_engagement = delta_engagement
                 best_pos = next_pos
                 best_engagement = engagement

            # 3. Local minimum check (stop if delta starts increasing *after finding a best*)
            # This check is tricky. Let's simplify: We iterate all angles and pick the best overall.
            # The paper's check `if best < Etar and d > dbest` seems aimed at preventing
            # spiraling *out* too quickly if the target can't be met.
            # Let's omit this specific check for now and rely on picking the overall best.

        # After checking all angles:
        if best_pos is not None and best_engagement > 0: # Return best found if it had positive engagement
             # print(f"Selected best overall move. Engagement: {best_engagement}, Delta: {min_delta_engagement}")
             return best_pos, True
        elif found_positive_engagement: # Found some positive engagement, but best had none? Return something.
             # This case might need the original last valid positive point? Revisit logic.
             # For now, return based on best_pos check
             return None, True # Indicate positive engagement was seen, but no suitable move chosen
        else:
            # print("No suitable spiral move found.")
            return None, False


    # Corresponds to Algorithm 4: FindBoundaryMove
    def _find_boundary_move(self, current_path: List[Tuple[float, float]],
                           last_direction: Tuple[float, float]) -> Tuple[Optional[Tuple[float, float]], bool]:
        """
        Tries to find a move by following the boundary (zero engagement contour).

        Returns: (boundary_point_found | None, success)
        """
        prev_engagement = -999 # Sentinel value
        prev_pos = None

        # Explore angles: 0, +1, -1, +2, -2, ... (Wider range might be needed for boundary)
        angles_to_check = [0] + [a * sgn for a in range(1, int(self.max_angle_dev_deg * 1.5) + 1) for sgn in [1, -1]] # Wider search

        # First pass to get initial prev_engagement (using smallest angle)
        _, initial_engagement, initial_gouge = self._get_engagement_for_angle(current_path, last_direction, angles_to_check[0])
        if initial_gouge: initial_engagement = -1 # Use -1 for gouge
        prev_engagement = initial_engagement

        for angle_deg in angles_to_check:
            next_pos, engagement, is_gouge = self._get_engagement_for_angle(current_path, last_direction, angle_deg)

            if next_pos is None: continue # Skip calculation errors

            current_engagement = -1 if is_gouge else engagement # Use -1 for gouge engagement

            # Algorithm 4 Logic: Look for transitions involving zero engagement or gouge (-1)
            # Transition from non-cutting/gouge to cutting: Doesn't fit boundary following goal
            # Transition from cutting to non-cutting/gouge: We want the point *before* the transition

            # Check 1: prev was gouge (-1) and current is non-cutting (0) -> Return current pos (just cleared gouge)
            if prev_engagement == -1 and current_engagement == 0:
                 # print(f"Boundary Found (Gouge->Clear): Angle {angle_deg}, Pos {next_pos}")
                 return next_pos, True

            # Check 2: prev was non-cutting (0) and current is gouge (-1) -> Return *previous* pos
            elif prev_engagement == 0 and current_engagement == -1:
                 # print(f"Boundary Found (Clear->Gouge): Angle {angle_deg}, Prev Pos {prev_pos}")
                 if prev_pos: # Need the previous position to return
                      return prev_pos, True
                 else: # This happens if the very first angle check hits this
                      continue # Can't return previous if it doesn't exist

            # Check 3: (Implicit) prev was cutting (>0) and current is non-cutting (0) or gouge (-1)
            # This signifies hitting the wall. We might want the previous point?
            # Let's stick to the paper's explicit checks for now.

            # Update for next iteration
            prev_engagement = current_engagement
            prev_pos = next_pos # Store the position corresponding to prev_engagement

        # print("No boundary move found.")
        return None, False


    # Corresponds to Algorithm 5: Overall Move Function
    def _find_next_move(self, current_path: List[Tuple[float, float]],
                       last_direction: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """Determines the next point by trying spiral then boundary following."""

        # 1. Try to find a spiral move within engagement tolerance
        best_spiral_pos, spiral_had_positive_engagement = self._find_best_spiral_move(current_path, last_direction)

        if best_spiral_pos is not None:
            # print("Using Spiral Move")
            return best_spiral_pos
        else:
            # print("Spiral failed, trying Boundary Move")
            # 2. If spiral fails, try to find a boundary move
            boundary_pos, boundary_success = self._find_boundary_move(current_path, last_direction)
            if boundary_success:
                # print("Using Boundary Move")
                return boundary_pos
            else:
                # print("All strategies failed.")
                return None # Both failed

    def generate_raw_toolpath(self, max_steps=5000):
        """Generates the raw toolpath using the simulation."""
        start_point = self._calculate_start_point()
        self.raw_toolpath_world = [start_point]
        self.raw_engagements = [0] # Engagement for the start point is 0

        # --- Initial Move ---
        # Make a small initial step, e.g., horizontally, to establish direction
        last_direction = (1.0, 0.0) # Initial direction
        initial_step_pos = (start_point[0] + self.step_size, start_point[1])
        # Check if initial step is valid (might start near edge)
        initial_engagement, initial_gouge = self.sim_env.check_engagement(initial_step_pos)
        if not initial_gouge and initial_engagement >= 0 :
             self.raw_toolpath_world.append(initial_step_pos)
             self.sim_env.cut(initial_step_pos)
             self.raw_engagements.append(initial_engagement)
             print(f"Step 1: Initial move. Engagement: {initial_engagement}")
        else:
             # Try other initial directions if horizontal fails? Or just stop?
             print("Initial move failed (gouge or no engagement). Stopping.")
             return self.raw_toolpath_world, self.raw_engagements


        step_count = 1
        while step_count < max_steps:
            current_pos = self.raw_toolpath_world[-1]
            prev_pos = self.raw_toolpath_world[-2]

            # Update last direction
            dx = current_pos[0] - prev_pos[0]
            dy = current_pos[1] - prev_pos[1]
            norm = math.sqrt(dx**2 + dy**2)
            if norm > 1e-9:
                last_direction = (dx / norm, dy / norm)
            # else: keep previous last_direction


            next_point = self._find_next_move(self.raw_toolpath_world, last_direction)

            if next_point:
                # Must recalculate engagement for the *chosen* point before cutting
                final_engagement, final_gouge = self.sim_env.check_engagement(next_point)
                if final_gouge:
                     print(f"Step {step_count+1}: Chosen move resulted in gouge! Stopping.")
                     break

                self.raw_toolpath_world.append(next_point)
                self.sim_env.cut(next_point) # Update simulation state
                self.raw_engagements.append(final_engagement)
                step_count += 1

                if step_count % 50 == 0:
                     print(f"Step {step_count}: Pos {next_point}, Engagement: {final_engagement}")
                     # Optional: Visualize progress
                     # self.sim_env.visualize(self.raw_toolpath_world, f"Step {step_count}")

                # --- Stopping condition ---
                # The paper suggests stopping after a full boundary traversal with no cutting.
                # Implementing this robustly is hard. Simpler: stop if boundary follower
                # fails or after N steps, or if engagement stays near zero for too long.
                if len(self.raw_engagements) > 100 and np.mean(self.raw_engagements[-50:]) < 1:
                     print(f"Step {step_count+1}: Low engagement for extended period. Stopping.")
                     break

            else:
                print(f"Step {step_count+1}: Failed to find next valid move. Stopping.")
                break

        print(f"Raw toolpath generated with {len(self.raw_toolpath_world)} points.")
        return self.raw_toolpath_world, self.raw_engagements

    # --- Post-Processing (Outlined) ---

    def _segment_regions(self, toolpath, engagements, engagement_threshold=1):
        """(Algorithm 6) Groups path into cutting/non-cutting regions."""
        print("Segmenting regions...")
        regions = []
        if not toolpath: return regions

        current_region = {'points': [], 'cutting': None, 'group': -1}
        is_first_point = True

        for i, point in enumerate(toolpath):
            engagement = engagements[i]
            is_cutting = engagement >= engagement_threshold # Treat 0 as non-cutting

            if is_first_point:
                current_region['points'].append(point)
                current_region['cutting'] = is_cutting
                is_first_point = False
            elif is_cutting == current_region['cutting']:
                current_region['points'].append(point)
            else:
                # End previous region
                if len(current_region['points']) > 1: # Need at least 2 points for a segment
                     regions.append(current_region.copy())
                # Start new region
                current_region = {'points': [toolpath[i-1], point], # Include transition point
                                  'cutting': is_cutting, 'group': -1}

        # Add the last region
        if len(current_region['points']) > 1:
            regions.append(current_region)

        print(f"Segmented into {len(regions)} regions.")
        return regions


    def _group_trochoidal(self, regions):
         """(Algorithm 7) Groups cutting regions into trochoidal stacks."""
         # Requires complex geometric checks (parallelism, distance)
         # Placeholder implementation
         print("Grouping trochoidal regions (Placeholder)...")
         trochoid_stacks = []
         cutting_regions = [r for r in regions if r['cutting']]
         assigned = [False] * len(cutting_regions)

         stack_index = 0
         for i, region1 in enumerate(cutting_regions):
              if assigned[i]: continue

              # Start a new stack
              new_stack = [region1]
              region1['group'] = stack_index
              assigned[i] = True

              # Find other parallel regions (Simplified check)
              # A real check needs point sampling and distance checks
              # centroid1 = sg.LineString(region1['points']).centroid
              # approx_dir1 = # Calculate average direction vector

              # for j, region2 in enumerate(cutting_regions):
              #     if i == j or assigned[j]: continue
                   # centroid2 = ...
                   # approx_dir2 = ...
                   # is_parallel = # Vector dot product close to 1 or -1
                   # distance = # Distance between centroids or sampled points
                   # if is_parallel and abs(distance - tool_stepover) < tolerance:
                   #     new_stack.append(region2)
                   #     region2['group'] = stack_index
                   #     assigned[j] = True

              trochoid_stacks.append(new_stack)
              stack_index += 1

         print(f"Grouped into {len(trochoid_stacks)} potential trochoid stacks.")
         return trochoid_stacks, regions # Regions now have group IDs


    def _group_boundary(self, regions):
         """Groups overlapping boundary (non-cutting) regions."""
         # Requires checking geometric overlap (e.g., LineString intersection/contains)
         print("Grouping boundary regions (Placeholder)...")
         boundary_stacks = []
         non_cutting_regions = [r for r in regions if not r['cutting']]
         # ... similar logic to trochoidal grouping but checking overlap ...
         return boundary_stacks, regions

    def _compose_toolpath(self, regions, trochoid_stacks, boundary_stacks):
         """(Algorithm 8) Composes the final path from grouped regions."""
         # Needs careful handling of trochoid/boundary group insertion
         # and adding linking moves.
         print("Composing final toolpath (Placeholder)...")
         final_path = []
         # ... iterate regions, add stacks only once, add minimal boundary, add links ...
         # For now, just return the raw path concatenated
         for r in regions:
              final_path.extend(r['points'][1:]) # Avoid duplicating connection points
         return final_path


    def _smooth_corners(self, toolpath):
         """Applies smoothing (e.g., Chaikin) to the toolpath."""
         # Chaikin's algorithm implementation or using a library function
         print("Smoothing corners (Placeholder)...")
         # ... implementation ...
         return toolpath


    def post_process(self, raw_toolpath, raw_engagements):
        """Applies all post-processing steps."""
        if not raw_toolpath or len(raw_toolpath) < 2:
            return raw_toolpath

        regions = self._segment_regions(raw_toolpath, raw_engagements)
        trochoid_stacks, regions = self._group_trochoidal(regions)
        boundary_stacks, regions = self._group_boundary(regions)
        composed_path = self._compose_toolpath(regions, trochoid_stacks, boundary_stacks)
        # smoothed_path = self._smooth_corners(composed_path)
        # Gouge check final path?

        # For now, return composed (which is just raw concatenated)
        return composed_path

    def generate(self, max_steps=5000):
        """Generates the full toolpath with post-processing."""
        raw_path, raw_eng = self.generate_raw_toolpath(max_steps)
        # final_path = self.post_process(raw_path, raw_eng) # Enable when implemented
        final_path = raw_path # Use raw path for now
        return final_path


# --- Example Usage ---
if __name__ == "__main__":
    # --- Define Pocket Geometry (Example: L-shape with island) ---
    l_shape_ext = [(0, 0), (100, 0), (100, 60), (40, 60), (40, 100), (0, 100), (0, 0)]
    island1 = [(15, 70), (30, 70), (30, 85), (15, 85), (15, 70)]
    pocket = sg.Polygon(l_shape_ext, [island1])

    # --- Parameters ---
    TOOL_RADIUS = 5.0
    STEP_SIZE = 1.0   # World units, smaller step = smoother path
    SIM_RESOLUTION = 512*10 # Pixels for the longer dimension
    MAX_ANGLE_DEV_DEG = 65 # Max angle change per step for spiral
    # How much engagement do we want? Related to stepover.
    # Let's target engagement proportional to the tool radius in pixels
    TARGET_ENGAGEMENT_FACTOR = 0.4 # Target engagement = 0.4 * tool_radius_px
    ENGAGEMENT_TOLERANCE_FACTOR = 0.5 # Tolerance = 0.5 * target_engagement_px
    MAX_STEPS = 3000

    # --- Generate ---
    generator = ToolpathGenerator(pocket, TOOL_RADIUS, STEP_SIZE,
                                  TARGET_ENGAGEMENT_FACTOR, ENGAGEMENT_TOLERANCE_FACTOR,
                                  MAX_ANGLE_DEV_DEG, SIM_RESOLUTION)
    
    # Generate the path
    final_toolpath = generator.generate(max_steps=MAX_STEPS)

    # Visualize final state and path
    generator.sim_env.visualize(final_toolpath, title="Final State and Generated Toolpath")

    # Plot Engagement Profile
    plt.figure(figsize=(10, 4))
    plt.plot(generator.raw_engagements, label="Engagement (pixels)")
    plt.xlabel("Step")
    plt.ylabel("Engagement (pixels)")
    plt.title("Tool Engagement Profile (Raw Path)")
    plt.axhline(generator.target_engagement_px, color='r', linestyle='--', label='Target')
    plt.axhline(generator.target_engagement_px + generator.engagement_tolerance_px, color='g', linestyle=':', label='Tolerance')
    plt.axhline(generator.target_engagement_px - generator.engagement_tolerance_px, color='g', linestyle=':')
    plt.legend()
    plt.grid(True)
    plt.show()