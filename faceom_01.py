import numpy as np
import math
import matplotlib.pyplot as plt

# --- Helper Functions ---

def normalize(v):
    """Normalizes a vector."""
    norm = np.linalg.norm(v)
    if norm == 0:
       return v
    return v / norm

def rotate_vector(v, angle_rad):
    """Rotates a 2D vector by a given angle in radians."""
    rotation_matrix = np.array([
        [math.cos(angle_rad), -math.sin(angle_rad)],
        [math.sin(angle_rad), math.cos(angle_rad)]
    ])
    return rotation_matrix @ v

def get_contour_point(t, contour_func):
    """Evaluates the contour function at parameter t."""
    return np.array(contour_func(t))

def get_contour_tangent_numerical(t, contour_func, dt_small=1e-6):
    """Numerically estimates the tangent vector of the contour at t."""
    p1 = get_contour_point(t - dt_small / 2, contour_func)
    p2 = get_contour_point(t + dt_small / 2, contour_func)
    tangent = p2 - p1
    return normalize(tangent)

def line_circle_intersection(line_start, line_dir_vec, circle_center, circle_radius):
    """
    Finds intersection points of a ray (line_start, line_dir_vec) and a circle.
    Returns the intersection point closest to line_start along the ray direction,
    or None if no intersection exists on the ray.

    Equation of line: P = L_start + k * L_dir (k >= 0 for ray)
    Equation of circle: ||P - C_center||^2 = r^2
    Substitute P: ||(L_start - C_center) + k * L_dir||^2 = r^2
    Let D = L_start - C_center, V = L_dir (assume normalized)
    (V.V)*k^2 + 2*(D.V)*k + (D.D - r^2) = 0
    1*k^2 + 2*(D.V)*k + (D.D - r^2) = 0 (since V.V = 1 for normalized V)
    """
    line_dir_vec = normalize(line_dir_vec)
    d = line_start - circle_center
    v = line_dir_vec

    # Quadratic equation coefficients: ak^2 + bk + c = 0
    a = 1.0 # Since v is normalized, v.dot(v) = 1
    b = 2 * d.dot(v)
    c = d.dot(d) - circle_radius**2

    discriminant = b**2 - 4*a*c

    if discriminant < 0:
        # No real intersection
        return None
    else:
        sqrt_discriminant = math.sqrt(discriminant)
        k1 = (-b + sqrt_discriminant) / (2*a)
        k2 = (-b - sqrt_discriminant) / (2*a)

        valid_k = []
        if k1 >= -1e-9: # Allow for small numerical errors around zero
             valid_k.append(k1)
        if k2 >= -1e-9:
             valid_k.append(k2)

        if not valid_k:
            # Intersections exist, but not on the forward ray
            return None
        else:
            # Choose the smallest non-negative k (closest intersection on the ray)
            chosen_k = min(valid_k)
            intersection_point = line_start + chosen_k * line_dir_vec
            return intersection_point

# --- FACEOM Algorithm ---

def faceom(contour_func, t_min, t_max, t0, beta_deg, theta_deg, r_tool, dt, max_steps=10000):
    """
    Implements the Fast Constant Engagement Offsetting Method (FACEOM).

    Args:
        contour_func: Function c(t) returning (x, y) for the workpiece contour.
        t_min: Minimum parameter value for the contour.
        t_max: Maximum parameter value for the contour.
        t0: Starting parameter value on the contour.
        beta_deg: Initial angle (degrees) between tool path tangent and contour tangent.
        theta_deg: Desired constant cutter engagement angle (degrees).
        r_tool: Tool radius.
        dt: Step size for the parameter t.
        max_steps: Maximum number of iterations to prevent infinite loops.

    Returns:
        List of tool path points (np.array).
    """
    if r_tool <= 0:
        raise ValueError("Tool radius must be positive.")
    if dt <= 0:
         raise ValueError("Step size dt must be positive.")

    # Convert angles to radians
    theta_rad = math.radians(theta_deg)
    beta_rad = math.radians(beta_deg)
    alpha_rad = math.radians(90.0) - theta_rad # Angle for rotating PC vector

    # --- Initialization (Section 2.3 & Fig. 5) ---
    i = 0
    ti = t0
    tool_path_points = []
    contour_points_used = [] # For debugging/visualization

    # Calculate initial contour point C0
    c0 = get_contour_point(ti, contour_func)

    # Calculate initial contour tangent c'(t0)
    c_prime_0 = get_contour_tangent_numerical(ti, contour_func)
    if np.linalg.norm(c_prime_0) < 1e-9:
         raise ValueError(f"Contour tangent is near zero at t={ti}. Cannot initialize.")


    # Calculate initial rotation angle omega (Fig 5)
    omega_rad = beta_rad + theta_rad + math.radians(90.0)

    # Rotate tangent vector C'(t0) by omega
    # The direction vector *from* C0 *towards* P0 is opposite to this rotated tangent
    direction_c0_to_p0 = -rotate_vector(c_prime_0, omega_rad)

    # Find initial tool center P0
    p0 = c0 + r_tool * normalize(direction_c0_to_p0)
    tool_path_points.append(p0)
    contour_points_used.append(c0)


    # Calculate initial tool path tangent v0 (Fig 2 / Fig 4 logic)
    # Vector from P0 to C0
    vec_p0_c0 = c0 - p0
    if np.linalg.norm(vec_p0_c0) < 1e-9:
         raise ValueError("Initial P0 and C0 are coincident. Cannot calculate initial v0.")

    # Rotate P0C0 by alpha = 90 - theta around P0 to get direction of v0
    v0 = rotate_vector(vec_p0_c0, alpha_rad)
    vi = normalize(v0) # Current tool path direction vector

    # --- Main Loop (Section 2.4 & Fig. 4) ---
    while ti < t_max and i < max_steps:
        pi = tool_path_points[-1] # Current tool center position

        # Step 1: Calculate next contour point C(i+1)
        t_next = ti + dt
        # Ensure we don't overshoot t_max significantly due to dt
        if t_next > t_max:
             t_next = t_max
             # Optional: adjust dt for the last step if needed,
             # but simply capping t_next is usually sufficient

        c_next = get_contour_point(t_next, contour_func)

        # Step 2: Find intersection P(i+1) between ray (Pi, vi) and circle(C(i+1), r_tool)
        p_next = line_circle_intersection(pi, vi, c_next, r_tool)

        if p_next is None:
            print(f"Warning: No intersection found at step {i+1} (t={t_next:.4f}). Stopping.")
            print(f"  Pi={pi}, vi={vi}, C_next={c_next}, r_tool={r_tool}")
            # Check distance:
            dist_pi_cnext = np.linalg.norm(c_next - pi)
            print(f"  Distance Pi to C_next = {dist_pi_cnext}")
            if dist_pi_cnext < r_tool - 1e-6:
                 print("  Reason: Pi is inside the next tool radius circle.")
            elif dist_pi_cnext > r_tool + np.linalg.norm(pi - tool_path_points[-2]) + 1e-6 if len(tool_path_points)>1 else r_tool + 1e-6:
                 print("  Reason: C_next is likely too far for the step size dt.")
            break # Stop generation if intersection fails


        # Step 3: Calculate next tool path tangent v(i+1)
        vec_p_next_c_next = c_next - p_next
        if np.linalg.norm(vec_p_next_c_next) < 1e-9:
            print(f"Warning: P(i+1) and C(i+1) are coincident at step {i+1}. Using previous direction.")
            v_next = vi # Maintain direction as a fallback
        else:
            # Rotate P(i+1)C(i+1) by alpha around P(i+1)
            v_next_unnormalized = rotate_vector(vec_p_next_c_next, alpha_rad)
            v_next = normalize(v_next_unnormalized)

        # Step 4: Update state
        tool_path_points.append(p_next)
        contour_points_used.append(c_next) # Store for plotting
        vi = v_next
        ti = t_next
        i += 1

        # Break if we exactly hit t_max in the previous iteration
        if abs(ti - t_max) < 1e-9 and t_next == t_max:
             break


    if i == max_steps:
        print(f"Warning: Reached maximum steps ({max_steps}).")

    return np.array(tool_path_points), np.array(contour_points_used)


# --- Example Usage ---

# Example 1: Sine wave contour (similar to Fig 8)
def sine_wave_contour(t):
    # Adjust amplitude and frequency as needed
    amplitude = 2.0
    frequency = 0.5
    return (t, amplitude * math.sin(frequency * t))

t_min_sine = 0.0
t_max_sine = 30.0
t0_sine = t_min_sine
r_tool_sine = 2.0 # Dtool = 10 mm -> r_tool = 5 mm
theta_deg_sine = 60.0 # Engagement angle
beta_deg_sine = 90.0 # Start perpendicular to contour tangent (common choice)
dt_sine = 0.1 # Step size along contour parameter

print("Generating tool path for Sine Wave...")
tool_path_sine, contour_pts_sine = faceom(
    sine_wave_contour,
    t_min_sine, t_max_sine, t0_sine,
    beta_deg_sine, theta_deg_sine,
    r_tool_sine, dt_sine
)
print(f"Generated {len(tool_path_sine)} points.")

# Example 2: Circular contour
center_circ = (20, 20)
radius_circ = 15.0
def circular_contour(t): # t is angle in radians [0, 2*pi]
    return (center_circ[0] + radius_circ * math.cos(t),
            center_circ[1] + radius_circ * math.sin(t))

t_min_circ = 0.0
t_max_circ = 2 * math.pi
t0_circ = t_min_circ
r_tool_circ = 4.0
theta_deg_circ = 45.0
beta_deg_circ = 90.0
dt_circ = 0.05 # Smaller dt for smoother circle sampling

print("\nGenerating tool path for Circle...")
tool_path_circ, contour_pts_circ = faceom(
    circular_contour,
    t_min_circ, t_max_circ, t0_circ,
    beta_deg_circ, theta_deg_circ,
    r_tool_circ, dt_circ
)
print(f"Generated {len(tool_path_circ)} points.")


# --- Visualization ---
plt.style.use('seaborn-v0_8-whitegrid')

# Plot Sine Wave Example
plt.figure(figsize=(10, 6))
# Plot original contour
t_vals_plot = np.linspace(t_min_sine, t_max_sine, 500)
contour_plot = np.array([sine_wave_contour(t) for t in t_vals_plot])
plt.plot(contour_plot[:, 0], contour_plot[:, 1], 'b-', label='Original Contour c(t)')

# Plot generated tool path
if len(tool_path_sine) > 0:
    plt.plot(tool_path_sine[:, 0], tool_path_sine[:, 1], 'r.-', label='FACEOM Tool Path P(t)')
    # Plot tool circles at intervals
    interval = max(1, len(tool_path_sine) // 15) # Show ~15 circles
    for i in range(0, len(tool_path_sine), interval):
        circle = plt.Circle(tool_path_sine[i], r_tool_sine, color='grey', fill=False, alpha=0.3)
        plt.gca().add_patch(circle)
        # Plot connection line P_i C_i (for verification)
        if i < len(contour_pts_sine):
             plt.plot([tool_path_sine[i,0], contour_pts_sine[i,0]],
                      [tool_path_sine[i,1], contour_pts_sine[i,1]], 'g--', alpha=0.4)


plt.title(f'FACEOM Example: Sine Wave (theta={theta_deg_sine} deg)')
plt.xlabel('X [mm]')
plt.ylabel('Y [mm]')
plt.legend()
plt.grid(True)
plt.axis('equal')
plt.show()


# Plot Circular Example
plt.figure(figsize=(8, 8))
# Plot original contour
t_vals_plot_circ = np.linspace(t_min_circ, t_max_circ, 200)
contour_plot_circ = np.array([circular_contour(t) for t in t_vals_plot_circ])
plt.plot(contour_plot_circ[:, 0], contour_plot_circ[:, 1], 'b-', label='Original Contour c(t)')

# Plot generated tool path
if len(tool_path_circ) > 0:
    plt.plot(tool_path_circ[:, 0], tool_path_circ[:, 1], 'r.-', label='FACEOM Tool Path P(t)')
    # Plot tool circles at intervals
    interval = max(1, len(tool_path_circ) // 20) # Show ~20 circles
    for i in range(0, len(tool_path_circ), interval):
        circle = plt.Circle(tool_path_circ[i], r_tool_circ, color='grey', fill=False, alpha=0.3)
        plt.gca().add_patch(circle)
        # Plot connection line P_i C_i (for verification)
        if i < len(contour_pts_circ):
             plt.plot([tool_path_circ[i,0], contour_pts_circ[i,0]],
                      [tool_path_circ[i,1], contour_pts_circ[i,1]], 'g--', alpha=0.4)


plt.title(f'FACEOM Example: Circle (theta={theta_deg_circ} deg)')
plt.xlabel('X [mm]')
plt.ylabel('Y [mm]')
plt.legend()
plt.grid(True)
plt.axis('equal')
plt.show()