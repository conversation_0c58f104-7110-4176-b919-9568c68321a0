from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
import math

def create_distance_matrix(points):
    """Creates a distance matrix with a dummy node for open paths."""
    num_points = len(points)
    distance_matrix = []
    for i in range(num_points):
        row = []
        for j in range(num_points):
            dx = points[i][0] - points[j][0]
            dy = points[i][1] - points[j][1]
            row.append(int(math.hypot(dx, dy) * 1000))  # Integer scaling
        row.append(0)  # Cost to dummy node
        distance_matrix.append(row)
    # Dummy node row (prevents intermediate dummy usage)
    dummy_row = [**********] * (num_points + 1)
    distance_matrix.append(dummy_row)
    return distance_matrix

def tsp_with_constraints(points, start_point, constraints):
    """Solves TSP with open path, start point, and next constraints."""
    distance_matrix = create_distance_matrix(points)
    dummy_index = len(points)  # Index of dummy node

    # Initialize Routing Index Manager
    manager = pywrapcp.RoutingIndexManager(
        len(distance_matrix),  # Total nodes (original + dummy)
        1,  # Single vehicle
        [start_point],  # Start node
        [dummy_index]  # End at dummy node (open path)
    )

    routing = pywrapcp.RoutingModel(manager)

    # Distance callback
    def distance_callback(from_index, to_index):
        from_node = manager.IndexToNode(from_index)
        to_node = manager.IndexToNode(to_index)
        return distance_matrix[from_node][to_node]

    transit_callback_index = routing.RegisterTransitCallback(distance_callback)
    routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)

    # Add constraints (e.g., 4 -> 1, 12 ->5)
    solver = routing.solver()
    for from_node, to_node in constraints:
        index_from = manager.NodeToIndex(from_node)
        index_to = manager.NodeToIndex(to_node)
        solver.Add(routing.NextVar(index_from) == index_to)

    # Configure search parameters (OR-Tools 9.11+ compatible)
    search_parameters = pywrapcp.DefaultRoutingSearchParameters()
    search_parameters.first_solution_strategy = (
        routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC
    )
    search_parameters.local_search_metaheuristic = (
        routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
    )
    search_parameters.time_limit.seconds = 1  # Correct for v9.11

    # Solve
    assignment = routing.SolveWithParameters(search_parameters)

    # Extract route
    if assignment:
        route = []
        index = routing.Start(0)
        while not routing.IsEnd(index):
            node = manager.IndexToNode(index)
            route.append(node)
            index = assignment.Value(routing.NextVar(index))
        return route
    return None

# Example usage
if __name__ == "__main__":
    points = [(i, i) for i in range(20)]  # Test data
    start_point = 0
    constraints = [(4, 1), (12, 5)]

    route = tsp_with_constraints(points, start_point, constraints)
    print("Optimal path:", route if route else "No solution found")