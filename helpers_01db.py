import bpy
from mathutils import Vector
import numpy as np
import trimesh
import shapely.geometry
from pyclothoids import Clothoid, SolveG2
import pyclothoids
import time
from shapely.geometry import <PERSON>y<PERSON>, Point, LineString
from shapely.strtree import STRtree
import math
import itertools
from shapely import affinity, ops, line_merge, line_locate_point, line_interpolate_point
from shapely import Point, LineString, Polygon, MultiLineString, GeometryCollection, MultiPoint
import time
import scipy.optimize

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []
    
    geometry_list = []
    for obj in selected_objects:
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj




def expand_by_x(indices, x, total_elements):
  """
  Expands a sequence of circular indices by x elements at the beginning
  and x elements at the end.

  Args:
    indices: A NumPy array or list of integers representing the indices.
    x: The number of elements to expand by on each side (must be non-negative).
    total_elements: The total number of elements in the circle (default is 72).

  Returns:
    A NumPy array with the expanded sequence of indices.
  """
  if not isinstance(indices, np.ndarray):
    indices = np.array(indices)

  if indices.size == 0 or x < 0:
    return indices
      
  if x == 0:
    return indices

  # Calculate preceding and succeeding indices
  first = indices[0]
  last = indices[-1]
  
  # Create ranges and handle wrapping with modulo
  preceding = (first - np.arange(x, 0, -1)) % total_elements
  succeeding = (last + np.arange(1, x + 1)) % total_elements
  
  # Combine all indices
  return np.concatenate((preceding, indices, succeeding))


def max_front_engagement(sample_distances, radii, tool_radius, num_theta=36):
    """
    Compute maximum engagement angles for all pairs of consecutive circles using vectorized operations.
    
    Parameters:
    - sample_distances: Array of cumulative distances (shape: (N+1,))
    - radii: Array of circle radii (shape: (N+1,))
    - tool_radius: Radius of the tool (scalar)
    - num_theta: Number of angles to sample for each circle (default: 100)
    
    Returns:
    - Array of maximum engagement angles in radians (shape: (N,))
    """
    # Number of segments
    N = len(radii) - 1
    
    # Generate angle array for sampling
    theta = np.linspace(0, 2 * np.pi, num_theta, endpoint=False)
    
    # Compute segment lengths and radii for consecutive pairs
    # l = sample_distances[1:] - sample_distances[:-1]  # shape: (N,)
    l = sample_distances[1:]
    r1 = np.full_like(l, radii[0])  # shape: (N,)
    r2 = radii[1:]   # shape: (N,)
    
    # Offset radii by tool radius
    R1 = r1 + tool_radius  # shape: (N,)
    R2 = r2 + tool_radius  # shape: (N,)
    
    # Tool center coordinates (C) for all pairs and angles
    C_x = l[:, None] + r2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    C_y = r2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    C = np.stack((C_x, C_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # Tangent points (N) on offset second circle
    N_x = l[:, None] + R2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    N_y = R2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    N = np.stack((N_x, N_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # First circle center (O1) at origin for all pairs
    num_segments = len(r1)  # Use integer value instead of N array
    O1 = np.zeros((num_segments, 1, 2))  # shape: (num_segments, 1, 2)
    
    # Distance from O1 to C
    d = np.sqrt(np.sum((C - O1)**2, axis=-1))  # shape: (N, num_theta)
    
    # Intersection condition
    mask = (d >= np.abs(R1[:, None] - tool_radius)) & (d <= (R1[:, None] + tool_radius))
    
    # Compute gamma (angle for intersection points)
    gamma = np.arccos(np.clip((d**2 + R1[:, None]**2 - tool_radius**2) / (2 * d * R1[:, None]), -1.0, 1.0))
    gamma = np.nan_to_num(gamma, nan=0.0)  # shape: (N, num_theta)
    
    # Unit vector from O1 to C
    u = (C - O1) / d[..., None]  # shape: (N, num_theta, 2)
    u = np.nan_to_num(u, nan=0.0)
    
    # Perpendicular vector (rotated 90 degrees)
    v = np.stack((-u[..., 1], u[..., 0]), axis=-1)  # shape: (N, num_theta, 2)
    
    # Intersection points D1 and D2
    cos_gamma = np.cos(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    sin_gamma = np.sin(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    D1 = O1 + R1[:, None, None] * (u * cos_gamma + v * sin_gamma)  # shape: (N, num_theta, 2)
    D2 = O1 + R1[:, None, None] * (u * cos_gamma - v * sin_gamma)  # shape: (N, num_theta, 2)
    
    # Distances from N to D1 and D2
    DN1 = np.sqrt(np.sum((D1 - N)**2, axis=-1))  # shape: (N, num_theta)
    DN2 = np.sqrt(np.sum((D2 - N)**2, axis=-1))  # shape: (N, num_theta)
    
    # Engagement angles
    alpha1 = np.arccos(np.clip(1 - (DN1**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    alpha2 = np.arccos(np.clip(1 - (DN2**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    
    # Apply mask and compute maximum
    alpha1 = np.where(mask, alpha1, 0.0)
    alpha2 = np.where(mask, alpha2, 0.0)
    max_alpha = np.max(np.maximum(alpha1, alpha2), axis=1)  # shape: (N,)
        
    return max_alpha


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def angle_engagement(next_center: np.ndarray, prev_center: np.ndarray, r: float, stock_poly: Polygon, arc_resolution=12) -> float | None:
    if not isinstance(stock_poly, (Polygon, shapely.geometry.MultiPolygon)) or not stock_poly.is_valid: return None
    if np.array_equal(next_center, prev_center): return 0.0
    vec = [next_center[0] - prev_center[0], next_center[1] - prev_center[1]]
    vec_mag = math.sqrt(vec[0]**2 + vec[1]**2)
    if vec_mag < 1e-9: return 0.0
    movement_angle = np.arctan2(vec[1], vec[0])
    start_angle = movement_angle + np.pi / 2
    end_angle = movement_angle - np.pi / 2
    arc_points = [(next_center[0] + r * np.cos(t), next_center[1] + r * np.sin(t))
                  for t in np.linspace(end_angle, start_angle, arc_resolution)]
    if len(arc_points) < 2: return None
    arc = shapely.LineString(arc_points)
    if not arc.is_valid: return None
    try:
        intersection_geom = shapely.intersection(arc, stock_poly)
        if not intersection_geom or intersection_geom.is_empty: return None
        total_length = 0.0
        if intersection_geom.geom_type == "LineString": total_length = intersection_geom.length
        elif intersection_geom.geom_type == "MultiLineString": total_length = sum(line.length for line in intersection_geom.geoms)
        elif intersection_geom.geom_type == "GeometryCollection":
             for geom in intersection_geom.geoms:
                  if geom.geom_type == "LineString": total_length += geom.length
                  elif geom.geom_type == "MultiLineString": total_length += sum(line.length for line in geom.geoms)
        engagement_angle = total_length / r
        return max(0.0, min(engagement_angle, np.pi))
    except Exception: return None


def advancing_front_ccwe(path, polygon, R, CWE, samples_per_milimeter = 10.0, min_radius=5):    
    """Generate advancing front points along path with Cconstant cutter work-piece engagement."""
    path = np.asarray(path)
    if path.shape[0] <= 1:
        return np.array([]), np.array([])

    # Sample path
    sampler = trimesh.path.traversal.PathSample(path)    
    sample_step = 1.0 / samples_per_milimeter
    
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)
    points = sampler.sample(sample_distances)
    radii_full = boundary_distance(polygon, points)
    radii = radii_full

    # Find first index where radius drops below min_radius
    min_radius_idx = np.argmax(radii < min_radius) if np.any(radii < min_radius) else None    
    if min_radius_idx is not None and min_radius_idx > 0:
        # We found a point where radius drops below min_radius        
        sample_distances = sample_distances[:min_radius_idx]
        radii = radii[:min_radius_idx]
        points = points[:min_radius_idx]
    
    closest_indices = []
    cycle_offset = 0
    distance_offset = 0
       
    while True:    
        slice_start = cycle_offset
            
        # Calculate engagement for current slice
        slice_distances = sample_distances[slice_start:] - distance_offset
        # print(f'slice_distances: {slice_distances}, slice_start: {slice_start}, distance_offset: {distance_offset}')
        slice_radii = radii[slice_start:]
        mfe = max_front_engagement(sample_distances=slice_distances, radii=slice_radii, tool_radius=R)       
        
        # Find indices where values are greater than CWE
        indices = np.where(mfe > CWE)[0]

        # Get the first index if any exist
        if len(indices) > 0:
            local_idx = indices[0]-1
        else:
            local_idx = np.argmin(np.abs(mfe - CWE))
        
        global_idx = slice_start + local_idx
        
        # Update tracking variables
        cycle_offset += local_idx
        distance_offset += slice_distances[local_idx]

        if closest_indices and closest_indices[-1] == global_idx:
            break

        closest_indices.append(global_idx)
        if np.linalg.norm(points[-1] - points[global_idx]) < (radii[-1] + radii[global_idx]+(R*2)):    
            break
        
    # Create buffer circles for remaining points
    last_idx = closest_indices[-1]

    # Store results
    result_points = points[closest_indices]
    result_radii = radii_full[closest_indices]
    remaining_points = points[last_idx:]
    remaining_radii = radii[last_idx:]

    return result_points, result_radii, remaining_points, remaining_radii


def clearing_front_ccwe(remaining_centers, remaining_radii, main_centers, main_radii, R, CWE, samples_per_milimeter = 10.0, min_radius=5):    
    """Generate advancing front points along path with Cconstant cutter work-piece engagement."""
    
    # Create union of all circles along remaining path    
    circles = [Point(p[0], p[1]).buffer(r+R) for p, r in zip(remaining_centers, remaining_radii)]
    circles_all = shapely.ops.unary_union(circles).simplify(0.05)
    
    # Create special buffer circles for the start and from the end points.
    buffer_factor = 1.05
    circle_start = Point(*main_centers[-1]).buffer(main_radii[-1] + (R * buffer_factor))
    circle_end = Point(*remaining_centers[-1]).buffer(remaining_radii[-1] + (R * buffer_factor))
    circle_ends = shapely.union(circle_start, circle_end)
    
    # Get the difference between all circles and the end circles
    stack_rest = shapely.difference(circles_all, circle_ends)    
    
    circle_resolution = 72
    all_arc_data = {}
    clearing_centers = []
    clearing_radii = []

    last_biggest_engagement = 0.0    
    last_biggest_arc_center = np.ndarray
    last_biggest_arc_radius = 0.0    
    arc_number = 0
    speedup_offset = 20

    for idx, (c, r) in enumerate(zip(remaining_centers[::speedup_offset], remaining_radii[::speedup_offset])):
        # Handle the case when the next index would be out of bounds
        next_idx = min((idx+1)*speedup_offset, len(remaining_centers)-1)
        arc_vector = [c[0] - remaining_centers[next_idx][0], c[1] - remaining_centers[next_idx][1]]
        # if geom.geom_type == 'MultiPolygon':
        #     geom = max(geom.geoms, key=lambda p: p.area)
        stack_loop = stack_rest            
        angles = []            
        arc_points = find_valid_arc_points(c, r, stack_loop, arc_vector, R, circle_resolution, expand_by=1)
        
        if arc_points.size > 0:
            for i in range(len(arc_points)-1):
                prev_point = arc_points[i]
                next_point = arc_points[i+1]                    
                cutter = Point(*next_point).buffer(R)
                engagement = angle_engagement(next_point, prev_point, R, stack_loop, arc_resolution=24)
                if engagement:
                    angles.append(engagement)
                    # --- Update Stock ---
                    try:  
                        if stack_loop.geom_type == 'MultiPolygon':
                            new_parts = []
                            for part in stack_loop.geoms:
                                if part.intersects(cutter):
                                    diff = shapely.difference(part, cutter)
                                    if not diff.is_empty:
                                            new_parts.append(diff)
                                else:
                                    new_parts.append(part)
                            new_stock = shapely.ops.unary_union(new_parts) # Reassemble
                        else: # Single Polygon
                            new_stock = shapely.difference(stack_loop, cutter)

                        # Validate and potentially fix new stock
                        if not new_stock.is_valid:
                            new_stock = new_stock.buffer(0)
                            if not new_stock.is_valid:
                                print(f"Error: Stock remains invalid after buffer(0) at step {i+1}. Stopping.")
                                break
                        stack_loop = new_stock
                        if stack_loop.is_empty:
                            print(f"Stopping toolpath generation at step {i+1}: Stock depleted.")
                            break
                    except Exception as e:
                        print(f"Error during stock update at step {i+1}: {e}")
                        break

            if len(angles) > 0:
                loop_max_engagement = np.max(angles)

                if loop_max_engagement >= CWE:                    
                    stack_rest = shapely.difference(stack_rest, shapely.LineString(arc_points).buffer(R))
                    last_biggest_engagement = 0.0
                    # Store arc data
                    clearing_centers.append(c)
                    clearing_radii.append(r)

                elif loop_max_engagement > last_biggest_engagement:
                        last_biggest_engagement = loop_max_engagement                        
                        last_biggest_arc_center = c
                        last_biggest_arc_radius = r                        
                    
        # Check if we are in the last iteration
        if idx == len(remaining_centers[::20]) - 1:
            if len(angles) > 0 and loop_max_engagement >= CWE:
                continue
            else:                
                # Store arc data                        
                clearing_centers.append(last_biggest_arc_center)
                clearing_radii.append(last_biggest_arc_radius)
    
    return clearing_centers, clearing_radii


def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, edge_length):
    """Generate points along an arc in counter-clockwise direction."""
    center = np.array(center)
    radius = max(1e-9, radius)
        
    arc_length = radius * (angle_start_rad -angle_end_rad)
    num_points = max(2, int(np.ceil(arc_length / edge_length)) + 1)
    angles = np.linspace(angle_end_rad, angle_start_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculate arc angles based on current and next centers/radii."""
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)
    angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if segment_len > 1e-9 else 0.0

    start_angle = angle_dir + np.pi / 2.0
    end_angle = angle_dir - np.pi / 2.0

    if R_next > R_current and segment_len > 1e-9:
        delta_angle = np.arcsin(np.clip((R_next - R_current) / segment_len, 0.0, 1.0))
        start_angle += delta_angle
        end_angle -= delta_angle

    return start_angle, end_angle


def find_nearby_edges(polygon: Polygon, point: Point, max_distance: float) -> list[LineString]:
    # Extract coordinates and create edges
    coords = list(polygon.exterior.coords)
    edges = [LineString([coords[i], coords[i + 1]]) 
            for i in range(len(coords) - 1)]
    
    # Create spatial index
    tree = STRtree(edges)
    
    # Create a search buffer around the point
    search_area = point.buffer(max_distance)
    
    # Query the spatial index for potential edges
    potential_edges = tree.query(search_area)
    
    # Final precise distance check
    nearby_edges = [
        edges[idx] for idx in potential_edges 
        if edges[idx].distance(point) <= max_distance
    ]
    
    return nearby_edges


def create_circle_points(center, radius, vector, resolution=72):
    """Generate points along a circle with given center and radius."""
    # Normalize the vector
    vector = np.array(vector)
    if np.linalg.norm(vector) > 1e-9:
        vector = vector / np.linalg.norm(vector)
        # Calculate start angle based on vector direction
        start_angle = np.arctan2(vector[1], vector[0])
    else:
        start_angle = 0.0
        
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)
    
    return np.column_stack([
        center[0] + radius * np.cos(theta),
        center[1] + radius * np.sin(theta)
    ])


def find_valid_arc_points(center, radius, stock_poly, vector, tool_radius, circle_resolution=72, expand_by=1):
    """Find valid points on a circle that satisfy distance and containment criteria.
    
    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius
        stock_poly: Stock polygon to check against
        tool_radius: Tool radius for distance check
        circle_resolution: Number of points to sample on the circle
        
    Returns:
        Array of valid points on the circle
        start_angle: Starting angle of the valid arc in radians
        end_angle: Ending angle of the valid arc in radians
    """
    # Create circle points
    circle_points = create_circle_points(center, radius, vector, circle_resolution)
    
    # Check distance and containment
    p_dist = boundary_distance(stock_poly, circle_points)
                    
    # Find points that satisfy both criteria
    if np.any(p_dist < tool_radius) and p_dist.size > 1:                
        # Create a continuous range of indices from the first to the last index where p_dist < tool_radius
        # This ensures we get a complete arc segment rather than potentially disconnected points
        valid_indices = np.arange(np.min(np.where(p_dist < tool_radius)[0]), np.max(np.where(p_dist < tool_radius)[0]) + 1)
        valid_indices = expand_by_x(valid_indices, expand_by, circle_resolution)
        
        return circle_points[valid_indices]
    else:
        return np.array([])


def process_arcs_and_transitions(main_centers, main_radii, edge_length):
    """Process arcs and transitions to generate path segments and arc data.
    
    Args:
        main_centers: List of center points for arcs
        main_radii: List of radii for arcs
        edge_length: Length of elementary edge in the geometry
        
    Returns:
        Tuple containing arc points and arc data dictionary
    """
    all_arc_data = {}
    
    # Process arcs and transitions
    for i in range(len(main_centers)):
        C_i, R_i = main_centers[i], max(1e-6, main_radii[i])
        
        if i < len(main_centers) - 1:
            C_next, R_next = main_centers[i+1], max(1e-6, main_radii[i+1])            
            start_angle, end_angle = calculate_arc_angles(C_i, R_i, C_next, R_next)            
        else: # Last arc - use previous arc for direction
            C_prev, R_prev = main_centers[i-1], max(1e-6, main_radii[i-1])
            segment_vec = C_i - C_prev
            angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if np.linalg.norm(segment_vec) > 1e-9 else 0.0
            start_angle = angle_dir + np.pi/2
            end_angle = angle_dir - np.pi/2

            if R_i > R_prev and np.linalg.norm(segment_vec) > 1e-9:
                delta_angle = np.arcsin(np.clip((R_i - R_prev) / np.linalg.norm(segment_vec), 0.0, 1.0))
                start_angle += delta_angle
                end_angle -= delta_angle

        # Store arc data
        all_arc_data[i] = {
            "center": C_i,
            "radius": R_i,
            "start_angle": start_angle + np.pi * 2,
            "end_angle": end_angle + np.pi * 2
        }

        # Generate arc points
        arc_points = generate_arc_points(C_i, R_i, 
                                       all_arc_data[i]["start_angle"],
                                       all_arc_data[i]["end_angle"], 
                                       edge_length)
        
        all_arc_data[i]["points"] = arc_points
        
    return all_arc_data


def generate_final_path(arcs_data, edge_length):
    """Generate the final path by connecting arcs with clothoid transitions.
    
    Args:
        arcs_data: Dictionary containing arc data including centers, radii and points
        edge_length: Length of elementary edge in the geometry
        
    Returns:
        List of points forming the final path
    """
    final_path = []
    
    # Sort keys to ensure we process arcs in order
    arc_keys = sorted(arcs_data.keys())
    
    for i, key in enumerate(arc_keys):
        arc_data = arcs_data[key]
        arc_points = arc_data["points"]
        
        if i == 0:            
            final_path.extend(arc_points)
        else:
            # Generate clothoid transition
            prev_key = arc_keys[i-1]
            prev_arc = arcs_data[prev_key]
            
            x0 = float(prev_arc["center"][0] + prev_arc["radius"] * np.cos(prev_arc["start_angle"]))
            y0 = float(prev_arc["center"][1] + prev_arc["radius"] * np.sin(prev_arc["start_angle"]))            
            theta0 = float(prev_arc["start_angle"] + np.pi/2)
            
            x1 = float(arc_data["center"][0] + arc_data["radius"] * np.cos(arc_data["end_angle"]))
            y1 = float(arc_data["center"][1] + arc_data["radius"] * np.sin(arc_data["end_angle"]))
            theta1 = float(arc_data["end_angle"] + np.pi/2)
            
            clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)
            num_points = max(2, int(np.ceil(clothoid.length / edge_length)) + 1)
            clothoid_points = np.array(clothoid.SampleXY(num_points)).T
            
            final_path.extend(clothoid_points[1:])
            final_path.extend(arc_points[1:])
    
    return final_path

def spiral_toolpath_snail(center      : tuple[float, float],
                          R           : float,
                          init_r      : float,
                          finish_r    : float,
                          cwe_start   : float,
                          cwe_finish  : float,
                          dtheta      : float = 0.02,
                          ccw         : bool  = True) -> np.ndarray:
    """
    Generate a 2-D toolpath whose cutter engagement (CWE) varies
    linearly from `cwe_start` to `cwe_finish`.

    Parameters
    ----------
    center      (cx, cy) : spiral origin [mm]
    R                   : cutter radius  [mm]
    init_r              : first centre-line radius  ≥ R   [mm]
    finish_r            : last  centre-line radius  > init_r   [mm]
    cwe_start           : CWE of the first revolution  (0 < … ≤ 2R) [mm]
    cwe_finish          : CWE of the last  revolution  (0 < … ≤ 2R) [mm]
    dtheta              : angular increment (rad) – smaller ⇒ smoother path
    ccw                 : True ⇒ counter-clockwise, False ⇒ clockwise

    Returns
    -------
    path : (N, 2) float32 numpy array of XY tool-centre coordinates
    """

    # ------------- basic sanity checks -----------------------------------
    # if init_r < R:
    #     raise ValueError("init_r must not be smaller than the cutter radius R")
    if finish_r <= init_r:
        raise ValueError("finish_r must be greater than init_r")
    for cwe, label in ((cwe_start, "cwe_start"), (cwe_finish, "cwe_finish")):
        if cwe <= 0 or cwe > 2 * R:
            raise ValueError(f"{label} must lie in the open interval (0 , 2·R]")

    # ------------- total revolutions needed ------------------------------
    # If CWE grows (or shrinks) linearly with θ, the mean engagement equals
    #            (cwe_start + cwe_finish) / 2
    # Every full revolution removes that radial thickness.
    mean_cwe = 0.5 * (cwe_start + cwe_finish)
    revs     = (finish_r - init_r) / mean_cwe          # total turns
    theta_max = revs * 2.0 * np.pi                     # final angle (rad)

    # ------------- discretise the spiral ---------------------------------
    sign   = 1.0 if ccw else -1.0
    theta  = np.arange(0.0, theta_max + dtheta, dtheta)
    n      = theta.size
    r      = np.empty(n, dtype=np.float32)
    r[0]   = init_r

    # incremental integration: r_{i+1} = r_i + dr
    # with dr = (CWE(θ) / 2π) · dθ  and  CWE(θ) linear in θ
    cwe_delta = cwe_finish - cwe_start
    two_pi    = 2.0 * np.pi

    for i in range(1, n):
        frac      = theta[i-1] / theta_max                       # progress 0…1
        cwe_now   = cwe_start + cwe_delta * frac
        dr_dtheta = cwe_now / two_pi
        r[i]      = r[i-1] + dr_dtheta * dtheta

    # ------------- Cartesian coordinates ---------------------------------
    theta *= sign
    cx, cy = center
    x = cx + r * np.cos(theta)
    y = cy + r * np.sin(theta)

    return np.column_stack((x, y)).astype(np.float32)


def distances_from_linestring_a_to_linestring_b_vectorized(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b using vectorized operations.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        return np.full(num_points_a, np.nan)

    # If linestring B is a single point
    if num_points_b == 1:
        # Distances from all points in ls_a to the single point in ls_b
        # ls_a is (N, 2), ls_b[0] is (2,), ls_b[0][np.newaxis, :] is (1,2)
        # Broadcasting (N,2) - (1,2) -> (N,2)
        diff = ls_a - ls_b[0] # or ls_a - ls_b[0][np.newaxis, :]
        distances = np.linalg.norm(diff, axis=1)
        return distances

    # Prepare segments of linestring B
    # b_starts are points p1 of segments, b_ends are points p2
    b_starts = ls_b[:-1]  # Shape (M-1, 2)
    b_ends = ls_b[1:]    # Shape (M-1, 2)
    
    num_segments_b = b_starts.shape[0]
    if num_segments_b == 0: # Should be caught by num_points_b == 1, but good check
        # This case implies ls_b had only one point, handled above.
        # If somehow reached, it means ls_b was malformed or empty.
        # For safety, if ls_b had points but no segments (e.g., ls_b = [[0,0]]),
        # we should have handled it with num_points_b == 1.
        # If ls_b was truly empty, already handled.
        # This path is unlikely if prior checks are robust.
        return np.full(num_points_a, np.nan)


    # Expand dimensions for broadcasting:
    # ls_a: (N, 1, 2) - for each point in A, we consider all segments of B
    # b_starts, b_ends: (1, M, 2) - for each segment in B, we consider all points of A
    p = ls_a[:, np.newaxis, :]     # (N, 1, 2)
    s1 = b_starts[np.newaxis, :, :] # (1, M, 2)
    s2 = b_ends[np.newaxis, :, :]   # (1, M, 2)

    # Vectors for segments and from segment start to points in ls_a
    seg_vectors = s2 - s1      # (1, M, 2), segment vectors (s1_s2)
    ap_vectors = p - s1        # (N, M, 2), vectors from s1 to p

    # Length squared of segments. Add epsilon to avoid division by zero for zero-length segments.
    # seg_lens_sq will be (1, M)
    seg_lens_sq = np.sum(seg_vectors**2, axis=2) + 1e-9 # Add epsilon for stability

    # Project ap_vectors onto seg_vectors
    # dot_product will be (N, M)
    dot_product = np.sum(ap_vectors * seg_vectors, axis=2)
    
    # t is the projection parameter
    # t will be (N, M)
    t = dot_product / seg_lens_sq

    # Clip t to be between 0 and 1.
    # If t < 0, closest point is s1.
    # If t > 1, closest point is s2.
    # Else, closest point is s1 + t * seg_vectors.
    t_clipped = np.clip(t, 0, 1)

    # Calculate the closest points on the segments (or their extensions if not clipped)
    # to each point in ls_a.
    # closest_points_on_lines will be (N, M, 2)
    # t_clipped needs to be (N, M, 1) for broadcasting with seg_vectors (1, M, 2)
    # or seg_vectors needs to be (N,M,2) (already is after broadcasting rules apply)
    closest_points_on_segments = s1 + t_clipped[:, :, np.newaxis] * seg_vectors

    # Calculate distances from points in p to these closest points
    # distances_to_segments will be (N, M)
    distances_to_segments = np.linalg.norm(p - closest_points_on_segments, axis=2)
    
    # For each point in ls_a, find the minimum distance to any segment in ls_b
    # min_distances will be (N,)
    min_distances = np.min(distances_to_segments, axis=1)
    
    return min_distances


def angle_between_points(center_point, point_a, point_b):
    """
    Calculates the angle (in radians or degrees) between two points
    relative to a center point.

    The angle is measured counter-clockwise from the vector (center -> point_a)
    to the vector (center -> point_b).

    Args:
        center_point (shapely.geometry.Point): The central reference point.
        point_a (shapely.geometry.Point): The first point.
        point_b (shapely.geometry.Point): The second point.        

    Returns:
        float: The angle between the two vectors.
    """

    # Calculate vectors from the center to point_a and point_b
    vec_a_x = point_a.x - center_point.x
    vec_a_y = point_a.y - center_point.y

    vec_b_x = point_b.x - center_point.x
    vec_b_y = point_b.y - center_point.y

    # Use math.atan2 to get the angle of each vector relative to the positive x-axis
    # atan2(y, x)
    angle_a_rad = math.atan2(vec_a_y, vec_a_x)
    angle_b_rad = math.atan2(vec_b_y, vec_b_x)

    # Calculate the difference between the angles
    # This gives the angle from vec_a to vec_b (counter-clockwise)
    angle_diff_rad = angle_b_rad - angle_a_rad

    # Normalize the angle to be between -pi and pi
    # This helps ensure consistent results for angles spanning across the 0/360 boundary
    angle_diff_rad = (angle_diff_rad + math.pi) % (2 * math.pi) - math.pi

    return angle_diff_rad


def compute_affine_coeffs(scale_y, pivot):
    """
    Compute affine transformation coefficients for scaling a geometry
    by scale_y along the y-axis rotated by angle_deg degrees around the origin point.

    Parameters:
    - scale_y (float): Scaling factor along the rotated y-axis.
    - angle_deg (float): Rotation angle in degrees (positive for counterclockwise).
    - origin (tuple): (O_x, O_y) local origin point.

    Returns:
    - list: [a, b, d, e, xoff, yoff] coefficients for Shapely's affine_transform.
    """
    
    # Calculate angle between pivot vector and x-axis
    theta = np.arctan2(pivot['vector'][1], pivot['vector'][0])-np.pi/2
    
    # Rotation matrix for theta
    R_theta = np.array([
        [np.cos(theta), -np.sin(theta)],
        [np.sin(theta), np.cos(theta)]
    ])
    
    # Rotation matrix for -theta
    R_minus_theta = np.array([
        [np.cos(theta), np.sin(theta)],
        [-np.sin(theta), np.cos(theta)]
    ])
    
    # Scaling matrix: scale along y-axis
    S = np.array([
        [1, 0],
        [0, scale_y]
    ])
    
    # Linear transformation matrix M = R_theta * S * R_minus_theta
    M = R_theta @ S @ R_minus_theta
    
    # Extract coefficients from M
    a, b = M[0, 0], M[0, 1]
    d, e = M[1, 0], M[1, 1]
    
    # Origin point
    O = np.array(pivot['coords'])
    
    # Compute M * O
    M_O = M @ O
    
    # Translation vector: O - M * O
    trans = O - M_O
    
    # Extract offsets
    xoff, yoff = trans[0], trans[1]
    
    return [a, b, d, e, xoff, yoff]


def rotate_vector(vector, angle_radians):    
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """    
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def compare_distances(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b using vectorized operations.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        return np.full(num_points_a, np.nan)

    # If linestring B is a single point
    if num_points_b == 1:
        # Distances from all points in ls_a to the single point in ls_b
        # ls_a is (N, 2), ls_b[0] is (2,), ls_b[0][np.newaxis, :] is (1,2)
        # Broadcasting (N,2) - (1,2) -> (N,2)
        diff = ls_a - ls_b[0] # or ls_a - ls_b[0][np.newaxis, :]
        distances = np.linalg.norm(diff, axis=1)
        return distances

    # Prepare segments of linestring B
    # b_starts are points p1 of segments, b_ends are points p2
    b_starts = ls_b[:-1]  # Shape (M-1, 2)
    b_ends = ls_b[1:]    # Shape (M-1, 2)
    
    num_segments_b = b_starts.shape[0]
    if num_segments_b == 0: # Should be caught by num_points_b == 1, but good check
        # This case implies ls_b had only one point, handled above.
        # If somehow reached, it means ls_b was malformed or empty.
        # For safety, if ls_b had points but no segments (e.g., ls_b = [[0,0]]),
        # we should have handled it with num_points_b == 1.
        # If ls_b was truly empty, already handled.
        # This path is unlikely if prior checks are robust.
        return np.full(num_points_a, np.nan)


    # Expand dimensions for broadcasting:
    # ls_a: (N, 1, 2) - for each point in A, we consider all segments of B
    # b_starts, b_ends: (1, M, 2) - for each segment in B, we consider all points of A
    p = ls_a[:, np.newaxis, :]     # (N, 1, 2)
    s1 = b_starts[np.newaxis, :, :] # (1, M, 2)
    s2 = b_ends[np.newaxis, :, :]   # (1, M, 2)

    # Vectors for segments and from segment start to points in ls_a
    seg_vectors = s2 - s1      # (1, M, 2), segment vectors (s1_s2)
    ap_vectors = p - s1        # (N, M, 2), vectors from s1 to p

    # Length squared of segments. Add epsilon to avoid division by zero for zero-length segments.
    # seg_lens_sq will be (1, M)
    seg_lens_sq = np.sum(seg_vectors**2, axis=2) + 1e-9 # Add epsilon for stability

    # Project ap_vectors onto seg_vectors
    # dot_product will be (N, M)
    dot_product = np.sum(ap_vectors * seg_vectors, axis=2)
    
    # t is the projection parameter
    # t will be (N, M)
    t = dot_product / seg_lens_sq

    # Clip t to be between 0 and 1.
    # If t < 0, closest point is s1.
    # If t > 1, closest point is s2.
    # Else, closest point is s1 + t * seg_vectors.
    t_clipped = np.clip(t, 0, 1)

    # Calculate the closest points on the segments (or their extensions if not clipped)
    # to each point in ls_a.
    # closest_points_on_lines will be (N, M, 2)
    # t_clipped needs to be (N, M, 1) for broadcasting with seg_vectors (1, M, 2)
    # or seg_vectors needs to be (N,M,2) (already is after broadcasting rules apply)
    closest_points_on_segments = s1 + t_clipped[:, :, np.newaxis] * seg_vectors

    # Calculate distances from points in p to these closest points
    # distances_to_segments will be (N, M)
    distances_to_segments = np.linalg.norm(p - closest_points_on_segments, axis=2)
    
    # For each point in ls_a, find the minimum distance to any segment in ls_b
    # min_distances will be (N,)
    min_distances = np.min(distances_to_segments, axis=1)
    
    return min_distances


def max_distance_right_side(l1, l2):
    """
    Find maximum distance from points of l2 that are on the right side of l1.
    Returns: max_distance
    """
    l1_coords = np.array(l1.coords)
    l2_coords = np.array(l2.coords)
    
    n_l2 = len(l2_coords)    
    
    # Broadcast arrays for vectorized operations
    p_broadcast = l2_coords[:, np.newaxis, :]
    a_broadcast = l1_coords[:-1][np.newaxis, :, :]
    b_broadcast = l1_coords[1:][np.newaxis, :, :]
    
    # Calculate projections for all point-segment pairs
    ab = b_broadcast - a_broadcast
    ap = p_broadcast - a_broadcast
    
    # Dot products
    ab_dot_ab = np.sum(ab * ab, axis=2)
    ap_dot_ab = np.sum(ap * ab, axis=2)
    
    # Clamp t values
    t = np.clip(ap_dot_ab / ab_dot_ab, 0, 1)
    
    # Calculate closest points
    closest = a_broadcast + t[:, :, np.newaxis] * ab
    
    # Calculate distances
    distances = np.linalg.norm(p_broadcast - closest, axis=2)
    
    # Find closest segment for each point
    closest_seg_idx = np.argmin(distances, axis=1)
    min_distances = np.min(distances, axis=1)
    
    # Calculate cross products for closest segments
    on_right = np.zeros(n_l2, dtype=bool)
    for i in range(n_l2):
        seg_idx = closest_seg_idx[i]
        a = l1_coords[seg_idx]
        b = l1_coords[seg_idx + 1]
        p = l2_coords[i]
        
        cross_z = (b[0] - a[0]) * (p[1] - a[1]) - (b[1] - a[1]) * (p[0] - a[0])
        on_right[i] = cross_z < 0
    
    # Get distances for points on the right
    right_distances = min_distances[on_right]
    
    if len(right_distances) > 0:
        max_dist = np.max(right_distances)
    else:
        max_dist = None
    
    return max_dist


def main():
    cutter_dim = 20 # in mm
    cutter_radius = cutter_dim / 2

    # Get geometry and validate
    geometry = get_geometry()

    if len(geometry) == 1:
        polygon = shapely.geometry.Polygon(shell=geometry[0])        
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)        
        medial_axis = shapely.geometry.LineString(geometry[1])
        
    polygon_buffered = polygon.buffer(-cutter_radius)        
    geoms_coords = [np.array(polygon_buffered.exterior.coords)] + [np.array(interior.coords) for interior in polygon_buffered.interiors]
        
    # create_line_object(contour_coords, "polygon_buffered", color=(0.05, 1, 0.1, 1))
    # create_line_object(polygon_buffered.interiors[0].coords, "polygon", color=(0.05, 1, 0.1, 1))

    start_inner_radius = polygon_buffered.boundary.distance(shapely.geometry.Point(medial_axis.coords[0]))
    start_outer_radius = polygon.boundary.distance(shapely.geometry.Point(medial_axis.coords[0]))
    
    outer_ellipse = shapely.geometry.LineString(create_circle_points(medial_axis.coords[0], start_outer_radius, (0, 1), 72))
    inter = medial_axis.intersection(outer_ellipse)    

    ### Pivot ####
    middle_coords = inter.geoms[-1].coords[-1]
    starter_vector = np.array(middle_coords) - np.array(medial_axis.coords[0])

    fitted_ellipse_pivot = {
        "coords": middle_coords,
        "vector": starter_vector
    }

    # Normalize the vector
    fitted_ellipse_pivot["vector"] = np.array(fitted_ellipse_pivot["vector"]) / np.linalg.norm(fitted_ellipse_pivot["vector"])    
    ### Pivot ####

    inner_ellipse = shapely.geometry.Polygon(create_circle_points(medial_axis.coords[0], start_inner_radius+0.5, fitted_ellipse_pivot["vector"], 72))
    outer_ellipse = shapely.geometry.LineString(create_circle_points(medial_axis.coords[0], start_outer_radius, fitted_ellipse_pivot["vector"], 72))

    ### Front ####
    inter = outer_ellipse.intersection(polygon_buffered)
    outer_ellipse_front = shapely.line_merge(shapely.geometry.MultiLineString([inter.geoms[0], inter.geoms[-1]]))
    outer_ellipse_front_offset_back = outer_ellipse_front.parallel_offset(cutter_radius, 'left')
    outer_ellipse_front_offset_front = outer_ellipse_front.parallel_offset(-cutter_radius, 'left')
    
    ## Create polygon from the two linestrings
    combined_lines = list(outer_ellipse_front_offset_back.coords) + list(reversed(outer_ellipse_front_offset_front.coords))
    front_polygon = shapely.geometry.Polygon(combined_lines)    
    # create_line_object(front_polygon.exterior.coords, "front_polygon", color=(0, 1, 0, 1))    
    # create_line_object(outer_ellipse_front.coords, "outer_ellipse_front", color=(0, 1, 0, 1))
    
    ### Sides ####
    sides = []
    for coords in geoms_coords:
        line = shapely.LineString(coords)        
        if line.intersects(outer_ellipse):     
            inter = line.intersection(shapely.Polygon(outer_ellipse.coords))
            if inter.geom_type == "MultiLineString":
                inter = shapely.line_merge(inter)
                            
            inter = inter.difference(inner_ellipse)
            if inter.geom_type == "MultiLineString":        
                for line in inter.geoms:
                    if line.touches(outer_ellipse_front):
                        sides.append(line)        
            else:
                if inter.touches(outer_ellipse_front):
                    sides.append(inter)
        
    if sides:
        if len(sides) == 1:
            sides = shapely.LineString(sides)
        if len(sides) == 2:
            sides = shapely.MultiLineString(sides)
        else:
            print("Problem with sides")    
            return
    ''''
    if sides.geom_type == "MultiLineString":
        for line in sides.geoms:
            create_line_object(line.coords, "sides", color=(1, 0, 0, 1))
    elif sides.geom_type == "LineString":
        create_line_object(sides.coords, "sides", color=(1, 0, 0, 1))
    '''
        
    
    ### Ellipse ###    
    scale_factor = ((start_outer_radius*2)-cutter_dim) / (start_outer_radius*2)    
    fitted_ellipse = shapely.affinity.scale(outer_ellipse, xfact=scale_factor, yfact=scale_factor, origin=fitted_ellipse_pivot['coords'])

    def ellipse_find_center(move_param):
        # Calculate new position
        fitted_ellipse_move_to_coords = np.array(outer_ellipse_front.interpolate(fitted_ellipse_act_pos + move_param)[0].coords[0])
        fitted_ellipse_move_to_vector = fitted_ellipse_move_to_coords - fitted_ellipse_pivot['coords']
        angle_diff_rad = angle_between_points(shapely.Point(medial_axis.coords[0]), 
                                            shapely.Point(fitted_ellipse_pivot['coords']), 
                                            shapely.Point(fitted_ellipse_move_to_coords))
        
        # Transform ellipse
        moved_ellipse = shapely.affinity.translate(inital_fitted_ellipse, 
                                                xoff=fitted_ellipse_move_to_vector[0], 
                                                yoff=fitted_ellipse_move_to_vector[1])
        moved_ellipse = shapely.affinity.rotate(moved_ellipse, angle_diff_rad, 
                                            origin=shapely.Point(moved_ellipse.coords[0]), 
                                            use_radians=True)
        
        # Calculate distances to sides
        distances = [line.distance(moved_ellipse) for line in sides.geoms]
        mean_dist = sum(distances)/len(distances)
        
        # Objective: minimize difference between distances (for balanced positioning)
        return abs(distances[0] - mean_dist)
    

    fitted_ellipse_act_pos = shapely.line_locate_point(outer_ellipse_front, shapely.Point(fitted_ellipse_pivot['coords']))

    time1 = time.time()
    while True:        
        ## Vertical scale down
        ##     ^^
        ##     ||
        ##     ||
        ##     VV
        inital_fitted_ellipse = fitted_ellipse
        scale_step = 0.01
        scale_factor = 1 - scale_step
        maximum_right_dist = 0.3 # in mm
        mean_dist = float('inf')
        
        while True:
            coeffs = compute_affine_coeffs(scale_factor, fitted_ellipse_pivot)    
            fitted_ellipse = shapely.affinity.affine_transform(inital_fitted_ellipse, coeffs)
            inter = fitted_ellipse.intersection(front_polygon)
            inter = shapely.line_merge(shapely.geometry.MultiLineString([geom for geom in inter.geoms]))
            # dist = compare_distances(np.array(outer_ellipse_front.coords), np.array(inter.coords))
            max_distance_right = max_distance_right_side(outer_ellipse_front, inter)
            # print(f'max_distance_right: {max_distance_right}')
            # act_mean_dist = np.mean(dist)

            if max_distance_right is not None and max_distance_right > maximum_right_dist:
                break
            # if act_mean_dist > mean_dist:
            #     break
            # mean_dist = act_mean_dist
            scale_factor -= scale_step

        if scale_factor > 0.95:
            ## Scale up, at the end to best fit
            inital_fitted_ellipse = fitted_ellipse
            scale_step = 0.001
            scale_factor = 1 + scale_step
            while True:
                fitted_ellipse = shapely.affinity.scale(inital_fitted_ellipse, xfact=scale_factor, yfact=scale_factor, origin=fitted_ellipse_pivot['coords'])                
                if fitted_ellipse.intersects(sides):
                    break
                scale_factor += scale_step
            break


        ## Scale down
        inital_fitted_ellipse = fitted_ellipse
        scale_step = 0.01
        scale_factor = 1 - scale_step
        
        while True:
            fitted_ellipse = shapely.affinity.scale(inital_fitted_ellipse, xfact=scale_factor, yfact=scale_factor, origin=fitted_ellipse_pivot['coords'])
            if not fitted_ellipse.intersects(sides):
                break
            scale_factor -= scale_step


        ## Center
        inital_fitted_ellipse = fitted_ellipse
        # Initial guess (start with 1 mm movement)
        initial_guess = 1        
        # Set bounds to prevent moving outside of the line
        bounds = (-fitted_ellipse_act_pos, outer_ellipse_front.length - fitted_ellipse_act_pos)  # Can't go outside of the line    

        result = scipy.optimize.minimize(
            ellipse_find_center,
            initial_guess,
            method='L-BFGS-B',  # Good for bounded problems
            bounds=[bounds],
            options={'ftol': 1e-3}
        )

        # Apply the optimal movement
        optimal_move = result.x
        fitted_ellipse_move_to_coords = np.array(outer_ellipse_front.interpolate(fitted_ellipse_act_pos + optimal_move)[0].coords[0])
        fitted_ellipse_move_to_vector = fitted_ellipse_move_to_coords - fitted_ellipse_pivot['coords']
        angle_diff_rad = angle_between_points(shapely.Point(medial_axis.coords[0]), 
                                            shapely.Point(fitted_ellipse_pivot['coords']), 
                                            shapely.Point(fitted_ellipse_move_to_coords))
        
        # Apply final transformation
        fitted_ellipse = shapely.affinity.translate(inital_fitted_ellipse, 
                                                xoff=fitted_ellipse_move_to_vector[0], 
                                                yoff=fitted_ellipse_move_to_vector[1])
        fitted_ellipse = shapely.affinity.rotate(fitted_ellipse, angle_diff_rad, 
                                            origin=shapely.Point(fitted_ellipse.coords[0]), 
                                            use_radians=True)
        
        # Update pivot information
        fitted_ellipse_pivot['coords'] = fitted_ellipse.coords[0]
        fitted_ellipse_pivot['vector'] = rotate_vector(fitted_ellipse_pivot['vector'], angle_diff_rad)
        fitted_ellipse_act_pos += optimal_move

    time2 = time.time()
    print(f'Time: {time2-time1}')
    create_line_object(fitted_ellipse.coords, "intersection", color=(1, 0.1, 0, 1))

if __name__ == "__main__":    
    main()    
