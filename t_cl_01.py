import numpy as np
from shapely.geometry import LineString, MultiLineString
import matplotlib.pyplot as plt

def fraction_of_linestring_close_to_another(
    l0: LineString, 
    l1: LineString, 
    distance: float
) -> float:
    """
    Calculates what fraction of the length of a LineString l0 is within a
    specified distance of another LineString l1.

    Args:
        l0 (LineString): The primary linestring to measure.
        l1 (LineString): The linestring to measure proximity to.
        distance (float): The close distance threshold (buffer radius).

    Returns:
        float: The fraction (from 0.0 to 1.0) of l0's length that is close to l1.
    """
    # 1. Handle edge cases and get the total length of the primary linestring.
    if l0.is_empty or l1.is_empty:
        return 0.0
    
    total_length_l0 = l0.length
    if total_length_l0 == 0:
        return 0.0 # If l0 has no length, no part of it can be close.

    # 2. Create a buffer zone (a Polygon) around the second linestring.
    # This polygon represents all points within the specified 'distance' of l1.
    buffer_l1 = l1.buffer(distance)

    # 3. Find the part of l0 that falls inside this buffer zone.
    # The intersection can be a LineString, a MultiLineString, or other geometry types.
    intersection_geometry = l0.intersection(buffer_l1)

    # 4. Calculate the length of the resulting intersection.
    # The .length property correctly handles single or multiple line segments.
    length_within_buffer = intersection_geometry.length

    # 5. Calculate and return the final fraction.
    fraction = length_within_buffer / total_length_l0
    
    return fraction

# --- Example Usage and Visualization ---

# Define the "close distance"
CLOSE_DISTANCE = 0.5

# Create two sample linestrings using NumPy and Shapely
# l0: A wavy line (the one we are measuring)
x0 = np.linspace(0, 10, 100)
y0 = np.sin(x0)
l0 = LineString(zip(x0, y0))

# l1: A slightly sloped straight line (the one we are comparing against)
l1 = LineString([(0, 0.2), (10, 1.2)])

# --- Calculation ---
fraction_close = fraction_of_linestring_close_to_another(l0, l1, CLOSE_DISTANCE)

print(f"Total length of l0: {l0.length:.4f}")
print(f"Fraction of l0 within {CLOSE_DISTANCE} distance of l1: {fraction_close:.4f}")
print(f"Percentage of l0 close to l1: {fraction_close * 100:.2f}%")


# --- Visualization (to understand what's happening) ---
fig, ax = plt.subplots()

# Plot the original linestrings
ax.plot(*l0.xy, label='l0 (The line being measured)', color='blue', linewidth=3, solid_capstyle='round')
ax.plot(*l1.xy, label='l1 (The reference line)', color='red', linestyle='--', linewidth=2, solid_capstyle='round')

# Plot the buffer zone around l1
buffer_polygon = l1.buffer(CLOSE_DISTANCE)
ax.plot(*buffer_polygon.exterior.xy, color='red', alpha=0.3, label=f'Buffer Zone (distance={CLOSE_DISTANCE})')
ax.fill(*buffer_polygon.exterior.xy, color='red', alpha=0.1)


# Plot the intersection (the part of l0 inside the buffer)
intersection = l0.intersection(buffer_polygon)
if not intersection.is_empty:
    if isinstance(intersection, LineString):
        ax.plot(*intersection.xy, color='limegreen', linewidth=4, label='Intersection', solid_capstyle='round')
    elif isinstance(intersection, MultiLineString):
        for line in intersection.geoms:
            ax.plot(*line.xy, color='limegreen', linewidth=4, solid_capstyle='round')
        # Add a single label for the MultiLineString
        ax.plot([], [], color='limegreen', linewidth=4, label='Intersection') # Dummy plot for label

print(f"\nLength of the intersection part: {intersection.length:.4f}")

ax.set_title("Fraction of LineString l0 Close to LineString l1")
ax.legend()
ax.set_aspect('equal', 'box')
ax.grid(True, linestyle=':', alpha=0.6)
plt.show()