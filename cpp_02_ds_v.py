import networkx as nx
from itertools import combinations

def solve_cpp(G, start_node):
    # Step 1: Check if the graph is Eulerian (all nodes even degree)
    if nx.is_eulerian(G):
        eulerian_circuit = list(nx.eulerian_circuit(G, source=start_node))
        # Convert circuit to trail by stopping at last edge
        return eulerian_circuit[:-1]

    # Step 2: Find odd-degree nodes and compute min-weight matching
    odd_degree_nodes = [v for v in G.nodes() if G.degree(v) % 2 != 0]

    if len(odd_degree_nodes) % 2 != 0:
        raise ValueError("Number of odd-degree nodes must be even.")

    # Create complete graph between odd nodes with shortest path distances
    complete_graph = nx.Graph()
    for u, v in combinations(odd_degree_nodes, 2):
        path_length = nx.shortest_path_length(G, u, v)
        complete_graph.add_edge(u, v, weight=path_length)

    # Compute min-weight matching
    matching = nx.algorithms.min_weight_matching(complete_graph, weight='weight')

    # Duplicate edges along the shortest paths to make G Eulerian
    G_eulerian = G.copy()
    for u, v in matching:
        path = nx.shortest_path(G, u, v)
        for i in range(len(path) - 1):
            G_eulerian.add_edge(path[i], path[i + 1])

    # Step 3: Find Eulerian trail (not circuit)
    eulerian_trail = list(nx.eulerian_circuit(G_eulerian, source=start_node))
    return eulerian_trail

# Example usage:
if __name__ == "__main__":
    G = nx.Graph()
    G.add_edge('A', 'B')
    G.add_edge('B', 'C')
    G.add_edge('B', 'D')
    G.add_edge('D', 'E')
    G.add_edge('D', 'F')
    G.add_edge('F', 'G')
    G.add_edge('F', 'H')
    start_node = "A"
    trail = solve_cpp(G, start_node)
    print("Eulerian Trail (CPP solution):", trail)
