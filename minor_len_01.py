import numpy as np

def calculate_semi_minor_axis(semi_major_axis: float, required_min_radius_of_curvature: float) -> float | None:
    """
    Calculates the semi-minor axis of an ellipse based on major axis and required minimum radius of curvature.

    Args:
        major_axis (float): Length of the major axis (2a)
        required_min_radius_of_curvature (float): Required minimum radius of curvature

    Returns:
        float: The semi-minor axis (b)
        None: If constraints are impossible
    """    
    if required_min_radius_of_curvature > semi_major_axis:
        return None

    return np.sqrt(required_min_radius_of_curvature * semi_major_axis)


print(calculate_semi_minor_axis(50, 25))