import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize, differential_evolution
from matplotlib.patches import Ellipse, Polygon
from shapely.geometry import Point, Polygon as ShapelyPolygon
from shapely.geometry import LineString

# Create the same polygon
polygon_vertices = np.array([
    [1, 1], [4, 0.5], [6, 2], [5.5, 4], 
    [3, 5], [0.5, 4], [0, 2]
])

# Create shapely polygon for robust geometric operations
shapely_polygon = ShapelyPolygon(polygon_vertices)

def generate_ellipse_points(cx, cy, a, b, theta, n_points=100):
    """Generate points on ellipse boundary"""
    t = np.linspace(0, 2*np.pi, n_points)
    cos_theta, sin_theta = np.cos(theta), np.sin(theta)
    
    x_ellipse = cx + a * np.cos(t) * cos_theta - b * np.sin(t) * sin_theta
    y_ellipse = cy + a * np.cos(t) * sin_theta + b * np.sin(t) * cos_theta
    
    return np.column_stack([x_ellipse, y_ellipse])

def constraint_all_points_inside(params):
    """Constraint: all ellipse points must be inside polygon"""
    cx, cy, a, b, theta = params
    ellipse_points = generate_ellipse_points(cx, cy, a, b, theta, n_points=200)
    
    # Check if all points are inside using shapely
    violations = 0
    for point in ellipse_points:
        if not shapely_polygon.contains(Point(point)):
            # Calculate how far outside the point is
            distance_to_boundary = Point(point).distance(shapely_polygon.boundary)
            violations += distance_to_boundary
    
    return -violations  # Constraint should be >= 0

def inscribed_ellipse_area_objective(params):
    """Maximize ellipse area"""
    cx, cy, a, b, theta = params
    return -(np.pi * a * b)  # Negative because we minimize

# Method 1: Constrained optimization
print("Method 1: Constrained Optimization")
print("="*40)

constraints = {'type': 'ineq', 'fun': constraint_all_points_inside}

# Better initial guess - start smaller
bounds_inscribed = [
    (1, 5),      # cx
    (1, 4),      # cy  
    (0.1, 2.5),  # a (smaller initial range)
    (0.1, 2.0),  # b
    (-np.pi, np.pi)  # theta
]

result_constrained = minimize(
    inscribed_ellipse_area_objective,
    [2.5, 2.5, 1.5, 1.0, 0.0],  # Conservative initial guess
    bounds=bounds_inscribed,
    constraints=constraints,
    method='SLSQP',
    options={'maxiter': 500}
)

print(f"Success: {result_constrained.success}")
print(f"Center: ({result_constrained.x[0]:.3f}, {result_constrained.x[1]:.3f})")
print(f"Semi-axes: a={result_constrained.x[2]:.3f}, b={result_constrained.x[3]:.3f}")
print(f"Area: {np.pi * result_constrained.x[2] * result_constrained.x[3]:.3f}")




# Method 4: Advanced approach using differential evolution with better constraints
print("Method 4: Differential Evolution with Adaptive Constraints")
print("="*55)

def advanced_inscribed_objective(params):
    """Advanced objective with adaptive penalty"""
    cx, cy, a, b, theta = params
    
    # Generate dense sampling of ellipse boundary
    ellipse_points = generate_ellipse_points(cx, cy, a, b, theta, n_points=300)
    
    # Check containment and calculate penalties
    penalty = 0
    min_margin = float('inf')
    
    for point in ellipse_points:
        point_geom = Point(point)
        if shapely_polygon.contains(point_geom):
            # Point is inside - calculate margin to boundary
            margin = point_geom.distance(shapely_polygon.boundary)
            min_margin = min(min_margin, margin)
        else:
            # Point is outside - heavy penalty proportional to distance
            distance_outside = point_geom.distance(shapely_polygon.boundary)
            penalty += distance_outside * 1000
    
    # Encourage larger ellipses with safety margin
    area = np.pi * a * b
    
    # If any point is outside, return large positive value (bad)
    if penalty > 0:
        return penalty
    
    # If all points inside but too close to boundary, add small penalty
    if min_margin < 0.05:
        penalty += (0.05 - min_margin) * 100
    
    return -area + penalty

# Wider bounds for differential evolution
bounds_de_advanced = [
    (0.5, 5.5),    # cx
    (0.5, 4.5),    # cy  
    (0.2, 3.5),    # a
    (0.2, 3.0),    # b
    (-np.pi, np.pi) # theta
]

result_de_advanced = differential_evolution(
    advanced_inscribed_objective,
    bounds_de_advanced,
    seed=42,
    maxiter=100,
    popsize=20,
    atol=1e-6,
    tol=1e-6
)

print(f"Success: {result_de_advanced.success}")
print(f"Center: ({result_de_advanced.x[0]:.3f}, {result_de_advanced.x[1]:.3f})")
print(f"Semi-axes: a={result_de_advanced.x[2]:.3f}, b={result_de_advanced.x[3]:.3f}")
print(f"Area: {np.pi * result_de_advanced.x[2] * result_de_advanced.x[3]:.3f}")

# Method 5: Iterative refinement approach
print("\nMethod 5: Iterative Refinement")
print("="*35)

def iterative_inscribed_ellipse(polygon_vertices, max_iterations=50):
    """Iteratively grow ellipse until it touches boundary"""
    
    # Start with a small ellipse at polygon centroid
    centroid = np.mean(polygon_vertices, axis=0)
    cx, cy = centroid
    a, b = 0.5, 0.5
    theta = 0.0
    
    best_params = [cx, cy, a, b, theta]
    best_area = np.pi * a * b
    
    for iteration in range(max_iterations):
        # Try to grow the ellipse in different ways
        growth_attempts = [
            [cx, cy, a*1.05, b, theta],      # grow a
            [cx, cy, a, b*1.05, theta],      # grow b
            [cx, cy, a*1.02, b*1.02, theta], # grow both
            [cx + 0.02, cy, a, b, theta],    # move right
            [cx - 0.02, cy, a, b, theta],    # move left
            [cx, cy + 0.02, a, b, theta],    # move up
            [cx, cy - 0.02, a, b, theta],    # move down
            [cx, cy, a, b, theta + 0.05],    # rotate
            [cx, cy, a, b, theta - 0.05],    # rotate other way
        ]
        
        improved = False
        for attempt in growth_attempts:
            # Check if this ellipse fits inside polygon
            ellipse_points = generate_ellipse_points(*attempt, n_points=100)
            
            all_inside = True
            for point in ellipse_points:
                if not shapely_polygon.contains(Point(point)):
                    all_inside = False
                    break
            
            if all_inside:
                area = np.pi * attempt[2] * attempt[3]
                if area > best_area:
                    best_params = attempt.copy()
                    best_area = area
                    cx, cy, a, b, theta = attempt
                    improved = True
                    break
        
        if not improved:
            break
    
    return best_params, best_area

iterative_params, iterative_area = iterative_inscribed_ellipse(polygon_vertices)
print(f"Center: ({iterative_params[0]:.3f}, {iterative_params[1]:.3f})")
print(f"Semi-axes: a={iterative_params[2]:.3f}, b={iterative_params[3]:.3f}")
print(f"Area: {iterative_area:.3f}")