import numpy as np
from scipy.optimize import least_squares

# ------------------------------------------------------------------
# 1.  Residuals (same as before – numerically stable)
# ------------------------------------------------------------------
def residuals(p, xy):
    x0, y0, a, b, theta = p
    c, s = np.cos(theta), np.sin(theta)

    dx = xy[:, 0] - x0
    dy = xy[:, 1] - y0

    u = c * dx + s * dy
    v = -s * dx + c * dy
    return (u / a) ** 2 + (v / b) ** 2 - 1.0


# ------------------------------------------------------------------
# 2.  Analytic Jacobian  ∂r/∂p  shape (n_points, 5)
# ------------------------------------------------------------------
def jac_residuals(p, xy):
    x0, y0, a, b, theta = p
    c, s = np.cos(theta), np.sin(theta)

    dx = xy[:, 0] - x0
    dy = xy[:, 1] - y0

    u = c * dx + s * dy
    v = -s * dx + c * dy

    # common factors
    ua2 = u / (a * a)
    vb2 = v / (b * b)

    # partial wrt x0
    dr_dx0 = -2 * (c * ua2 - s * vb2)
    # wrt y0
    dr_dy0 = -2 * (s * ua2 + c * vb2)
    # wrt a
    dr_da = -2 * u * u / (a ** 3)
    # wrt b
    dr_db = -2 * v * v / (b ** 3)
    # wrt theta
    dr_dth = 2 * u * ua2 * (-s * dx + c * dy) + 2 * v * vb2 * (-c * dx - s * dy)

    return np.column_stack((dr_dx0, dr_dy0, dr_da, dr_db, dr_dth))


# ------------------------------------------------------------------
# 3.  Thin wrapper
# ------------------------------------------------------------------
def fit_ellipse_5pt(points, init, bounds, *, verbose=False):
    result = least_squares(
        residuals,
        init,
        jac=jac_residuals,
        args=(points,),
        bounds=bounds,
        method='trf',
        ftol=1e-6, xtol=1e-6, gtol=1e-6,
        max_nfev=30,
        verbose=2 if verbose else 0,
    )
    return result.x


# ------------------------------------------------------------------
# 4.  Re-run your example
# ------------------------------------------------------------------
if __name__ == "__main__":
    points = np.array([[-18107.85742188,  -9668.421875  ],
                       [-18109.07421875,  -9649.95117188],
                       [-18133.55859375,  -9622.34765625],
                       [-18161.0234375,   -9615.94433594],
                       [-18180.34570312,  -9623.63476562]], dtype=np.float64)

    bounds = ([-18170, -9679, 30, 20, np.deg2rad(320)],
              [-18130, -9625, 60, 40, np.deg2rad(340)])

    initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)])

    import time
    time1 = time.time()
    opt = fit_ellipse_5pt(points, initial_guess, bounds, verbose=True)
    time2 = time.time()
    print(f'Time: {time2-time1}')
    print("Optimised parameters:", opt)