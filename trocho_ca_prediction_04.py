import numpy as np

import numpy as np
# import warnings # Optional: For suppressing runtime warnings if needed

def _wrap(angle):
    """Wrap angle to the interval (-π, π]. (Vectorized)"""
    return (angle + np.pi) % (2 * np.pi) - np.pi

def _interval_overlap_length(start1, end1, start2, end2):
    """Calculates overlap length between intervals. (Vectorized)"""
    overlap_start = np.maximum(start1, start2)
    overlap_end = np.minimum(end1, end2)
    return np.maximum(0.0, overlap_end - overlap_start)

def _front_side_engagement_vectorized(beta, theta, feed_dir):
    """
    Vectorized calculation of front-side engagement angle for multiple transitions.

    Args:
        beta: Angles from current cutter center to previous loop center. Shape (N, M).
        theta: Half-angles of the cleared wedge. Shape (N, M).
        feed_dir: Current feed direction angles. Shape (N, M).
                  N = number of transitions, M = nsamples.

    Returns:
        Front-side engagement angles in radians. Shape (N, M).
    """
    front_start, front_end = -np.pi / 2, np.pi / 2
    total_front_angle = np.pi

    beta_rel = _wrap(beta - feed_dir)
    wedge_start = beta_rel - theta
    wedge_end = beta_rel + theta

    # Calculate overlap with the front interval and its +/- 2pi shifts
    cleared_in_front = (
        _interval_overlap_length(wedge_start, wedge_end, front_start, front_end) +
        _interval_overlap_length(wedge_start, wedge_end, front_start - 2 * np.pi, front_end - 2 * np.pi) +
        _interval_overlap_length(wedge_start, wedge_end, front_start + 2 * np.pi, front_end + 2 * np.pi)
    )

    cleared_in_front = np.minimum(cleared_in_front, total_front_angle)
    engagement = total_front_angle - cleared_in_front
    return np.maximum(0.0, engagement)


def calculate_max_engagement_series_vectorized(R, radii, sample_distances, nsamples=360):
    """
    Calculates max front-side engagement for a series of loops using vectorization.

    Args:
        R: Cutter radius [mm] (scalar, > 0).
        radii: NumPy array of trochoid loop radii [mm]. Shape (L,), L >= 2. All elements >= 0.
        sample_distances: NumPy array of cumulative distances of loop centers [mm].
                          Shape (L,), monotonically increasing.
        nsamples: Number of points to sample along each loop's circumference.

    Returns:
        NumPy array of maximum front-side engagement angles in degrees (0 to 180).
        Shape (L-1,). Contains NaN for transitions with non-positive d_off.
    """
    # --- Input Validation and Preparation ---
    if not isinstance(radii, np.ndarray):
        radii = np.array(radii, dtype=float)
    if not isinstance(sample_distances, np.ndarray):
        sample_distances = np.array(sample_distances, dtype=float)

    L = len(radii)
    if L != len(sample_distances):
        raise ValueError("radii and sample_distances must have the same length.")
    if R <= 0:
        raise ValueError("Cutter radius R must be positive.")
    if np.any(radii < 0):
        raise ValueError("All loop radii must be non-negative.")
    if L < 2:
        return np.array([], dtype=float) # No transitions

    num_transitions = L - 1
    # Ignore numpy warnings (like division by zero) temporarily, handle with NaNs later
    # with warnings.catch_warnings():
    #    warnings.simplefilter("ignore", category=RuntimeWarning)

    # Extract parameters for each transition (shape: (N,)) where N = num_transitions
    r_prev = radii[:-1]
    r_next = radii[1:]
    d_off = np.diff(sample_distances) # Distances between consecutive centers

    # --- Handle Invalid Transitions ---
    # Mark transitions with non-positive offset (no movement or backward)
    invalid_transition_mask = (d_off <= 1e-12) # Use tolerance for float comparison
    # Initialize results array with NaN
    max_engagement_results_deg = np.full(num_transitions, np.nan, dtype=float)

    # If all transitions are invalid, return NaNs early
    if np.all(invalid_transition_mask):
        return max_engagement_results_deg

    # Create masks to select only valid transitions for computation
    valid_mask = ~invalid_transition_mask
    N_valid = np.sum(valid_mask) # Number of valid transitions

    # Filter inputs to only include valid transitions
    r_prev_v = r_prev[valid_mask]
    r_next_v = r_next[valid_mask]
    d_off_v = d_off[valid_mask]

    # Reshape valid inputs for broadcasting (N_valid, 1)
    r_prev_v = r_prev_v[:, np.newaxis]
    r_next_v = r_next_v[:, np.newaxis]
    d_off_v = d_off_v[:, np.newaxis]

    # --- Vectorized Calculation for Valid Transitions ---

    # Sample angles (shape: (M,)) -> Reshape to (1, M) for broadcasting
    psi = np.linspace(0, 2 * np.pi, nsamples, endpoint=False).reshape(1, nsamples)
    cos_psi = np.cos(psi)
    sin_psi = np.sin(psi)

    # Effective previous radius (shape: (N_valid, 1))
    R_prev_eff_v = R + r_prev_v

    # Calculate cutter center coordinates relative to previous loop center
    # Shapes: d_off_v(N_valid, 1), r_next_v(N_valid, 1), cos/sin_psi(1, M)
    # Result shape: (N_valid, M)
    cx_v = d_off_v + r_next_v * cos_psi
    cy_v = r_next_v * sin_psi

    # Distance 'd' from current point to previous center (shape: (N_valid, M))
    d_v = np.hypot(cx_v, cy_v)
    # Add epsilon to avoid division by zero later, esp. if d_off=0 and psi=pi
    d_v = np.maximum(d_v, 1e-12)

    # Feed direction (tangent) (shape: (N_valid, M))
    # tx = -r_next_v * sin_psi # Not explicitly needed
    # ty = r_next_v * cos_psi # Not explicitly needed
    feed_dir_v = np.arctan2(r_next_v * cos_psi, -r_next_v * sin_psi)

    # Angle 'beta' back to previous center (shape: (N_valid, M))
    beta_v = np.arctan2(-cy_v, -cx_v)

    # --- Determine Engagement State (Vectorized) ---
    # Masks (shape: (N_valid, M))
    no_intersection_mask_v = d_v >= (R + R_prev_eff_v)
    inside_clearance_mask_v = d_v <= np.abs(R_prev_eff_v - R)

    # Initialize engagement angles (radians, shape: (N_valid, M))
    engaged_angle_rad_v = np.zeros_like(d_v)

    # Case 1: No intersection -> Full front engagement (pi)
    engaged_angle_rad_v[no_intersection_mask_v] = np.pi

    # Case 2: Inside clearance -> Zero front engagement (already initialized)

    # Case 3: Circles intersect -> Calculate partial engagement
    calc_mask_v = ~ (no_intersection_mask_v | inside_clearance_mask_v)

    # Only proceed if there are any intersections to calculate
    # if np.any(calc_mask_v): # Avoids computation if unnecessary, but adds branching
    # Perform calculation everywhere, masks handle assignment

    # Calculate half-angle 'theta' using law of cosines (shape: (N_valid, M))
    # Denominator: 2 * d_v * R
    denominator_v = 2 * d_v * R
    # Avoid division by zero, although d_v has epsilon. R > 0 is checked.
    # If denominator is near zero, cos_theta_arg -> inf, clip handles it.
    cos_theta_arg_v = (d_v**2 + R**2 - R_prev_eff_v**2) / denominator_v

    # Clamp argument for arccos: [-1, 1]
    cos_theta_arg_v = np.clip(cos_theta_arg_v, -1.0, 1.0)
    theta_v = np.arccos(cos_theta_arg_v) # Half-angle of cleared wedge (rad)

    # Calculate front-side engagement where needed (Case 3)
    engagement_calc_v = _front_side_engagement_vectorized(
        beta_v, theta_v, feed_dir_v
    )

    # Assign calculated engagement only where calc_mask_v is True
    # np.where is cleaner than boolean indexing assignment here sometimes
    engaged_angle_rad_v = np.where(calc_mask_v, engagement_calc_v, engaged_angle_rad_v)
    # Ensure Case 1 (no intersection) still has pi
    engaged_angle_rad_v = np.where(no_intersection_mask_v, np.pi, engaged_angle_rad_v)

    # --- Final Result ---
    # Find the maximum engagement along the sample axis (axis=1)
    # Result shape: (N_valid,)
    max_engagement_rad_v = np.max(engaged_angle_rad_v, axis=1)

    # Convert valid results to degrees
    max_engagement_deg_v = np.degrees(max_engagement_rad_v)

    # Place valid results back into the full results array
    max_engagement_results_deg[valid_mask] = max_engagement_deg_v

    return max_engagement_results_deg


# --- Example Usage ---
R_cutter = 10.0   # cutter radius [mm]

# Define loop radii and their center positions (cumulative distance)
# loop_radii = np.array([10.0, 10.5, 10.8, 11.0, 10.8, 10.5, 10.0]) # Example radii
loop_radii = np.array([40, 44])/2
# Center positions along a path
center_distances = np.array([0.0, 5.0]) # Example distances


# Calculate the max engagement for each transition
max_engagements_deg = calculate_max_engagement_series_vectorized(
    R_cutter, loop_radii, center_distances)
print(max_engagements_deg)
