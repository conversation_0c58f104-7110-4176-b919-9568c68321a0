import numpy as np
from scipy.spatial.distance import cdist
from scipy.special import logsumexp
from scipy.optimize import minimize
import time


def ellipse_boundary_points(center_x, center_y, a, b, theta, num_points=1000):
    """Generate discretized boundary points of the ellipse."""
    t = np.linspace(0, 2 * np.pi, num_points)
    cos_theta, sin_theta = np.cos(theta), np.sin(theta)
    x = center_x + a * np.cos(t) * cos_theta - b * np.sin(t) * sin_theta
    y = center_y + a * np.cos(t) * sin_theta + b * np.sin(t) * cos_theta
    return np.column_stack([x, y])

def signed_distance_to_segment(point, start, end):
    """Signed distance from point to infinite line of segment. Positive on right side."""
    # Ensure float dtype to avoid casting errors
    point = np.asarray(point, dtype=float)
    start = np.asarray(start, dtype=float)
    end = np.asarray(end, dtype=float)
    
    vec = end - start  # Direction vector
    perp_vec = np.array([-vec[1], vec[0]], dtype=float)  # Perpendicular (right-hand rotation)
    norm = np.linalg.norm(perp_vec) + 1e-10
    perp_vec /= norm  # Unit vector to right
    
    # Vector from start to point
    to_point = point - start
    
    # Projection onto perp vec gives signed dist (positive right, negative left)
    signed_dist = np.dot(to_point, perp_vec)
    
    return signed_dist

def min_distance_to_segments(points, segments):
    """Min Euclidean distance from each point to any segment (vectorized)."""
    if len(points) == 0:
        return np.array([])
    
    # segments: (N_seg, 2, 2) -> [start, end] for each
    starts = segments[:, 0]
    ends = segments[:, 1]
    vec = ends - starts
    len_sq = np.sum(vec**2, axis=1)
    
    # Project t parameter (clipped [0,1])
    proj_num = np.sum((points[:, None] - starts) * vec, axis=2)
    t = np.clip(proj_num / (len_sq + 1e-10), 0, 1)
    
    # Projection points: (N_points, N_seg, 2)
    projections = starts + t[:, :, None] * vec
    
    # Distances: (N_points, N_seg)
    dists = np.linalg.norm(points[:, None] - projections, axis=2)
    return np.min(dists, axis=1)

def max_right_side_distance(params, polyline, num_points=1000, softness=10.0):
    """
    Computes a differentiable approx of the max distance from ellipse points on the right side
    of the polyline to the nearest polyline segment.

    Args:
        params (array): [center_x, center_y, a, b, theta]
        polyline (np.ndarray): (N, 2) array of polyline points [x, y]
        num_points (int): Number of ellipse points to discretize
        softness (float): Higher = closer to true max (but steeper gradient)

    Returns:
        float: Soft max distance (minimize this); 0 if no points on right side
    """
    center_x, center_y, a, b, theta = params
    ellipse_points = ellipse_boundary_points(center_x, center_y, a, b, theta, num_points)
    
    if len(polyline) < 2:
        return 0.0  # Degenerate case
    
    # Convert polyline to float to avoid dtype issues downstream
    polyline = np.asarray(polyline, dtype=float)
    
    # Create segments: (N-1, 2, 2)
    segments = np.stack([polyline[:-1], polyline[1:]], axis=1)
    
    # Compute distances to all segments: (N_points, N_seg)
    dists_to_segments = np.linalg.norm(ellipse_points[:, None] - 
        (segments[:, 0] + 
         np.clip(np.sum((ellipse_points[:, None] - segments[:, 0]) * (segments[:, 1] - segments[:, 0]), axis=2) / 
                 (np.sum((segments[:, 1] - segments[:, 0])**2, axis=1) + 1e-10), 0, 1)[:, :, None] * 
         (segments[:, 1] - segments[:, 0])), axis=2)
    
    min_dists_per_point = np.min(dists_to_segments, axis=1)
    closest_seg_indices = np.argmin(dists_to_segments, axis=1)
    
    # Signed dists using closest segment
    signed_dists = np.array([
        signed_distance_to_segment(p, segments[i, 0], segments[i, 1])
        for p, i in zip(ellipse_points, closest_seg_indices)
    ])
    
    # Filter points on right side (positive signed dist)
    right_mask = signed_dists > 0
    right_distances = min_dists_per_point[right_mask]
    
    if len(right_distances) == 0:
        return 0.0
    
    # Soft max approximation (differentiable)
    soft_max = (1 / softness) * logsumexp(softness * right_distances)
    return soft_max


# Example polyline
polyline = np.array([[2, -15], [1, 15]])

# Initial guess for params [cx, cy, a, b, theta]
#random params array (1000 x 5)
ran_params = np.random.rand(1000, 5)
initial_params = np.array([0.0, 0.0, 5.0, 5.0, 0.0])
iterations = 1000
time1 = time.time()
for _ in range(iterations):
    res = max_right_side_distance(initial_params, polyline, num_points=72, softness=30.0)
time2 = time.time()
print(f'Time: {time2-time1}')


print(res)