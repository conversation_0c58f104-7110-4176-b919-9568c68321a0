"""
stripe_mill.py
==============

1‑D interval (stripe) representation for flat‑end‑mill engagement,
sub‑pixel accurate in X.

Author: 2025‑04  (o3 High / GPT‑4)
"""
from __future__ import annotations
import math
from bisect import bisect_left
import numpy as np
import pandas as pd


# ----------------------------------------------------------------------
# helpers ---------------------------------------------------------------
def _subtract(intervals: list[tuple[float, float]],
              rem: tuple[float, float]) -> tuple[list[tuple[float, float]], float]:
    """
    Subtract *rem* = (a,b) from a sorted list of disjoint intervals.
    Returns (new_list, removed_length).
    """
    a, b = rem
    removed = 0.0
    out = []
    for x0, x1 in intervals:
        if x1 <= a or x0 >= b:              # no overlap
            out.append((x0, x1))
        else:
            # overlap exists
            if x0 < a:
                out.append((x0, a))
            if x1 > b:
                out.append((b, x1))
            removed += max(0.0, min(x1, b) - max(x0, a))
    return out, removed


# ----------------------------------------------------------------------
# work‑piece container --------------------------------------------------
class StripeWP:
    """
    Work‑piece as Y‑stripes with X‑interval lists.
    """
    def __init__(self, xmin: float, xmax: float,
                 ymin: float, ymax: float,
                 dy: float):
        self.xmin, self.xmax = xmin, xmax
        self.ymin = ymin
        self.dy = dy
        self.n = int(math.ceil((ymax - ymin)/dy))
        full = [(xmin, xmax)]
        self.stripes: list[list[tuple[float, float]]] = [full.copy() for _ in range(self.n)]

    # ------------------------------------------
    def remove_disk(self, cx: float, cy: float, R: float) -> float:
        """
        Remove material of a Ø2R cutter centred at (cx,cy).
        Returns removed AREA [mm²].
        """
        k_min = max(0, int(math.floor((cy - R - self.ymin)/self.dy)))
        k_max = min(self.n-1, int(math.ceil((cy + R - self.ymin)/self.dy)))
        area = 0.0
        for k in range(k_min, k_max+1):
            yk = self.ymin + (k + 0.5)*self.dy     # stripe centre
            dy = abs(yk - cy)
            if dy > R:
                continue
            half = math.sqrt(R*R - dy*dy)
            rem_iv = (cx - half, cx + half)
            new_iv, removed_len = _subtract(self.stripes[k], rem_iv)
            self.stripes[k] = new_iv
            area += removed_len * self.dy
        return area


# ----------------------------------------------------------------------
# simulation loop -------------------------------------------------------
def simulate_stripe(tool_D: float,
                    dy: float,
                    bbox: tuple[float, float, float, float],
                    path: np.ndarray,
                    ap: float,
                    fz: float,
                    z: int,
                    n_rpm: float) -> pd.DataFrame:
    """
    Same signature idea as before, but using stripe engine.
    """
    xmin, xmax, ymin, ymax = bbox
    wp = StripeWP(xmin, xmax, ymin, ymax, dy)
    R = tool_D/2
    vf = n_rpm * z * fz

    data = []
    prev_x, prev_y = path[0]
    for step, (cx, cy) in enumerate(path):
        ds = math.hypot(cx-prev_x, cy-prev_y) if step else fz
        prev_x, prev_y = cx, cy

        A_step = wp.remove_disk(cx, cy, R)           # [mm²] actual
        A_fz   = 0.0 if ds == 0 else A_step * (fz/ds)  # normalise

        V_fz = A_fz * ap
        MRR  = V_fz * z * n_rpm
        a_e  = A_fz / fz if fz else 0.0
        a_e  = min(max(a_e, 0.0), 2*R)
        theta = math.degrees(math.acos((R-a_e)/R)) if a_e <= R else 180.0

        data.append(dict(step=step, X=cx, Y=cy, ds=ds,
                         area=A_fz, V_fz=V_fz, MRR=MRR,
                         a_e=a_e, theta=theta))
    return pd.DataFrame(data)


# ----------------------------------------------------------------------
# demo -----------------------------------------------------------------
if __name__ == "__main__":
    # tool & process
    D  = 10.0
    dy = 0.01            # 10 µm stripe height
    ap = 5.0
    fz = 0.05
    z  = 4
    n  = 4_000

    bbox = (0, 45, 0, 40)      # simple blank

    # tiny displacements example
    path = np.array([
    [10.0, 10.0],          # first pass
    [20.0, 10.0],          # full slot
    [22.0, 10.0],          # full slot
    [22.0, 10.1],          # full slot
    [20.0, 15.0],          # step over 5 mm = R
    [10.0, 12.0]           # back, 50 % engagement
    ])

    df = simulate_stripe(D, dy, bbox, path, ap, fz, z, n)
    print(df)

    # Created/Modified files during execution: