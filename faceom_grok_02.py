import numpy as np
import bpy
import shapely.geometry
from shapely.geometry import <PERSON><PERSON><PERSON>, Point, LineString
from mathutils import Vector


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []
    
    geometry_list = []
    for obj in selected_objects:
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    edges = [(i, i + 1) for i in range(len(vertices) - 1)]
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def rotate_vector(vec, angle_deg):
    """Rotate a 2D vector by angle_deg counterclockwise."""
    angle_rad = np.deg2rad(angle_deg)
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)
    rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
    return rotation_matrix @ vec


def create_contour_points(points):
    """Create a 2D contour from points."""    
    # Compute cumulative distance for parameterization
    diffs = np.diff(points, axis=0)    
    distances = np.sqrt((diffs**2).sum(axis=1))    
    t = np.zeros(points.shape[0])    
    t[1:] = np.cumsum(distances)    
    return points, t


def create_interpolation_functions(points, t):
    """Create interpolation functions for the contour."""
    def cs_x(t_val):
        return np.interp(t_val, t, points[:, 0])

    def cs_y(t_val):
        return np.interp(t_val, t, points[:, 1])
    
    def c(t_val):
        """Return the [x, y] position on the contour at parameter t."""
        return np.array([cs_x(t_val), cs_y(t_val)])
    
    def tangent(t_val, delta=1e-5):
        """Return the unit tangent vector at parameter t using finite difference."""
        # Use finite difference to approximate derivative
        t_plus = min(t_val + delta, t[-1])
        t_minus = max(t_val - delta, t[0])
        
        p_plus = np.array([cs_x(t_plus), cs_y(t_plus)])
        p_minus = np.array([cs_x(t_minus), cs_y(t_minus)])
        
        # Calculate tangent vector
        deriv = (p_plus - p_minus) / (t_plus - t_minus)
        norm = np.linalg.norm(deriv)
        
        # Return normalized tangent vector
        return deriv / norm if norm > 0 else deriv
    
    return c, tangent


def calculate_tool_path(c, tangent, t, theta=60, r_tool=10, delta_t=0.1, beta=0):
    """Calculate the tool path using the FACEOM algorithm."""
    t_min = 0
    t_max = t[-1]
    
    # Initialization
    t_i = t_min
    C_i = c(t_i)          # Initial contour point
    T_i = tangent(t_i)    # Unit tangent vector at C_i
    omega = beta + theta + 90
    U_i = rotate_vector(T_i, omega)  # Direction for initial tool position
    P_i = C_i + r_tool * U_i         # Initial tool position
    PC = C_i - P_i
    alpha = 90 - theta
    V_i = rotate_vector(PC, alpha)   # Initial feed direction

    tool_path = [P_i]  # List to store tool path points

    # Iterative Tool Path Calculation
    while t_i < t_max:
        t_i += delta_t
        if t_i > t_max:
            t_i = t_max
        C_next = c(t_i)
        print(f't_i: {t_i}')        
        print(f'C_next: {C_next}')
        
        # Find intersection of half-line P_i + s * V_i with circle at C_next
        D = P_i - C_next
        a = np.dot(V_i, V_i)
        b = 2 * np.dot(D, V_i)
        c_coeff = np.dot(D, D) - r_tool**2
        discriminant = b**2 - 4 * a * c_coeff
        
        if discriminant < 0:
            print("No intersection found")
            break
        
        sqrt_disc = np.sqrt(discriminant)
        s1 = (-b - sqrt_disc) / (2 * a)
        s2 = (-b + sqrt_disc) / (2 * a)
        positive_s = [s for s in [s1, s2] if s > 0]
        
        if not positive_s:
            print("No positive intersection found")
            break
        
        s = min(positive_s)  # Choose smallest positive s
        P_next = P_i + s * V_i
        
        # Update feed direction
        PC_next = C_next - P_next
        V_next = rotate_vector(PC_next, alpha)
        
        # Update current position and direction
        P_i = P_next
        V_i = V_next
        tool_path.append(P_i)
    
    return np.array(tool_path)

def main():
    # Get geometry and validate
    geometry = get_geometry()
    if len(geometry) != 1:
        print("Please select exactly two objects: a boundary polygon (active) and a path.")
        return
    else:
        contour_points = geometry[0]

    # Create contour
    points, t = create_contour_points(contour_points)
    
    # Create interpolation functions
    c, tangent = create_interpolation_functions(points, t)

    # print(f'tangent: {tangent}')
    
    # Calculate tool path with increased delta_t for lower resolution
    tool_path = calculate_tool_path(c, tangent, t, delta_t=2)  # Increased from 0.1 to 0.5
    
    tool_path_cutted = shapely.geometry.LineString(tool_path).buffer(5, quad_segs=32, cap_style='round').exterior.coords
    # create_ring_object(tool_path_cutted, "tool_path")
    create_line_object(tool_path, "tool_path_org")

if __name__ == "__main__":
    main()
