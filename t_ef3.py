import numpy as np
from scipy.optimize import least_squares
import time

def fit_ellipse_5point(points, bounds, initial_guess, method='trf', max_nfev=50):
    """
    Fast 5-point ellipse fitting using scipy.optimize.least_squares
    
    Parameters:
    -----------
    points : ndarray, shape (5, 2)
        Input points to fit
    bounds : tuple of arrays
        (lower_bounds, upper_bounds) for [x, y, a, b, theta]
    initial_guess : ndarray, shape (5,)
        Initial parameters [x, y, a, b, theta]
    method : str, default 'trf'
        Optimization method ('trf', 'dogbox', 'lm')
    max_nfev : int, default 50
        Maximum function evaluations for speed
    
    Returns:
    --------
    params : ndarray
        Optimized parameters [x, y, a, b, theta]
    result : OptimizeResult
        Full optimization result
    """
    
    # Vectorized residual calculation
    def ellipse_residual(params, points):
        x0, y0, a, b, theta = params
        
        # Vectorized distance calculation
        dx = points[:, 0] - x0
        dy = points[:, 1] - y0
        
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # Rotate points to ellipse coordinate system
        x_rot = cos_theta * dx + sin_theta * dy
        y_rot = -sin_theta * dx + cos_theta * dy
        
        # Ellipse equation: (x_rot/a)^2 + (y_rot/b)^2 - 1 = 0
        residuals = (x_rot / a) ** 2 + (y_rot / b) ** 2 - 1.0
        
        return residuals
    
    # Fast Jacobian approximation
    def ellipse_jacobian(params, points):
        x0, y0, a, b, theta = params
        
        dx = points[:, 0] - x0
        dy = points[:, 1] - y0
        
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        x_rot = cos_theta * dx + sin_theta * dy
        y_rot = -sin_theta * dx + cos_theta * dy
        
        # Pre-compute common terms
        x2_a2 = x_rot**2 / a**2
        y2_b2 = y_rot**2 / b**2
        
        # Jacobian matrix
        J = np.empty((len(points), 5))
        
        # Partial derivatives
        J[:, 0] = -2 * (cos_theta * x_rot / a**2 - sin_theta * y_rot / b**2)
        J[:, 1] = -2 * (sin_theta * x_rot / a**2 + cos_theta * y_rot / b**2)
        J[:, 2] = -2 * x2_a2 / a
        J[:, 3] = -2 * y2_b2 / b
        J[:, 4] = 2 * (x_rot * y_rot / a**2 - x_rot * y_rot / b**2)
        
        return J
    
    # Optimize with bounds
    result = least_squares(
        ellipse_residual,
        initial_guess,
        jac=ellipse_jacobian,
        bounds=bounds,
        method=method,
        args=(points,),
        max_nfev=max_nfev,
        ftol=1e-6,
        xtol=1e-6,
        gtol=1e-6,        
        diff_step=1e-8  # Smaller step for numerical derivatives
    )
    
    return result.x, result

# Test with your data
points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

bounds = ([-18170, -9679, 30, 20, np.deg2rad(320)],  # min: x,y, a,b, theta
          [-18130, -9625, 60, 40, np.deg2rad(340)])   # max

initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)

# Single run test
start_time = time.time()
# for _ in range(20):
params, result = fit_ellipse_5point(points, bounds, initial_guess)
single_time = time.time() - start_time

print("=== Single Run Results ===")
print(f"Optimized parameters: x={params[0]:.3f}, y={params[1]:.3f}, a={params[2]:.3f}, b={params[3]:.3f}, theta={np.rad2deg(params[4]):.2f}°")
print(f"Residual norm: {result.cost:.6f}")
print(f"Function evaluations: {result.nfev}")
print(f"Time: {single_time}")
