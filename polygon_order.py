from shapely.geometry import Polygon

# Create a CW square
# Vertices: (0,0), (0,1), (1,1), (1,0) -> CW
polygon_cw_exterior = Polygon([(0, 0), (0, 1), (1, 1), (1, 0)])

print(f"Original CW exterior is CCW: {polygon_cw_exterior.exterior.is_ccw}") # Expected: False

# Normalize it
normalized_polygon_cw = polygon_cw_exterior.reverse()

print(f"Normalized exterior is CCW: {normalized_polygon_cw.exterior.is_ccw}") # Expected: True

# Let's also check the coordinates to see if they were reversed
print(f"Original coordinates: {list(polygon_cw_exterior.exterior.coords)}")
print(f"Normalized coordinates: {list(normalized_polygon_cw.exterior.coords)}")