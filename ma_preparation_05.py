import numpy as np
import bpy
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point
from shapely import prepare, crosses
import math
import random


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []
    

def get_collection(name: str, rand_int: int) -> bpy.types.Collection:
    """Get a collection by name, creating it if it doesn't exist."""
    name = name + str(rand_int)
    if name not in bpy.data.collections:
        bpy.data.collections.new(name)
        bpy.context.scene.collection.children.link(bpy.data.collections[name])
    return bpy.data.collections[name]


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def make_medial_edges(contour):
    pv = pyvoronoi.Pyvoronoi(100)

    # Process the contour (exterior polygon)    
    coords = list(contour.exterior.coords)    

    # Keep track of which sites are segments
    segment_sites = []

    # Add segments for each edge of the contour (excluding the last point which is same as first)
    for i in range(len(coords) - 1):
        start = coords[i]
        end = coords[i + 1]
        site_index = pv.AddSegment([start, end])
        segment_sites.append(site_index)

    # Process islands (holes) if provided
    if contour.interiors:
        # If islands is a MultiPolygon, iterate through its polygons
        for island in contour.interiors:
            hole_coords = list(island.coords)
            # Add segments for each edge of the island
            for i in range(len(hole_coords) - 1):
                start = hole_coords[i]
                end = hole_coords[i + 1]
                site_index = pv.AddSegment([start, end])
                segment_sites.append(site_index)

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    # Collect all edges data first
    linear_edges = []
    linear_boundary_edges = []
    curved_edges = []
    twins = []

    contour_boundary_buffer = contour.buffer(-1e-2)
    prepare(contour_boundary_buffer)            

    for cell in cells:
        for edge_idx in cell.edges:
            e = edges[edge_idx]
            
            if not e.is_primary:
                continue

            # Skip edges with invalid vertices
            if e.start == -1 or e.end == -1:
                continue

            startVertex = vertices[e.start]            
            endVertex = vertices[e.end]
            mid_point = ((startVertex.X + endVertex.X) / 2, (startVertex.Y + endVertex.Y) / 2)

            # Check if points are inside the polygon
            if not contour.contains(Point(mid_point)):
                continue

            start_point = (startVertex.X, startVertex.Y)
            end_point = (endVertex.X, endVertex.Y)

            # Skip if we've already processed this edge from its twin
            if e.twin in twins:
                continue            
            twins.append(edge_idx)
            
            if e.is_linear:
                coords = [start_point, end_point]
                edge = shapely.geometry.LineString(coords)
                if crosses(contour_boundary_buffer, edge):
                    linear_boundary_edges.append(edge)
                else:
                    linear_edges.append(edge)   
                
            else:
                # Calculate distance only for curved edges
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                         (startVertex.Y - endVertex.Y)**2) / 10                
                points = pv.DiscretizeCurvedEdge(edge_idx, max_distance*1, parabola_equation_tolerance=0.001)
                curve = shapely.geometry.LineString(points).simplify(0.05)
                curved_edges.append(curve)

    # Extract medial axis - these are the interior edges of the Voronoi diagram
    return linear_edges, linear_boundary_edges, curved_edges

def main():
    rand_int = random.randint(0, 1000000)
    geometry = get_geometry()
    boundary_collection = get_collection("boundary", rand_int)
    ma_collection = get_collection("ma", rand_int)

    if len(geometry) == 1:
        polygon = shapely.geometry.Polygon(shell=geometry[0])
    else:
        holes = [geom for geom in geometry[1:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)    
        
    linear_edges, linear_boundary_edges, curved_edges = make_medial_edges(polygon)
    for i, edge in enumerate(linear_edges+curved_edges):
        obj = create_line_object(edge.coords, f"edge_{i}", color=(0.6, 0, 0.7, 1))
        try:
            ma_collection.objects.link(obj)
            bpy.context.collection.objects.unlink(obj)
        except:
            pass
    
    for i, edge in enumerate(linear_boundary_edges):
        obj = create_line_object(edge.coords, f"edge_boundary_{i}", color=(1, 0, 0, 1))
        try:
            boundary_collection.objects.link(obj)
            bpy.context.collection.objects.unlink(obj)
        except:
            pass
    

if __name__ == "__main__":
    main()
