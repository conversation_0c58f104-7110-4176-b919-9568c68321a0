import bpy
from mathutils import Vector
import math
import time
from scipy.integrate import odeint

from pyclothoids import Clothoid

import numpy as np
import trimesh # Assuming trimesh is used for PathSample
import shapely.geometry # Assuming shapely is used for Polygon

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) == 2:
        contour = shapely.geometry.Polygon(shell=geometry[0])
        path = shapely.geometry.LineString(geometry[1])
    else:
        print("Please select a valid mesh object.")
        return []
    return contour, path


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def create_circle_object(center, radius, name):
    """Create a circle object in Blender to visualize a maximally inscribed disc.

    Args:
        center: [x, y] coordinates of the circle center
        radius: Radius of the circle
        name: Name for the created circle object

    Returns:
        The created Blender object
    """
    # Create a mesh for the circle
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Number of vertices in the circle
    vertices_count = 32

    # Generate vertices around the circle
    verts = []
    for i in range(vertices_count):
        angle = 2.0 * math.pi * i / vertices_count
        x = center[0] + radius * math.cos(angle)
        y = center[1] + radius * math.sin(angle)
        verts.append(Vector((x, y, 0)))

    # Create edges around the circle (connect vertices to form a loop)
    edges = [(i, (i+1) % vertices_count) for i in range(vertices_count)]

    # Create the mesh from vertices and edges (no faces)
    mesh.from_pydata(verts, edges, [])
    mesh.update()

    # Set display properties
    mat = bpy.data.materials.new(name=f"{name}_material")
    mat.diffuse_color = (0.2, 0.8, 0.2, 1.0)  # Solid green for better visibility as a line
    obj.data.materials.append(mat)

    return obj


def boundary_distance(polygon, points):
    """
    Find the distance between a polygon's boundary and an
    array of points.

    Parameters
    -------------
    polygon : shapely.geometry.Polygon
      Polygon to query
    points : (n, 2) float
      2D points

    Returns
    ------------
    distance : (n,) float
      Minimum distance from each point to polygon boundary
    """     
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front(path, polygon, step, step_treshold=0.33, min_radius=5):
    def find_min_radii_index(radii: np.ndarray, min_radius: float = 5.0):
        """
        Finds the index where radius drops below min_radius value,
        within the initial strictly decreasing segment of an array.
        Only considers segments where values actually drop below min_radius.

        Args:
            radii (np.ndarray): The input NumPy array of radii.
            min_radius (float): The minimum radius threshold.

        Returns:
            int or None: The index where radius drops below min_radius, 
                        or None if no such drop occurs.
        """    
        
        if radii.size == 0:
            return None
            
        # Find where array stops decreasing
        decreasing_end = np.where(np.diff(radii) >= 0)[0]
        end_idx = decreasing_end[0] + 1 if decreasing_end.size > 0 else len(radii)
        
        # Find where values drop below min_radius in decreasing segment
        drops_below = np.where(radii[:end_idx] < min_radius)[0]
        if drops_below.size == 0:
            return None
            
        # Return first index where value drops below min_radius
        return drops_below[0]


    path = np.asanyarray(path)
    if not trimesh.util.is_shape(path, (-1, 2)):
         raise ValueError("Path should be N Gons x 2 dimensions")
    if not isinstance(polygon, shapely.geometry.Polygon):
         raise ValueError("Polygon must be a shapely Polygon object")
    if not path.shape[0] > 1:
         raise ValueError("Path must have more than one point")

    # create a sampler object which can sample the path by distance
    sampler = trimesh.path.traversal.PathSample(path)
    # sample finely relative to the desired step size
    sample_step = step / 25.0
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)

    # get the points and boundary distance (radius) for each sample
    points = sampler.sample(sample_distances)
    radii = boundary_distance(polygon=polygon, points=points)    
    
    end_index = find_min_radii_index(radii, min_radius)
    if end_index is None:
        end_index = len(radii) - 1    
    end_distance = sample_distances[end_index]    
                
    # store results: distance along path, and radius
    # always include the first point
    result_distances = [0.0]
    result_radii = [radii[0]]
    # store the index of the last point added
    last_idx = 0

    # loop through sampled points
    for i in range(1, len(points)):
        if sample_distances[i] > end_distance:
            # vector between candidate point and last point added
            vector = points[end_index] - points[last_idx]
            # calculate the edge-to-edge distance between circles
            front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[end_index]            
            if front_distance >= (step_treshold * step):
                result_distances.append(end_distance)
                result_radii.append(radii[end_index])
            break

        # vector between candidate point and last point added
        vector = points[i] - points[last_idx]
        # calculate the edge-to-edge distance between circles
        front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[i]
        # if the distance exceeds threshold add point to results
        if front_distance >= step:
            result_distances.append(sample_distances[i])
            result_radii.append(radii[i])
            last_idx = i    

    return np.array(result_distances), np.array(result_radii)


# --- Arc Generation Function ---
def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, edge_length):
    """Generate points along an arc in counter-clockwise direction.
    
    Args:
        center: [x, y] coordinates of arc center
        radius: arc radius (must be positive)
        angle_start_rad: start angle in radians
        angle_end_rad: end angle in radians
        edge_length: desired length between points along the arc
    """
    center = np.array(center)
    radius = max(1e-9, radius)  # Ensure positive radius
    
    # Ensure CCW direction by adjusting end angle if needed
    # while angle_end_rad < angle_start_rad:
    #     angle_end_rad += 2 * np.pi
    
    # Calculate arc length
    arc_length = radius * (angle_end_rad - angle_start_rad)
    
    # Calculate number of points based on edge_length
    num_points = max(2, int(np.ceil(arc_length / edge_length)) + 1)
    
    angles = np.linspace(angle_start_rad, angle_end_rad, 10)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


# --- Helper to Calculate Arc Angles (Simplified Usage) ---
def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculates potentially extended start/end angles for the current arc
       based ONLY on the immediate next step.
    """
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)

    if segment_len < 1e-9: # Handle coincident points
        # Cannot determine direction, default to horizontal sweep
        angle_dir = 0.0
    else:
        dir_vec = segment_vec / segment_len
        angle_dir = np.arctan2(dir_vec[1], dir_vec[0])

    Std_Start_Angle = angle_dir + np.pi / 1.95
    Std_End_Angle = angle_dir - np.pi / 1.95
    delta_angle = 0.0

    # Extend only if next radius is larger AND distance allows calculation
    if R_next > R_current and segment_len > 1e-9:
        delta_R = R_next - R_current
        ratio = delta_R / segment_len
        # Use arcsin, clipped to handle floating point / geometry issues
        delta_angle = np.arcsin(np.clip(ratio, 0.0, 1.0))

    Actual_Start_Angle = Std_Start_Angle + delta_angle
    Actual_End_Angle = Std_End_Angle - delta_angle
    return Actual_Start_Angle, Actual_End_Angle


def cubic_bezier(t, p0, p1, p2, p3):
    p0, p1, p2, p3 = map(np.array, [p0, p1, p2, p3])
    t = np.clip(t, 0.0, 1.0)
    return ((1 - t)**3 * p0 + 3 * (1 - t)**2 * t * p1 + 3 * (1 - t) * t**2 * p2 + t**3 * p3)


def generate_bezier_transition(p0, t0, p3, t3, num_points=20, default_alpha_beta_factor=0.5, alpha=None, beta=None):
    p0, p3 = np.array(p0), np.array(p3)
    t0_norm, t3_norm = np.linalg.norm(t0), np.linalg.norm(t3)
    _t0 = np.array(t0) / t0_norm if t0_norm > 1e-9 else np.array([1.0, 0.0])
    _t3 = np.array(t3) / t3_norm if t3_norm > 1e-9 else np.array([1.0, 0.0])
    # Optional warnings removed for brevity
    if alpha is None or beta is None:
        dist = np.linalg.norm(p3 - p0)
        alpha_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        beta_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        _alpha = alpha if alpha is not None else alpha_calc
        _beta = beta if beta is not None else beta_calc
    else: _alpha, _beta = alpha, beta
    _alpha, _beta = max(0, _alpha), max(0, _beta)
    p1, p2 = p0 + _alpha * _t0, p3 - _beta * _t3
    if num_points < 2: return np.array([p0]) if num_points == 1 else np.empty((0, 2))
    t_values = np.linspace(0, 1, num_points)
    return np.array([cubic_bezier(t, p0, p1, p2, p3) for t in t_values])


def distance(p1, p2):
    """Calculate Euclidean distance between two points."""
    return np.linalg.norm(p1 - p2)

def resample_curve_by_length(points, target_edge_length):
    """
    Resamples a 2D curve by placing points at target distance intervals
    along the original curve path.

    Args:
        points (np.array): Input curve points (N, 2).
        target_edge_length (float): The desired distance between points
                                    along the curve path.

    Returns:
        np.array: Resampled curve points (M, 2).
    """
    if points.shape[0] < 2 or target_edge_length <= 0:
        return points # Cannot resample

    resampled_points = [points[0]]
    current_target_dist = target_edge_length
    
    # Calculate cumulative distances along the original polyline
    segment_lengths = np.linalg.norm(np.diff(points, axis=0), axis=1)
    cumulative_lengths = np.concatenate(([0], np.cumsum(segment_lengths)))
    total_length = cumulative_lengths[-1]

    if total_length <= 0: # Handle case of single point or coincident points
        return points[0:1] if points.shape[0] >= 1 else np.empty((0,2))

    while current_target_dist < total_length:
        # Find the segment where the target distance falls
        # Find first index where cumulative length exceeds the target
        segment_idx = np.searchsorted(cumulative_lengths, current_target_dist, side='right') - 1
        
        # Ensure segment_idx is valid (should be unless current_target_dist is exactly 0)
        segment_idx = max(0, segment_idx) 

        # Points defining the segment
        p1 = points[segment_idx]
        p2 = points[segment_idx + 1]
        
        # Distance needed *within* this segment
        dist_into_segment = current_target_dist - cumulative_lengths[segment_idx]
        
        segment_vec = p2 - p1
        seg_len = segment_lengths[segment_idx] # Use pre-calculated length

        if seg_len > 1e-9: # Avoid division by zero for zero-length segments
            fraction = dist_into_segment / seg_len
            # Interpolate the new point
            new_point = p1 + fraction * segment_vec
            resampled_points.append(new_point)
        else:
            # If segment has zero length, effectively just use p1 (or p2)
            # This might happen if target distance falls exactly on an original vertex
            # Check if the last added point is already p1 to avoid duplicates
            if len(resampled_points) == 0 or not np.allclose(resampled_points[-1], p1):
                 resampled_points.append(p1)

        # Move to the next target distance
        current_target_dist += target_edge_length

    # Always include the last point of the original curve
    if not np.allclose(resampled_points[-1], points[-1]):
         resampled_points.append(points[-1])

    return np.array(resampled_points)


def main():
    #global parameters
    edge_length = 0.15

    # Arc0 parameters
    arc0_wide = np.pi
    arc0_angle_shift = 0
    a0_center = np.array([0, 0])
    a0_radius = 1.5
    a0_start_angle = 0 + arc0_angle_shift
    a0_end_angle = a0_start_angle + arc0_wide

    # Arc1 parameters
    arc1_wide = np.pi / 2
    arc1_angle_shift = np.pi/4
    a1_center = np.array([0, 0.8])
    a1_radius = 1
    a1_start_angle = 0 + arc1_angle_shift
    a1_end_angle = a1_start_angle + arc1_wide
    
    # Calculate start point and direction for clothoid
    x0 = a0_center[0] + a0_radius * np.cos(a0_end_angle)  # x = 1
    y0 = a0_center[1] + a0_radius * np.sin(a0_end_angle)  # y = 0
    theta0 = a0_end_angle + np.pi/2  # direction at start = 0 radians
    # create_line_object(generate_arc_points(a0_center, a0_radius, a0_start_angle, a0_end_angle, edge_length), "arc0")
    # create_line_object(generate_arc_points(a1_center, a1_radius, a1_start_angle, a1_end_angle, edge_length), "arc1")
    
    # Calculate end point and direction for clothoid
    x1 = a1_center[0] + a1_radius * np.cos(a1_start_angle)    # x = 0
    y1 = a1_center[1] + a1_radius * np.sin(a1_start_angle)    # y = 1
    theta1 = a1_start_angle + np.pi/2 # direction at end = π/2 radians

    # Create clothoid with these parameters
    # print(f'theta0: {np.rad2deg(theta0)}')
    # print(f'theta1: {np.rad2deg(theta1)}')
    clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)
    
    clothoid_resoulution = max(2, int(np.ceil(clothoid.length / edge_length)) + 1)
    points = clothoid.SampleXY(clothoid_resoulution)
    # create_line_object(np.array(points).T, "clothoid")

    create_line_object(generate_arc_points(a0_center, a0_radius, np.pi, -0.5, edge_length), "arc0")

if __name__ == "__main__":
    main()
