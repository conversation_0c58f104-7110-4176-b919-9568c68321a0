import numpy as np

def compute_max_engagement_angles(loop_radii, center_distances, cutter_radius):
    """
    Computes the maximum engagement angle for each transition in a trochoidal milling operation.

    Parameters:
    - loop_radii: np.ndarray of loop radii
    - center_distances: np.ndarray of distances between centers of consecutive loops
    - cutter_radius: float, radius of the cutter

    Returns:
    - np.ndarray of maximum engagement angles in radians
    """
    angles = []
    for i in range(len(loop_radii) - 1):
        r_prev = loop_radii[i]
        r_next = loop_radii[i + 1]
        d = center_distances[i + 1]  # Distance between loop i and i+1

        a = r_prev - cutter_radius
        b = r_next - cutter_radius

        # Check if cutter is inside both loops and if triangle inequality holds
        if a <= 0 or b <= 0 or (a + b < d):
            angles.append(0.0)
            continue

        # Compute cosine of the angle
        numerator = a**2 + b**2 - d**2
        denominator = 2 * a * b

        # Clip to avoid numerical errors
        cos_theta = np.clip(numerator / denominator, -1.0, 1.0)
        theta_rad = np.arccos(cos_theta)
        angles.append(theta_rad)

    return np.array(angles)

loop_radii = np.array([20.0, 25.0])
center_distances = np.array([0.0, 10.0])
cutter_radius = 10.0

max_engagement_angles = compute_max_engagement_angles(loop_radii, center_distances, cutter_radius)
print("Max Engagement Angles (radians):", np.rad2deg(max_engagement_angles))