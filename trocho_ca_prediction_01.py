import numpy as np

def _wrap(angle):
    """Wrap angle to the interval (-π, π]."""
    return (angle + np.pi) % (2 * np.pi) - np.pi


def _front_side_engagement_vectorized(beta, theta, feed_dir):
    """
    Vectorized version of front-side engagement calculation.
    """
    # Front-half interval centred on the feed vector
    front_start, front_end = -np.pi / 2, np.pi / 2
    
    # Express the cleared-area wedge relative to the feed direction
    beta_rel = _wrap(beta - feed_dir)
    w1, w2 = beta_rel - theta, beta_rel + theta  # wedge limits
    
    # Handle wrap-around cases
    wrap_left = w1 < -np.pi
    wrap_right = w2 > np.pi
    
    # Calculate overlaps for different cases
    cleared_in_front = np.zeros_like(beta)
    
    # No wrap case
    normal_mask = ~(wrap_left | wrap_right)
    cleared_in_front[normal_mask] = np.maximum(
        0.0, 
        np.minimum(w2[normal_mask], front_end) - np.maximum(w1[normal_mask], front_start)
    )
    
    # Left wrap case
    if np.any(wrap_left):
        left_overlap = np.maximum(0.0, np.minimum(np.pi, front_end) - np.maximum(w1[wrap_left] + 2*np.pi, front_start))
        right_overlap = np.maximum(0.0, np.minimum(w2[wrap_left], front_end) - np.maximum(-np.pi, front_start))
        cleared_in_front[wrap_left] = left_overlap + right_overlap
    
    # Right wrap case
    if np.any(wrap_right):
        left_overlap = np.maximum(0.0, np.minimum(np.pi, front_end) - np.maximum(w1[wrap_right], front_start))
        right_overlap = np.maximum(0.0, np.minimum(w2[wrap_right] - 2*np.pi, front_end) - np.maximum(-np.pi, front_start))
        cleared_in_front[wrap_right] = left_overlap + right_overlap
    
    # π is the full front half (180°)
    return np.maximum(0.0, np.pi - cleared_in_front)


def max_front_engagement(R, r_prev, r_next, d_off, nsamples=360,
                         return_where=False):
    """
    Maximum *front-side* engagement angle (deg) along one trochoid loop.
    Fully vectorized implementation.
    """
    R_prev = R + r_prev
    ψ = np.linspace(0, 2 * np.pi, nsamples, endpoint=False)

    # Cutter-centre coordinates
    x = d_off + r_next * np.cos(ψ)
    y = r_next * np.sin(ψ)
    d = np.hypot(x, y)  # distance to previous loop centre

    # Feed direction (tangent to the loop)
    tx = -r_next * np.sin(ψ)
    ty = r_next * np.cos(ψ)
    feed_dir = np.arctan2(ty, tx)  # angle of (tx, ty)

    beta = np.arctan2(-y, -x)  # to previous loop centre

    # Create masks for different cases
    no_intersection_mask = d >= R + R_prev
    inside_clearance_mask = d <= abs(R_prev - R)
    
    # Calculate engagement angles
    engaged = np.zeros_like(d)
    
    # No intersection → full 180°
    engaged[no_intersection_mask] = np.pi
    
    # Calculate theta for intersection cases
    calc_mask = ~(no_intersection_mask | inside_clearance_mask)
    
    if np.any(calc_mask):
        d_calc = d[calc_mask]
        theta = np.arccos(
            (d_calc**2 + R**2 - R_prev**2) / (2 * d_calc * R)
        )
        engaged[calc_mask] = _front_side_engagement_vectorized(
            beta[calc_mask], theta, feed_dir[calc_mask]
        )

    idx = np.argmax(engaged)
    φ_max = engaged[idx]

    if return_where:
        return np.degrees(φ_max), ψ[idx]
    return np.degrees(φ_max)

R       = 6.0   # cutter radius [mm]
r_prev  = 20.0  # previous loop radius
r_next  = 22.0  # current  loop radius
d_off   = 2.0   # distance between loop centres

φ_max_deg, ψ_at_max = max_front_engagement(
    R, r_prev, r_next, d_off, return_where=True
)

print(f"Maximum front-side engagement : {φ_max_deg:.1f}°")
print(f"Occurs at ψ = {ψ_at_max:.3f} rad")
