import numpy as np

def max_engagement_angle(r1, r2, l, r):
    """
    Calculate the maximum engagement angle for a trochoid loop between two circles.
    
    Parameters:
    - r1: Radius of the first circle
    - r2: Radius of the second circle
    - l: Distance between the centers of the two circles
    - r: Tool radius
    
    Returns:
    - Maximum engagement angle in radians
    """
    R1 = r1 + r  # Offset radius of first circle
    R2 = r2 + r  # Offset radius of second circle
    theta = np.linspace(0, 2 * np.pi, num=100)  # Angles for one full rotation
    max_alpha = 0.0

    for th in theta:
        # Tool center position when rolling around second circle
        C = np.array([l + r2 * np.cos(th), r2 * np.sin(th)])
        # Tangent point on the offset second circle
        N = np.array([l + R2 * np.cos(th), R2 * np.sin(th)])
        O1 = np.array([0.0, 0.0])  # First circle center at origin
        
        # Distance from O1 to C
        d = np.linalg.norm(C - O1)
        
        # Check for intersection between tool circle and C_w1
        if d < abs(R1 - r) or d > R1 + r:
            continue  # No intersection
        
        try:
            # <PERSON>le to find intersection points
            gamma = np.arccos((d**2 + R1**2 - r**2) / (2 * d * R1))
        except ValueError:
            continue  # Skip if arccos argument is invalid
        
        # Unit vector from O1 to C
        u = (C - O1) / d
        # Perpendicular vector
        v = np.array([-u[1], u[0]])
        
        # Intersection points D1 and D2
        D1 = O1 + R1 * (u * np.cos(gamma) + v * np.sin(gamma))
        D2 = O1 + R1 * (u * np.cos(gamma) - v * np.sin(gamma))
        
        # Distances from N to D1 and D2
        DN1 = np.linalg.norm(D1 - N)
        DN2 = np.linalg.norm(D2 - N)
        
        # Engagement angles
        alpha1 = np.arccos(1 - (DN1**2) / (2 * r**2))
        alpha2 = np.arccos(1 - (DN2**2) / (2 * r**2))
        
        # Take the maximum for this position
        current_max = max(alpha1, alpha2)
        max_alpha = max(max_alpha, current_max)
    
    return max_alpha

def compute_max_engagement_angles(sample_distances, radii, tool_radius):
    """
    Compute maximum engagement angles for all pairs of consecutive circles.
    
    Parameters:
    - sample_distances: Array of cumulative distances
    - radii: Array of circle radii
    - tool_radius: Radius of the tool
    
    Returns:
    - Array of maximum engagement angles in radians
    """
    max_alphas = []
    
    for n in range(len(radii) - 1):
        r1 = radii[n]
        r2 = radii[n + 1]
        l = sample_distances[n + 1] - sample_distances[n]
        max_alpha = max_engagement_angle(r1, r2, l, tool_radius)
        max_alphas.append(max_alpha)
    
    return np.array(max_alphas)

# Example usage
if __name__ == "__main__":
    sample_distances = np.array([0.0, 18])
    radii = np.array([40, 20])/2
    tool_radius = 10.0  # Assumed tool radius in units consistent with radii
    
    max_angles = compute_max_engagement_angles(sample_distances, radii, tool_radius)
    print("Maximum engagement angles (radians):", np.rad2deg(max_angles))