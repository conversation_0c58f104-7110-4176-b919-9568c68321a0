import numpy as np
from shapely.geometry import LineString

def add_fish_tails_to_polygon(polygon, tail_indices):
    """
    Create a single LineString for a polygon with fish tail loops at specified vertices.
    
    Parameters:
    - polygon: NumPy array of shape (n, 2) with vertex coordinates, v0 at [0,0].
    - tail_indices: Array of vertex indices where tails are added.
    
    Returns:
    - LineString object representing the closed path.
    """
    n = len(polygon)
    if n < 3:
        raise ValueError("Polygon must have at least 3 vertices.")
    if not np.allclose(polygon[0], [0, 0]):
        raise ValueError("Vertex v0 must be at [0,0].")

    # Compute average side length for tail size
    side_lengths = [np.linalg.norm(polygon[i] - polygon[(i + 1) % n]) for i in range(n)]
    average_side = np.mean(side_lengths)
    k = 0.1 * average_side  # Tail length is 10% of average side length

    # Convert tail_indices to a set for O(1) lookup
    tail_set = set(tail_indices)

    # Build the sequence of points
    sequence = []
    for i in range(n):
        vi = polygon[i]
        sequence.append(vi)  # Visit the vertex
        if i in tail_set:
            # Add the tail loop: V -> P1 -> P2 -> V
            P1, P2 = compute_tail_points(i, polygon, k)
            sequence.extend([P1, P2, vi])

    # Close the loop by appending v0
    sequence.append(polygon[0])

    # Create and return the LineString
    return LineString(sequence)

def compute_tail_points(i, polygon, k):
    """
    Compute tail points P1 and P2 for vertex i.
    
    Parameters:
    - i: Index of the vertex.
    - polygon: NumPy array of vertices.
    - k: Length of tail segments.
    
    Returns:
    - P1, P2: NumPy arrays with coordinates of tail points.
    """
    n = len(polygon)
    V = polygon[i]
    A = polygon[(i + 1) % n]  # Next vertex
    B = polygon[(i - 1) % n]  # Previous vertex

    # Vectors from V to adjacent vertices
    VA = A - V
    VB = B - V
    U = VA / np.linalg.norm(VA)  # Normalize
    W = VB / np.linalg.norm(VB)  # Normalize

    # Interior bisector
    D = U + W
    if np.allclose(D, [0, 0]):  # Colinear case
        D = np.array([-U[1], U[0]])  # Perpendicular to U
    else:
        D = D / np.linalg.norm(D)

    E = -D  # Exterior bisector

    # Rotate by ±15 degrees
    theta = np.deg2rad(15)
    R_plus = np.array([[np.cos(theta), -np.sin(theta)], 
                       [np.sin(theta), np.cos(theta)]])
    R_minus = np.array([[np.cos(theta), np.sin(theta)], 
                        [-np.sin(theta), np.cos(theta)]])

    dir1 = R_plus @ E
    dir2 = R_minus @ E

    # Compute tail points
    P1 = V + k * dir1
    P2 = V + k * dir2
    return P1, P2

# Example usage
if __name__ == "__main__":
    # Square polygon: v0 at [0,0]
    polygon = np.array([[0, 0], [1, 0], [1, 1], [0, 1]], dtype=float)
    tail_indices = np.array([0, 2])  # Tails at v0 and v2
    line_string = add_fish_tails_to_polygon(polygon, tail_indices)
    print("LineString coordinates:")
    for coord in line_string.coords:
        print(coord)