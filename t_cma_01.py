from fcmaes import retry
from fcmaes.optimizer import Bite_cpp, De_cpp, Cma_cpp, Sequence
from scipy.optimize import Bounds
import numpy as np
import time

# Define the objective function (sphere function: sum of squares)
def objective_function(x):
    print(x)
    return np.sum(x**2)

# Set up the optimization problem
dim = 3 # number of dimensions
lower_bounds = np.array([-5.0] * dim)  # lower bounds for each dimension
upper_bounds = np.array([5.0] * dim)   # upper bounds for each dimension
initial_guess = np.array([3.0] * dim)  # starting point

time1 = time.time()
ret = retry.minimize(
    fun=objective_function,
    bounds=Bounds(lower_bounds, upper_bounds),
    # workers=1,
    optimizer=Cma_cpp(
        max_evaluations=1,
        guess=initial_guess
    ))

time2 = time.time()


# Print results
print(f"Best value found: {ret.fun}")
print(f"Best solution: {ret.x}")
print(f"Number of evaluations: {ret.nfev}")
print(f'Time: {time2 - time1:.2f}s')