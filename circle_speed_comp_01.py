import numpy as np
from shapely.geometry import Point
import timeit
import math

# --- Method 1: NumPy Calculation ---
def create_circle_numpy(cx, cy, r, num_points):
    """
    Generates circle polygon coordinates using NumPy sin/cos.

    Args:
        cx (float): Center x-coordinate.
        cy (float): Center y-coordinate.
        r (float): Radius.
        num_points (int): Number of vertices in the polygon approximation.

    Returns:
        np.ndarray: Array of shape (num_points, 2) with coordinates.
    """
    if num_points < 3:
        raise ValueError("Number of points must be at least 3")
    # Generate angles from 0 to 2*pi (exclusive of 2*pi)
    angles = np.linspace(0, 2 * np.pi, num_points, endpoint=False)
    x = cx + r * np.cos(angles)
    y = cy + r * np.sin(angles)
    # Stack x and y columns horizontally
    coords = np.column_stack((x, y))
    return coords

# --- Method 2: <PERSON><PERSON><PERSON><PERSON> Buffering ---
def create_circle_shapely(cx, cy, r, num_points):
    """
    Generates circle polygon coordinates using <PERSON><PERSON><PERSON><PERSON>'s buffer method.

    Args:
        cx (float): Center x-coordinate.
        cy (float): Center y-coordinate.
        r (float): Radius.
        num_points (int): Desired approximate number of vertices.
                         Shapely uses 'resolution' (segments per quarter circle).

    Returns:
        np.ndarray: Array of shape (approx_num_points, 2) with coordinates.
                    The actual number of points might differ slightly from num_points.
                    Excludes the duplicate closing point.
    """
    if num_points < 3:
        raise ValueError("Number of points must be at least 3")

    point = Point(cx, cy)
    # Shapely's resolution is number of segments per *quarter* circle
    # We want roughly num_points total, so resolution should be num_points / 4
    # Ensure resolution is at least 1 (minimum 4 points total)
    resolution = max(1, int(round(num_points / 4)))
    polygon = point.buffer(r, resolution=resolution)

    # Extract exterior coordinates and convert to NumPy array
    # Shapely's exterior coords include a duplicate closing point, remove it [:-1]
    # Ensure it's actually a polygon (buffer can sometimes return other things if radius is 0?)
    if polygon.geom_type == 'Polygon':
        coords = np.array(polygon.exterior.coords[:-1])
        return coords
    else:
        # Handle cases like zero radius if necessary, or just return empty
        print(f"Warning: Shapely buffer did not return a Polygon (returned {polygon.geom_type}). Check radius.")
        return np.empty((0, 2))


# --- Comparison Setup ---
center_x = 10.0
center_y = 20.0
radius = 5.0
# List of different numbers of points (vertices) to test
num_points_list = [16, 32, 64, 128, 256, 512, 1024]
# How many times to generate the circle within each timing run
# Increase this if individual calls are too fast for reliable timing
num_iterations = 100
# How many times to repeat the timing measurement for each method/num_points
num_repeats = 5

print("--- Circle Generation Performance Comparison ---")
print(f"Center: ({center_x}, {center_y}), Radius: {radius}")
print(f"Number of generations per timing sample: {num_iterations}")
print("-" * 50)
print(f"{'Num Points':<12} | {'NumPy Time (ms)':<18} | {'Shapely Time (ms)':<20} | {'Actual Shapely Pts':<18}")
print("-" * 75)

results_numpy = {}
results_shapely = {}
actual_shapely_points = {}

# --- Run Timing ---
for n_pts in num_points_list:
    # Check output consistency (optional, but good practice)
    # We run this *outside* the timing loop
    try:
        coords_np = create_circle_numpy(center_x, center_y, radius, n_pts)
        coords_sh = create_circle_shapely(center_x, center_y, radius, n_pts)
        actual_shapely_points[n_pts] = coords_sh.shape[0] # Store actual points generated by Shapely
        # print(f"N={n_pts}: NumPy shape={coords_np.shape}, Shapely shape={coords_sh.shape}")
    except Exception as e:
        print(f"Error during pre-check for N={n_pts}: {e}")
        continue

    # Time NumPy
    setup_code_np = f'''
import numpy as np
from __main__ import create_circle_numpy
cx, cy, r, n_pts = {center_x}, {center_y}, {radius}, {n_pts}
    '''
    stmt_code_np = f'''
for _ in range({num_iterations}):
    create_circle_numpy(cx, cy, r, n_pts)
    '''
    times_np = timeit.repeat(stmt=stmt_code_np, setup=setup_code_np, number=1, repeat=num_repeats)
    results_numpy[n_pts] = min(times_np) # Store the minimum time

    # Time Shapely
    setup_code_sh = f'''
import numpy as np
from shapely.geometry import Point
from __main__ import create_circle_shapely
cx, cy, r, n_pts = {center_x}, {center_y}, {radius}, {n_pts}
    '''
    stmt_code_sh = f'''
for _ in range({num_iterations}):
    create_circle_shapely(cx, cy, r, n_pts)
    '''
    times_sh = timeit.repeat(stmt=stmt_code_sh, setup=setup_code_sh, number=1, repeat=num_repeats)
    results_shapely[n_pts] = min(times_sh) # Store the minimum time

    # Print results for this n_pts
    time_np_ms = (results_numpy[n_pts] / num_iterations) * 1000
    time_sh_ms = (results_shapely[n_pts] / num_iterations) * 1000
    shapely_pts_count = actual_shapely_points.get(n_pts, 'N/A') # Get actual point count

    print(f"{n_pts:<12} | {time_np_ms:<18.4f} | {time_sh_ms:<20.4f} | {shapely_pts_count:<18}")


# --- Conclusion ---
print("-" * 75)
print("\nSummary:")
if all(results_numpy[n] < results_shapely[n] for n in num_points_list if n in results_numpy and n in results_shapely):
    print("NumPy (sin/cos) was consistently faster.")
elif all(results_numpy[n] > results_shapely[n] for n in num_points_list if n in results_numpy and n in results_shapely):
     print("Shapely (buffer) was consistently faster.")
else:
     print("Performance varied. Check the table for details.")
     # Find crossover point if any
     faster_method = None
     for n in num_points_list:
         if n in results_numpy and n in results_shapely:
             current_faster = "NumPy" if results_numpy[n] < results_shapely[n] else "Shapely"
             if faster_method is None:
                 faster_method = current_faster
             elif faster_method != current_faster:
                 print(f"Performance crossover may occur around N={n} points.")
                 break
     else:
          if faster_method:
                print(f"{faster_method} appears faster across the tested range.")


print(f"\nNote: Times are average per single circle generation (in milliseconds), based on the minimum of {num_repeats} runs of {num_iterations} generations each.")
print("Note: Shapely's actual vertex count might differ slightly from the requested 'Num Points'.")