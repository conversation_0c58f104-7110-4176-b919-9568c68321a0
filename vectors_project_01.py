import numpy as np

def project_on_basis(v2, v1):
    """
    Projects vector v2 onto the coordinate system defined by vector v1.

    Args:
        v2 (np.ndarray): The 2D vector to be projected.
        v1 (np.ndarray): The 2D normalized vector that defines the new system's x-axis.

    Returns:
        np.ndarray: The coordinates of v2 in the new coordinate system.
    """
    # Ensure vectors are numpy arrays
    v1 = np.asarray(v1)
    v2 = np.asarray(v2)

    # v1 is the new x-axis (u_x)
    # It must be a unit vector for this to be a valid basis
    u_x = v1 / np.linalg.norm(v1)

    # The new y-axis (u_y) is perpendicular to u_x.
    # In 2D, a vector (x, y) has a perpendicular vector (-y, x).
    u_y = np.array([-u_x[1], u_x[0]])

    # The new coordinates are the dot products of v2 with the new basis vectors.
    new_x = np.dot(v2, u_x)
    new_y = np.dot(v2, u_y)

    return np.array([new_x, new_y])

# --- Example Usage ---
# Define two 2D normalized vectors
# v1 is rotated 30 degrees from the original x-axis
angle_v1 = np.deg2rad(30)
v1 = np.array([np.cos(angle_v1), np.sin(angle_v1)])

# v2 is rotated 75 degrees from the original x-axis
angle_v2 = np.deg2rad(75)
v2 = np.array([np.cos(angle_v2), np.sin(angle_v2)])

# Project v2 onto the coordinate system of v1
v2_in_v1_coords = project_on_basis(v2, v1)

print(f"Original v1: {v1}")
print(f"Original v2: {v2}")
print("-" * 30)
print(f"Coordinates of v2 in v1's system: {v2_in_v1_coords}")

# --- Verification ---
# The angle between v2 and v1 is 75 - 30 = 45 degrees.
# So in the new system, v2 should be a vector at 45 degrees.
# Its coordinates should be (cos(45), sin(45)) because v2 is also normalized.
angle_diff = np.deg2rad(45)
expected_coords = np.array([np.cos(angle_diff), np.sin(angle_diff)])

print(f"Expected coordinates: {expected_coords}")
# Check if the result is close to the expected value
assert np.allclose(v2_in_v1_coords, expected_coords)
print("✅ Result matches expectation.")