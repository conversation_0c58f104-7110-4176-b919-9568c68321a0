import numpy as np
from shapely.geometry import LineString
from shapely.affinity import rotate
from sklearn.decomposition import PCA
import math
import matplotlib.pyplot as plt

# --- Step 1: Helper function to create a full ellipse ---
# Note: A Shapely LineString is open, so we create points for a full loop.
def create_full_ellipse(center=(0, 0), radii=(3, 1), rotation_deg=0, num_points=100):
    """
    Creates a Shapely LineString representing a full, rotated ellipse.
    """
    # Create points from 0 to 2*pi to make a full loop
    t = np.linspace(0, 2 * np.pi, num_points)
    
    # Create points for a standard ellipse at the origin
    x = radii[0] * np.cos(t)
    y = radii[1] * np.sin(t)
    points = np.column_stack([x, y])
    
    # Create a LineString and rotate it
    ellipse_line = LineString(points)
    rotated_line = rotate(ellipse_line, angle=rotation_deg, origin=(0, 0))
    
    # Translate to the final center
    final_points = [(p[0] + center[0], p[1] + center[1]) for p in rotated_line.coords]
    
    # Return a closed LinearRing for better geometric representation
    return LineString(final_points)


# --- Step 2: The core PCA function (NO CHANGES NEEDED) ---
def get_orientation_angle(linestring: LineString) -> float:
    """
    Calculates the orientation angle of a LineString using PCA.
    This function works for arcs, full ellipses, or any cloud of points.
    """
    coords = np.array(linestring.coords)
    pca = PCA(n_components=1)
    pca.fit(coords)
    component_vector = pca.components_[0]
    angle_rad = math.atan2(component_vector[1], component_vector[0])
    return angle_rad

# --- Main execution ---

# 1. Create two full ellipses with different parameters
ellipse1 = create_full_ellipse(center=(5, 12), radii=(10, 4), rotation_deg=15)
ellipse2 = create_full_ellipse(center=(20, 5), radii=(8, 3), rotation_deg=-40)

# 2. Get the orientation angle of each ellipse
angle1_rad = get_orientation_angle(ellipse1)
angle2_rad = get_orientation_angle(ellipse2)

print(f"Orientation of Ellipse 1: {math.degrees(angle1_rad):.2f} degrees")
print(f"Orientation of Ellipse 2: {math.degrees(angle2_rad):.2f} degrees")

# 3. Calculate the angle difference to align ellipse2 with ellipse1
angle_diff_rad = angle1_rad - angle2_rad
angle_diff_deg = math.degrees(angle_diff_rad)

print(f"\nRequired rotation for Ellipse 2 to align: {angle_diff_deg:.2f} degrees")

# 4. Rotate ellipse2 to align it.
aligned_ellipse2 = rotate(ellipse2, angle=angle_diff_deg, origin='center')

# 5. Get the angle of the newly aligned ellipse to verify
aligned_angle2_rad = get_orientation_angle(aligned_ellipse2)
print(f"New orientation of aligned Ellipse 2: {math.degrees(aligned_angle2_rad):.2f} degrees")


# --- Step 5: Visualize the results ---
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
fig.suptitle("Aligning Full Ellipses using PCA", fontsize=16)

# Plot "Before"
ax1.plot(*ellipse1.xy, label='Ellipse 1', color='blue')
ax1.plot(*ellipse2.xy, label='Ellipse 2 (Original)', color='red')
ax1.set_title('Before Alignment')
ax1.set_aspect('equal', adjustable='box')
ax1.legend()
ax1.grid(True, linestyle='--', alpha=0.6)

# Plot "After"
ax2.plot(*ellipse1.xy, label='Ellipse 1', color='blue')
ax2.plot(*aligned_ellipse2.xy, label='Ellipse 2 (Aligned)', color='green')
ax2.set_title('After Alignment')
ax2.set_aspect('equal', adjustable='box')
ax2.legend()
ax2.grid(True, linestyle='--', alpha=0.6)

plt.show()