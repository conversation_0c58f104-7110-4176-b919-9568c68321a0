import numpy as np
import math

def calculate_engagement_angle_at_distance(d_OC, tool_radius, material_radius):
    """
    Calculates the engagement angle between two intersecting circles
    given the distance between their centers.

    Formula derived from the Law of Cosines on the triangle formed by the
    tool center (C), material boundary center (O1), and one intersection
    point (D). Or equivalently, using the distance from C to the chord DE.
    cos(a/2) = distance(C, midpoint of DE) / r
    Distance from C to midpoint of DE = |d^2 + r^2 - R1^2| / (2*d)

    Args:
        d_OC (float): Distance between the centers of the tool circle (C)
                      and the material boundary circle (O1).
        tool_radius (float): Radius of the tool circle (r).
        material_radius (float): Radius of the material boundary circle (R1).

    Returns:
        float: Engagement angle in degrees. Returns 0.0 if circles don't intersect
               or if centers coincide in a way that prevents a defined angle.
    """
    r = tool_radius
    R1 = material_radius
    d = d_OC

    # Check for intersection: Circles intersect if the distance between centers
    # is less than or equal to the sum of radii and greater than or equal to
    # the absolute difference of radii.
    if d > R1 + r or d < abs(R1 - r):
        return 0.0

    # Handle the edge case where centers coincide (d=0).
    # This scenario is unlikely in the context of a tool path around another center (O2).
    # If d is very close to zero, the formula's denominator becomes problematic.
    # In a realistic machining scenario where d_OC -> 0, it typically implies
    # a plunge or special condition not covered by the standard engagement angle.
    # Return 0.0 as a practical default for this edge case in the formula calculation.
    if np.isclose(d, 0):
         # If R1 == r, concentric and same radius, could be 360 depending on context
         # If R1 < r, tool contains material boundary, full wrap (360 deg)
         # If R1 > r, material boundary contains tool, 0 deg engagement
         # Given the formula context (arccos argument), d=0 isn't directly supported.
         # Returning 0.0 as the angle subtended by a zero-length chord when centers coincide.
         return 0.0


    # Calculate the argument for arccos in the formula a = 2 * arccos(|d^2 + r^2 - R1^2| / (2 * d * r))
    # This argument is the ratio (distance from C to chord midpoint) / r
    arccos_arg = (d**2 + r**2 - R1**2) / (2 * d * r)

    # The absolute value is needed for the distance, but arccos is applied to the ratio.
    # The sign of (d^2 + r^2 - R1^2) determines which side of C the chord midpoint lies.
    # However, the engagement angle definition (angle DCE) means we need the chord length,
    # and chord length is related to (d^2 + r^2 - R1^2)^2.
    # Let's use the direct cos(a) formula derived earlier:
    # cos(a) = -1 + (d^2 + r^2 - R1^2)^2 / (2 * d^2 * r^2)
    numerator = (d**2 + r**2 - R1**2)**2
    denominator = 2 * d**2 * r**2

    # Should not be zero if d > 0 and r > 0, but handle for robustness
    if np.isclose(denominator, 0):
         return 0.0

    cos_a = -1 + numerator / denominator

    # Clamp the argument to arccos to [-1, 1] due to potential floating point inaccuracies
    cos_a = np.clip(cos_a, -1.0, 1.0)

    # Calculate the angle in radians
    angle_rad = np.arccos(cos_a)

    # Convert to degrees
    return math.degrees(angle_rad)


def calculate_max_engagement_angle_two_circles(tool_radius, mat_offset_radius, path_offset_radius, center_dist_O1O2):
    """
    Estimates the maximum engagement angle during the phase where the tool center
    rolls on an arc around O2 and cuts against material boundary defined by Cur1.

    Based on the paper's context (multiple-circle continuous machining in pockets)
    and geometry (Fig 1b), R1 is the radius of the offset material boundary circle
    (Cur1, center O1), and R2 is the radius of the offset tool center path arc
    (around O2). The tool radius is r. The distance between O1 and O2 is l.

    The tool center C moves on an arc of radius R2 around O2. The material boundary
    is circle (O1, R1). The engagement angle depends on the distance |CO1|.
    The range of distances |CO1| achieved by C on the full circle (O2, R2) is [|l - R2|, l + R2].
    The range of distances |CO1| where the tool circle (C, r) intersects circle (O1, R1)
    is [|R1 - r|, R1 + r].

    The distances |CO1| where intersection occurs *on the path* are in the overlap
    of these two ranges: [max(|l - R2|, |R1 - r|), min(l + R2, R1 + r)].
    Let this effective range of intersecting distances on the path be [D1, D2].

    The engagement angle function a(d) = 2 * arccos(abs(d^2 + r^2 - R1^2) / (2 * d * r)).
    This angle is maximized when abs((d^2 + r^2 - R1^2) / (2 * d * r)) is minimized.
    This term is minimized at d_optimal = sqrt(abs(R1^2 - r^2)).
    - If d_optimal is in [D1, D2], the max angle is 180 degrees.
    - If d_optimal is outside [D1, D2], the minimum value of the term occurs at the
      boundary of [D1, D2] closest to d_optimal. This corresponds to distance D1 or D2.

    Args:
        tool_radius (float): Radius of the tool (r).
        mat_offset_radius (float): Radius of the offset material boundary circle (R1 = r1+r).
        path_offset_radius (float): Radius of the offset tool path arc around O2 (R2 = r2+r).
        center_dist_O1O2 (float): Distance between centers O1 and O2 (l).

    Returns:
        float: Estimated maximum engagement angle in degrees. Returns 0.0 if no
               intersection occurs on the path arc.
    """
    r = tool_radius
    R1 = mat_offset_radius
    R2 = path_offset_radius
    l = center_dist_O1O2

    # 1. Range of distances |CO1| on the full path circle (O2, R2)
    d_path_min = abs(l - R2)
    d_path_max = l + R2

    # 2. Range of distances |CO1| for intersection between tool (C, r) and material (O1, R1)
    d_int_min = abs(R1 - r)
    d_int_max = R1 + r

    # 3. Overlap range of distances [D1, D2] where intersection occurs on the path arc
    D1 = max(d_path_min, d_int_min)
    D2 = min(d_path_max, d_int_max)

    # If D1 > D2, the ranges don't overlap, so no intersection happens on the path arc
    if D1 > D2 + 1e-9: # Use tolerance for float comparison
        return 0.0

    # The maximum engagement angle occurs at the distance `d` in [D1, D2]
    # that minimizes `abs((d^2 + r^2 - R1^2) / (2 * d * r))`.
    # This minimum is achieved at `d_optimal = sqrt(abs(R1^2 - r^2))`.

    d_optimal_sq_diff = R1**2 - r**2

    # Calculate d_optimal based on the sign of R1^2 - r^2
    if d_optimal_sq_diff >= 0:
        d_optimal = math.sqrt(d_optimal_sq_diff)
    else: # R1 < r case
        d_optimal = math.sqrt(-d_optimal_sq_diff) # sqrt(r^2 - R1^2)

    # Determine which distance in [D1, D2] gives the maximum angle
    # If d_optimal is within [D1, D2], the theoretical max angle is 180.
    # Otherwise, the max angle occurs at the boundary of [D1, D2] that is closest to d_optimal.

    max_angle = 0.0 # Default if no intersection

    # Check if d_optimal is within the achievable intersecting range [D1, D2]
    if d_optimal >= D1 - 1e-9 and d_optimal <= D2 + 1e-9:
        # If the distance for theoretical 180deg angle is reachable and intersects,
        # the maximum angle is 180 degrees.
        max_angle = 180.0
    else:
        # If the optimal distance is not reachable within the intersecting path segment,
        # the maximum angle will be at one of the boundary points D1 or D2.
        # The angle a(d) increases as d approaches d_optimal. So the maximum
        # angle on [D1, D2] is at the boundary point closest to d_optimal.

        # Check angle at D1
        angle_at_D1 = calculate_engagement_angle_at_distance(D1, r, R1)

        # Check angle at D2
        angle_at_D2 = calculate_engagement_angle_at_distance(D2, r, R1)

        max_angle = max(angle_at_D1, angle_at_D2)
        
        # Note: This logic assumes the arc EFS covers the points corresponding
        # to distances D1 and D2 on the circle (O2, R2). The actual arc segment
        # geometry might impose further constraints, which are not fully specified
        # by just R2 and l. This is a simplified model based on the paper's figures.


    return max_angle

# --- Example Usage based on Fig 6 parameters ---
# Note: There seems to be a discrepancy between the calculated results based on
# a direct interpretation of the paper's definitions (R1, R2 as offset radii)
# and the simulation values shown in Fig 6. The code below implements the
# logic derived from the geometry and definitions, which may not precisely
# match the simulation curves without further geometric details of the path segments.

print("--- Fig 6(a) Simulation Parameters ---")
r_tool_a = 2.0
R1_mat_a = 5.0 # Interpreted as Offset material radius (R1 = r1 + r)
R2_path_a = 4.0 # Interpreted as Offset path radius (R2 = r2 + r)

l_values_a = [3.6, 2.9, 2.2, 1.5]
expected_angles_a = [~150, ~160, ~150, ~118] # Approximate values from graph

for i, l_dist in enumerate(l_values_a):
    max_angle_a_calc = calculate_max_engagement_angle_two_circles(r_tool_a, R1_mat_a, R2_path_a, l_dist)
    print(f"l={l_dist}: Calculated Max Engagement Angle = {max_angle_a_calc:.2f} degrees. (Fig 6(a) Approx Expected: {expected_angles_a[i]})")

print("\n--- Fig 6(b) Simulation Parameters ---")
r_tool_b = 3.0
R1_mat_b = 5.0 # Interpreted as Offset material radius (R1 = r1 + r)
R2_path_b = 5.0 # Interpreted as Offset path radius (R2 = r2 + r)

l_values_b = [3.0, 2.0, 1.0, 0.5]
expected_angles_b = [~58, ~139, ~162, ~76] # Approximate values from graph

for i, l_dist in enumerate(l_values_b):
    max_angle_b_calc = calculate_max_engagement_angle_two_circles(r_tool_b, R1_mat_b, R2_path_b, l_dist)
    print(f"l={l_dist}: Calculated Max Engagement Angle = {max_angle_b_calc:.2f} degrees. (Fig 6(b) Approx Expected: {expected_angles_b[i]})")