import numpy as np
from shapely.geometry import Polygon, Point
from skimage.draw import polygon as draw_polygon
from scipy.ndimage import distance_transform_edt
import matplotlib.pyplot as plt

# Parameters (adjustable based on tool and pocket size)
GRID_SCALE = 0.1  # mm per pixel
TOOL_DIAMETER = 10.0  # mm
TOOL_RADIUS = TOOL_DIAMETER / 2
PLUNGE_RADIUS = TOOL_RADIUS * 1.1  # Slightly larger than tool
STEP_SIZE = TOOL_DIAMETER / 10  # Delta step for tool movement
THETA_MAX = 90  # Max deviation angle in degrees
TARGET_ENGAGEMENT = 50  # Target pixels cut per move (tune as needed)
TOLERANCE = 0.1 * TARGET_ENGAGEMENT  # Engagement tolerance

def rasterize_polygon(polygon, grid_scale):
    """Convert Shapely polygon to a binary grid."""
    minx, miny, maxx, maxy = polygon.bounds
    width = int(np.ceil((maxx - minx) / grid_scale))
    height = int(np.ceil((maxy - miny) / grid_scale))
    pocket_mask = np.zeros((height, width), dtype=bool)
    
    # Scale coordinates to pixel units
    scale = 1 / grid_scale
    exterior_coords = np.array(polygon.exterior.coords) * scale
    rr, cc = draw_polygon(exterior_coords[:, 1], exterior_coords[:, 0], shape=(height, width))
    pocket_mask[rr, cc] = True
    
    # Handle interiors (holes)
    for interior in polygon.interiors:
        interior_coords = np.array(interior.coords) * scale
        rr, cc = draw_polygon(interior_coords[:, 1], interior_coords[:, 0], shape=(height, width))
        pocket_mask[rr, cc] = False
    
    return pocket_mask

def compute_engagement(p, tool_radius, material, pocket_mask, grid_scale):
    """Compute tool engagement at position p without modifying material."""
    i_p, j_p = (p / grid_scale).astype(int)
    r_pixels = int(np.ceil(tool_radius / grid_scale))
    i_min = max(0, i_p - r_pixels)
    i_max = min(material.shape[0], i_p + r_pixels + 1)
    j_min = max(0, j_p - r_pixels)
    j_max = min(material.shape[1], j_p + r_pixels + 1)
    
    engagement = 0
    for i in range(i_min, i_max):
        for j in range(j_min, j_max):
            pixel_center = np.array([i, j]) * grid_scale + grid_scale / 2
            if np.linalg.norm(pixel_center - p) < tool_radius:
                if not pocket_mask[i, j]:
                    return -1  # Gouge detected
                if material[i, j]:
                    engagement += 1
    return engagement

def render_tool(p, tool_radius, material, pocket_mask, grid_scale):
    """Update material by rendering tool at position p."""
    i_p, j_p = (p / grid_scale).astype(int)
    r_pixels = int(np.ceil(tool_radius / grid_scale))
    i_min = max(0, i_p - r_pixels)
    i_max = min(material.shape[0], i_p + r_pixels + 1)
    j_min = max(0, j_p - r_pixels)
    j_max = min(material.shape[1], j_p + r_pixels + 1)
    
    for i in range(i_min, i_max):
        for j in range(j_min, j_max):
            pixel_center = np.array([i, j]) * grid_scale + grid_scale / 2
            if np.linalg.norm(pixel_center - p) < tool_radius and pocket_mask[i, j]:
                material[i, j] = 0

def rotate_vector(vec, theta_deg):
    """Rotate a 2D vector by theta degrees."""
    theta = np.radians(theta_deg)
    cos_t, sin_t = np.cos(theta), np.sin(theta)
    return np.array([vec[0] * cos_t - vec[1] * sin_t, vec[0] * sin_t + vec[1] * cos_t])

def find_best_move(p_n, d_prev, tool_radius, material, pocket_mask, grid_scale):
    """Find the best cutting move based on target engagement."""
    angles = np.arange(-THETA_MAX, THETA_MAX + 1, 1)
    best_delta = float('inf')
    best_p = None
    
    for theta in angles:
        d_theta = rotate_vector(d_prev, theta)
        p = p_n + d_theta * STEP_SIZE
        engagement = compute_engagement(p, tool_radius, material, pocket_mask, grid_scale)
        if engagement >= 0:
            delta = abs(engagement - TARGET_ENGAGEMENT)
            if delta < TOLERANCE:
                return p, True
            if delta < best_delta:
                best_delta = delta
                best_p = p
    
    return best_p, best_delta < TARGET_ENGAGEMENT if best_p is not None else False

def find_boundary_move(p_n, d_prev, tool_radius, material, pocket_mask, grid_scale):
    """Find a move along the boundary when cutting move fails."""
    angles = np.arange(-THETA_MAX, THETA_MAX + 1, 1)
    prev_engagement = compute_engagement(p_n + rotate_vector(d_prev, -THETA_MAX) * STEP_SIZE,
                                         tool_radius, material, pocket_mask, grid_scale)
    
    for theta in angles[1:]:
        d_theta = rotate_vector(d_prev, theta)
        p = p_n + d_theta * STEP_SIZE
        engagement = compute_engagement(p, tool_radius, material, pocket_mask, grid_scale)
        if prev_engagement == -1 and engagement == 0:
            return p, True
        elif prev_engagement == 0 and engagement == -1:
            prev_theta = theta - 1
            d_prev_theta = rotate_vector(d_prev, prev_theta)
            return p_n + d_prev_theta * STEP_SIZE, True
        prev_engagement = engagement
    return None, False

def generate_toolpath(pocket_polygon):
    """Generate a curvilinear toolpath for the pocket."""
    # Step 1: Rasterize the pocket
    pocket_mask = rasterize_polygon(pocket_polygon, GRID_SCALE)
    material = pocket_mask.copy().astype(np.uint8)
    
    # Step 2: Compute distance transform to find starting point
    dist_transform = distance_transform_edt(pocket_mask)
    start_idx = np.unravel_index(np.argmax(dist_transform), dist_transform.shape)
    start_pos = np.array(start_idx) * GRID_SCALE + GRID_SCALE / 2
    
    
    # Step 3: Create plunge hole
    render_tool(start_pos, PLUNGE_RADIUS, material, pocket_mask, GRID_SCALE)
    
    # Step 4: Find initial cutting position
    p_n = start_pos
    d_init = np.array([1.0, 0.0])  # Initial direction
    while True:
        engagement = compute_engagement(p_n, TOOL_RADIUS, material, pocket_mask, GRID_SCALE)
        if engagement > 0:
            break
        p_n = p_n + d_init * STEP_SIZE
    
    # Step 5: Generate toolpath
    toolpath = [p_n]
    d_prev = d_init
    
    while True:
        # Try best cutting move
        p_next, valid = find_best_move(p_n, d_prev, TOOL_RADIUS, material, pocket_mask, GRID_SCALE)
        if not valid:
            # Try boundary move
            p_next, valid = find_boundary_move(p_n, d_prev, TOOL_RADIUS, material, pocket_mask, GRID_SCALE)
            if not valid:
                break  # Stop if no valid move found
        
        # Update toolpath and material
        render_tool(p_next, TOOL_RADIUS, material, pocket_mask, GRID_SCALE)
        toolpath.append(p_next)
        d_prev = (p_next - p_n) / np.linalg.norm(p_next - p_n)
        p_n = p_next
    
    return np.array(toolpath)

# Example usage
if __name__ == "__main__":
    # Define a simple L-shaped pocket
    pocket = Polygon([(0, 0), (100, 0), (100, 50), (50, 50), (50, 100), (0, 100)])
    
    # Generate toolpath
    toolpath = generate_toolpath(pocket)   
    