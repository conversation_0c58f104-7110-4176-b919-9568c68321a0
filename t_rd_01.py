import numpy as np
import time
import shapely
from shapely.geometry import LineString, Point

def points_right_of_segments(arr0, arr1, *, eps=1e-6):
    """
    Determine which points of `arr1` lie on the right‑hand side of **any**
    directed segment of the polyline defined by `arr0`.

    Parameters
    ----------
    arr0 : ndarray, shape (M, 2)
        Vertices of the reference polyline (M ≥ 2).
    arr1 : ndarray, shape (N, 2)
        Points to be tested.
    eps : float, optional
        Numerical tolerance for “zero”. Points with |cross| < eps are treated
        as collinear and are *not* counted as right‑hand side.

    Returns
    -------
    right_mask : ndarray, shape (N,)
        Boolean array – True where the point is on the right side of at least
        one segment (and the orthogonal projection falls inside that segment).
    first_seg_idx : ndarray, shape (N,)
        Index of the *first* segment that classifies the point as right.
        -1 means “no segment qualifies”.
    weighted_score : ndarray, shape (N,)
        Length‑weighted measure:
        Σ (segment_length * |cross|) over all qualifying segments.
        You can use this score for ranking / thresholding if needed.
    """
    # ------------------------------------------------------------------ #
    # 1) Build the (M‑1) directed segments from arr0
    # ------------------------------------------------------------------ #
    A = arr0[:-1]                 # start of each segment, shape (S, 2)
    B = arr0[1:]                  # end   of each segment, shape (S, 2)
    v = B - A                     # segment vectors, shape (S, 2)
    seg_len2 = np.einsum('ij,ij->i', v, v)   # |v|², shape (S,)
    seg_len  = np.sqrt(seg_len2)             # |v|,   shape (S,)

    # Guard against zero‑length segments (they give NaNs later)
    # Those segments are simply ignored.
    valid = seg_len2 > eps
    if not np.all(valid):
        v = v[valid]
        A = A[valid]
        seg_len2 = seg_len2[valid]
        seg_len  = seg_len[valid]

    # ------------------------------------------------------------------ #
    # 2) Vectors from each segment start to every point
    # ------------------------------------------------------------------ #
    # w[i, j, :] = arr1[j] - A[i]   → shape (S, N, 2)
    w = arr1[None, :, :] - A[:, None, :]

    # ------------------------------------------------------------------ #
    # 3) Signed 2‑D cross product  (v × w)   → shape (S, N)
    # ------------------------------------------------------------------ #
    cross = v[:, None, 0] * w[:, :, 1] - v[:, None, 1] * w[:, :, 0]

    # ------------------------------------------------------------------ #
    # 4) Dot product v·w  (used for the parametric coordinate t)
    # ------------------------------------------------------------------ #
    # Because w is (S, N, 2) we need the subscript order 'ij,ikj->ik'
    dot = np.einsum('ij,ikj->ik', v, w)      # shape (S, N)

    # ------------------------------------------------------------------ #
    # 5) Parameter t = (v·w) / |v|²   → shape (S, N)
    # ------------------------------------------------------------------ #
    t = dot / seg_len2[:, None]

    # ------------------------------------------------------------------ #
    # 6) Masks
    # ------------------------------------------------------------------ #
    # a) Right‑hand side ?   (cross < -eps)   (negative = right)
    mask_right = cross < -eps

    # b) Projection inside the finite segment ?
    mask_inside = (t >= 0.0) & (t <= 1.0)

    # c) Combined condition per segment
    mask_seg = mask_right & mask_inside        # shape (S, N)

    # ------------------------------------------------------------------ #
    # 7) Global decision per point (any segment qualifies)
    # ------------------------------------------------------------------ #
    right_mask = mask_seg.any(axis=0)          # shape (N,)

    return right_mask


def point_to_polyline_distance(P, L):
    """
    P : (N,2) numpy array – point cloud
    L : (M,2) numpy array – polyline vertices in order
    returns : (N,) array – shortest distance from each point to the polyline
    """

    # 1. All vectors from every point to every segment vertex
    #    Shape broadcasting: (N,1,2) - (M,2) -> (N,M,2)
    diff = P[:, None, :] - L[None, :, :]

    # 2. Squared distance to every vertex
    d2_vtx = np.sum(diff**2, axis=2)          # (N,M)

    # 3. Project each point onto every segment
    #    Segment vectors (M-1,2)
    seg_vec   = L[1:] - L[:-1]                  # (M-1,2)
    seg_len2  = np.sum(seg_vec**2, axis=1)    # (M-1,)

    # 4. t parameter for the projection onto each segment
    #    t = ((P-A)·(B-A)) / |B-A|²   with A=L[:-1], B=L[1:]
    t = np.sum((P[:, None, :] - L[:-1][None, :, :]) * seg_vec[None, :, :], axis=2)
    t = t / seg_len2[None, :]                 # (N,M-1)

    # 5. Clamp t to [0,1] to stay on the segment
    t = np.clip(t, 0.0, 1.0)

    # 6. Closest point on segment = A + t*(B-A)
    closest = L[:-1][None, :, :] + t[..., None] * seg_vec[None, :, :]  # (N,M-1,2)

    # 7. Distance to every segment
    d2_seg = np.sum((P[:, None, :] - closest)**2, axis=2)  # (N,M-1)

    # 8. Combine distances to vertices and segments
    d2_all = np.concatenate([d2_vtx, d2_seg], axis=1)        # (N, M+(M-1))
    return np.sqrt(np.min(d2_all, axis=1))

# ---------------------------------------------------------------------- #
# Example usage
# ---------------------------------------------------------------------- #
if __name__ == "__main__":
    # Example polyline (arr0) – a “Z” shape    
    arr0 = np.array(
        [[ 11.34318924, -13.32184792],
            [  7.76665211,  -3.34005761],
            [ -6.14932919,  0.43156314],
            [ -0.36184147, 12.26664925],
            [-36.85007095,  53.1151123 ]]
    )
    arr1 = np.array(
        [[  7.72110081, -23.01229668],
            [  3.72226238, -10.49670029],
            [  4.02374935,  0.73557281],
            [ 1.06522655,   6.99305344 ],
            [-23.55054474, 20.17207909],
            [-43.53945923, 31.29267311],
            [-58.34957123, 36.66784668]]
    )

    l0 = LineString(arr0)
    l1 = LineString(arr1)

    hausdorff_distance = l0.hausdorff_distance(l1)

    time1 = time.time()
    right_mask = points_right_of_segments(arr0, arr1)    
    distances = point_to_polyline_distance(arr1[right_mask], arr0)
    print(f'distances: {distances}')
    print(f'Hausdorff distance: {hausdorff_distance}')
    time2 = time.time()
    print(f'Time: {time2-time1}')
    print(f"Number of points on the right side of at least one segment: {right_mask.sum()} / {len(arr1)}")
    
    