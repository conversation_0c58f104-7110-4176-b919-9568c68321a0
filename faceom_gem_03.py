import numpy as np
import math
from shapely.geometry import <PERSON><PERSON><PERSON>, LineString, Point
from shapely.ops import transform
import matplotlib.pyplot as plt

# --- Helper Functions ---

def normalize_vector(v):
    """Normalizes a 2D vector."""
    norm = np.linalg.norm(v)
    if norm == 0:
        return np.array([0.0, 0.0]) # Or raise error?
    return v / norm

def rotate_vector(v, angle_rad):
    """Rotates a 2D vector counter-clockwise by a given angle in radians."""
    x, y = v
    cos_a = math.cos(angle_rad)
    sin_a = math.sin(angle_rad)
    new_x = x * cos_a - y * sin_a
    new_y = x * sin_a + y * cos_a
    return np.array([new_x, new_y])

def solve_ray_circle_intersection(ray_origin, ray_direction, circle_center, circle_radius):
    """
    Finds the intersection point(s) of a ray and a circle.

    Args:
        ray_origin (np.array): Starting point of the ray (Pi).
        ray_direction (np.array): Normalized direction vector of the ray (Vi).
        circle_center (np.array): Center of the circle (Ci+1).
        circle_radius (float): Radius of the circle (r_tool).

    Returns:
        np.array or None: The intersection point Pi+1 closest to ray_origin along the ray,
                          or None if no intersection or only behind the origin.
    """
    p = ray_origin
    v = ray_direction
    c = circle_center
    r = circle_radius

    # Vector from circle center to ray origin
    pc = p - c

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # A = v . v (should be 1 if v is normalized)
    # B = 2 * (v . (p - c))
    # C = (p - c) . (p - c) - r^2
    A = np.dot(v, v) # Should be 1.0
    B = 2 * np.dot(v, pc)
    C = np.dot(pc, pc) - r**2

    # Calculate discriminant
    discriminant = B**2 - 4*A*C

    if discriminant < -1e-9: # Allow small tolerance for floating point errors
        # No real intersection
        # print("Warning: No intersection between ray and circle.")
        return None
    elif abs(discriminant) < 1e-9:
        # One intersection (tangent)
        t = -B / (2 * A)
        if t < -1e-9: # Check if intersection is behind ray origin
             # print("Warning: Tangent intersection behind ray origin.")
             return None
        intersection_point = p + max(0, t) * v # Ensure t >= 0
        return intersection_point
    else:
        # Two intersections
        sqrt_discriminant = math.sqrt(discriminant)
        t1 = (-B + sqrt_discriminant) / (2 * A)
        t2 = (-B - sqrt_discriminant) / (2 * A)

        # We want the intersection point along the ray direction (t >= 0)
        # and typically the *first* one encountered (smallest non-negative t)
        valid_ts = [t for t in [t1, t2] if t >= -1e-9] # Allow small tolerance

        if not valid_ts:
             # print("Warning: Both intersections behind ray origin.")
             return None

        t_chosen = min(valid_ts)
        intersection_point = p + t_chosen * v
        return intersection_point

# --- FACEOM Algorithm ---

def generate_faceom_path(
    contour_shapely,
    tool_radius,
    engagement_angle_deg,
    step_distance,
    milling_type='climb', # 'climb' or 'conventional'
    max_steps=10000,
    stop_early_factor=0.99 # Stop slightly before completing the full loop
):
    """
    Generates a milling tool path using the FACEOM method.

    Args:
        contour_shapely (Polygon or LineString): The workpiece boundary.
                                                Assumes counter-clockwise winding for Polygon.
        tool_radius (float): Radius of the milling tool (r_tool).
        engagement_angle_deg (float): Desired constant engagement angle (theta).
        step_distance (float): Distance to step along the *contour* for each calculation.
                               Should be small relative to features and tool radius.
        milling_type (str): 'climb' (tool left of contour) or
                            'conventional' (tool right of contour).
        max_steps (int): Maximum number of steps to prevent infinite loops.
        stop_early_factor(float): Multiplies contour length to define stopping distance.

    Returns:
        np.array: An array of (x, y) coordinates representing the tool center path.
                  Returns None if initialization or generation fails.
    """
    if not isinstance(contour_shapely, (Polygon, LineString)):
        raise ValueError("contour_shapely must be a Shapely Polygon or LineString")

    # Ensure the contour is treated as a LineString for interpolation
    if isinstance(contour_shapely, Polygon):
        # Check winding order for climb/conventional consistency if needed,
        # but interpolation works regardless. We assume CCW for Polygons.
        contour_line = LineString(contour_shapely.exterior.coords)
        is_closed = True
    else:
        contour_line = contour_shapely
        is_closed = contour_line.is_closed

    if not is_closed:
        print("Warning: FACEOM typically used for closed contours (pockets/islands). "
              "Applying to open LineString.")

    contour_length = contour_line.length
    if step_distance <= 0 or tool_radius <= 0:
        raise ValueError("step_distance and tool_radius must be positive.")
    if step_distance > contour_length / 10:
         print(f"Warning: step_distance ({step_distance}) seems large relative to "
               f"contour length ({contour_length:.2f}).")

    # --- Initialization (Based on Figure 5) ---
    current_dist_on_contour = 0.0
    C0_pt = contour_line.interpolate(current_dist_on_contour)
    C0 = np.array([C0_pt.x, C0_pt.y])

    # Estimate tangent at C0 (look slightly ahead)
    look_ahead_dist = min(step_distance * 0.1, contour_length * 0.001)
    if is_closed:
        next_pt_dist = (current_dist_on_contour + look_ahead_dist) % contour_length
    else:
        next_pt_dist = min(current_dist_on_contour + look_ahead_dist, contour_length)
    C_next_pt = contour_line.interpolate(next_pt_dist)
    tangent_approx = normalize_vector(np.array([C_next_pt.x, C_next_pt.y]) - C0)

    # Determine normal vector direction based on milling type
    # Assume CCW contour for polygon:
    #   - Climb: Normal points OUTWARD (tool on left) -> Rotate tangent -90 deg (CW)
    #   - Conventional: Normal points INWARD (tool on right) -> Rotate tangent +90 deg (CCW)
    if milling_type == 'climb':
        normal_rot_angle_rad = math.radians(-90.0)
    elif milling_type == 'conventional':
        normal_rot_angle_rad = math.radians(90.0)
    else:
        raise ValueError("milling_type must be 'climb' or 'conventional'")

    normal_vec = rotate_vector(tangent_approx, normal_rot_angle_rad)
    P0 = C0 + normal_vec * tool_radius

    # Calculate initial feed vector V0 (Based on Figure 4 logic applied initially)
    engagement_angle_rad = math.radians(engagement_angle_deg)
    # Angle alpha rotates P->C vector to get feed vector V
    alpha_rad = math.radians(90.0 - engagement_angle_deg)

    # Adjust rotation direction for alpha based on milling type
    # Climb: P->C rotated CCW by alpha gives V
    # Conventional: P->C rotated CW by alpha gives V
    if milling_type == 'conventional':
       alpha_rad = -alpha_rad # Rotate clockwise

    P_to_C_vec = C0 - P0 # Vector from initial tool pos to contour point
    V0 = normalize_vector(rotate_vector(P_to_C_vec, alpha_rad))

    if np.allclose(V0, [0,0]):
        print("Error: Initial feed vector V0 is zero. Check parameters or geometry.")
        return None

    # --- Iteration Loop (Based on Figure 4 and FACEOM Steps) ---
    tool_path_points = [P0]
    current_P = P0
    current_V = V0
    steps_taken = 0

    stop_distance = contour_length * stop_early_factor if is_closed else contour_length

    while current_dist_on_contour < stop_distance and steps_taken < max_steps:
        steps_taken += 1

        # Step 1: Find next point Ci+1 on contour
        current_dist_on_contour += step_distance
        if is_closed:
            dist_on_contour_i1 = current_dist_on_contour % contour_length
        else:
            dist_on_contour_i1 = min(current_dist_on_contour, contour_length)

        Ci1_pt = contour_line.interpolate(dist_on_contour_i1)
        Ci1 = np.array([Ci1_pt.x, Ci1_pt.y])

        # Step 2: Find Pi+1 (intersection of ray from Pi along Vi with circle at Ci+1)
        Pi1 = solve_ray_circle_intersection(current_P, current_V, Ci1, tool_radius)

        if Pi1 is None:
            print(f"Error: Could not find intersection at step {steps_taken}, "
                  f"dist_on_contour={current_dist_on_contour:.2f}. Stopping.")
            # Optional: Try reducing step size here?
            break

        # Check for excessive jumps (potential instability)
        step_len_sq = np.sum((Pi1 - current_P)**2)
        if step_len_sq > (5 * step_distance)**2: # Heuristic check
             print(f"Warning: Large jump detected at step {steps_taken} "
                   f"(jump {math.sqrt(step_len_sq):.2f} vs step_dist {step_distance}). "
                   "Consider smaller step_distance.")
             # Potentially break or add damping if this happens often

        # Check if Pi1 is too close to previous point (numerical stall)
        if step_len_sq < 1e-12:
             print(f"Warning: Step {steps_taken} resulted in very small movement. Stopping.")
             break


        tool_path_points.append(Pi1)

        # Step 3: Calculate next feed vector Vi+1
        P_to_C_i1_vec = Ci1 - Pi1
        if np.linalg.norm(P_to_C_i1_vec) < 1e-9:
             print(f"Warning: P and C coincided at step {steps_taken}. Stopping.")
             break

        # alpha_rad determined earlier based on milling type
        Vi1 = normalize_vector(rotate_vector(P_to_C_i1_vec, alpha_rad))

        if np.allclose(Vi1, [0,0]):
            print(f"Error: Feed vector Vi+1 is zero at step {steps_taken}. Stopping.")
            break

        # Step 4: Update for next iteration
        current_P = Pi1
        current_V = Vi1
        # current_dist_on_contour updated at start of loop

    if steps_taken >= max_steps:
        print(f"Warning: Reached maximum steps ({max_steps}). Path may be incomplete.")

    return np.array(tool_path_points)


# --- Plotting Function ---
def plot_results(contour_shapely, tool_path, tool_radius, title="FACEOM Path"):
    """Plots the contour, tool path, and sample tool circles."""
    fig, ax = plt.subplots(figsize=(8, 8))

    # Plot contour
    if isinstance(contour_shapely, Polygon):
        x, y = contour_shapely.exterior.xy
        ax.plot(x, y, label='Contour', color='blue', linewidth=1.5)
    else: # LineString
        x, y = contour_shapely.xy
        ax.plot(x, y, label='Contour', color='blue', linewidth=1.5)


    # Plot tool path
    if tool_path is not None and len(tool_path) > 0:
        path_x, path_y = tool_path.T
        ax.plot(path_x, path_y, label='Tool Path (FACEOM)', color='red', linestyle='--', marker='.', markersize=3)

        # Plot tool circles at sample points
        num_samples = min(len(tool_path), 15)
        indices = np.linspace(0, len(tool_path) - 1, num_samples, dtype=int)
        for i in indices:
            tool_center = tool_path[i]
            circle = plt.Circle(tool_center, tool_radius, color='gray', fill=False, linestyle=':', linewidth=0.8)
            ax.add_patch(circle)
        # Add one for the first point too if not included
        if 0 not in indices:
             tool_center = tool_path[0]
             circle = plt.Circle(tool_center, tool_radius, color='gray', fill=False, linestyle=':', linewidth=0.8)
             ax.add_patch(circle)

    ax.set_aspect('equal', adjustable='box')
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title(title)
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.6)
    plt.show()

# --- Example Usage ---

# 1. Simple Square Contour (Convex Corners)
square_poly = Polygon([(0, 0), (50, 0), (50, 50), (0, 50)])
tool_rad = 2.0
engagement = 60.0 # degrees
step_dist = 0.5 # Small step for accuracy

print("--- Generating path for Square ---")
faceom_path_square = generate_faceom_path(square_poly, tool_rad, engagement, step_dist, milling_type='climb')
if faceom_path_square is not None:
    print(f"Generated {len(faceom_path_square)} points.")
    plot_results(square_poly, faceom_path_square, tool_rad, "FACEOM - Square (Climb)")

# 2. Contour with Concave Corner (L-shape)
# Note: Sharp concave corners (< tool radius) are inherently problematic for constant engagement
l_shape_poly = Polygon([(0,0), (40,0), (40,30), (20,30), (20,50), (0,50)])
tool_rad_l = 6.0
engagement_l = 75.0
step_dist_l = 0.4

print("\n--- Generating path for L-Shape ---")
# The algorithm might struggle right *at* the sharp concave corner if step_distance
# is too large, potentially causing the ray-circle intersection to fail or jump inside.
# Smaller step_distance helps navigate turns more smoothly.
faceom_path_l = generate_faceom_path(l_shape_poly, tool_rad_l, engagement_l, step_dist_l, milling_type='climb')
if faceom_path_l is not None:
    print(f"Generated {len(faceom_path_l)} points.")
    plot_results(l_shape_poly, faceom_path_l, tool_rad_l, f"FACEOM - L-Shape (Climb, Engagement={engagement_l}deg)")

# 3. Sine-wave like contour (similar to Fig 8) - using LineString approximation
amplitude = 8
frequency = 0.1
length = 100
num_pts = 200
x_pts = np.linspace(0, length, num_pts)
y_pts = amplitude * np.sin(x_pts * frequency)
# Create closed loop approximating the sine wave path
sine_coords = list(zip(x_pts, y_pts))
# Add points to close loop underneath
sine_coords.extend([(length, -amplitude*1.5), (0, -amplitude*1.5), (0,0)]) # Crude closing
sine_poly = Polygon(sine_coords)

tool_rad_sine = 4.0
engagement_sine = 60.0
step_dist_sine = 0.5

print("\n--- Generating path for Sine-like Shape ---")
faceom_path_sine = generate_faceom_path(sine_poly, tool_rad_sine, engagement_sine, step_dist_sine, milling_type='climb')
if faceom_path_sine is not None:
    print(f"Generated {len(faceom_path_sine)} points.")
    plot_results(sine_poly, faceom_path_sine, tool_rad_sine, "FACEOM - Sine Shape (Climb)")

# 4. Example with Conventional Milling
print("\n--- Generating path for Square (Conventional) ---")
faceom_path_square_conv = generate_faceom_path(square_poly, tool_rad, engagement, step_dist, milling_type='conventional')
if faceom_path_square_conv is not None:
    print(f"Generated {len(faceom_path_square_conv)} points.")
    plot_results(square_poly, faceom_path_square_conv, tool_rad, "FACEOM - Square (Conventional)")