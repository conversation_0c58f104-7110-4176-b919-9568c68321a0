import numpy as np
import shapely
from shapely.geometry import Point, Polygon
from shapely.affinity import scale
import time

# --- The helper function 'create_ellipse' remains the same ---
def create_ellipse(center_xy, x_radius, y_radius, resolution=164):
    if x_radius <= 0 or y_radius <= 0:
        raise ValueError("<PERSON><PERSON><PERSON> must be positive")
    center = Point(center_xy)
    circle = center.buffer(1, resolution=resolution)
    ellipse = scale(circle, xfact=x_radius, yfact=y_radius, origin=center)
    return ellipse

# --- The main function to find the farthest point (CORRECTED) ---
def find_farthest_outside_point(ellipse_a: Polygon, ellipse_b: Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        ellipse_a (Polygon): The inner ellipse, which may partially be outside.
        ellipse_b (Polygon): The outer ellipse.

    Returns:
        tuple: A tuple containing:
               - max_distance (float): The maximum distance found. 0 if no points are outside.
               - farthest_point (np.ndarray): The [x, y] coordinates of the farthest point. None if no points are outside.
               - outside_points (np.ndarray): An array of all [x, y] points on A's boundary outside B. Empty if none.
    """
    # 1. Get the boundary coordinates of ellipse A as a NumPy array
    coords_a_boundary = np.array(ellipse_a.exterior.coords)
    
    # *** CORRECTION 1: Create an array of Shapely Point objects ***
    # The vectorized Shapely functions expect arrays of Geometries, not arrays of coordinates.
    # `shapely.points()` is the highly efficient, vectorized way to do this.
    points_on_a = shapely.points(coords_a_boundary)

    # 2. Filter to find which points are outside of ellipse B
    # Now we pass the array of Point objects to `contains`, which is the correct usage.
    is_inside_b = shapely.contains(ellipse_b, points_on_a)
    
    # Using the boolean array, filter the ORIGINAL coordinate array to get the points that are outside.
    outside_coords = coords_a_boundary[~is_inside_b]

    # 3. Handle the case where no points are outside
    if outside_coords.shape[0] == 0:
        return 0.0

    # *** CORRECTION 2: Create Point objects for the outside points for distance calculation ***
    # We need to calculate the distance from the outside points to ellipse B.
    # Again, we must provide Shapely Geometries.
    outside_points_geom = shapely.points(outside_coords)
    
    # 4. Calculate the distance from each outside point to ellipse B
    # This now works correctly because `outside_points_geom` is an array of Point objects.
    distances = shapely.distance(ellipse_b, outside_points_geom)

    # 5. Find the maximum distance and the corresponding coordinate
    max_distance = np.max(distances)
    # farthest_point_index = np.argmax(distances)
    
    # # Get the coordinate from our `outside_coords` array
    # farthest_point_coord = outside_coords[farthest_point_index]

    return max_distance

def find_farthest_outside_point_optimized(ellipse_a: Polygon, ellipse_b: Polygon):
    # Get boundary coordinates
    coords_a_boundary = np.array(ellipse_a.exterior.coords)
    
    # Create Point objects once
    points_on_a = shapely.points(coords_a_boundary)
    
    # Filter points outside ellipse B
    is_inside_b = shapely.contains(ellipse_b, points_on_a)
    outside_mask = ~is_inside_b
    
    # Early exit if no points outside
    if not np.any(outside_mask):
        return 0.0
    
    # Reuse the same Point objects for distance calculation
    outside_points_geom = points_on_a[outside_mask]
    
    # Calculate distances
    distances = shapely.distance(ellipse_b, outside_points_geom)
    
    # Find maximum
    max_distance = np.max(distances)    
    
    return max_distance


# --- Example Usage (this part remains the same) ---

# Create ellipse 'b' (the larger, outer one)
ellipse_b = create_ellipse(center_xy=(0, 0), x_radius=5, y_radius=3)
shapely.prepare(ellipse_b)

# Create ellipse 'a' (mostly inside b, but poking out on the right)
ellipse_a = create_ellipse(center_xy=(0.5, 0), x_radius=4.8, y_radius=2.5)

time1 = time.time()
# Run the analysis
max_dist = find_farthest_outside_point(ellipse_a, ellipse_b)

time2 = time.time()
print(f'Time: {time2-time1}')

time1 = time.time()
# Run the analysis
max_dist2 = find_farthest_outside_point_optimized(ellipse_a, ellipse_b)

time2 = time.time()
print(f'Time: {time2-time1}')

# --- Print the results ---

print(f"Its distance to the perimeter of ellipse 'b' is: {max_dist:.4f}")
print(f"Its distance to the perimeter of ellipse 'b' is: {max_dist2:.4f}")

