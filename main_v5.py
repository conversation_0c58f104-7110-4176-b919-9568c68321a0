import bpy
import numpy as np
import networkx as nx
import shapely
from mathutils import Vector
import time
from shapely.geometry import LineString, box
from shapely.strtree import STRtree

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None


def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None


def geometry_to_shapely(geometry):
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = False
    return contour, islands


def create_ring_object(coords, name):
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i+1)%n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects
    

def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.
    
    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects
    
    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []
    
    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))
    
    return blender_objects


def decompose_to_polygons(geom):    
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def sort_containment(arr, exterior_list):
    # Initialize a directed graph
    G = nx.DiGraph()

    # Add edges from the array
    for container, contained in zip(arr[0], arr[1]):
        G.add_edge(exterior_list[container], exterior_list[contained])

    # Ensure the graph is a DAG and compute transitive reduction
    if nx.is_directed_acyclic_graph(G):
        containment_graph = nx.transitive_reduction(G)
    else:
        containment_graph = G.copy()
        containment_graph.remove_edges_from(nx.find_cycle(containment_graph))

    # Create a new graph for the containment (direct edges)
    directed_containment_graph = nx.DiGraph()
    directed_containment_graph.add_edges_from(containment_graph.edges())
    return directed_containment_graph


def create_graph_containment(polygons_list):
    tree = shapely.STRtree(polygons_list)
    contain_information = tree.query(polygons_list, predicate='contains_properly')            
    return sort_containment(contain_information, polygons_list)


def traverse_dag_postorder(graph: nx.DiGraph, leaf_order: list) -> list:
    """ Traverse the DAG starting from nodes in leaf_order (expected to be leaf nodes) and traverse upward (post-order: predecessors are visited before their parent).
    If a node is reached from multiple leaves, it will only appear once.
    """
    visited = set()
    result = []

    def dfs(node):
        if node in visited:
            return
        # First traverse all predecessors (i.e. nodes that feed into this node)
        for parent in graph.predecessors(node):
            dfs(parent)
        visited.add(node)
        result.append(node)

    for leaf in leaf_order:
        if leaf not in graph:
            raise ValueError(f"Leaf node {leaf} not found in graph")
        dfs(leaf)
    return reversed(result)


def solve_tsp_for_nodes(nodes):
    """
    Solve Traveling Salesman Problem for a list of nodes with geometric properties.
    
    Args:
        nodes: List of objects that have centroid property (Shapely geometries)
        
    Returns:
        List of indices representing the optimal route through the nodes
    """
    # Get centroids
    centroids = [node.centroid for node in nodes]    
    # Convert Shapely points to coordinate tuples
    coords = [(c.x, c.y) for c in centroids]

    # Create a complete graph with Euclidean distances as edge weights
    tsp_graph = nx.Graph()
    for i, (x1, y1) in enumerate(coords):
        for j, (x2, y2) in enumerate(coords[i+1:], start=i+1):
            distance = shapely.geometry.Point(x1, y1).distance(shapely.geometry.Point(x2, y2))
            tsp_graph.add_edge(i, j, weight=distance)

    # Solve TSP using NetworkX's Christofides approximation
    return nx.approximation.traveling_salesman_problem(tsp_graph, cycle=False)


def segmentize(coords: np.ndarray, min_segment_length: float) -> np.ndarray:
    """
    Re-samples the input polyline or polygon by preserving original vertices and adding intermediate points
    on edges longer than min_segment_length, ensuring each resulting segment has a length of at least
    min_segment_length. Edges shorter than min_segment_length are left untouched.

    Parameters:
        coords (np.ndarray): Array of shape (N, 2) representing the polyline or polygon boundary.
        min_segment_length (float): Minimum length for each resulting segment.
        is_closed (bool): If True, treats coords as a closed polygon; if False, as an open polyline.

    Returns:
        np.ndarray: Array of points with original vertices preserved and additional points added where needed.
    """
    coords = np.asarray(coords)
    if coords.shape[0] < 2:
        return coords.copy()

    n_vertices = coords.shape[0]    
    loop_range = n_vertices
    result = []  # List to collect all points

    for i in range(loop_range):
        # Get start and end points of the current edge
        start = coords[i]
        end = coords[(i + 1) % n_vertices]
        diff = end - start
        # Calculate edge length
        edge_length = np.sqrt((diff ** 2).sum())

        # Always add the start point (original vertex)
        result.append(start[np.newaxis, :])

        if edge_length > min_segment_length:
            # Calculate the number of segments needed
            n_segments = int(np.floor(edge_length / min_segment_length))
            if n_segments < 1:
                n_segments = 1  # Minimum one segment
            segment_length = edge_length / n_segments

            # Since edge_length > min_segment_length and n_segments is floor(edge_length / min_segment_length),
            # segment_length will always be >= min_segment_length
            t = np.linspace(0, 1, n_segments + 1)[1:-1]  # Intermediate points, excluding start and end
            new_points = start + np.outer(t, diff)
            result.append(new_points)    

    # Concatenate all points into a single array
    return np.concatenate(result)


def compute_edge_metrics(inner, outer): 
    """ Given two polygons (inner and outer) as numpy arrays of shape (n_points, 2), compute for each pairing (ipX, opX): - angle_inner: between vector from inner[ipX-1] to inner[ipX] and vector from inner[ipX] to outer[opX] - angle_outer: between vector from outer[opX] to inner[ipX] and vector from outer[opX] to outer[opX+1] - edge_length: Euclidean distance from inner[ipX] to outer[opX]

    The vertices indices wrap-around (i.e., ipX-1 for the first vertex is the last vertex; opX+1 for the
    last vertex is the first vertex).

    Returns:
        A dictionary with keys:
        - 'angle_inner': a (m, n) numpy array of angles (in radians) at inner vertices.
        - 'angle_outer': a (m, n) numpy array of angles (in radians) at outer vertices.
        - 'edge_length': a (m, n) numpy array of distances.
        where m = number of vertices in inner and n = number of vertices in outer.
    """
    m = inner.shape[0]
    n = outer.shape[0]

    # Get previous vertex for each inner point (with wrap-around)
    inner_prev = inner[np.arange(m) - 1]

    # Get next vertex for each outer point (with wrap-around)
    outer_next = outer[(np.arange(n) + 1) % n]

    # Reshape inner and outer for broadcasting over all combinations
    inner_current = inner.reshape(m, 1, 2)  # shape (m, 1, 2)
    outer_points = outer.reshape(1, n, 2)     # shape (1, n, 2)

    # --- Compute inner angle at each inner vertex ---
    # Vector along inner polygon edge ending at inner_current (same for all corresponding outer points)
    v_inner = inner - inner_prev             # shape (m,2)
    # Vector from inner_current to each outer point
    v_to_outer = outer_points - inner_current # shape (m, n, 2)

    # Calculate cosine of inner angle for each (inner, outer) pair.
    dot_inner = np.sum(v_inner.reshape(m, 1, 2) * v_to_outer, axis=-1)  # shape (m, n)
    norm_v_inner = np.linalg.norm(v_inner, axis=1).reshape(m, 1)         # shape (m, 1)
    norm_v_to_outer = np.linalg.norm(v_to_outer, axis=-1)                # shape (m, n)
    cos_angle_inner = dot_inner / (norm_v_inner * norm_v_to_outer)
    cos_angle_inner = np.clip(cos_angle_inner, -1, 1)
    angle_inner = np.arccos(cos_angle_inner)  # shape (m, n)

    # --- Compute outer angle at each outer vertex ---
    # For each pair, compute the vector from outer to inner (which is the negative of above)
    v_from_outer_to_inner = inner_current - outer_points  # shape (m, n, 2)
    # For each outer vertex, compute the polygon edge leading out (constant over inner index)
    v_outer = outer_next - outer                           # shape (n, 2)
    v_outer = v_outer.reshape(1, n, 2)                       # shape (1, n, 2)

    dot_outer = np.sum(v_from_outer_to_inner * v_outer, axis=-1)  # shape (m, n)
    norm_v_from_outer_to_inner = np.linalg.norm(v_from_outer_to_inner, axis=-1)  # shape (m, n)
    norm_v_outer = np.linalg.norm(v_outer, axis=-1)   # shape (1, n)
    cos_angle_outer = dot_outer / (norm_v_from_outer_to_inner * norm_v_outer)
    cos_angle_outer = np.clip(cos_angle_outer, -1, 1)
    angle_outer = np.arccos(cos_angle_outer)  # shape (m, n)

    # --- Compute length of the imagined edge ---
    edge_length = norm_v_to_outer.copy()  # shape (m, n)

    return {
        'angle_inner': angle_inner,
        'angle_outer': angle_outer,
        'edge_length': edge_length
    }


def detect_line_intersections(lines): 
    """ Given an array 'lines' of shape (N, 2, 2) where each line is represented by two endpoints, returns an (M, 2) array of index pairs (i, j) for which line i and line j intersect. The computation is fully vectorized: bounding boxes are computed and used to prefilter candidate pairs, and then line intersection parameters are computed for the remaining candidate pairs.

    The intersection test for two segments with endpoints:
        p1 = (x1, y1), p2 = (x2, y2) for the first segment, and
        p3 = (x3, y3), p4 = (x4, y4) for the second
    uses the parameterization:

        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        t = ((x1-x3)*(y1-y2) - (y1-y3)*(x1-x2)) / denom
        u = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        
    and the segments intersect if 0<=t<=1 and 0<=u<=1, provided that |denom| > epsilon.

    This function is optimized for large numbers of line segments.
    """
    pts1 = lines[:, 0]
    pts2 = lines[:, 1]
    x1, y1 = pts1[:, 0], pts1[:, 1]
    x2, y2 = pts2[:, 0], pts2[:, 1]

    # Compute bounding boxes for each line segment.
    min_x = np.minimum(x1, x2)
    max_x = np.maximum(x1, x2)
    min_y = np.minimum(y1, y2)
    max_y = np.maximum(y1, y2)

    N = len(lines)
    # Get all unique candidate pairs (i,j) with i < j
    i_idx, j_idx = np.triu_indices(N, k=1)

    # Prefilter candidate pairs using bounding box overlap
    valid_bb = ((min_x[i_idx] <= max_x[j_idx]) & (max_x[i_idx] >= min_x[j_idx]) &
                (min_y[i_idx] <= max_y[j_idx]) & (max_y[i_idx] >= min_y[j_idx]))
    cand_i = i_idx[valid_bb]
    cand_j = j_idx[valid_bb]

    # Get candidate coordinates
    x1_c = x1[cand_i]
    y1_c = y1[cand_i]
    x2_c = x2[cand_i]
    y2_c = y2[cand_i]

    x3_c = x1[cand_j]
    y3_c = y1[cand_j]
    x4_c = x2[cand_j]
    y4_c = y2[cand_j]

    # Compute denominator for the intersection formulas.
    denom = (x1_c - x2_c) * (y3_c - y4_c) - (y1_c - y2_c) * (x3_c - x4_c)

    # Compute t and u numerators
    t_num = (x1_c - x3_c) * (y1_c - y2_c) - (y1_c - y3_c) * (x1_c - x2_c)
    u_num = (x1_c - x3_c) * (y3_c - y4_c) - (y1_c - y3_c) * (x3_c - x4_c)

    # Avoid division by very small numbers
    eps = 1e-10
    valid_denom = np.abs(denom) > eps
    # Initialize t and u to -1 (indicating no valid intersection)
    t = np.full_like(denom, -1, dtype=np.float64)
    u = np.full_like(denom, -1, dtype=np.float64)
    t[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    # Check for intersection parameters within [0, 1]
    intersect = (t >= 0) & (t <= 1) & (u >= 0) & (u <= 1)

    # Return the intersecting pairs (indices) in an (M, 2) array.
    intersect_pairs = np.vstack((cand_i[intersect], cand_j[intersect])).T
    return intersect_pairs


def valid_inner_outer_connections(inner, outer):
    """ Given two 2D polygons, inner and outer, represented as numpy arrays of shape (M,2) and (N,2), 
    this function returns an (K,2) array of index pairs (i, j) such that the straight connection from inner[i] to outer[j] 
    does not intersect any edge of the outer polygon (except at the outer vertex endpoint). The computation is fully vectorized 
    using bounding box pre-filtering and vectorized intersection tests.

    Parameters:
      inner: np.ndarray with shape (M,2)
      outer: np.ndarray with shape (N,2) representing vertices of a closed polygon (ordered), 
             where edges are defined between consecutive vertices and the last vertex connects to the first.

    Returns:
      np.ndarray of shape (K,2) where each row is a pair [i, j] indicating a valid connection from inner[i] to outer[j].
    """
    M = inner.shape[0]
    N = outer.shape[0]

    # Outer polygon edges: each edge from outer[k] to outer[(k+1)%N]
    outer_edges = np.stack([outer, np.roll(outer, -1, axis=0)], axis=1)  # shape: (N, 2, 2)

    # Build candidate connection segments from each inner vertex to each outer vertex
    candidate_P = np.repeat(inner, N, axis=0)     # shape: (M*N, 2)
    candidate_Q = np.tile(outer, (M, 1))            # shape: (M*N, 2)

    # Reshape for broadcasting
    P = candidate_P[:, None, :]   # shape: (M*N, 1, 2)
    Q = candidate_Q[:, None, :]   # shape: (M*N, 1, 2)

    # Outer edges endpoints
    A = outer_edges[:, 0][None, :, :]  # shape: (1, N, 2)
    B = outer_edges[:, 1][None, :, :]  # shape: (1, N, 2)

    # Bounding box pre-filtering
    cand_min = np.minimum(P, Q)   # shape: (M*N, 1, 2)
    cand_max = np.maximum(P, Q)
    edge_min = np.minimum(A, B)   # shape: (1, N, 2)
    edge_max = np.maximum(A, B)
    bbox_overlap = (cand_min[..., 0] <= edge_max[..., 0]) & (cand_max[..., 0] >= edge_min[..., 0]) & \
                   (cand_min[..., 1] <= edge_max[..., 1]) & (cand_max[..., 1] >= edge_min[..., 1])

    # Vectorized intersection using cross product method
    diff_PQ = Q - P           # shape: (M*N, 1, 2)
    diff_AB = B - A           # shape: (1, N, 2)
    cross = lambda a, b: a[..., 0]*b[..., 1] - a[..., 1]*b[..., 0]

    denom = cross(diff_PQ, diff_AB)   # shape: (M*N, N)
    eps = 1e-10

    diff_AP = A - P   # shape: (M*N, N, 2)
    t_num = cross(diff_AP, diff_AB)   # shape: (M*N, N)
    u_num = cross(diff_AP, diff_PQ)   # shape: (M*N, N)
    valid_denom = np.abs(denom) > eps
    t_val = np.full_like(denom, -1, dtype=np.float64)
    u_val = np.full_like(denom, -1, dtype=np.float64)
    t_val[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u_val[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    intersect = bbox_overlap & valid_denom & (t_val >= 0) & (t_val <= 1) & (u_val >= 0) & (u_val <= 1)

    # Exclude intersections due to shared endpoint (i.e., when the outer vertex equals A or B)
    shared = np.all(np.isclose(Q, A), axis=-1) | np.all(np.isclose(Q, B), axis=-1)
    intersect = intersect & (~shared)

    # A candidate connection is invalid if it intersects any outer edge
    invalid_conn = np.any(intersect, axis=1)
    valid_conn = ~invalid_conn

    valid_idx = np.nonzero(valid_conn)[0]
    inner_idx = valid_idx // N
    outer_idx = valid_idx % N
    return np.stack((inner_idx, outer_idx), axis=1)


def valid_inner_outer_connections_raycast(inner, outer):
    """ Given two 2D polygons, inner and outer, represented as numpy arrays of shape (M,2) and (N,2), 
    this function returns an (K,2) array of index pairs (i, j) such that the ray from inner[i] to outer[j] 
    does not encounter any outer polygon edge before reaching the outer vertex (except when the outer vertex is a shared endpoint). 
    This implementation uses a raycasting approach with vectorized intersection tests.

    Parameters:
      inner: np.ndarray, shape (M,2)
      outer: np.ndarray, shape (N,2), where the vertices define a closed polygon with edges between consecutive vertices and the last vertex connecting to the first.

    Returns:
      np.ndarray of shape (K,2) containing valid connections as pairs [i, j].
    """
    M = inner.shape[0]
    N = outer.shape[0]

    # Outer polygon edges: each edge from outer[k] to outer[(k+1)%N]
    outer_edges = np.stack([outer, np.roll(outer, -1, axis=0)], axis=1)  # shape: (N, 2, 2)

    # Build candidate rays from each inner vertex to each outer vertex
    candidate_P = np.repeat(inner, N, axis=0)  # shape: (M*N, 2)
    candidate_Q = np.tile(outer, (M, 1))         # shape: (M*N, 2)

    # Reshape for broadcasting
    P = candidate_P[:, None, :]   # shape: (M*N, 1, 2)
    Q = candidate_Q[:, None, :]   # shape: (M*N, 1, 2)

    # Outer edges endpoints
    A = outer_edges[:, 0][None, :, :]  # shape: (1, N, 2)
    B = outer_edges[:, 1][None, :, :]  # shape: (1, N, 2)

    # Bounding box pre-filtering
    cand_min = np.minimum(P, Q)   # shape: (M*N, 1, 2)
    cand_max = np.maximum(P, Q)
    edge_min = np.minimum(A, B)   # shape: (1, N, 2)
    edge_max = np.maximum(A, B)
    bbox_overlap = (cand_min[..., 0] <= edge_max[..., 0]) & (cand_max[..., 0] >= edge_min[..., 0]) & \
                   (cand_min[..., 1] <= edge_max[..., 1]) & (cand_max[..., 1] >= edge_min[..., 1])

    # Vectorized raycasting intersection using cross product method
    diff_PQ = Q - P           # shape: (M*N, 1, 2)
    diff_AB = B - A           # shape: (1, N, 2)
    cross = lambda a, b: a[..., 0]*b[..., 1] - a[..., 1]*b[..., 0]

    # Compute parameter t for the ray and u for the edge
    denom = cross(diff_PQ, diff_AB)   # shape: (M*N, N)
    eps = 1e-10

    diff_AP = A - P   # shape: (M*N, N, 2)
    t_num = cross(diff_AP, diff_AB)   # shape: (M*N, N)
    u_num = cross(diff_AP, diff_PQ)   # shape: (M*N, N)
    valid_denom = np.abs(denom) > eps
    t_val = np.full_like(denom, -1, dtype=np.float64)
    u_val = np.full_like(denom, -1, dtype=np.float64)
    t_val[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u_val[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    # For raycasting, a candidate ray is intersecting an edge if t is in [0, 1 - eps] and u in [0, 1]
    intersect = bbox_overlap & valid_denom & (t_val >= 0) & (t_val < 1 - eps) & (u_val >= 0) & (u_val <= 1)

    # Exclude intersections due to shared endpoint: when the outer vertex Q exactly matches an edge endpoint A or B.
    shared = np.all(np.isclose(Q, A), axis=-1) | np.all(np.isclose(Q, B), axis=-1)
    intersect = intersect & (~shared)

    # A candidate ray is invalid if any intersection is found along the ray before reaching Q
    invalid_ray = np.any(intersect, axis=1)  # shape: (M*N,)
    valid_ray = ~invalid_ray

    # Map valid candidate rays back to inner and outer indices
    valid_idx = np.nonzero(valid_ray)[0]
    inner_idx = valid_idx // N
    outer_idx = valid_idx % N
    return np.stack((inner_idx, outer_idx), axis=1)


def compute_offset_points_straight(inner: np.ndarray, outer: np.ndarray, offset: float, straight_idx: np.ndarray, search_distance: float) -> np.ndarray:
    """
    For each index in 'straight_idx', computes a query point based on the inner polygon edge defined by (indice-1, indice).
    The query point is generated by moving from inner[indice] in a direction perpendicular to the edge, scaled by 'offset'.
    Then, for each query point, finds and returns the closest point from the outer polygon.

    Parameters:
        inner (np.ndarray): An (N,2) array of inner polygon points.
        outer (np.ndarray): An (M,2) array of outer polygon points.
        offset (float): The length of the perpendicular offset vector.
        straight_idx (np.ndarray): Array of indices (in inner) where the angle is straight.
        search_distance (float): Maximum distance to search for closest point.

    Returns:
        np.ndarray: An array of closest points from the outer polygon corresponding to each query point.
    """
    # Calculate the directional vectors from the previous point to the current point
    vectors = inner[straight_idx] - inner[straight_idx - 1]

    # Compute the perpendicular vectors using a counter-clockwise rotation (-dy, dx)
    perp_vectors = np.column_stack((-vectors[:, 1], vectors[:, 0]))

    # Normalize the perpendicular vectors to get unit vectors
    norms = np.linalg.norm(perp_vectors, axis=1, keepdims=True)
    unit_perp = perp_vectors / norms

    # Compute the query points: move from inner point at straight_idx by offset along the perpendicular direction
    query_points = inner[straight_idx] + offset * unit_perp

    # Compute squared distances between each query point and all points in the outer polygon using broadcasting
    # query_points has shape (K, 2) and outer has shape (M, 2), result shape (K, M)
    diff = outer[np.newaxis, :, :] - query_points[:, np.newaxis, :]
    squared_dists = np.sum(diff**2, axis=2)

    # For each query point, get the index of the closest outer point if within search_distance, else use -1
    min_dists = np.min(squared_dists, axis=1)
    argmin_indices = np.argmin(squared_dists, axis=1)
    result = np.where(min_dists <= search_distance**2, argmin_indices, -1)
    return result


def compute_offset_points_acute(inner: np.ndarray, outer: np.ndarray, offset: float, acute_idx: np.ndarray, search_distance: float) -> tuple:
    """
    For each index in 'acute_idx', computes two query points based on the two edges adjacent to the vertex.
    For the edge (i-1,i), computes the query point as inner[i] + offset * unit_perp, where unit_perp is the unit perpendicular vector computed from inner[i] - inner[i-1].
    For the edge (i+1,i), computes the query point using the edge reversed: inner[i] - inner[i+1], then flips the perpendicular vector 180 degrees.
    Returns a tuple of two arrays containing the closest points from 'outer' corresponding to each query point.
    """
    # First segment: from edge (i-1, i)
    vec_prev = inner[acute_idx] - inner[acute_idx - 1]
    perp_prev = np.column_stack((-vec_prev[:, 1], vec_prev[:, 0]))
    norm_prev = np.linalg.norm(perp_prev, axis=1, keepdims=True)
    unit_perp_prev = perp_prev / norm_prev
    query_points_prev = inner[acute_idx] + offset * unit_perp_prev

    # Second segment: from edge (i+1, i); note: use reversed edge
    vec_next = inner[acute_idx] - inner[acute_idx + 1]
    perp_next = np.column_stack((-vec_next[:, 1], vec_next[:, 0]))
    norm_next = np.linalg.norm(perp_next, axis=1, keepdims=True)
    unit_perp_next = perp_next / norm_next
    # Flip the direction by multiplying by -1
    query_points_next = inner[acute_idx] + offset * (-unit_perp_next)

    # Find closest points in outer for both sets of query points
    diff_prev = outer[np.newaxis, :, :] - query_points_prev[:, np.newaxis, :]
    sq_dists_prev = np.sum(diff_prev**2, axis=2)
    min_dists_prev = np.min(sq_dists_prev, axis=1)
    argmin_prev = np.argmin(sq_dists_prev, axis=1)
    result_prev = np.where(min_dists_prev <= search_distance**2, argmin_prev, -1)

    diff_next = outer[np.newaxis, :, :] - query_points_next[:, np.newaxis, :]
    sq_dists_next = np.sum(diff_next**2, axis=2)
    min_dists_next = np.min(sq_dists_next, axis=1)
    argmin_next = np.argmin(sq_dists_next, axis=1)
    result_next = np.where(min_dists_next <= search_distance**2, argmin_next, -1)

    return result_prev, result_next


def compute_offset_points_obtuse(inner: np.ndarray, outer: np.ndarray, offset: float, obtuse_idx: np.ndarray, search_distance: float) -> np.ndarray:
    """
    For each index in 'obtuse_idx', computes the query point based on the angle bisector of the two adjacent edges at the vertex.

    For a vertex at index i, the two edges are defined by (i-1, i) and (i+1, i). The function computes the unit vectors in the direction of these edges (from inner[i] towards inner[i-1] and inner[i+1] respectively), sums them to get the bisector, normalizes the bisector, and then computes the query point as inner[i] - offset * bisector_unit.

    Parameters:
        inner (np.ndarray): (N,2) array of inner polygon points.
        outer (np.ndarray): (M,2) array of outer polygon points.
        offset (float): The length of the offset vector.
        obtuse_idx (np.ndarray): Array of indices in inner corresponding to obtuse vertices.
        search_distance (float): Maximum distance to search for closest point.

    Returns:
        np.ndarray: An array of closest points from the outer polygon corresponding to each query point.
    """
    # For each obtuse vertex at index i, compute the two edge vectors
    v1 = inner[obtuse_idx] - inner[obtuse_idx - 1]
    v2 = inner[obtuse_idx] - inner[obtuse_idx + 1]

    # Normalize the edge vectors
    u1 = v1 / np.linalg.norm(v1, axis=1, keepdims=True)
    u2 = v2 / np.linalg.norm(v2, axis=1, keepdims=True)

    # Compute the bisector as the sum of the unit vectors and normalize it
    bisector = u1 + u2
    bisector_unit = bisector / np.linalg.norm(bisector, axis=1, keepdims=True)

    # Compute the query points by offsetting from the vertex in the opposite direction of the bisector
    query_points = inner[obtuse_idx] - offset * bisector_unit

    # Find the closest points in outer using vectorized numpy operations
    diff = outer[np.newaxis, :, :] - query_points[:, np.newaxis, :]
    sq_dists = np.sum(diff**2, axis=2)
    min_dists = np.min(sq_dists, axis=1)
    argmin_indices = np.argmin(sq_dists, axis=1)
    result = np.where(min_dists <= search_distance**2, argmin_indices, -1)

    return result


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_polygon_angles(vertices, tolerance=0.1):
    """
    Classify vertices of a polygon based on their angles from outside perspective.
    
    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    tolerance: float, tolerance for angle classification
    
    Returns:
    tuple of three arrays containing indices of acute, straight, and obtuse angles
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)
    
    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices
    
    # Normalize vectors
    v1_norm = v1 / np.linalg.norm(v1, axis=1)[:, np.newaxis]
    v2_norm = v2 / np.linalg.norm(v2, axis=1)[:, np.newaxis]
    
    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)
    
    # Clip dot products to [-1, 1] to avoid numerical errors
    dot_products = np.clip(dot_products, -1, 1)
    
    # Calculate angles in radians
    angles = np.arccos(dot_products)
    
    # Calculate 2D cross product manually (z-component of 3D cross product)
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]
    
    # Flip angles where cross product is negative (inside angles)
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)
    
    # Classify angles with tolerance
    acute_mask = angles < np.pi - tolerance
    straight_mask = np.abs(angles - np.pi) <= tolerance
    obtuse_mask = angles > np.pi + tolerance
    
    return (
        np.where(acute_mask)[0],
        np.where(straight_mask)[0],
        np.where(obtuse_mask)[0]
    )


def generate_radial_edges(inner, outer, polygon_length, window):
    offsets = np.arange(window)
    outer_offsets = (outer[:, np.newaxis] + offsets) % polygon_length
    inner_expanded = inner[:, np.newaxis]  # Shape (n, 1)
    return np.stack([np.broadcast_to(inner_expanded, (len(inner), window)), outer_offsets], axis=-1)


def generate_continue_segments(outer, polygon_length, window):
    offsets = np.arange(window)
    current = (outer[:, np.newaxis] + offsets) % polygon_length
    next_vals = (current + 1) % polygon_length
    return np.stack([current, next_vals], axis=-1)
    

def vectorized_intersection_mask(radial_edges_indices, continue_segments_indices, inner_verts, outer_verts):
    """
    Vectorized intersection check between radial edges and continuation segments
    
    Args:
        radial_edges_indices: (M,N,2) array of [inner_idx, outer_idx] pairs
        continue_segments_indices: (K,2) array of [outer_idx1, outer_idx2] pairs
        inner_verts: (I,2) array of inner polygon vertices
        outer_verts: (O,2) array of outer polygon vertices
    
    Returns:
        (M,N) boolean mask where True indicates intersection with any continue segment
    """
    # Convert indices to coordinates
    radial_starts = inner_verts[radial_edges_indices[..., 0]]  # (M,N,2)
    radial_ends = outer_verts[radial_edges_indices[..., 1]]   # (M,N,2)
    
    cont_starts = outer_verts[continue_segments_indices[:, 0]]  # (K,2)
    cont_ends = outer_verts[continue_segments_indices[:, 1]]    # (K,2)

    # Add dimensions for broadcasting
    radial_starts = radial_starts[:, :, np.newaxis, np.newaxis, :]  # (M,N,1,1,2)
    radial_ends = radial_ends[:, :, np.newaxis, np.newaxis, :]      # (M,N,1,1,2)
    cont_starts = cont_starts[np.newaxis, np.newaxis, :, :]        # (1,1,K,2)
    cont_ends = cont_ends[np.newaxis, np.newaxis, :, :]            # (1,1,K,2)

    # Vectorized line intersection checks
    # Using cross product method for all segment pairs
    v1 = radial_ends - radial_starts  # Radial edge vectors (M,N,1,1,2)
    v2 = cont_ends - cont_starts      # Continue segment vectors (1,1,K,2)
    
    # Cross products
    cross = v1[..., 0] * v2[..., 1] - v1[..., 1] * v2[..., 0]  # (M,N,K)
    
    # Calculate t and u parameters
    delta = cont_starts - radial_starts  # (M,N,K,2)
    t_numerator = delta[..., 0] * v2[..., 1] - delta[..., 1] * v2[..., 0]
    u_numerator = delta[..., 0] * v1[..., 1] - delta[..., 1] * v1[..., 0]
    
    # Avoid division by zero
    epsilon = 1e-8
    cross_safe = np.where(np.abs(cross) < epsilon, np.sign(cross)*epsilon, cross)
    
    t = t_numerator / cross_safe
    u = u_numerator / cross_safe
    
    # Intersection conditions
    t_valid = (t >= 0) & (t <= 1)
    u_valid = (u >= 0) & (u <= 1)
    intersects = t_valid & u_valid
    
    # Bounding box quick check
    radial_min = np.minimum(radial_starts, radial_ends)
    radial_max = np.maximum(radial_starts, radial_ends)
    cont_min = np.minimum(cont_starts, cont_ends)
    cont_max = np.maximum(cont_starts, cont_ends)
    
    bbox_overlap = (
        (radial_max[..., 0] >= cont_min[..., 0]) & 
        (radial_min[..., 0] <= cont_max[..., 0]) &
        (radial_max[..., 1] >= cont_min[..., 1]) & 
        (radial_min[..., 1] <= cont_max[..., 1])
    )
    
    # Final intersection check
    valid_intersections = intersects & bbox_overlap
    
    # Aggregate over continue segments dimension
    return np.any(valid_intersections, axis=2)


def compute_intersection_mask(radial_edges, continue_segments, inner, outer): 
    # Convert radial_edges indices to coordinates 
    # radial_edges is (M, N, 2), where [i,j,0] is inner index, [i,j,1] is outer index 
    radial_segments = np.array([ [inner[i], outer[j]] for i, j in radial_edges.reshape(-1, 2) ]).reshape(*radial_edges.shape[:2], 2, 2)

    # Convert continue_segments indices to coordinates
    # continue_segments is (K, L, 2), where [k,l,0] and [k,l,1] are outer indices
    continue_seg_coords = np.array([
        [outer[k], outer[l]] for k, l in continue_segments.reshape(-1, 2)
    ]).reshape(*continue_segments.shape[:2], 2, 2)

    # Reshape for broadcasting
    radial_segments = radial_segments[:, :, np.newaxis, np.newaxis, :, :]  # Shape (M, N, 1, 1, 2, 2)
    continue_segments = continue_seg_coords[np.newaxis, np.newaxis, :, :, :, :]  # Shape (1, 1, K, L, 2, 2)

    # Extract coordinates for all pairs
    A = radial_segments[..., 0, :]  # Start points of radial edges
    B = radial_segments[..., 1, :]  # End points of radial edges
    C = continue_segments[..., 0, :]  # Start points of continue segments
    D = continue_segments[..., 1, :]  # End points of continue segments

    # Compute vectors
    AB = B - A
    CD = D - C

    # Compute cross products
    cross_AB_CD = np.cross(AB, CD)
    cross_AC_AB = np.cross(C - A, AB)
    cross_AC_CD = np.cross(C - A, CD)

    # Avoid division by zero by using a small epsilon
    epsilon = 1e-8
    cross_AB_CD = np.where(np.abs(cross_AB_CD) < epsilon, epsilon, cross_AB_CD)

    # Compute parameters t and u
    t = cross_AC_CD / cross_AB_CD
    u = cross_AC_AB / cross_AB_CD

    # Check if t and u are within [0, 1]
    t_in_range = (t >= 0) & (t <= 1)
    u_in_range = (u >= 0) & (u <= 1)
    intersect = t_in_range & u_in_range

    # Check bounding box overlaps as a quick rejection
    # Compute min and max coordinates for each segment
    radial_min = np.minimum(A, B)
    radial_max = np.maximum(A, B)
    continue_min = np.minimum(C, D)
    continue_max = np.maximum(C, D)

    # Check overlap in x and y directions
    overlap_x = (radial_min[..., 0] <= continue_max[..., 0]) & (radial_max[..., 0] >= continue_min[..., 0])
    overlap_y = (radial_min[..., 1] <= continue_max[..., 1]) & (radial_max[..., 1] >= continue_min[..., 1])
    bbox_overlap = overlap_x & overlap_y

    # Final intersection check considering both conditions
    intersections = bbox_overlap & intersect

    # Aggregate over continue_segments dimensions (K, L)
    mask = np.any(intersections, axis=(2, 3))

    return mask.reshape(radial_edges.shape[:2] + (1,))  # Reshape to 3D as per user request

import numpy as np

def check_intersections(radial_edges, continue_segments, inner_polygon, outer_polygon):
    # Get coordinates
    radial_starts = inner_polygon[radial_edges[:, :, 0]]  # (M, N, 2)
    radial_ends = outer_polygon[radial_edges[:, :, 1]]    # (M, N, 2)
    continue_starts = outer_polygon[continue_segments[:, :, 0]]  # (K, L, 2)
    continue_ends = outer_polygon[continue_segments[:, :, 1]]    # (K, L, 2)
    
    # Broadcast to (M, N, K, L, 2)
    A = radial_starts[:, :, None, None, :]
    B = radial_ends[:, :, None, None, :]
    C = continue_starts[None, None, :, :, :]
    D = continue_ends[None, None, :, :, :]
    
    # Compute vectors
    vec_AB = B - A
    vec_CD = D - C
    vec_CA = A - C
    vec_CB = B - C
    vec_AC = C - A
    vec_AD = D - A
    
    # Compute orientations
    dir_CDA = vec_CD[..., 0] * vec_CA[..., 1] - vec_CD[..., 1] * vec_CA[..., 0]
    dir_CDB = vec_CD[..., 0] * vec_CB[..., 1] - vec_CD[..., 1] * vec_CB[..., 0]
    dir_ABC = vec_AB[..., 0] * vec_AC[..., 1] - vec_AB[..., 1] * vec_AC[..., 0]
    dir_ABD = vec_AB[..., 0] * vec_AD[..., 1] - vec_AB[..., 1] * vec_AD[..., 0]
    
    # Check intersections (strict crossing)
    intersect = (dir_CDA * dir_CDB < 0) & (dir_ABC * dir_ABD < 0)
    
    # Any intersection per radial segment
    any_intersect = np.any(intersect, axis=(2, 3))  # (M, N)
    
    # Match requested shape (M, N, 2), assuming duplication
    result = np.stack([any_intersect, any_intersect], axis=2)
    
    return result


def check_segment_intersections(radial_edges, continue_segments, inner_polygon, outer_polygon):
    """
    Check if segments from radial_edges intersect with segments from continue_segments.
    
    Parameters:
    -----------
    radial_edges : numpy.ndarray
        3D array of shape (M, N, 2) where:
        - [i,j,0] is the index from inner_polygon
        - [i,j,1] is the index from outer_polygon
    continue_segments : numpy.ndarray
        3D array of shape (K, L, 2) where:
        - [k,l,0] and [k,l,1] are both indices from outer_polygon
    inner_polygon : numpy.ndarray
        2D array of coordinates for inner polygon vertices
    outer_polygon : numpy.ndarray
        2D array of coordinates for outer polygon vertices
    
    Returns:
    --------
    numpy.ndarray
        3D boolean mask array of shape radial_edges, where True indicates an intersection
    """
    # Extract actual coordinates for the radial edges
    M, N, _ = radial_edges.shape
    K, L, _ = continue_segments.shape
    
    # Get coordinates of radial edge start points (from inner polygon)
    radial_start = inner_polygon[radial_edges[:, :, 0]]  # Shape (M, N, 2) - x,y coordinates
    
    # Get coordinates of radial edge end points (from outer polygon)
    radial_end = outer_polygon[radial_edges[:, :, 1]]    # Shape (M, N, 2) - x,y coordinates
    
    # Get coordinates of continuation segments
    cont_start = outer_polygon[continue_segments[:, :, 0]]  # Shape (K, L, 2)
    cont_end = outer_polygon[continue_segments[:, :, 1]]    # Shape (K, L, 2)
    
    # Reshape to enable broadcasting for vectorized computation
    # For radial edges: (M, N, 1, 1, 2) - adding dimensions for continue_segments
    rs = radial_start[:, :, np.newaxis, np.newaxis, :]
    re = radial_end[:, :, np.newaxis, np.newaxis, :]
    
    # For continue segments: (1, 1, K, L, 2) - adding dimensions for radial_edges
    cs = cont_start[np.newaxis, np.newaxis, :, :, :]
    ce = cont_end[np.newaxis, np.newaxis, :, :, :]
    
    # --- Implement line segment intersection test ---
    
    # Convert line segments to parametric form: p + t*r where p is start point and r is direction vector
    r_dir = re - rs  # Direction vector for radial edges
    s_dir = ce - cs  # Direction vector for continue segments
    
    # Cross product of direction vectors (for determining if lines are parallel)
    # Shape will be (M, N, K, L)
    r_cross_s = r_dir[:, :, :, :, 0] * s_dir[:, :, :, :, 1] - r_dir[:, :, :, :, 1] * s_dir[:, :, :, :, 0]
    
    # If r_cross_s is zero, lines are parallel (no intersection unless collinear)
    # Adding small epsilon to avoid floating point precision issues
    epsilon = 1e-10
    parallel = np.abs(r_cross_s) < epsilon
    
    # Vector between start points
    q_minus_p = cs - rs
    
    # Parameters for intersection point
    # For radial edge: p + t*r (t parameter)
    t = (q_minus_p[:, :, :, :, 0] * s_dir[:, :, :, :, 1] - q_minus_p[:, :, :, :, 1] * s_dir[:, :, :, :, 0]) / (r_cross_s + epsilon * parallel)
    
    # For continue segment: q + u*s (u parameter)
    u = (q_minus_p[:, :, :, :, 0] * r_dir[:, :, :, :, 1] - q_minus_p[:, :, :, :, 1] * r_dir[:, :, :, :, 0]) / (r_cross_s + epsilon * parallel)
    
    # Intersection occurs when 0 <= t <= 1 and 0 <= u <= 1
    valid_t = (0 <= t) & (t <= 1)
    valid_u = (0 <= u) & (u <= 1)
    
    # Intersection mask - reshape back to original radial_edges shape
    # We need to check if any continue segment intersects with a radial edge
    intersect_mask = (valid_t & valid_u & ~parallel).any(axis=(2, 3))
    
    # Special case: check for endpoint coincidences (point to point is normal situation)
    # This is already handled in the above calculation as endpoints will give t=0, t=1, u=0, or u=1
    
    return intersect_mask


def line_segment_intersects_polygon_edges(A, B, P, max_segment_length):
    """
    Check if the line segment AB intersects any edge of the polygon P.

    Parameters:
        A : array_like, shape (2,)
            Starting point of the line segment [x, y].
        B : array_like, shape (2,)
            Ending point of the line segment [x, y].
        P : array_like, shape (N, 2)
            The polygon vertices [[x0, y0], [x1, y1], ...].
        max_segment_length : float
            The maximum length of any segment in the polygon.

    Returns:
        bool
            True if the line segment intersects any edge of the polygon, False otherwise.
    """
    # Convert inputs to NumPy arrays if they aren't already
    A = np.asarray(A)
    B = np.asarray(B)
    P = np.asarray(P)

    # Buffer distance is the maximum polygon segment length
    buffer = max_segment_length

    # Compute the buffered bounding box around the line segment
    min_x = min(A[0], B[0]) - buffer
    max_x = max(A[0], B[0]) + buffer
    min_y = min(A[1], B[1]) - buffer
    max_y = max(A[1], B[1]) + buffer

    # Polygon edges: start_points are P, end_points are shifted by one
    start_points = P
    end_points = np.roll(P, -1, axis=0)

    # Compute bounding boxes for all polygon edges
    e_min_x = np.minimum(start_points[:, 0], end_points[:, 0])
    e_max_x = np.maximum(start_points[:, 0], end_points[:, 0])
    e_min_y = np.minimum(start_points[:, 1], end_points[:, 1])
    e_max_y = np.maximum(start_points[:, 1], end_points[:, 1])

    # Filter edges that overlap with the buffered bounding box
    mask = (e_min_x <= max_x) & (e_max_x >= min_x) & \
           (e_min_y <= max_y) & (e_max_y >= min_y)
    filtered_indices = np.where(mask)[0]

    print("Filtered indices:", filtered_indices)

    # If no edges overlap with the buffered bounding box, no intersection is possible
    if len(filtered_indices) == 0:
        return False

    # Extract filtered start and end points
    filtered_start = start_points[filtered_indices]
    filtered_end = end_points[filtered_indices]

    # Vector from A to B
    diff_BA = B - A

    # Compute sides for filtered polygon edges relative to AB
    # side_R and side_S determine if filtered_start and filtered_end are on opposite sides of AB
    side_R = diff_BA[0] * (filtered_start[:, 1] - A[1]) - \
             diff_BA[1] * (filtered_start[:, 0] - A[0])
    side_S = diff_BA[0] * (filtered_end[:, 1] - A[1]) - \
             diff_BA[1] * (filtered_end[:, 0] - A[0])

    # Compute vectors for each filtered edge
    diff_edge = filtered_end - filtered_start

    # Compute sides for A and B relative to each filtered edge
    # side_A and side_B determine if A and B are on opposite sides of the edge
    side_A = diff_edge[:, 0] * (A[1] - filtered_start[:, 1]) - \
             diff_edge[:, 1] * (A[0] - filtered_start[:, 0])
    side_B = diff_edge[:, 0] * (B[1] - filtered_start[:, 1]) - \
             diff_edge[:, 1] * (B[0] - filtered_start[:, 0])

    # Check intersection conditions: opposite sides for both pairs
    # Using <= 0 includes cases where segments touch at endpoints
    intersects = (side_R * side_S <= 0) & (side_A * side_B <= 0)

    # Return True if any intersection is found
    return np.any(intersects)


import geopandas as gpd

def geopandas_intersections(lines, polygon):
    # Convert lines to GeoDataFrame
    gdf_lines = gpd.GeoDataFrame(geometry=[
        LineString(line) if not isinstance(line, LineString) else line 
        for line in lines
    ])
    
    # Convert polygon to GeoDataFrame
    gdf_polygon = gpd.GeoDataFrame(geometry=[polygon.boundary])
    
    # Spatial join to find intersections
    joined = gpd.sjoin(gdf_lines, gdf_polygon, predicate='intersects')
    
    # Return indices of intersecting lines
    return joined.index.tolist()


from shapely.geometry import LineString, Polygon
from shapely.prepared import prep
import numpy as np
from rtree import index

def check_line_polygon_intersections(lines, polygon):
    """
    Efficiently check which lines intersect with polygon edges.
    
    Args:
        lines: List of LineString objects or line coordinates [(x1,y1), (x2,y2)]
        polygon: Shapely Polygon object
    
    Returns:
        List of indices of lines that intersect with polygon edges
    """
    # Convert polygon to just its boundary (the edges)
    boundary = polygon.boundary
    
    # Create a prepared geometry for fast containment tests
    prepared_boundary = prep(boundary)
    
    # Create an R-tree spatial index for the line segments
    idx = index.Index()
    for i, line in enumerate(lines):
        if not isinstance(line, LineString):
            line = LineString(line)
        idx.insert(i, line.bounds)
    
    # Use the spatial index to find potential intersections
    # based on bounding box overlap
    potential_matches = []
    for i in idx.intersection(boundary.bounds):
        potential_matches.append(i)
    
    # Verify actual intersections
    intersecting_lines = []
    for i in potential_matches:
        line = lines[i]
        if not isinstance(line, LineString):
            line = LineString(line)
        
        # Check if line intersects boundary (not just contains)
        if prepared_boundary.intersects(line) and not line.touches(boundary):
            intersecting_lines.append(i)
    
    return intersecting_lines


def create_rotated_bboxes_for_radial_edges(radial_edges, inner_polygon, outer_polygon, buffer_size):
    """
    Create rotated bounding boxes for multiple line segments defined by radial edges
    between inner and outer polygons.
    
    Parameters:
    radial_edges: numpy.ndarray of shape (M, N, 2) where [i,j,0] is the index from inner_polygon
                  and [i,j,1] is the index from outer_polygon
    inner_polygon: numpy.ndarray of coordinates for inner polygon vertices
    outer_polygon: numpy.ndarray of coordinates for outer polygon vertices
    buffer_size: float, buffer distance to add perpendicular to each line
    
    Returns:
    bboxes: numpy.ndarray of shape (M*N, 4, 2) containing corners of each rotated bbox
    """
    # Get shape information
    M, N = radial_edges.shape[:2]
    
    # Reshape radial_edges for easier indexing
    radial_edges_flat = radial_edges.reshape(-1, 2)
    
    # Get start points (from inner polygon) and end points (from outer polygon)
    start_points = inner_polygon[radial_edges_flat[:, 0]].astype(float)  # Convert to float
    end_points = outer_polygon[radial_edges_flat[:, 1]].astype(float)    # Convert to float
    
    # Calculate line vectors
    line_vectors = end_points - start_points  # Shape: (M*N, 2)
    
    # Calculate line lengths
    line_lengths = np.linalg.norm(line_vectors, axis=1)  # Shape: (M*N,)
    
    # Handle very short or zero-length lines
    is_short = line_lengths < 1e-10
    
    # Prepare arrays for normalized vectors
    line_unit_vectors = np.zeros_like(line_vectors)
    
    # Only normalize non-short lines to avoid division by zero
    non_short_mask = ~is_short
    line_unit_vectors[non_short_mask] = line_vectors[non_short_mask] / line_lengths[non_short_mask, np.newaxis]
    
    # Calculate perpendicular vectors (90 degrees counterclockwise)
    perp_vectors = np.zeros_like(line_unit_vectors)
    perp_vectors[:, 0] = -line_unit_vectors[:, 1]
    perp_vectors[:, 1] = line_unit_vectors[:, 0]
    perp_vectors = perp_vectors * buffer_size  # Use assignment instead of in-place operation
    
    # Create the four corners for each bounding box
    bboxes = np.zeros((M*N, 4, 2), dtype=float)
    
    # For non-short lines: use the perpendicular vectors
    bboxes[non_short_mask, 0] = start_points[non_short_mask] - perp_vectors[non_short_mask]  # bottom left
    bboxes[non_short_mask, 1] = end_points[non_short_mask] - perp_vectors[non_short_mask]    # bottom right
    bboxes[non_short_mask, 2] = end_points[non_short_mask] + perp_vectors[non_short_mask]    # top right
    bboxes[non_short_mask, 3] = start_points[non_short_mask] + perp_vectors[non_short_mask]  # top left
    
    # For short lines: create square boxes centered on the start point
    # These are special cases where the line is too short
    if np.any(is_short):
        short_start = start_points[is_short]
        buffer_vec = np.array([buffer_size, buffer_size], dtype=float)
        bboxes[is_short, 0] = short_start - buffer_vec  # bottom left
        bboxes[is_short, 1] = short_start + np.array([buffer_size, -buffer_size], dtype=float)  # bottom right
        bboxes[is_short, 2] = short_start + buffer_vec  # top right
        bboxes[is_short, 3] = short_start + np.array([-buffer_size, buffer_size], dtype=float)  # top left
    
    return bboxes


def check_points_in_rotated_bboxes_vectorized(points, bboxes):
    """
    Vectorized check for which points are inside which rotated bounding boxes.
    
    Parameters:
    points: numpy array of shape (P, 2) containing points to check
    bboxes: numpy array of shape (B, 4, 2) containing corners of each rotated bbox
    
    Returns:
    numpy array of booleans of shape (B, P) where result[i, j] is True if 
    point j is inside bbox i
    """
    # Ensure points are float type
    points = points.astype(float)
    
    # Number of points and bboxes
    B = bboxes.shape[0]
    P = points.shape[0]
    
    # For each bbox, get edges by adding the first point at the end to close the polygon
    first_points = np.expand_dims(bboxes[:, 0], axis=1)  # Shape: (B, 1, 2)
    closed_bboxes = np.concatenate([bboxes, first_points], axis=1)  # Shape: (B, 5, 2)
    
    # Create edge vectors for all bboxes
    v0 = closed_bboxes[:, :-1]  # Starting vertices, shape: (B, 4, 2)
    v1 = closed_bboxes[:, 1:]   # Ending vertices, shape: (B, 4, 2)
    edge_vectors = v1 - v0       # Shape: (B, 4, 2)
    
    # Reshape for broadcasting with all points
    v0_expanded = np.expand_dims(v0, 2)          # Shape: (B, 4, 1, 2)
    edge_vectors_expanded = np.expand_dims(edge_vectors, 2)  # Shape: (B, 4, 1, 2)
    points_expanded = np.expand_dims(np.expand_dims(points, 0), 0)  # Shape: (1, 1, P, 2)
    
    # Vector from each edge start to each point for all bboxes
    to_points_vectors = points_expanded - v0_expanded  # Shape: (B, 4, P, 2)
    
    # Cross product to determine if points are on the left of each edge
    # For 2D vectors [a,b] and [c,d], cross product is a*d - b*c
    cross_products = (edge_vectors_expanded[:, :, :, 0] * to_points_vectors[:, :, :, 1] - 
                      edge_vectors_expanded[:, :, :, 1] * to_points_vectors[:, :, :, 0])  # Shape: (B, 4, P)
    
    # A point is inside if all cross products have the same sign (all >= 0)
    # Since our polygons are counter-clockwise, inside points should have all cross products >= 0
    inside_mask = np.all(cross_products >= 0, axis=1)  # Shape: (B, P)
    
    return inside_mask




def main():
    offset = 0.005
    passes = 100
    island_passes = 1
    base_name = "OffsetObject"
    buffer_attributes = {
        "join_style": 'mitre',
        "mitre_limit": 1.1
        }

    geometry = get_geometry()    
    # contour, islands = geometry_to_shapely(geometry)
    # polygon = geometry_to_polygon(geometry)
    exterior_list = []
    interior_list = []  

    seg_val = 0.005

    # inner_polygon = geometry[1] # 1
    inner_polygon = segmentize(geometry[1], seg_val)
    # outer_polygon = geometry[0]
    line_segment = geometry[0]
    outer_polygon = segmentize(geometry[0], seg_val)

    print("Inner polygon:\n", inner_polygon.shape)
    print("Outer polygon:\n", outer_polygon.shape)

    # print("Inner polygon:\n", inner_polygon)
    # print("line_segment:\n", line_segment)
    outer_polygon_sh = shapely.geometry.Polygon(outer_polygon)

    # time_start = time.time()
    # intersects = line_segment_intersects_polygon_edges(line_segment[0], line_segment[1], inner_polygon, 0.0001)
    # print("Time:", time.time() - time_start)
    # print("Intersects:", intersects)

    
    shapely_to_blender(shapely.geometry.Polygon(inner_polygon), "segmentized_inn", interiors=False)    
    shapely_to_blender(shapely.geometry.Polygon(outer_polygon), "segmentized_out", interiors=False)
    
    angle_groups = classify_polygon_angles(inner_polygon, tolerance=0.05)
    
    # print("Straight vertices:\n", np.sort(angle_groups[1]))
    # print("Acute vertices:\n", np.sort(angle_groups[0]))
    # print("Obtuse vertices:\n", np.sort(angle_groups[2]))
    
    inner_straight = angle_groups[1]
    outer_straight = compute_offset_points_straight(inner_polygon, outer_polygon, offset, inner_straight, 0.01) #straight

    # Create a boolean mask where outer_straight is not equal to -1 (we dont find the point in outer polygon)
    straight_mask = outer_straight != -1

    # Use the mask to filter the arrays
    inner_straight = inner_straight[straight_mask]
    outer_straight = outer_straight[straight_mask]
    
    polygon_length = outer_polygon.shape[0]
    window = 10
    
    radial_edges = generate_radial_edges(inner_straight, outer_straight, polygon_length, window)

    # print("Radial edges:\n", radial_edges.shape)
   
    lines = [LineString([inner_polygon[i], outer_polygon[j]]) for i, j in radial_edges.reshape(-1, 2)]
    time_start = time.time()
    intersecting_lines = check_line_polygon_intersections(lines, outer_polygon_sh)
    print("Time:", time.time() - time_start)
    # print("Intersecting lines:", intersecting_lines)

    bboxes = create_rotated_bboxes_for_radial_edges(radial_edges, inner_polygon, outer_polygon, 0.005)
    # print("BBoxes:\n", bboxes.shape)

    time2 = time.time()
    result = check_points_in_rotated_bboxes_vectorized(outer_polygon, bboxes)
    print(f"Time taken to check points in bboxes: {time.time() - time2}")
    # print(f"Result shape: {result[:44]}")

    time2 = time.time()
    result3 = geopandas_intersections(lines, outer_polygon_sh)
    print(f"Time taken to check intersections: {time.time() - time2}")

    

    # mask = check_intersections(radial_edges, continue_segments, inner_polygon, outer_polygon)
    
    
    

    '''
    # print(f'radial_edges:\n {radial_edges}')
    # print(f'continue_segments:\n {continue_segments}')
    #     

    # points = compute_offset_points_acute(inner_polygon, outer_polygon, offset, angle_groups[0], 0.01) #acute
    # points = compute_offset_points_obtuse(inner_polygon, outer_polygon, offset, angle_groups[2], 0.01) #obtuse
    
    # print(angle_groups[1].shape)
    # print(points.shape)
    # print(f'points:\n {points}')
    
    # time_start = time.time()
    # ac, rt, obt = classify_vertex_angles(new_coords, tolerance=2.0)       
    # print("Classification time:", time.time() - time_start)
    # # print("Acute vertices:\n", np.sort(ac))
    # # print("Straight vertices:\n", np.sort(rt))
    # # print("Obtuse vertices:\n", np.sort(obt))    
    

    # print(geometry)
    # metrics = compute_edge_metrics(geometry[1], geometry[0])
    # # print(metrics['angle_inner'])
    # print(metrics['angle_outer'].shape)
    # print(metrics['edge_length'])
    # time_start = time.time()
    # connetions = valid_inner_outer_connections(geometry[1], geometry[0])
    # print("valid_inner_outer_connections time:", time.time() - time_start)
    # print(connetions[connetions[:, 0] == 12])
    # time_start = time.time()
    # connections_raycast = valid_inner_outer_connections_raycast(geometry[1], geometry[0])
    # print("valid_inner_outer_connections_raycast time:", time.time() - time_start)
       
    # print(np.diff(connections_raycast, connetions))
    # print(connetions[connetions[:, 0] == 12])
    # print(connections_raycast[connections_raycast[:, 0] == 12])

    
    # for pass_num in range(passes):
    #     buffer_offset = offset * (pass_num + 1)
    #     buffer = polygon.buffer(-buffer_offset, **buffer_attributes)
    #     if buffer.is_empty:
    #         print(f'{pass_num} passes completed')
    #         break

    #     exteriors, interiors = decompose_to_polygons(buffer)
    #     exterior_list.extend(exteriors)
    #     interior_list.extend(interiors)

    # contour_graph = create_graph_containment(exterior_list)    
    # deepest_nodes = [node for node in contour_graph.nodes if contour_graph.out_degree(node) == 0]    

    # if len(deepest_nodes) < 2:
    #     shapely_list_to_blender(contour_graph, "order")
    #     return

    # tsp_indices = solve_tsp_for_nodes(deepest_nodes)
    # tsp_route = [deepest_nodes[i] for i in tsp_indices]    
    
    # # post_order = traverse_dag_postorder(contour_graph, tsp_route)
    # shapely_list_to_blender(post_order, "order")
    # coords = np.array(contour.exterior.coords)
    # min_length = 0.05
    # new_coords = segmentize(coords, min_length)    
    # new_contour = shapely.geometry.Polygon(new_coords)
    # shapely_to_blender(new_contour, "segmentized", interiors=False)
    '''



if __name__ == "__main__":
    main()