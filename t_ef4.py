import numpy as np
from scipy.optimize import least_squares

class FastEllipseFitter:
    """Optimized for 100+ iterations with minimal overhead"""
    
    def __init__(self, max_nfev=15, ftol=1e-6):
        self.max_nfev = max_nfev
        self.ftol = ftol
    
    @staticmethod
    def _residual(params, points):
        """Vectorized residual calculation"""
        x0, y0, a, b, theta = params
        
        cos_t, sin_t = np.cos(theta), np.sin(theta)
        a2, b2 = a * a, b * b
        
        dx = points[:, 0] - x0
        dy = points[:, 1] - y0
        
        x_rot = cos_t * dx + sin_t * dy
        y_rot = -sin_t * dx + cos_t * dy
        
        return (x_rot * x_rot) / a2 + (y_rot * y_rot) / b2 - 1.0
    
    # Fast Jacobian approximation
    @staticmethod
    def ellipse_jacobian(params, points):
        x0, y0, a, b, theta = params
        
        dx = points[:, 0] - x0
        dy = points[:, 1] - y0
        
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        x_rot = cos_theta * dx + sin_theta * dy
        y_rot = -sin_theta * dx + cos_theta * dy
        
        # Pre-compute common terms
        x2_a2 = x_rot**2 / a**2
        y2_b2 = y_rot**2 / b**2
        
        # Jacobian matrix
        J = np.empty((len(points), 5))
        
        # Partial derivatives
        J[:, 0] = -2 * (cos_theta * x_rot / a**2 - sin_theta * y_rot / b**2)
        J[:, 1] = -2 * (sin_theta * x_rot / a**2 + cos_theta * y_rot / b**2)
        J[:, 2] = -2 * x2_a2 / a
        J[:, 3] = -2 * y2_b2 / b
        J[:, 4] = 2 * (x_rot * y_rot / a**2 - x_rot * y_rot / b**2)
        
        return J
    
    def fit(self, points, bounds, initial_guess):
        """Single optimized fit"""
        result = least_squares(
            self._residual,
            initial_guess,
            bounds=bounds,
            method='trf',
            args=(points,),
            max_nfev=self.max_nfev,
            ftol=self.ftol,
            xtol=self.ftol,
            gtol=self.ftol,
            jac=self.ellipse_jacobian,
            diff_step=1e-8  # Smaller step for numerical derivatives
        )
        return result.x

import time
# Usage for your data
time1 = time.time()
fitter = FastEllipseFitter(max_nfev=15)
time2 = time.time()
print(f'Time: {time2-time1}')

points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

bounds = ([-18170, -9679, 30, 20, np.deg2rad(320)],
          [-18130, -9625, 60, 40, np.deg2rad(340)])

initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)

# Single call
time1 = time.time()
# for _ in range(20):
params = fitter.fit(points, bounds, initial_guess)
time2 = time.time()
print(f'Time: {time2-time1}')
print(params)