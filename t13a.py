import numpy as np

def find_closest_point_on_circle_to_segment_numpy(circle_center, circle_radius, p1, p2):
    """
    Optimized version of finding the closest point on a circle to a line segment.
    
    Args:
        circle_center: (2,) array-like, circle center coordinates
        circle_radius: float, circle radius
        p1, p2: (2,) array-like, segment endpoints
    
    Returns:
        tuple: (closest point on circle, minimum distance, closest point on segment)
    """
    # Convert inputs to numpy arrays and compute vectors in one go
    center = np.asarray(circle_center, dtype=np.float64)
    p1 = np.asarray(p1, dtype=np.float64)
    line_vec = np.asarray(p2, dtype=np.float64) - p1
    p1_to_center = center - p1
    
    # Compute line length squared
    line_len_sq = np.dot(line_vec, line_vec)
    
    if line_len_sq < 1e-12:  # Zero-length segment check
        closest_on_segment = p1
    else:
        # Project and clamp in one step
        t = np.clip(np.dot(p1_to_center, line_vec) / line_len_sq, 0, 1)
        closest_on_segment = p1 + t * line_vec
    
    # Vector from center to closest point
    vec_to_segment = closest_on_segment - center
    dist_to_segment = np.linalg.norm(vec_to_segment)
    
    if dist_to_segment < 1e-12:  # Center on segment check
        # Quick arbitrary point calculation
        point_on_circle = center + np.array([circle_radius, 0.0])
        return point_on_circle, 0.0, closest_on_segment
    
    # Final calculations
    direction = vec_to_segment / dist_to_segment
    closest_on_circle = center + circle_radius * direction
    min_distance = max(0.0, dist_to_segment - circle_radius)
    
    return closest_on_circle, min_distance, closest_on_segment


# --- Example Usage (same as Method 1) ---
C = (1, 1)
R = 5.0
P1 = (-2.35507, 9.83426)
P2 = (7, 10)

closest_point, min_dist, closest_on_segment_np = find_closest_point_on_circle_to_segment_numpy(C, R, P1, P2)

print(f"Circle Center: {C}, Radius: {R}")
print(f"Segment Endpoints: {P1}, {P2}")
print("-" * 30)
if closest_point is not None:
    print(f"Closest Point on Circle: ({closest_point[0]:.4f}, {closest_point[1]:.4f})")
    print(f"Closest Point on Segment: ({closest_on_segment_np[0]:.4f}, {closest_on_segment_np[1]:.4f})")
    print(f"Minimum Distance: {min_dist:.4f}")

else:
     print("Center lies on the segment. Minimum distance is 0.")