import numpy as np
from scipy.optimize import minimize
from scipy.linalg import svd
import time
class EllipseFitter_manus:
    """
    A comprehensive ellipse fitting class that handles various degenerate cases
    and provides multiple fitting methods.
    """

    def __init__(self):
        self.last_error = None
        self.last_method_used = None

    def fit(self, x, y, method='auto'):
        """
        Fit an ellipse to the given points using the specified method.

        Parameters:
        -----------
        x, y : array-like
            Coordinates of the points
        method : str
            'auto' - try multiple methods automatically
            'direct' - Fitzgibbon direct least squares
            'svd' - SVD-based fitting
            'geometric' - Geometric distance minimization

        Returns:
        --------
        dict or None
            Dictionary with ellipse parameters if successful, None if failed
        """
        x = np.asarray(x, dtype=float)
        y = np.asarray(y, dtype=float)

        if len(x) != len(y):
            self.last_error = "x and y must have the same length"
            return None

        if len(x) < 5:
            self.last_error = "At least 5 points are required to fit an ellipse"
            return None

        # Check for degenerate cases
        if self._check_degenerate_cases(x, y):
            return None

        if method == 'auto':
            return self._fit_auto(x, y)
        elif method == 'direct':
            return self._fit_direct(x, y)
        elif method == 'svd':
            return self._fit_svd(x, y)
        elif method == 'geometric':
            return self._fit_geometric(x, y)
        else:
            self.last_error = f"Unknown method: {method}"
            return None

    def _check_degenerate_cases(self, x, y):
        """Check for various degenerate cases"""

        # Check for duplicate points
        unique_points = len(set(zip(x, y)))
        if unique_points < 5:
            self.last_error = f"Only {unique_points} unique points, need at least 5"
            return True

        # Check for collinearity
        if self._is_collinear(x, y):
            self.last_error = "Points are collinear"
            return True

        # Check for points forming a line segment (all points lie on a line)
        if self._points_on_line_segment(x, y):
            self.last_error = "Points lie on a line segment"
            return True

        return False

    def _is_collinear(self, x, y, tolerance=1e-10):
        """Check if points are approximately collinear"""
        if len(x) < 3:
            return True

        # Use cross product to check collinearity
        for i in range(2, len(x)):
            # Vector from point 0 to point 1
            v1 = np.array([x[1] - x[0], y[1] - y[0]])
            # Vector from point 0 to point i
            v2 = np.array([x[i] - x[0], y[i] - y[0]])
            # Cross product magnitude
            cross_product = abs(v1[0] * v2[1] - v1[1] * v2[0])
            if cross_product > tolerance:
                return False
        return True

    def _points_on_line_segment(self, x, y, tolerance=1e-8):
        """Check if all points lie approximately on a line segment"""
        if len(x) < 3:
            return True

        # Fit a line to the points and check residuals
        A = np.vstack([x, np.ones(len(x))]).T
        try:
            m, b = np.linalg.lstsq(A, y, rcond=None)[0]
            residuals = np.abs(y - (m * x + b))
            return np.all(residuals < tolerance)
        except:
            return False

    def _fit_auto(self, x, y):
        """Try multiple methods automatically"""
        methods = ['direct', 'svd', 'geometric']

        for method in methods:
            result = getattr(self, f'_fit_{method}')(x, y)
            if result is not None:
                self.last_method_used = method
                return result

        self.last_error = "All fitting methods failed"
        return None

    def _fit_direct(self, x, y):
        """Direct least squares ellipse fitting (Fitzgibbon method)"""
        try:
            D1 = np.vstack([x**2, x*y, y**2]).T
            D2 = np.vstack([x, y, np.ones(len(x))]).T
            S1 = D1.T @ D1
            S2 = D1.T @ D2
            S3 = D2.T @ D2

            # Check if S3 is invertible
            if np.linalg.det(S3) < 1e-12:
                self.last_error = "S3 matrix is singular (direct method)"
                return None

            T = -np.linalg.inv(S3) @ S2.T
            M = S1 + S2 @ T
            C = np.array(((0, 0, 2), (0, -1, 0), (2, 0, 0)), dtype=float)
            M = np.linalg.inv(C) @ M

            eigval, eigvec = np.linalg.eig(M)

            # Find eigenvectors that satisfy the ellipse constraint
            con = 4 * eigvec[0] * eigvec[2] - eigvec[1]**2
            valid_indices = np.where(con > 0)[0]

            if len(valid_indices) == 0:
                self.last_error = "No valid ellipse solution found (direct method)"
                return None

            # Choose the eigenvector with the smallest positive eigenvalue
            valid_eigenvalues = eigval[valid_indices]
            min_idx = valid_indices[np.argmin(valid_eigenvalues)]
            ak = eigvec[:, min_idx]

            coeffs = np.concatenate((ak, T @ ak)).ravel()
            return self._coeffs_to_params(coeffs)

        except Exception as e:
            self.last_error = f"Direct method failed: {str(e)}"
            return None

    def _fit_svd(self, x, y):
        """SVD-based ellipse fitting"""
        try:
            # Design matrix for general conic: ax^2 + bxy + cy^2 + dx + ey + f = 0
            D = np.column_stack([x**2, x*y, y**2, x, y, np.ones(len(x))])

            # SVD decomposition
            U, s, Vt = svd(D)

            # The solution is the last column of V (or last row of Vt)
            coeffs = Vt[-1, :]

            # Check if this represents an ellipse (4ac - b^2 > 0)
            a, b, c = coeffs[0], coeffs[1], coeffs[2]
            discriminant = 4*a*c - b**2

            if discriminant <= 0:
                self.last_error = "SVD solution does not represent an ellipse"
                return None

            return self._coeffs_to_params(coeffs)

        except Exception as e:
            self.last_error = f"SVD method failed: {str(e)}"
            return None

    def _fit_geometric(self, x, y):
        """Geometric ellipse fitting using optimization"""
        try:
            # Initial guess
            x_center = np.mean(x)
            y_center = np.mean(y)
            a_init = np.std(x) * 2
            b_init = np.std(y) * 2
            theta_init = 0

            initial_params = [x_center, y_center, a_init, b_init, theta_init]

            def objective(params):
                x0, y0, a, b, theta = params
                if a <= 0 or b <= 0:
                    return 1e10

                # Calculate geometric distance from each point to the ellipse
                total_distance = 0
                for xi, yi in zip(x, y):
                    # Transform point to ellipse coordinate system
                    cos_theta = np.cos(theta)
                    sin_theta = np.sin(theta)
                    dx = xi - x0
                    dy = yi - y0
                    x_rot = dx * cos_theta + dy * sin_theta
                    y_rot = -dx * sin_theta + dy * cos_theta

                    # Find closest point on ellipse (approximate)
                    if abs(x_rot) < 1e-10 and abs(y_rot) < 1e-10:
                        distance = 0
                    else:
                        t = np.arctan2(y_rot * a, x_rot * b)
                        x_ellipse = a * np.cos(t)
                        y_ellipse = b * np.sin(t)

                        # Transform back
                        x_closest = x0 + x_ellipse * cos_theta - y_ellipse * sin_theta
                        y_closest = y0 + x_ellipse * sin_theta + y_ellipse * cos_theta

                        distance = np.sqrt((xi - x_closest)**2 + (yi - y_closest)**2)

                    total_distance += distance**2

                return total_distance

            # Bounds to ensure positive semi-axes
            bounds = [(-np.inf, np.inf), (-np.inf, np.inf), (0.01, np.inf), (0.01, np.inf), (-np.pi, np.pi)]

            result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B')

            if result.success:
                x0, y0, a, b, theta = result.x
                # Ensure a >= b (semi-major >= semi-minor)
                if b > a:
                    a, b = b, a
                    theta += np.pi/2

                e = np.sqrt(1 - (b/a)**2) if a > b else 0

                return {
                    'center': (x0, y0),
                    'semi_major': a,
                    'semi_minor': b,
                    'eccentricity': e,
                    'rotation': theta % np.pi,
                    'method': 'geometric'
                }
            else:
                self.last_error = "Geometric optimization failed to converge"
                return None

        except Exception as e:
            self.last_error = f"Geometric method failed: {str(e)}"
            return None

    def _coeffs_to_params(self, coeffs):
        """Convert conic coefficients to ellipse parameters"""
        try:
            if len(coeffs) != 6:
                return None

            a, b, c, d, e, f = coeffs

            # Check if it's an ellipse
            discriminant = b**2 - 4*a*c
            if discriminant >= 0:
                self.last_error = "Coefficients do not represent an ellipse"
                return None

            # Calculate center
            den = b**2 - 4*a*c
            x0 = (2*c*d - b*e) / den
            y0 = (2*a*e - b*d) / den

            # Calculate semi-axes and rotation
            # This uses the standard formulas for conic section parameters
            theta = 0.5 * np.arctan2(b, a - c) if abs(b) > 1e-10 else 0

            # Calculate the semi-axes lengths
            cos_theta = np.cos(theta)
            sin_theta = np.sin(theta)

            # Transform to canonical form
            A = a * cos_theta**2 + b * cos_theta * sin_theta + c * sin_theta**2
            C = a * sin_theta**2 - b * cos_theta * sin_theta + c * cos_theta**2

            # Calculate the constant term in canonical form
            F = a*x0**2 + b*x0*y0 + c*y0**2 + d*x0 + e*y0 + f

            if F * A > 0 or F * C > 0:
                self.last_error = "Invalid ellipse parameters (negative semi-axes)"
                return None

            semi_major = np.sqrt(-F / A)
            semi_minor = np.sqrt(-F / C)

            # Ensure semi_major >= semi_minor
            if semi_minor > semi_major:
                semi_major, semi_minor = semi_minor, semi_major
                theta += np.pi/2

            eccentricity = np.sqrt(1 - (semi_minor/semi_major)**2) if semi_major > semi_minor else 0

            return {
                'center': (x0, y0),
                'semi_major': semi_major,
                'semi_minor': semi_minor,
                'eccentricity': eccentricity,
                'rotation': theta % np.pi,
                'coefficients': coeffs,
                'method': 'algebraic'
            }

        except Exception as e:
            self.last_error = f"Parameter conversion failed: {str(e)}"
            return None

    def get_ellipse_points(self, params, npts=100):
        """Generate points on the fitted ellipse"""
        if params is None:
            return None, None

        x0, y0 = params['center']
        a = params['semi_major']
        b = params['semi_minor']
        theta = params['rotation']

        t = np.linspace(0, 2*np.pi, npts)
        x = x0 + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
        y = y0 + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)

        return x, y


def test_ellipse_fitting():
    """Test the ellipse fitting with a basic example."""
    fitter = EllipseFitter_manus()

    # Simple test case (5 non‑collinear points that roughly form an ellipse)
    case = {
        'x': np.array([1, 3, 5, 2, 4]),
        'y': np.array([2, 1, 3, 5, 4])
    }

    points = np.array([[-18107.85742188,  -9668.421875  ],
                       [-18109.07421875,  -9649.95117188],
                       [-18133.55859375,  -9622.34765625],
                       [-18161.0234375,   -9615.94433594],
                       [-18180.34570312,  -9623.63476562]], dtype=np.float64)


    points = np.array([[-18207.74825912,  -9692.71639934],
                       [-18184.12844828,  -9717.36587973],
                       [-18211.82190713,  -9658.83258577],
                       [-18152.71974009,  -9719.75775507],
                       [-18196.4769546,   -9635.31620922]], dtype=np.float64)


    # Try the automatic method (will fall back to the first successful method)
    time1 = time.time()
    result = fitter.fit(points[:, 0], points[:, 1], method='svd')
    time2 = time.time()
    print(f"Time taken: {time2 - time1} seconds")

    if result is not None:
        print(f"✓ Fit successful using {result['method']} method")
        print(f"  Center: ({result['center'][0]:.3f}, {result['center'][1]:.3f})")
        print(f"  Semi‑major axis: {result['semi_major']:.3f}")
        print(f"  Semi‑minor axis: {result['semi_minor']:.3f}")
        print(f"  Eccentricity: {result['eccentricity']:.3f}")
        print(f"  Rotation: {np.degrees(result['rotation']):.1f}°")
    else:
        print(f"✗ Fit failed: {fitter.last_error}")

if __name__ == '__main__':
    test_ellipse_fitting()    
