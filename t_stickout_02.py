import numpy as np
from scipy.optimize import minimize_scalar

# --- Key Parameters (Tweak these for experiments!) ---
# eA parameters (fixed ellipse, centered at (0,0), no rotation)
a_a = 15.0  # Semi-major axis (along x)
b_a = 5.0   # Semi-minor axis (along y)

# eB parameters (the one we're moving/rotating)
a_b = 3.0  # Semi-major axis (before rotation)
b_b = 2.5   # Semi-minor axis (before rotation)
rot_b = 0.0  # Rotation angle in radians (ccw)

# Pivot setup: Define the pivot point on eB (in its original coord system)
# Original "very bottom" of eB was at (0, -5) if center at (0,-2.5) and b=2.5
pivot_x_b_original = 0.0
pivot_y_b_original = -5.0  # Adjust if you mean a different "very bottom"

# Target pivot location on eA (e.g., 3 o'clock is (a_a, 0))
pivot_x_a = a_a  # 15.0 for 3 o'clock
pivot_y_a = 0.0

# Compute eB's center after moving pivot to target (before rotation)
# Original center relative to pivot
cx_b_original = pivot_x_b_original
cy_b_original = pivot_y_b_original + 2.5  # Assuming original center at (0, -2.5) relative to bottom at (0,-5)
# Shift to place pivot at (pivot_x_a, pivot_y_a)
cx_b = pivot_x_a - pivot_x_b_original
cy_b = pivot_y_a - pivot_y_b_original + cy_b_original  # Wait, this might need adjustment based on your intent

# Precomputed squares for efficiency
A = a_a**2
B = b_a**2

# --- Functions (Don't need to change these unless customizing logic) ---
def point_on_eb(phi):
    """Get point on rotated eB at angle phi."""
    cos_rot = np.cos(rot_b)
    sin_rot = np.sin(rot_b)
    # Parametric point before rotation (relative to eB center)
    dx = a_b * np.cos(phi)
    dy = b_b * np.sin(phi)
    # Apply rotation
    x = cx_b + dx * cos_rot - dy * sin_rot
    y = cy_b + dx * sin_rot + dy * cos_rot
    return x, y

def compute_distance(x0, y0):
    """Exact min distance from (x0,y0) to eA via quartic solving."""
    val = (x0 / a_a)**2 + (y0 / b_a)**2
    if val <= 1.0:
        return 0.0  # Inside or on eA
    
    # Quartic coefficients
    c4 = -1.0
    c3 = -2 * (A + B)
    c2 = (A * x0**2 + B * y0**2) - (A**2 + 4 * A * B + B**2)
    c1 = (2 * A * x0**2 * B + 2 * B * y0**2 * A) - 2 * A * B * (A + B)
    c0 = A * x0**2 * B**2 + B * y0**2 * A**2 - A**2 * B**2
    
    coeffs = [c4, c3, c2, c1, c0]
    roots = np.roots(coeffs)
    
    dists = []
    for s in roots:
        if np.iscomplex(s):
            continue
        s = np.real(s)
        denom_x = 1 + s / A
        denom_y = 1 + s / B
        if denom_x == 0 or denom_y == 0:
            continue
        Qx = x0 / denom_x
        Qy = y0 / denom_y
        ell_val = (Qx / a_a)**2 + (Qy / b_a)**2
        if abs(ell_val - 1) > 1e-6:
            continue
        dist = np.sqrt((x0 - Qx)**2 + (y0 - Qy)**2)
        if dist > 0:
            dists.append(dist)
    
    if not dists:
        # Fallback approximation (radial scaling to boundary)
        r = np.sqrt(x0**2 + y0**2)
        if r == 0:
            return 0.0
        dir_x = x0 / r
        dir_y = y0 / r
        denom = (dir_x / a_a)**2 + (dir_y / b_a)**2
        t = 1 / np.sqrt(denom)
        approx_dist = r - t
        return max(0, approx_dist)
    return min(dists)

# --- Computation ---
# Sample many phi to find global max distance (increase n_samples for more precision)
n_samples = 100
phis = np.linspace(0, 2 * np.pi, n_samples)
dists = [compute_distance(*point_on_eb(phi)) for phi in phis]

max_dist = np.max(dists)
max_idx = np.argmax(dists)
max_phi = phis[max_idx]
max_x, max_y = point_on_eb(max_phi)

print(f"Maximum sticking out distance: {max_dist}")
print(f"Occurs at phi: {max_phi} radians")
print(f"At point: ({max_x}, {max_y})")
print(f"eB center after setup: ({cx_b}, {cy_b})")