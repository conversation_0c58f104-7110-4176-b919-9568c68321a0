import numpy as np
from scipy.optimize import minimize
from shapely.geometry import <PERSON>y<PERSON>, Point, LineString
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse

# --- Helper Functions ---

def get_polygon_halfspaces(polygon: Polygon):
    """
    Returns a list of (h, k) for each edge, where h is the outward normal
    and h.T @ x <= k defines the half-space containing the polygon.
    """
    coords = np.array(polygon.exterior.coords)
    edges = []
    # Ensure polygon centroid is robustly inside
    # centroid = np.array(polygon.centroid.coords[0])
    
    # Use a point guaranteed to be inside for normal orientation
    # A point slightly offset from a vertex towards centroid
    pt_inside = polygon.representative_point()
    # If it's a very thin polygon, representative_point might be on boundary.
    # A safer bet is using centroid, assuming it's not degenerate.
    # If centroid itself is an issue, one might need more robust "point in polygon" logic.
    
    for i in range(len(coords) - 1):
        p1 = coords[i]
        p2 = coords[i+1]
        
        edge_vec = p2 - p1
        # Normal: rotate edge_vec by 90 deg
        # (dx, dy) -> (-dy, dx) or (dy, -dx)
        normal_vec = np.array([edge_vec[1], -edge_vec[0]])
        normal_vec = normal_vec / np.linalg.norm(normal_vec)
        
        # Orient normal outwards
        # Test point: midpoint of the edge + small epsilon along normal
        mid_point = (p1 + p2) / 2
        # if polygon.contains(Point(mid_point + 1e-6 * normal_vec)): # normal points inwards
        # A more robust check: if h.T @ (pt_inside - mid_point) < 0, normal points outward
        if np.dot(normal_vec, pt_inside.coords[0] - mid_point) > 0: # normal points inward
            normal_vec = -normal_vec
            
        k = np.dot(normal_vec, p1) # or p2
        edges.append({'h': normal_vec, 'k': k, 'p1':p1, 'p2':p2})
    return edges

def get_tangent_normal_at_point(polygon: Polygon, point_coords: np.ndarray):
    """
    Finds the polygon edge the point lies on and returns its outward normal.
    Assumes point_coords is exactly on the boundary.
    If point_coords is a vertex, it needs a rule to pick an edge.
    For simplicity, picks the edge *starting* at this vertex in polygon order.
    """
    poly_coords = np.array(polygon.exterior.coords)
    min_dist_to_edge = float('inf')
    best_normal = None
    
    # Check if point is a vertex
    vertex_index = -1
    for i, v_coord in enumerate(poly_coords[:-1]): # Exclude last closing point
        if np.allclose(v_coord, point_coords):
            vertex_index = i
            break

    if vertex_index != -1:
        # Point is a vertex. Choose the edge *starting* at this vertex.
        # (Could be configurable or require user to specify tangent edge index)
        p1 = poly_coords[vertex_index]
        p2 = poly_coords[(vertex_index + 1) % (len(poly_coords) - 1)]
        edge_vec = p2 - p1
        normal_vec = np.array([edge_vec[1], -edge_vec[0]])
        normal_vec = normal_vec / np.linalg.norm(normal_vec)
        
        # Orient outward (relative to p1->p2 segment)
        # Test with polygon centroid or representative_point
        mid_point = (p1 + p2) / 2
        pt_inside = polygon.representative_point().coords[0]
        if np.dot(normal_vec, pt_inside - mid_point) > 0: # normal points inward
            normal_vec = -normal_vec
        return normal_vec

    # Point is on an edge (not a vertex)
    for i in range(len(poly_coords) - 1):
        p1 = poly_coords[i]
        p2 = poly_coords[i+1]
        ls = LineString([p1, p2])
        dist = ls.distance(Point(point_coords))
        
        if np.isclose(dist, 0):
            # Point is on this edge segment
            edge_vec = p2 - p1
            normal_vec = np.array([edge_vec[1], -edge_vec[0]])
            normal_vec = normal_vec / np.linalg.norm(normal_vec)

            # Orient outward
            mid_point = (p1 + p2) / 2
            pt_inside = polygon.representative_point().coords[0]
            if np.dot(normal_vec, pt_inside - mid_point) > 0: # normal points inward
                normal_vec = -normal_vec
            return normal_vec
            
    raise ValueError(f"Point {point_coords} not found on polygon boundary or normal finding failed.")


# --- Optimization Target and Constraints ---

def objective_func(params):
    # cx, cy, log_a, log_b, theta = params
    # We want to maximize a*b, or log_a + log_b
    # So minimize -(log_a + log_b)
    return -(params[2] + params[3])

def ellipse_equation_val(x, y, params_vec):
    cx, cy, log_a, log_b, theta = params_vec
    a = np.exp(log_a)
    b = np.exp(log_b)
    ct, st = np.cos(theta), np.sin(theta)

    x_shifted = x - cx
    y_shifted = y - cy

    x_rot = x_shifted * ct + y_shifted * st
    y_rot = -x_shifted * st + y_shifted * ct
    
    # Handle potential division by zero if a or b become tiny
    # This shouldn't happen if log_a, log_b are reasonably bounded
    # or if optimizer behaves. Add small epsilon for safety if needed.
    # a = max(a, 1e-9)
    # b = max(b, 1e-9)

    return (x_rot / a)**2 + (y_rot / b)**2

def constraints_func(params_vec, P1_coords, P2_coords, n1_vec, n2_vec, polygon_halfspaces):
    # params_vec = [cx, cy, log_a, log_b, theta]
    cx, cy, log_a, log_b, theta = params_vec
    a = np.exp(log_a)
    b = np.exp(log_b)
    
    if a < 1e-6 or b < 1e-6 or b > a : # Basic sanity for a,b and a >=b
        return np.full(4 + len(polygon_halfspaces), 1e9) # Large penalty

    ct, st = np.cos(theta), np.sin(theta)
    
    # Constraint values (all should be <= 0 for inequality, or = 0 for equality)
    constraints = []

    # C1: Ellipse through P1 (equality)
    val_p1 = ellipse_equation_val(P1_coords[0], P1_coords[1], params_vec) - 1.0
    constraints.append(val_p1)

    # C2: Ellipse through P2 (equality)
    val_p2 = ellipse_equation_val(P2_coords[0], P2_coords[1], params_vec) - 1.0
    constraints.append(val_p2)

    # For C3, C4: Gradient of G = (x_rot/a)^2 + (y_rot/b)^2 - 1
    def get_gradient_G(x, y, current_params):
        _cx, _cy, _log_a, _log_b, _theta = current_params
        _a = np.exp(_log_a)
        _b = np.exp(_log_b)
        _ct, _st = np.cos(_theta), np.sin(_theta)

        _x_shifted = x - _cx
        _y_shifted = y - _cy
        _x_rot = _x_shifted * _ct + _y_shifted * _st
        _y_rot = -_x_shifted * _st + _y_shifted * _ct

        # dG/dx = dG/dx_rot * dx_rot/dx + dG/dy_rot * dy_rot/dx
        # dG/dx_rot = 2*x_rot / a^2
        # dG/dy_rot = 2*y_rot / b^2
        # dx_rot/dx = ct, dy_rot/dx = -st
        # dx_rot/dy = st, dy_rot/dy = ct
        
        # Add small epsilon to _a, _b to prevent division by zero if they become extremely small
        # This can happen during optimization steps, especially if bounds are loose
        safe_a_sq = max(_a**2, 1e-12)
        safe_b_sq = max(_b**2, 1e-12)

        dG_dx = (2 * _x_rot / safe_a_sq) * _ct - (2 * _y_rot / safe_b_sq) * _st
        dG_dy = (2 * _x_rot / safe_a_sq) * _st + (2 * _y_rot / safe_b_sq) * _ct
        return np.array([dG_dx, dG_dy])

    # C3: Tangency at P1 (normal_ellipse parallel to n1_vec (polygon outward normal))
    # So grad_G must be anti-parallel to n1_vec (or parallel to -n1_vec)
    # grad_G_p1 = k * (-n1_vec) for some k > 0
    # (grad_G_p1_x * (-n1_vec_y)) - (grad_G_p1_y * (-n1_vec_x)) = 0
    # (grad_G_p1_x * n1_vec_y) - (grad_G_p1_y * n1_vec_x) = 0
    grad_G_p1 = get_gradient_G(P1_coords[0], P1_coords[1], params_vec)
    tangency_p1 = grad_G_p1[0] * n1_vec[1] - grad_G_p1[1] * n1_vec[0]
    constraints.append(tangency_p1)
    
    # C4: Tangency at P2
    grad_G_p2 = get_gradient_G(P2_coords[0], P2_coords[1], params_vec)
    tangency_p2 = grad_G_p2[0] * n2_vec[1] - grad_G_p2[1] * n2_vec[0]
    constraints.append(tangency_p2)

    # C5: Ellipse containment (inequality: val <= 0)
    # h_i^T c + sqrt(h_i^T Q h_i) - k_i <= 0
    # Q = R D R^T; R = [[ct, -st], [st, ct]]; D = [[a^2,0],[0,b^2]]
    Q00 = ct**2 * a**2 + st**2 * b**2
    Q01 = ct * st * (a**2 - b**2)
    Q11 = st**2 * a**2 + ct**2 * b**2
    Q = np.array([[Q00, Q01], [Q01, Q11]])
    ellipse_center = np.array([cx, cy])

    for edge_info in polygon_halfspaces:
        h_i = edge_info['h']
        k_i = edge_info['k']
        
        hT_c = np.dot(h_i, ellipse_center)
        hT_Q_h = np.dot(h_i, np.dot(Q, h_i))
        
        # hT_Q_h must be non-negative. If a or b are tiny, it might be an issue.
        # It should be positive if Q is positive definite (a,b > 0)
        if hT_Q_h < 0: # Should not happen for valid a,b > 0
             # Penalize heavily if this occurs, indicates bad parameters
            constraints.append(1e9)
            continue
            
        support_val = hT_c + np.sqrt(hT_Q_h)
        constraints.append(support_val - k_i)
        
    return np.array(constraints)


# --- Main Execution ---
if __name__ == '__main__':
    # Define the polygon
    # Example: A slightly irregular pentagon
    polygon_coords = [(0,0), (2,0), (2.5,1.5), (1,2.5), (-0.5,1.5)]
    # polygon_coords = [(0,0), (4,0), (4,2), (2,3), (0,2)] # House shape
    # polygon_coords = [(0,0), (1,0), (1,1), (0,1)] # Square
    
    shapely_poly = Polygon(polygon_coords)
    
    # Select two points ON THE POLYGON BOUNDARY
    # For testing, let's pick midpoints of two edges
    # Edge 0: (0,0) to (2,0). Midpoint: (1,0)
    # Edge 2: (2.5,1.5) to (1,2.5). Midpoint: ( (2.5+1)/2, (1.5+2.5)/2 ) = (1.75, 2.0)
    
    # P1 = np.array(shapely_poly.exterior.coords[0]) # Vertex 0
    # P2 = np.array(shapely_poly.exterior.coords[2]) # Vertex 2
    
    # Taking points that are not necessarily vertices:
    idx1, idx2 = 0, 2 # Choose edges by index
    coords_list = list(shapely_poly.exterior.coords)
    
    # Point on first chosen edge
    pt_on_edge1_idx_start = idx1
    pt_on_edge1_idx_end = (idx1 + 1) % (len(coords_list)-1)
    P1 = (np.array(coords_list[pt_on_edge1_idx_start]) + np.array(coords_list[pt_on_edge1_idx_end])) / 2.0

    # Point on second chosen edge
    pt_on_edge2_idx_start = idx2
    pt_on_edge2_idx_end = (idx2 + 1) % (len(coords_list)-1)
    P2 = (np.array(coords_list[pt_on_edge2_idx_start]) + np.array(coords_list[pt_on_edge2_idx_end])) / 2.0

    print(f"Selected P1: {P1}")
    print(f"Selected P2: {P2}")

    # Get polygon half-spaces (h_i^T x <= k_i, h_i is outward normal)
    poly_halfspaces = get_polygon_halfspaces(shapely_poly)

    # Get outward normals at P1 and P2
    N1 = get_tangent_normal_at_point(shapely_poly, P1)
    N2 = get_tangent_normal_at_point(shapely_poly, P2)
    print(f"Normal at P1 (N1): {N1}")
    print(f"Normal at P2 (N2): {N2}")
    
    # Initial guess for [cx, cy, log_a, log_b, theta]
    # Center: midpoint of P1, P2 or polygon centroid
    initial_cx = (P1[0] + P2[0]) / 2 
    initial_cy = (P1[1] + P2[1]) / 2
    # initial_cx, initial_cy = shapely_poly.centroid.x, shapely_poly.centroid.y

    dist_p1_p2 = np.linalg.norm(P1 - P2)
    initial_log_a = np.log(dist_p1_p2 / 2.0 + 1e-3) # a is roughly half distance
    initial_log_b = np.log(dist_p1_p2 / 10.0 + 1e-3) # b is smaller
    
    # Angle of line P1-P2
    vec_p1_p2 = P2 - P1
    initial_theta = np.arctan2(vec_p1_p2[1], vec_p1_p2[0])
    
    x0 = [initial_cx, initial_cy, initial_log_a, initial_log_b, initial_theta]
    print(f"Initial guess x0: {x0} (a={np.exp(x0[2]):.2f}, b={np.exp(x0[3]):.2f})")

    # Define constraints for the optimizer
    # First 4 are equality, rest are inequality (<=0)
    cons = []
    # C1, C2, C3, C4 are equality
    for i in range(4): 
        cons.append({'type': 'eq', 'fun': lambda p, idx=i: constraints_func(p, P1, P2, N1, N2, poly_halfspaces)[idx]})
    # C5 are inequality
    for i in range(len(poly_halfspaces)):
        cons.append({'type': 'ineq', 'fun': lambda p, idx=i: -constraints_func(p, P1, P2, N1, N2, poly_halfspaces)[4+idx]}) # scipy wants g(x) >= 0

    # Bounds for parameters (optional but can help)
    # cx, cy within polygon bounds roughly
    min_x, min_y, max_x, max_y = shapely_poly.bounds
    bounds = [
        (min_x - (max_x-min_x)*0.5, max_x + (max_x-min_x)*0.5), # cx
        (min_y - (max_y-min_y)*0.5, max_y + (max_y-min_y)*0.5), # cy
        (np.log(1e-3), np.log(max(max_x-min_x, max_y-min_y))), # log_a
        (np.log(1e-3), np.log(max(max_x-min_x, max_y-min_y))), # log_b
        (-np.pi, np.pi) # theta
    ]
    # Add constraint a >= b by log_a >= log_b  => log_a - log_b >= 0
    cons.append({'type': 'ineq', 'fun': lambda p: p[2] - p[3]}) # log_a - log_b >= 0

    # Run optimization
    # SLSQP is good for this type of problem. 'trust-constr' is another option.
    # Increase maxiter if convergence is an issue.
    # ftol (tolerance) might need adjustment too.
    result = minimize(objective_func, x0, method='SLSQP', 
                      constraints=cons, bounds=bounds, 
                      options={'disp': True, 'maxiter': 1000, 'ftol': 1e-9})

    if result.success:
        print("Optimization successful!")
        opt_params = result.x
        opt_cx, opt_cy, opt_log_a, opt_log_b, opt_theta = opt_params
        opt_a = np.exp(opt_log_a)
        opt_b = np.exp(opt_log_b)
        
        # Ensure a is major axis if a < b from optimization result
        if opt_a < opt_b:
            opt_a, opt_b = opt_b, opt_a
            opt_theta += np.pi/2 # Adjust angle if axes swapped

        print(f"Optimal Ellipse: Center=({opt_cx:.3f}, {opt_cy:.3f}), a={opt_a:.3f}, b={opt_b:.3f}, theta_rad={opt_theta:.3f} ({(opt_theta*180/np.pi):.2f} deg)")
        print(f"Maximized area ~ pi * a * b = {np.pi * opt_a * opt_b:.3f}")
        
        # Check constraints satisfaction (approximate)
        final_constraints_vals = constraints_func(opt_params, P1, P2, N1, N2, poly_halfspaces)
        print(f"Final constraint values (first 4 should be ~0, rest <=0):")
        for i, val in enumerate(final_constraints_vals):
            type_str = "EQ" if i < 4 else "INEQ"
            print(f"  Constraint {i} ({type_str}): {val:.3e}")
        print(f"  log_a - log_b: {opt_log_a - opt_log_b:.3e} (should be >=0)")


        # Plotting
        fig, ax = plt.subplots(1, 1, figsize=(8,8))
        poly_patch = plt.Polygon(np.array(shapely_poly.exterior.coords), fill=True, alpha=0.3, color='lightblue', ec='black')
        ax.add_patch(poly_patch)

        ellipse_patch = Ellipse(xy=(opt_cx, opt_cy), 
                                width=2*opt_a, height=2*opt_b, 
                                angle=np.degrees(opt_theta), 
                                fill=True, alpha=0.5, color='salmon', ec='red')
        ax.add_patch(ellipse_patch)

        ax.plot(P1[0], P1[1], 'bo', markersize=8, label='P1')
        ax.plot(P2[0], P2[1], 'go', markersize=8, label='P2')
        
        # Plot normals (scaled for visibility)
        ax.quiver(P1[0], P1[1], N1[0], N1[1], color='blue', scale=5, label='N1 (poly outward)')
        ax.quiver(P2[0], P2[1], N2[0], N2[1], color='green', scale=5, label='N2 (poly outward)')
        
        # Plot ellipse gradient at P1, P2
        grad_G_P1_opt = get_gradient_G(P1[0], P1[1], opt_params)
        grad_G_P2_opt = get_gradient_G(P2[0], P2[1], opt_params)
        ax.quiver(P1[0], P1[1], grad_G_P1_opt[0], grad_G_P1_opt[1], color='purple', scale=20, width=0.005, label='Grad_G @ P1')
        ax.quiver(P2[0], P2[1], grad_G_P2_opt[0], grad_G_P2_opt[1], color='magenta', scale=20, width=0.005, label='Grad_G @ P2')


        ax.set_aspect('equal', 'box')
        ax.legend()
        plt.title("Maximum Inscribed Ellipse Touching Two Points")
        plt.grid(True)
        plt.show()

    else:
        print("Optimization failed.")
        print(result.message)
        # Print final constraint values even on failure
        final_constraints_vals = constraints_func(result.x, P1, P2, N1, N2, poly_halfspaces)
        print(f"Final constraint values on failure (first 4 should be ~0, rest <=0):")
        for i, val in enumerate(final_constraints_vals):
            type_str = "EQ" if i < 4 else "INEQ"
            print(f"  Constraint {i} ({type_str}): {val:.3e}")
        print(f"  log_a - log_b: {result.x[2] - result.x[3]:.3e} (should be >=0)")