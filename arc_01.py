import numpy as np
from scipy.interpolate import CubicSpline
import matplotlib.pyplot as plt

# Define quintic Hermite interpolation for 1D
def quintic_hermite(p0, v0, a0, p1, v1, a1):
    f = p0
    e = v0
    d = a0 / 2
    M = np.array([[1, 1, 1], [5, 4, 3], [20, 12, 6]])
    B = np.array([p1 - d - e - f, v1 - 2*d - e, a1 - 2*d])
    a, b, c = np.linalg.solve(M, B)
    return np.array([a, b, c, d, e, f])  # Coefficients for t^5 to t^0

# Evaluate a polynomial at points t
def eval_poly(coeffs, t):
    return np.polyval(coeffs, t)

# Example arc definitions (adjust as per your arcs)
C1 = np.array([0, 0])    # Center of a1
R1 = 4.0                 # Radius of a1
theta1_end = np.pi/2     # End angle of a1
C2 = np.array([3, 1])    # Center of a2
R2 = 2.0                 # Radius of a2
theta2_start = 0         # Start angle of a2
X = np.array([4, 0])   # Intermediate point

# Compute P1, v1, a1 from a1
P1 = C1 + R1 * np.array([np.cos(theta1_end), np.sin(theta1_end)])
v1 = R1 * np.array([-np.sin(theta1_end), np.cos(theta1_end)])
a1 = R1 * np.array([-np.cos(theta1_end), -np.sin(theta1_end)])

# Compute P2, v2, a2 from a2
P2 = C2 + R2 * np.array([np.cos(theta2_start), np.sin(theta2_start)])
v2 = R2 * np.array([-np.sin(theta2_start), np.cos(theta2_start)])
a2 = R2 * np.array([-np.cos(theta2_start), -np.sin(theta2_start)])

# Step 1: Cubic spline to estimate vX, aX
t = np.array([0, 1, 2])
points = np.array([P1, X, P2]).T  # Shape (2, 3)
spline = CubicSpline(t, points, axis=1, bc_type='natural')
vX = spline.derivative(1)(2)
aX = spline.derivative(2)(2)

# Step 2: Quintic Hermite spline coefficients
# Segment 1: P1 to X
coeffs_x1 = quintic_hermite(P1[0], v1[0], a1[0], X[0], vX[0], aX[0])
coeffs_y1 = quintic_hermite(P1[1], v1[1], a1[1], X[1], vX[1], aX[1])

# Segment 2: X to P2
coeffs_x2 = quintic_hermite(X[0], vX[0], aX[0], P2[0], v2[0], a2[0])
coeffs_y2 = quintic_hermite(X[1], vX[1], aX[1], P2[1], v2[1], a2[1])

# Step 3: Evaluate the splines
t_fine = np.linspace(0, 1, 100)
x1 = eval_poly(coeffs_x1, t_fine)
y1 = eval_poly(coeffs_y1, t_fine)
x2 = eval_poly(coeffs_x2, t_fine)
y2 = eval_poly(coeffs_y2, t_fine)

# Plotting
plt.plot(x1, y1, label='P1 to X')
plt.plot(x2, y2, label='X to P2')
plt.plot([P1[0], X[0], P2[0]], [P1[1], X[1], P2[1]], 'ro', label='Points')
plt.legend()
plt.title('G2 Continuous Curve')
plt.xlabel('x')
plt.ylabel('y')
plt.grid(True)
plt.axis('equal')
# plt.show()

# Optional: Plot the original arcs for context
theta1 = np.linspace(0, theta1_end, 100)
theta2 = np.linspace(theta2_start, np.pi/2, 100)
arc1_x = C1[0] + R1 * np.cos(theta1)
arc1_y = C1[1] + R1 * np.sin(theta1)
arc2_x = C2[0] + R2 * np.cos(theta2)
arc2_y = C2[1] + R2 * np.sin(theta2)
plt.plot(arc1_x, arc1_y, 'b--', label='Arc a1')
plt.plot(arc2_x, arc2_y, 'g--', label='Arc a2')
plt.legend()
plt.show()