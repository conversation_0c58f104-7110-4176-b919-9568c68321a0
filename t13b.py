import timeit
import numpy as np
from shapely.geometry import Point, LineString
from shapely.ops import nearest_points
import random

# ==============================================================================
# Function 1: Using Shapely
# ==============================================================================
def find_closest_point_on_circle_to_segment_shapely(circle_center, circle_radius, p1, p2):
    """
    Finds the mathematically precise point on a circle's circumference
    closest to a given line segment using Shapely.
    """
    center_np = np.array(circle_center)
    p1_np = np.array(p1)
    p2_np = np.array(p2)

    center_point_shapely = Point(center_np)
    line_segment_shapely = LineString([p1_np, p2_np])

    try:
        # Find the point on the line segment closest to the circle center
        _, closest_point_on_segment_shapely = nearest_points(center_point_shapely, line_segment_shapely)
        closest_point_on_segment_np = np.array(closest_point_on_segment_shapely.coords[0])
    except Exception as e:
        # Handle potential issues, e.g., zero-length segment though nearest_points might handle it
        print(f"Shapely nearest_points error: {e}")
        # Fallback or re-raise depending on desired behavior
        # For benchmark, let's find a point on segment manually as fallback
        line_vec = p2_np - p1_np
        line_len_sq = np.dot(line_vec, line_vec)
        if np.isclose(line_len_sq, 0):
             closest_point_on_segment_np = p1_np
        else:
             p1_to_center = center_np - p1_np
             t = np.dot(p1_to_center, line_vec) / line_len_sq
             t_clamped = np.clip(t, 0, 1)
             closest_point_on_segment_np = p1_np + t_clamped * line_vec


    vector_to_segment = closest_point_on_segment_np - center_np
    dist_center_to_segment = np.linalg.norm(vector_to_segment)

    if np.isclose(dist_center_to_segment, 0):
        # Handle center on segment case (arbitrary direction for point)
        direction = p2_np - p1_np
        if np.linalg.norm(direction) > 1e-9:
            norm_direction = direction / np.linalg.norm(direction)
        else: # p1 and p2 are the same point
            norm_direction = np.array([1.0, 0.0]) # Arbitrary direction
        closest_point_on_circle = center_np + circle_radius * norm_direction
        min_distance = 0.0
    else:
        direction_vector = vector_to_segment / dist_center_to_segment
        closest_point_on_circle = center_np + circle_radius * direction_vector
        min_distance = max(0.0, dist_center_to_segment - circle_radius)

    return closest_point_on_circle, min_distance

# ==============================================================================
# Function 2: Pure NumPy
# ==============================================================================
'''
def find_closest_point_on_circle_to_segment_numpy(circle_center, circle_radius, p1, p2):
    """
    Finds the mathematically precise point on a circle's circumference
    closest to a given line segment using only NumPy.
    """
    center_np = np.array(circle_center)
    p1_np = np.array(p1)
    p2_np = np.array(p2)

    line_vec = p2_np - p1_np
    line_len_sq = np.dot(line_vec, line_vec)
    p1_to_center = center_np - p1_np

    if np.isclose(line_len_sq, 0):
        closest_point_on_segment_np = p1_np
    else:
        t = np.dot(p1_to_center, line_vec) / line_len_sq
        t_clamped = np.clip(t, 0, 1)
        closest_point_on_segment_np = p1_np + t_clamped * line_vec

    vector_to_segment = closest_point_on_segment_np - center_np
    dist_center_to_segment = np.linalg.norm(vector_to_segment)

    if np.isclose(dist_center_to_segment, 0):
         # Handle center on segment case (arbitrary direction for point)
        direction = p2_np - p1_np
        if np.linalg.norm(direction) > 1e-9:
            norm_direction = direction / np.linalg.norm(direction)
        else: # p1 and p2 are the same point
            norm_direction = np.array([1.0, 0.0]) # Arbitrary direction
        closest_point_on_circle = center_np + circle_radius * norm_direction
        min_distance = 0.0
    else:
        direction_vector = vector_to_segment / dist_center_to_segment
        closest_point_on_circle = center_np + circle_radius * direction_vector
        min_distance = max(0.0, dist_center_to_segment - circle_radius)

    # Return same format as shapely function for consistency
    return closest_point_on_circle, min_distance

'''
def find_closest_point_on_circle_to_segment_numpy(circle_center, circle_radius, p1, p2):
    """
    Optimized version of finding the closest point on a circle to a line segment.
    
    Args:
        circle_center: (2,) array-like, circle center coordinates
        circle_radius: float, circle radius
        p1, p2: (2,) array-like, segment endpoints
    
    Returns:
        tuple: (closest point on circle, minimum distance, closest point on segment)
    """
    # Convert inputs to numpy arrays and compute vectors in one go
    center = np.asarray(circle_center, dtype=np.float64)
    p1 = np.asarray(p1, dtype=np.float64)
    line_vec = np.asarray(p2, dtype=np.float64) - p1
    p1_to_center = center - p1
    
    # Compute line length squared
    line_len_sq = np.dot(line_vec, line_vec)
    
    if line_len_sq < 1e-12:  # Zero-length segment check
        closest_on_segment = p1
    else:
        # Project and clamp in one step
        t = np.clip(np.dot(p1_to_center, line_vec) / line_len_sq, 0, 1)
        closest_on_segment = p1 + t * line_vec
    
    # Vector from center to closest point
    vec_to_segment = closest_on_segment - center
    dist_to_segment = np.linalg.norm(vec_to_segment)
    
    if dist_to_segment < 1e-12:  # Center on segment check
        # Quick arbitrary point calculation
        point_on_circle = center + np.array([circle_radius, 0.0])
        return point_on_circle, 0.0, closest_on_segment
    
    # Final calculations
    direction = vec_to_segment / dist_to_segment
    closest_on_circle = center + circle_radius * direction
    min_distance = max(0.0, dist_to_segment - circle_radius)
    
    return closest_on_circle, min_distance, closest_on_segment
# ==============================================================================
# Benchmark Setup
# ==============================================================================
N_ITERATIONS = 150  # How many different random geometries to test
N_REPEATS = 5       # How many times to repeat the timing for each geometry
N_LOOPS = 400      # How many times to run the function within each repeat

print(f"--- Benchmark Configuration ---")
print(f"Number of random geometries (Iterations): {N_ITERATIONS}")
print(f"Number of loops per timing (Number):     {N_LOOPS}")
print(f"Number of timing repeats (Repeat):       {N_REPEATS}")
print("-" * 30)

shapely_times = []
numpy_times = []

for i in range(N_ITERATIONS):
    # Generate random data for each iteration
    C = (random.uniform(-100, 100), random.uniform(-100, 100))
    R = random.uniform(1, 50)
    P1 = (random.uniform(-150, 150), random.uniform(-150, 150))
    # Ensure P2 is different from P1 to avoid zero-length segments often
    P2 = (P1[0] + random.uniform(-20, 20), P1[1] + random.uniform(-20, 20))
    if P1 == P2: P2 = (P1[0]+1, P1[1]+1) # Ensure non-zero length if they happen to be equal


    # Data to be passed to timeit's global namespace
    current_globals = {
        'find_closest_point_on_circle_to_segment_shapely': find_closest_point_on_circle_to_segment_shapely,
        'find_closest_point_on_circle_to_segment_numpy': find_closest_point_on_circle_to_segment_numpy,
        'C': C,
        'R': R,
        'P1': P1,
        'P2': P2,
        'np': np,  # NumPy might be needed directly if functions were simpler
        'Point': Point, # Needed for Shapely function internals if not fully encapsulated
        'LineString': LineString,
        'nearest_points': nearest_points
    }

    # Time Shapely version
    shapely_t = timeit.repeat(
        stmt='find_closest_point_on_circle_to_segment_shapely(C, R, P1, P2)',
        globals=current_globals,
        number=N_LOOPS,
        repeat=N_REPEATS
    )
    shapely_times.append(min(shapely_t) / N_LOOPS) # Store time per single execution

    # Time NumPy version
    numpy_t = timeit.repeat(
        stmt='find_closest_point_on_circle_to_segment_numpy(C, R, P1, P2)',
        globals=current_globals,
        number=N_LOOPS,
        repeat=N_REPEATS
    )
    numpy_times.append(min(numpy_t) / N_LOOPS) # Store time per single execution

    # Optional: Print progress
    if (i + 1) % (N_ITERATIONS // 10) == 0:
        print(f"Completed iteration {i+1}/{N_ITERATIONS}")


# ==============================================================================
# Results
# ==============================================================================
avg_shapely_time = sum(shapely_times) / N_ITERATIONS
avg_numpy_time = sum(numpy_times) / N_ITERATIONS

print("\n--- Benchmark Results ---")
print(f"Average time per call (Shapely): {avg_shapely_time * 1e6:.4f} microseconds")
print(f"Average time per call (NumPy):   {avg_numpy_time * 1e6:.4f} microseconds")
print("-" * 30)

if avg_numpy_time < avg_shapely_time:
    ratio = avg_shapely_time / avg_numpy_time
    print(f"NumPy version is approximately {ratio:.2f} times faster.")
elif avg_shapely_time < avg_numpy_time:
    ratio = avg_numpy_time / avg_shapely_time
    print(f"Shapely version is approximately {ratio:.2f} times faster.")
else:
    print("Both versions have approximately the same performance.")

# Optional: Analyze distribution (e.g., if performance varies significantly)
# import statistics
# print("\n--- Time Distribution (Microseconds) ---")
# print(f"Shapely: Min={min(shapely_times)*1e6:.2f}, Max={max(shapely_times)*1e6:.2f}, StdDev={statistics.stdev(t*1e6 for t in shapely_times):.2f}")
# print(f"NumPy:   Min={min(numpy_times)*1e6:.2f}, Max={max(numpy_times)*1e6:.2f}, StdDev={statistics.stdev(t*1e6 for t in numpy_times):.2f}")