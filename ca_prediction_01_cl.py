import numpy as np
import matplotlib.pyplot as plt

class MillingForcePredictor:
    def __init__(self, tool_radius, num_teeth, helix_angle, spindle_speed, feed_per_tooth, 
                 axial_depth, cutting_coefficients):
        self.r = tool_radius
        self.m = num_teeth
        self.beta = np.radians(helix_angle)
        self.n = spindle_speed
        self.fz = feed_per_tooth
        self.ap = axial_depth
        self.coef = cutting_coefficients
        self.tan_beta = np.tan(self.beta)
        
    def calculate_engagement_angle(self, circle1, circle2, position):
        """
        Calculate engagement angle using a direct geometric approach
        """
        # Extract circle parameters
        x_o1, y_o1, r1 = circle1
        x_o2, y_o2, r2 = circle2
        x_c, y_c = position
        
        # Create offset circles (Cur1 and Cur2)
        R1 = r1 + self.r
        R2 = r2 + self.r
        
        # Calculate tangent point N between tool and Cur2
        dx_o2c = x_c - x_o2
        dy_o2c = y_c - y_o2
        dist_o2c = np.sqrt(dx_o2c**2 + dy_o2c**2)
        
        if abs(dist_o2c - r2) < 1e-6:  # Tool is on Cur2
            x_n = x_c + self.r * dx_o2c / dist_o2c
            y_n = y_c + self.r * dy_o2c / dist_o2c
        else:
            # Calculate tangent point
            x_n = x_o2 + R2 * dx_o2c / dist_o2c
            y_n = y_o2 + R2 * dy_o2c / dist_o2c
        
        # Calculate intersection between tool circle and Cur1
        dx_o1c = x_c - x_o1
        dy_o1c = y_c - y_o1
        dist_o1c = np.sqrt(dx_o1c**2 + dy_o1c**2)
        
        # If tool doesn't intersect with Cur1, return 0
        if dist_o1c > R1 + self.r or dist_o1c < abs(R1 - self.r):
            return 0
        
        # Calculate intersection points using circle-circle intersection formula
        a = (self.r**2 - R1**2 + dist_o1c**2) / (2 * dist_o1c)
        
        # Check if the circles intersect
        if abs(a) > self.r:
            return 0
            
        h = np.sqrt(self.r**2 - a**2)
        
        # Calculate intersection points
        x2 = x_c + a * dx_o1c / dist_o1c
        y2 = y_c + a * dy_o1c / dist_o1c
        
        x_d1 = x2 + h * dy_o1c / dist_o1c
        y_d1 = y2 - h * dx_o1c / dist_o1c
        
        x_d2 = x2 - h * dy_o1c / dist_o1c
        y_d2 = y2 + h * dx_o1c / dist_o1c
        
        # Choose intersection point in cutting zone
        # Vector from C to N
        vec_cn = np.array([x_n - x_c, y_n - y_c])
        vec_cn_norm = vec_cn / np.linalg.norm(vec_cn)
        
        # Vectors from C to D1 and D2
        vec_cd1 = np.array([x_d1 - x_c, y_d1 - y_c])
        vec_cd1_norm = vec_cd1 / np.linalg.norm(vec_cd1)
        
        vec_cd2 = np.array([x_d2 - x_c, y_d2 - y_c])
        vec_cd2_norm = vec_cd2 / np.linalg.norm(vec_cd2)
        
        # Choose the point with the larger angle to CN
        dot1 = np.dot(vec_cn_norm, vec_cd1_norm)
        dot2 = np.dot(vec_cn_norm, vec_cd2_norm)
        
        if dot1 < dot2:
            x_d, y_d = x_d1, y_d1
        else:
            x_d, y_d = x_d2, y_d2
        
        # Calculate |DN|
        dn_length = np.sqrt((x_d - x_n)**2 + (y_d - y_n)**2)
        
        # Calculate engagement angle using cosine law
        cos_alpha = (2 * self.r**2 - dn_length**2) / (2 * self.r**2)
        cos_alpha = np.clip(cos_alpha, -1.0, 1.0)  # Ensure value is in valid range
        alpha = np.arccos(cos_alpha)
        
        return alpha
    
    def calculate_milling_forces(self, circle1, circle2, positions, time_steps):
        """
        Calculate milling forces for a series of positions
        """
        # Initialize force arrays
        Fx = np.zeros(len(positions))
        Fy = np.zeros(len(positions))
        Fz = np.zeros(len(positions))
        F_resultant = np.zeros(len(positions))
        engagement_angles = np.zeros(len(positions))
        
        # Calculate forces at each position
        for i, pos in enumerate(positions):
            # Calculate engagement angle
            alpha = self.calculate_engagement_angle(circle1, circle2, pos)
            engagement_angles[i] = np.degrees(alpha)
            
            if alpha <= 0:
                continue
            
            # Calculate chip thickness
            h = self.fz * np.sin(alpha)
            
            # Calculate micro unit cutting forces
            dz = self.ap / time_steps
            
            # Initialize forces for current position
            fx_pos = 0
            fy_pos = 0
            fz_pos = 0
            
            # Calculate forces for each tooth and axial element
            for j in range(self.m):  # For each tooth
                for k in range(time_steps):  # For each axial element
                    # Calculate tooth angle
                    tooth_angle = 2 * np.pi * j / self.m + k * dz * self.tan_beta / self.r
                    
                    # Check if tooth is in cutting zone
                    if 0 <= tooth_angle % (2 * np.pi) <= alpha:
                        # Calculate tangential, radial, and axial forces
                        dFt = (self.coef['Ktc'] * h + self.coef.get('Kte', 0)) * dz
                        dFr = (self.coef['Krc'] * h + self.coef.get('Kre', 0)) * dz
                        dFa = (self.coef['Kac'] * h + self.coef.get('Kae', 0)) * dz
                        
                        # Transform to Cartesian coordinates
                        sin_angle = np.sin(tooth_angle)
                        cos_angle = np.cos(tooth_angle)
                        
                        fx_pos += dFt * cos_angle - dFr * sin_angle
                        fy_pos += dFt * sin_angle + dFr * cos_angle
                        fz_pos += dFa
            
            # Store forces
            Fx[i] = fx_pos
            Fy[i] = fy_pos
            Fz[i] = fz_pos
            F_resultant[i] = np.sqrt(fx_pos**2 + fy_pos**2 + fz_pos**2)
        
        return {
            'Fx': Fx,
            'Fy': Fy,
            'Fz': Fz,
            'F_resultant': F_resultant,
            'engagement_angles': engagement_angles
        }
    
    def simulate_multiple_circle_machining(self, circle1, circle2, circle_interval, num_points=100):
        """
        Simulate multiple-circle continuous machining
        """
        x_o1, y_o1, r1 = circle1
        x_o2, y_o2, r2 = circle2
        
        # Calculate positions along the path
        positions = []
        
        # Calculate positions along the second circle (arc EFG in the paper)
        theta_values = np.linspace(0, 2*np.pi, num_points)
        for theta in theta_values:
            x = x_o2 + r2 * np.cos(theta)
            y = y_o2 + r2 * np.sin(theta)
            positions.append((x, y))
        
        # Calculate forces
        forces = self.calculate_milling_forces(circle1, circle2, positions, time_steps=10)
        
        return {
            'positions': positions,
            'forces': forces
        }

# Example usage
def run_simulation():
    # Define parameters
    tool_radius = 3  # mm
    num_teeth = 4
    helix_angle = 30  # degrees
    spindle_speed = 3000  # rpm
    feed_per_tooth = 0.08  # mm
    axial_depth = 3  # mm
    
    # Cutting coefficients from the paper
    cutting_coefficients = {
        'Ktc': 5325, 'Krc': 4902, 'Kac': 1823,
        'Kte': 0, 'Kre': 0, 'Kae': 0  # Edge coefficients
    }
    
    # Create milling force predictor
    predictor = MillingForcePredictor(
        tool_radius, num_teeth, helix_angle, spindle_speed, feed_per_tooth,
        axial_depth, cutting_coefficients
    )
    
    # Define circles for simulation
    circle1 = (0, 0, 5)  # (x, y, radius) in mm
    circle2 = (5.5, 0, 5)  # (x, y, radius) in mm
    circle_interval = 0.5  # mm
    
    # Run simulation
    results = predictor.simulate_multiple_circle_machining(circle1, circle2, circle_interval)
    
    # Plot results
    plt.figure(figsize=(12, 8))
    
    # Plot forces
    plt.subplot(2, 1, 1)
    plt.plot(results['forces']['Fx'], label='Fx')
    plt.plot(results['forces']['Fy'], label='Fy')
    plt.plot(results['forces']['Fz'], label='Fz')
    plt.plot(results['forces']['F_resultant'], label='F_resultant', linewidth=2)
    plt.title('Milling Forces')
    plt.xlabel('Position Index')
    plt.ylabel('Force (N)')
    plt.legend()
    plt.grid(True)
    
    # Plot engagement angles
    plt.subplot(2, 1, 2)
    plt.plot(results['forces']['engagement_angles'], label='Engagement Angle')
    plt.title('Engagement Angle')
    plt.xlabel('Position Index')
    plt.ylabel('Angle (degrees)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('milling_force_simulation.png')
    plt.show()
    
    # Visualize the machining setup
    plt.figure(figsize=(8, 8))
    
    # Plot circles
    x_o1, y_o1, r1 = circle1
    x_o2, y_o2, r2 = circle2
    
    # Plot circle O1
    circle1_plot = plt.Circle((x_o1, y_o1), r1, fill=False, color='blue', label='Circle O1')
    plt.gca().add_patch(circle1_plot)
    
    # Plot circle O2
    circle2_plot = plt.Circle((x_o2, y_o2), r2, fill=False, color='red', label='Circle O2')
    plt.gca().add_patch(circle2_plot)
    
    # Plot offset circles (Cur1 and Cur2)
    offset_circle1 = plt.Circle((x_o1, y_o1), r1 + tool_radius, fill=False, color='blue', linestyle='--', label='Cur1')
    plt.gca().add_patch(offset_circle1)
    
    offset_circle2 = plt.Circle((x_o2, y_o2), r2 + tool_radius, fill=False, color='red', linestyle='--', label='Cur2')
    plt.gca().add_patch(offset_circle2)
    
    # Plot tool positions
    x_positions = [pos[0] for pos in results['positions']]
    y_positions = [pos[1] for pos in results['positions']]
    plt.plot(x_positions, y_positions, 'g-', label='Tool Path')
    
    # Plot a few tool circles along the path
    for i in range(0, len(results['positions']), len(results['positions'])//5):
        x, y = results['positions'][i]
        tool_circle = plt.Circle((x, y), tool_radius, fill=False, color='green')
        plt.gca().add_patch(tool_circle)
    
    plt.title('Multiple-Circle Continuous Machining Setup')
    plt.xlabel('X (mm)')
    plt.ylabel('Y (mm)')
    plt.axis('equal')
    plt.grid(True)
    plt.legend()
    
    # Set axis limits
    margin = 5
    plt.xlim(min(x_o1 - r1, x_o2 - r2) - margin, max(x_o1 + r1, x_o2 + r2) + margin)
    plt.ylim(min(y_o1 - r1, y_o2 - r2) - margin, max(y_o1 + r1, y_o2 + r2) + margin)
    
    plt.savefig('machining_setup.png')
    plt.show()

# Comprehensive simulation with different configurations
def run_comprehensive_simulation():
    # Define parameters
    tool_radius = 3  # mm
    num_teeth = 4
    helix_angle = 30  # degrees
    spindle_speed = 3000  # rpm
    feed_per_tooth = 0.08  # mm
    axial_depth = 3  # mm
    
    # Cutting coefficients
    cutting_coefficients = {
        'Ktc': 5325, 'Krc': 4902, 'Kac': 1823,
        'Kte': 0, 'Kre': 0, 'Kae': 0
    }
    
    # Create milling force predictor
    predictor = MillingForcePredictor(
        tool_radius, num_teeth, helix_angle, spindle_speed, feed_per_tooth,
        axial_depth, cutting_coefficients
    )
    
    # Define different circle configurations
    configurations = [
        {
            'name': 'Equal Circles (Small Interval)',
            'circle1': (0, 0, 5),
            'circle2': (5.5, 0, 5),
            'interval': 0.5
        },
        {
            'name': 'Equal Circles (Large Interval)',
            'circle1': (0, 0, 5),
            'circle2': (7, 0, 5),
            'interval': 2
        },
        {
            'name': 'Variable Circles (Small to Large)',
            'circle1': (0, 0, 4),
            'circle2': (6, 0, 6),
            'interval': 2
        },
        {
            'name': 'Variable Circles (Large to Small)',
            'circle1': (0, 0, 6),
            'circle2': (6, 0, 4),
            'interval': 2
        }
    ]
    
    # Create a figure for all configurations
    plt.figure(figsize=(15, 10))
    
    for i, config in enumerate(configurations):
        # Run simulation
        results = predictor.simulate_multiple_circle_machining(
            config['circle1'], config['circle2'], config['interval']
        )
        
        # Plot engagement angles
        plt.subplot(2, 2, i+1)
        plt.plot(results['forces']['engagement_angles'], label='Engagement Angle')
        plt.title(config['name'])
        plt.xlabel('Position Index')
        plt.ylabel('Angle (degrees)')
        plt.grid(True)
    
    plt.tight_layout()    
    plt.show()
    
    # Create a figure for machining setups
    plt.figure(figsize=(15, 10))
    
    for i, config in enumerate(configurations):
        plt.subplot(2, 2, i+1)
        
        # Extract circle parameters
        circle1 = config['circle1']
        circle2 = config['circle2']
        x_o1, y_o1, r1 = circle1
        x_o2, y_o2, r2 = circle2
        
        # Plot circle O1
        circle1_plot = plt.Circle((x_o1, y_o1), r1, fill=False, color='blue', label='Circle O1')
        plt.gca().add_patch(circle1_plot)
        
        # Plot circle O2
        circle2_plot = plt.Circle((x_o2, y_o2), r2, fill=False, color='red', label='Circle O2')
        plt.gca().add_patch(circle2_plot)
        
        # Plot offset circles (Cur1 and Cur2)
        offset_circle1 = plt.Circle((x_o1, y_o1), r1 + tool_radius, fill=False, color='blue', linestyle='--', label='Cur1')
        plt.gca().add_patch(offset_circle1)
        
        offset_circle2 = plt.Circle((x_o2, y_o2), r2 + tool_radius, fill=False, color='red', linestyle='--', label='Cur2')
        plt.gca().add_patch(offset_circle2)
        
        # Run simulation to get positions
        results = predictor.simulate_multiple_circle_machining(circle1, circle2, config['interval'])
        
        # Plot tool positions
        x_positions = [pos[0] for pos in results['positions']]
        y_positions = [pos[1] for pos in results['positions']]
        plt.plot(x_positions, y_positions, 'g-', label='Tool Path')
        
        # Plot a few tool circles along the path
        for j in range(0, len(results['positions']), len(results['positions'])//5):
            x, y = results['positions'][j]
            tool_circle = plt.Circle((x, y), tool_radius, fill=False, color='green')
            plt.gca().add_patch(tool_circle)
        
        plt.title(config['name'])
        plt.xlabel('X (mm)')
        plt.ylabel('Y (mm)')
        plt.axis('equal')
        plt.grid(True)
        
        # Set axis limits
        margin = 5
        plt.xlim(min(x_o1 - r1, x_o2 - r2) - margin, max(x_o1 + r1, x_o2 + r2) + margin)
        plt.ylim(min(y_o1 - r1, y_o2 - r2) - margin, max(y_o1 + r1, y_o2 + r2) + margin)
    
    plt.tight_layout()    
    plt.show()

# Run both simulations
#run_simulation()
run_comprehensive_simulation()