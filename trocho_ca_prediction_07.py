import numpy as np

def compute_max_engagement_angles_vectorized(sample_distances, radii, tool_radius, num_theta=10):
    """
    Compute maximum engagement angles for all pairs of consecutive circles using vectorized operations.
    
    Parameters:
    - sample_distances: Array of cumulative distances (shape: (N+1,))
    - radii: Array of circle radii (shape: (N+1,))
    - tool_radius: Radius of the tool (scalar)
    - num_theta: Number of angles to sample for each circle (default: 100)
    
    Returns:
    - Array of maximum engagement angles in radians (shape: (N,))
    """
    # Number of segments
    N = len(radii) - 1
    
    # Generate angle array for sampling
    theta = np.linspace(0, 2 * np.pi, num_theta, endpoint=False)
    
    # Compute segment lengths and radii for consecutive pairs
    # l = sample_distances[1:] - sample_distances[:-1]  # shape: (N,)    
    l = sample_distances
    
    r1 = np.full_like(l, radii[0])  # shape: (N,)
    r2 = radii[1:]   # shape: (N,)
    
    # Offset radii by tool radius
    R1 = r1 + tool_radius  # shape: (N,)
    R2 = r2 + tool_radius  # shape: (N,)
    
    # Tool center coordinates (C) for all pairs and angles
    C_x = l[:, None] + r2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    C_y = r2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    C = np.stack((C_x, C_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # Tangent points (N) on offset second circle
    N_x = l[:, None] + R2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    N_y = R2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    N = np.stack((N_x, N_y), axis=-1)              # shape: (N, num_theta, 2)
    
    # First circle center (O1) at origin for all pairs
    num_segments = len(r1)  # Use integer value instead of N array
    O1 = np.zeros((num_segments, 1, 2))  # shape: (num_segments, 1, 2)
    
    # Distance from O1 to C
    d = np.sqrt(np.sum((C - O1)**2, axis=-1))  # shape: (N, num_theta)
    
    # Intersection condition
    mask = (d >= np.abs(R1[:, None] - tool_radius)) & (d <= (R1[:, None] + tool_radius))
    
    # Compute gamma (angle for intersection points)
    gamma = np.arccos(np.clip((d**2 + R1[:, None]**2 - tool_radius**2) / (2 * d * R1[:, None]), -1.0, 1.0))
    gamma = np.nan_to_num(gamma, nan=0.0)  # shape: (N, num_theta)
    
    # Unit vector from O1 to C
    u = (C - O1) / d[..., None]  # shape: (N, num_theta, 2)
    u = np.nan_to_num(u, nan=0.0)
    
    # Perpendicular vector (rotated 90 degrees)
    v = np.stack((-u[..., 1], u[..., 0]), axis=-1)  # shape: (N, num_theta, 2)
    
    # Intersection points D1 and D2
    cos_gamma = np.cos(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    sin_gamma = np.sin(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    D1 = O1 + R1[:, None, None] * (u * cos_gamma + v * sin_gamma)  # shape: (N, num_theta, 2)
    D2 = O1 + R1[:, None, None] * (u * cos_gamma - v * sin_gamma)  # shape: (N, num_theta, 2)
    
    # Distances from N to D1 and D2
    DN1 = np.sqrt(np.sum((D1 - N)**2, axis=-1))  # shape: (N, num_theta)
    DN2 = np.sqrt(np.sum((D2 - N)**2, axis=-1))  # shape: (N, num_theta)
    
    # Engagement angles
    alpha1 = np.arccos(np.clip(1 - (DN1**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    alpha2 = np.arccos(np.clip(1 - (DN2**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    
    # Apply mask and compute maximum
    alpha1 = np.where(mask, alpha1, 0.0)
    alpha2 = np.where(mask, alpha2, 0.0)
    max_alpha = np.max(np.maximum(alpha1, alpha2), axis=1)  # shape: (N,)
    
    # Convert to degrees
    max_alpha = np.rad2deg(max_alpha)
    
    return max_alpha

# Example usage
if __name__ == "__main__":
    tool_radius = 10.0
    # Test with small array
    # sample_distances = np.array([0.0, 12.0])
    sample_distances = np.array([9.5, 18.0])
    # radii = np.array([40.0, 32.0])/2
    radii = np.array([91.0, 89.0, 85.0])/2
    angles = compute_max_engagement_angles_vectorized(sample_distances, radii, tool_radius)
    print("Maximum engagement angles (radians):", angles)
