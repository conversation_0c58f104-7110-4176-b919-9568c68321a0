import numpy as np
import bpy # Still needed for get_geometry
import shapely
import pyvoronoi
from shapely.geometry import Point, LineString, Polygon
from shapely import prepare, intersection, difference, unary_union
import math
import random
from shapely.ops import linemerge, transform
import time
from mathutils import Vector

# --- Existing Helper Functions (get_geometry, MA, boundary_distance, angle_engagement, get_next_tool_position) ---
# These remain largely the same as the previous version.
# Minor cleanup/robustness added if needed during integration.

def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = list(bpy.context.selected_objects) # Make a mutable copy
    if not selected_objects:
        print("No objects selected.")
        return []
    if active_obj and active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    elif active_obj:
         # print("Active object not in selection, returning selection as is.") # Less verbose
         pass
    else:
        # print("No active object, returning selection as is.") # Less verbose
        pass

    return selected_objects

def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []

def make_medial_edges(polygon, rounding_precision=4):
    # (Using the robust version from the previous step)
    pv = pyvoronoi.Pyvoronoi(100)
    coords = np.array(polygon.exterior.coords[:-1])
    segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
    for segment in segments:
        start = [float(segment[0]), float(segment[1])]
        end = [float(segment[2]), float(segment[3])]
        if np.linalg.norm(np.array(start) - np.array(end)) > 1e-9:
             pv.AddSegment([start, end])
    for interior in polygon.interiors:
        coords = np.array(interior.coords[:-1])
        segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
        for segment in segments:
            start = [float(segment[0]), float(segment[1])]
            end = [float(segment[2]), float(segment[3])]
            if np.linalg.norm(np.array(start) - np.array(end)) > 1e-9:
                pv.AddSegment([start, end])
    try:
        pv.Construct()
    except Exception as e:
        print(f"Pyvoronoi construction failed: {e}")
        polygon_clean = polygon.buffer(0)
        if polygon_clean.is_valid and polygon_clean.geom_type == 'Polygon':
             print("Retrying MA with cleaned polygon...")
             return make_medial_edges(polygon_clean, rounding_precision)
        else: return []

    edges_pv = pv.GetEdges()
    vertices_pv = pv.GetVertices()
    cells = pv.GetCells()
    prepared_polygon = shapely.prepare(polygon)
    ma_edges = []
    twins = set()
    buffer_dist = 1e-6

    for cell in cells:
        for edge_idx in cell.edges:
            e = edges_pv[edge_idx]
            if not e.is_primary or e.start == -1 or e.end == -1 or edge_idx in twins: continue
            startVertex = vertices_pv[e.start]
            endVertex = vertices_pv[e.end]
            mid_x = (startVertex.X + endVertex.X) / 2
            mid_y = (startVertex.Y + endVertex.Y) / 2
            mid_point_buffered = Point(mid_x, mid_y).buffer(buffer_dist)
            if not prepared_polygon.contains(mid_point_buffered):
                 if polygon.boundary.distance(Point(mid_x, mid_y)) > buffer_dist * 10: continue
            twins.add(e.twin)
            start_point = (round(startVertex.X, rounding_precision), round(startVertex.Y, rounding_precision))
            end_point = (round(endVertex.X, rounding_precision), round(endVertex.Y, rounding_precision))
            if np.linalg.norm(np.array(start_point) - np.array(end_point)) > 1e-9:
                 ma_edges.append(shapely.geometry.LineString([start_point, end_point]))

    if not ma_edges: return []
    try: merged = unary_union(ma_edges)
    except Exception: merged = shapely.geometry.MultiLineString(ma_edges)

    simplified_edges = []
    simplify_tolerance = 0.01
    if merged.geom_type == 'LineString':
        simplified = merged.simplify(simplify_tolerance)
        if not simplified.is_empty and simplified.length > 1e-6 : simplified_edges.append(simplified)
    elif merged.geom_type == 'MultiLineString':
        for edge in merged.geoms:
             simplified = edge.simplify(simplify_tolerance)
             if not simplified.is_empty and simplified.length > 1e-6: simplified_edges.append(simplified)
    elif merged.geom_type == 'GeometryCollection':
        for geom in merged.geoms:
             if geom.geom_type in ('LineString', 'MultiLineString'):
                  simplified = geom.simplify(simplify_tolerance)
                  if not simplified.is_empty and simplified.length > 1e-6:
                      if simplified.geom_type == 'LineString': simplified_edges.append(simplified)
                      elif simplified.geom_type == 'MultiLineString': simplified_edges.extend(list(simplified.geoms))
    return simplified_edges


def angle_engagement(center: tuple[float, float], p_center: tuple[float, float], r: float, stock_poly: Polygon, arc_resolution=12) -> float | None:
    # (Using the robust version from the previous step)
    if not isinstance(stock_poly, (Polygon, shapely.geometry.MultiPolygon)) or not stock_poly.is_valid: return None
    if center == p_center: return 0.0
    vec = [center[0] - p_center[0], center[1] - p_center[1]]
    vec_mag = math.sqrt(vec[0]**2 + vec[1]**2)
    if vec_mag < 1e-9: return 0.0
    movement_angle = np.arctan2(vec[1], vec[0])
    start_angle = movement_angle + np.pi / 2
    end_angle = movement_angle - np.pi / 2
    arc_points = [(center[0] + r * np.cos(t), center[1] + r * np.sin(t))
                  for t in np.linspace(end_angle, start_angle, arc_resolution)]
    if len(arc_points) < 2: return None
    arc = shapely.LineString(arc_points)
    if not arc.is_valid: return None
    try:
        intersection_geom = intersection(arc, stock_poly)
        if not intersection_geom or intersection_geom.is_empty: return 0.0
        total_length = 0.0
        if intersection_geom.geom_type == "LineString": total_length = intersection_geom.length
        elif intersection_geom.geom_type == "MultiLineString": total_length = sum(line.length for line in intersection_geom.geoms)
        elif intersection_geom.geom_type == "GeometryCollection":
             for geom in intersection_geom.geoms:
                  if geom.geom_type == "LineString": total_length += geom.length
                  elif geom.geom_type == "MultiLineString": total_length += sum(line.length for line in geom.geoms)
        engagement_angle = total_length / r
        return max(0.0, min(engagement_angle, np.pi))
    except Exception: return None


def boundary_distance(polygon: Polygon, points: list[tuple[float, float]]) -> np.ndarray:
    # (Using the version from the previous step)
    if not points: return np.array([])
    boundary = polygon.boundary
    if boundary.is_empty or boundary is None: return np.zeros(len(points))
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def get_next_tool_position(current_pos: np.ndarray, prev_pos: np.ndarray, angle_rad: float, step_size: float) -> np.ndarray:
    # (Using the version from the previous step)
    direction_vec = current_pos - prev_pos
    vec_norm = np.linalg.norm(direction_vec)
    if vec_norm < 1e-9: base_direction = np.array([1.0, 0.0])
    else: base_direction = direction_vec / vec_norm
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)
    rotation_matrix = np.array([[cos_a, -sin_a], [sin_a,  cos_a]])
    rotated_direction = rotation_matrix @ base_direction
    next_pos = current_pos + rotated_direction * step_size
    return next_pos

# --- NEW Functions for Algorithms 3, 4, 5 ---

def find_best_spiral_move(
    ptp: list[np.ndarray],
    omax_rad: float,
    step_size: float,
    tool_radius: float,
    current_stock_polygon: Polygon,
    target_engagement_rad: float,
    engagement_diff_tolerance: float, # New parameter Stol from Alg 3
    angle_step_rad: float = np.radians(1.0),
    min_cutting_engagement_rad: float = 1e-4 # Threshold to consider a move "cutting"
    ) -> tuple[np.ndarray | None, bool]:
    """
    Implements Algorithm 3 logic: Finds a good spiral move.
    Prioritizes finding a move within engagement_diff_tolerance of the target quickly.
    Otherwise returns the move with the overall best engagement match found.

    Args:
        ptp, omax_rad, step_size, tool_radius, current_stock_polygon,
        target_engagement_rad, angle_step_rad: As before.
        engagement_diff_tolerance (Stol): Max allowed absolute difference between
                                           actual and target engagement for early exit.
        min_cutting_engagement_rad: Minimum engagement to consider valid cutting.

    Returns:
        Tuple (best_pos, is_valid):
            best_pos: The recommended next tool position [x, y] or None.
            is_valid: True if a valid cutting move was found, False otherwise.
    """
    if len(ptp) < 2: return None, False
    if not current_stock_polygon.is_valid or current_stock_polygon.is_empty: return None, False

    pn = ptp[-1]
    pn_1 = ptp[-2]

    best_engagement_diff = float('inf')
    best_pos_found = None
    best_engagement_found = -1.0 # Store the actual engagement of the best move

    # Generate angles
    num_angle_steps = int(omax_rad / angle_step_rad) if angle_step_rad > 1e-9 else 0
    angles_to_check = [0.0]
    for i in range(1, num_angle_steps + 1):
        angle = i * angle_step_rad
        angles_to_check.append(angle)
        angles_to_check.append(-angle)
    if abs(omax_rad - num_angle_steps * angle_step_rad) > 1e-9 :
        if omax_rad not in angles_to_check: angles_to_check.append(omax_rad)
        if -omax_rad not in angles_to_check: angles_to_check.append(-omax_rad)

    # Local minima detection variables (optional, implementation can be complex)
    # local_minima_candidate_pos = None
    # local_minima_diff = float('inf')

    for angle_rad in angles_to_check:
        candidate_pos = get_next_tool_position(pn, pn_1, angle_rad, step_size)
        candidate_pos_tuple = tuple(candidate_pos)

        engagement = angle_engagement(candidate_pos_tuple, tuple(pn), tool_radius, current_stock_polygon, arc_resolution=16)

        if engagement is not None and engagement >= min_cutting_engagement_rad:
            engagement_diff = abs(engagement - target_engagement_rad)

            # --- Early Exit Check (Alg 3) ---
            if engagement_diff <= engagement_diff_tolerance:
                # Found a "good enough" move, return immediately
                # print(f"Spiral early exit: diff {engagement_diff:.4f} <= tol {engagement_diff_tolerance:.4f}")
                return candidate_pos, True

            # --- Track Best Move ---
            if engagement_diff < best_engagement_diff:
                best_engagement_diff = engagement_diff
                best_pos_found = candidate_pos
                best_engagement_found = engagement

            # --- Local Minima Check (Simplified - check if diff increases after minimum) ---
            # if best_engagement_diff < engagement_diff_tolerance and engagement_diff > best_engagement_diff:
                 # If we found a minimum difference (better than tolerance) and the current diff is worse,
                 # we might have passed the optimal point. Return the best found so far.
                 # This prevents searching the whole range if a good minimum is found early.
                 # print(f"Spiral local minima detected? Returning best diff {best_engagement_diff:.4f}")
                 # return best_pos_found, True # Enable this line for local minima optimization

    # --- Loop finished, return best found overall ---
    if best_pos_found is not None and best_engagement_found >= min_cutting_engagement_rad:
        # print(f"Spiral best found: diff {best_engagement_diff:.4f}, eng {best_engagement_found:.4f}")
        return best_pos_found, True
    else:
        # print("Spiral: No valid cutting move found.")
        return None, False


def find_boundary_move(
    ptp: list[np.ndarray],
    omax_rad: float,
    step_size: float,
    tool_radius: float,
    initial_stock_polygon: Polygon,
    angle_step_rad: float = np.radians(1.0),
    min_cutting_engagement_rad: float = 1e-4 # Threshold to distinguish cutting/not cutting
    ) -> tuple[np.ndarray | None, bool]:
    """
    Implements Algorithm 4 logic: Attempts to find a move along the boundary
    by detecting transitions between cutting and non-cutting states.

    Args:
        ptp, omax_rad, step_size, tool_radius, current_stock_polygon, angle_step_rad,
        min_cutting_engagement_rad: As before.

    Returns:
        Tuple (boundary_pos, is_valid):
            boundary_pos: The estimated position on the boundary or None.
            is_valid: True if a boundary transition was found, False otherwise.
    """
    if len(ptp) < 2: return None, False
    if not initial_stock_polygon.is_valid or initial_stock_polygon.is_empty: return None, False

    pn = ptp[-1]
    pn_1 = ptp[-2]

    # Generate angles (can use a wider range for boundary finding, e.g., 180 deg)
    num_angle_steps = int(omax_rad / angle_step_rad) if angle_step_rad > 1e-9 else 0
    # Ensure check starts from -omax_rad
    angles_to_check = [-omax_rad]
    for i in range(-num_angle_steps+1, num_angle_steps + 1):
         angles_to_check.append(i * angle_step_rad)
    if abs(omax_rad - num_angle_steps * angle_step_rad) > 1e-9 :
        if omax_rad not in angles_to_check: angles_to_check.append(omax_rad)
        # -omax_rad already included

    # Initialize previous state
    prev_pos = get_next_tool_position(pn, pn_1, angles_to_check[0], step_size)
    prev_engagement = angle_engagement(tuple(prev_pos), tuple(pn), tool_radius, initial_stock_polygon)
    is_prev_cutting = prev_engagement is not None and prev_engagement >= min_cutting_engagement_rad

    for angle_rad in angles_to_check[1:]: # Start from the second angle
        current_pos = get_next_tool_position(pn, pn_1, angle_rad, step_size)
        current_engagement = angle_engagement(tuple(current_pos), tuple(pn), tool_radius, initial_stock_polygon)
        is_current_cutting = current_engagement is not None and current_engagement >= min_cutting_engagement_rad

        # --- Detect Transition ---
        # 1. Transition from Cutting to Not Cutting: Boundary is near the *previous* position
        if is_prev_cutting and not is_current_cutting:
            # print("Boundary found (Cutting -> Not Cutting)")
            return prev_pos, True

        # 2. Transition from Not Cutting to Cutting: Boundary is near the *current* position
        elif not is_prev_cutting and is_current_cutting:
            # print("Boundary found (Not Cutting -> Cutting)")
            return current_pos, True

        # Update previous state for next iteration
        prev_pos = current_pos
        prev_engagement = current_engagement
        is_prev_cutting = is_current_cutting

    # --- Loop finished, no transition found ---
    # print("Boundary: No transition found.")
    return None, False


def find_next_move(
    ptp: list[np.ndarray],
    omax_spiral_rad: float, # Max angle for spiral search
    omax_boundary_rad: float, # Potentially wider angle for boundary search
    step_size: float,
    tool_radius: float,
    current_stock_polygon: Polygon,
    initial_stock_polygon: Polygon,
    target_engagement_rad: float,
    engagement_diff_tolerance: float, # Stol for spiral move
    angle_step_rad: float = np.radians(1.0),
    min_cutting_engagement_rad: float = 1e-4
    ) -> tuple[np.ndarray | None, bool]:
    """
    Implements Algorithm 5 logic: Orchestrates finding the next move.
    Tries to find a spiral move first (Alg 3). If that fails, tries to find
    a boundary move (Alg 4).

    Args:
        omax_spiral_rad: Max deviation angle for find_best_spiral_move.
        omax_boundary_rad: Max deviation angle for find_boundary_move.
        Other parameters as defined in the called functions.

    Returns:
        Tuple (next_pos, is_valid):
            next_pos: The determined next position or None.
            is_valid: True if a valid move was found (either spiral or boundary), False otherwise.
    """
    # 1. Try Spiral Move (Algorithm 3)
    spiral_pos, spiral_valid = find_best_spiral_move(
        ptp, omax_spiral_rad, step_size, tool_radius, current_stock_polygon,
        target_engagement_rad, engagement_diff_tolerance, angle_step_rad,
        min_cutting_engagement_rad
    )

    if spiral_valid:
        return spiral_pos, True # Found a valid spiral move

    # 2. Spiral Failed, Try Boundary Move (Algorithm 4)
    # print("Spiral move failed, attempting boundary move...") # Verbose
    boundary_pos, boundary_valid = find_boundary_move(
        ptp, omax_boundary_rad, step_size, tool_radius, initial_stock_polygon,
        angle_step_rad, min_cutting_engagement_rad
    )

    # Return the result of the boundary search (could be valid or invalid)
    return boundary_pos, boundary_valid


# --- Modified generate_toolpath ---

def generate_toolpath(
    start_point: np.ndarray,
    initial_direction: np.ndarray,
    max_steps: int,
    omax_spiral_deg: float, # Max angle for spiral part
    omax_boundary_deg: float, # Max angle for boundary part
    step_size: float,
    tool_radius: float,
    initial_stock_polygon: Polygon,
    target_engagement_rad: float,
    engagement_diff_tolerance_frac: float = 0.1 # Stol as fraction of target_engagement_rad
    ) -> list[np.ndarray]:
    """
    Generates toolpath using the find_next_move strategy (Algorithms 3, 4, 5),
    including dynamic stock removal.
    """
    omax_spiral_rad = np.radians(omax_spiral_deg)
    omax_boundary_rad = np.radians(omax_boundary_deg)
    angle_step_rad = np.radians(1.0)
    quad_segs_buffer = 8
    min_cutting_engagement_rad = target_engagement_rad * 0.01 # e.g., 1% of target

    # Calculate absolute tolerance for spiral move early exit
    engagement_diff_tolerance = target_engagement_rad * engagement_diff_tolerance_frac

    p0 = start_point - initial_direction * step_size
    p1 = start_point
    toolpath = [p0, p1]

    current_stock = initial_stock_polygon
    if not current_stock.is_valid:
        print("Initial stock polygon is invalid. Attempting buffer(0).")
        current_stock = current_stock.buffer(0)
        if not current_stock.is_valid:
            print("Error: Cannot fix initial stock polygon. Aborting.")
            return []

    # --- Simulation Loop ---
    for i in range(max_steps):
        # --- Find next move using Algorithm 5 logic ---
        next_pos, is_valid_move = find_next_move(
            toolpath,
            omax_spiral_rad,
            omax_boundary_rad,
            step_size,
            tool_radius,
            current_stock,
            initial_stock_polygon,
            target_engagement_rad,
            engagement_diff_tolerance, # Pass absolute tolerance
            angle_step_rad,
            min_cutting_engagement_rad
        )

        if not is_valid_move or next_pos is None:
            print(f"Stopping toolpath generation at step {i+1}: find_next_move failed.")
            break

        # --- Basic Gouge Check (Optional) ---
        # if not initial_stock_polygon.contains(Point(next_pos).buffer(step_size)):
        #      print(f"Stopping (Gouge Check): Next point {next_pos} outside original boundary tolerance.")
        #      break

        # --- Add point to path ---
        toolpath.append(next_pos)

        # --- Update Stock ---
        try:
            tool_circle = shapely.geometry.Point(*next_pos).buffer(tool_radius, quad_segs_buffer)
            if not tool_circle.is_valid: continue # Skip update if buffer fails

            if current_stock.intersects(tool_circle):
                # Performance: If stock is MultiPolygon, process each part
                if current_stock.geom_type == 'MultiPolygon':
                     new_parts = []
                     for part in current_stock.geoms:
                          if part.intersects(tool_circle):
                               diff = difference(part, tool_circle)
                               if not diff.is_empty:
                                    new_parts.append(diff)
                          else:
                               new_parts.append(part)
                     new_stock = unary_union(new_parts) # Reassemble
                else: # Single Polygon
                     new_stock = difference(current_stock, tool_circle)

                # Validate and potentially fix new stock
                if not new_stock.is_valid:
                    new_stock = new_stock.buffer(0)
                    if not new_stock.is_valid:
                         print(f"Error: Stock remains invalid after buffer(0) at step {i+1}. Stopping.")
                         break
                current_stock = new_stock
                if current_stock.is_empty:
                    print(f"Stopping toolpath generation at step {i+1}: Stock depleted.")
                    break
        except Exception as e:
            print(f"Error during stock update at step {i+1}: {e}")
            # break # Safer to stop

    else: # Loop completed without break
        print(f"Stopped toolpath generation after reaching max_steps: {max_steps}")

    return toolpath[1:] # Return actual path


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj



# --- Main Execution Logic ---

def main():
    start_time = time.time()
    if not has_selected_objects(): return

    # --- Parameters ---
    tool_diameter = 40.0
    tool_radius = tool_diameter / 2.0
    target_engagement_deg = 75.0 # Target engagement angle (degrees) - Higher might work better with boundary follow
    target_engagement_rad = np.radians(target_engagement_deg)
    omax_spiral_deg = 30.0   # Max angle change for spiral (Alg 3)
    omax_boundary_deg = 90.0 # Wider angle range for boundary finding (Alg 4) - as per paper Alg 5 example
    step_size = tool_radius * 0.15 # Slightly larger steps might be okay now?
    max_steps = 4000
    engagement_diff_tolerance_frac = 0.15 # Stol fraction (15% of target engagement)

    geometry = get_geometry()
    if not geometry: print("Failed to get geometry."); return

    try:
        shell_coords = [tuple(p) for p in geometry[0]]
        holes = [[tuple(p) for p in hole] for hole in geometry[1:]] if len(geometry) > 1 else None
        polygon = shapely.geometry.Polygon(shell=shell_coords, holes=holes)
        if not polygon.is_valid:
             print("Fixing invalid input polygon with buffer(0).")
             polygon = polygon.buffer(0)
             if not polygon.is_valid or polygon.is_empty or not isinstance(polygon, (Polygon, shapely.geometry.MultiPolygon)):
                  print("Error: Polygon invalid/empty after buffer(0)."); return
    except Exception as e: print(f"Error creating Shapely polygon: {e}"); return

    # --- Find Start Point (MA method) ---
    start_point = None
    try:
        polygon_for_ma = polygon
        # Check if it's MultiPolygon, use largest part for MA
        if isinstance(polygon, shapely.geometry.MultiPolygon):
             print("Input is MultiPolygon, using largest part for MA start point.")
             polygon_for_ma = max(polygon.geoms, key=lambda p: p.area)

        buffer_amount = -0.05 * tool_radius
        # Ensure buffer doesn't eliminate the polygon
        if polygon_for_ma.area < abs(buffer_amount) * tool_radius * 10: # Heuristic check
             buffer_amount = -0.001 # Use very small buffer if area is small

        polygon_buffered_slightly = polygon_for_ma.buffer(buffer_amount)

        if not polygon_buffered_slightly.is_valid or polygon_buffered_slightly.is_empty:
             print("Warning: Polygon too small/complex for MA buffer. Using representative point.")
             start_point_shapely = polygon_for_ma.representative_point()
             start_point = np.array(start_point_shapely.coords[0])
        else:
            if polygon_buffered_slightly.geom_type == 'Polygon': ma_input = polygon_buffered_slightly
            elif polygon_buffered_slightly.geom_type == 'MultiPolygon': ma_input = max(polygon_buffered_slightly.geoms, key=lambda p: p.area)
            else: ma_input = None

            if ma_input: edges = make_medial_edges(ma_input)
            else: edges = []

            if not edges:
                 print("Warning: MA failed/empty. Using representative point.")
                 start_point_shapely = polygon_for_ma.representative_point()
                 start_point = np.array(start_point_shapely.coords[0])
            else:
                 points = list(set(p for edge in edges for p in edge.coords))
                 if not points:
                      print("Warning: No points on MA. Using representative point.")
                      start_point_shapely = polygon_for_ma.representative_point()
                      start_point = np.array(start_point_shapely.coords[0])
                 else:
                      radii = boundary_distance(polygon_for_ma, points) # Dist to original boundary part
                      if len(radii) > 0:
                           max_radius_index = np.argmax(radii)
                           start_point_candidate = np.array(points[max_radius_index])
                           if polygon_for_ma.contains(Point(start_point_candidate).buffer(1e-6)): # Check containment with tolerance
                                start_point = start_point_candidate
                           else:
                                print("Warning: Best MA point outside. Using representative point.")
                                start_point = np.array(polygon_for_ma.representative_point().coords[0])
                      else:
                           print("Warning: Radii calc failed. Using representative point.")
                           start_point = np.array(polygon_for_ma.representative_point().coords[0])
    except Exception as e:
        print(f"Error during start point MA calculation: {e}. Using representative point.")
        start_point = np.array(polygon.representative_point().coords[0]) # Fallback to overall polygon

    if start_point is None: print("Error: Failed to determine start point."); return
    print(f"Selected Start Point: {start_point}")

    initial_direction = np.array([1.0, 0.0]) # Default initial direction

    print("Starting toolpath generation (Alg 3+4+5)...")
    toolpath_points = generate_toolpath(
        start_point, initial_direction, max_steps,
        omax_spiral_deg, omax_boundary_deg, # Pass separate angles
        step_size, tool_radius, polygon, # Pass original polygon as initial stock
        target_engagement_rad, engagement_diff_tolerance_frac
    )
    print(f"Generated {len(toolpath_points)} toolpath points.")

    if toolpath_points:
        create_line_object(toolpath_points, "toolpath_alg345")
        # print("Toolpath generation successful.")
        # try:
        #      output_file = "/tmp/generated_toolpath_alg345.txt"
        #      np.savetxt(output_file, np.array(toolpath_points), fmt='%.6f', delimiter=',')
        #      print(f"Toolpath saved to {output_file}")
        # except Exception as e: print(f"Error saving toolpath: {e}")
    else: print("No toolpath points generated.")

    end_time = time.time()
    print(f"Script finished in {end_time - start_time:.2f} seconds.")


if __name__ == "__main__":
    main()