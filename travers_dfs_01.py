import networkx as nx

# Create an undirected graph
G = nx.Graph([(1, 2), (1, 3), (2, 4), (3, 4), (3, 5), (3,6)])

# Perform DFS starting from node 1
dfs_edges_sequence = list(nx.dfs_edges(G, source=1))
print("DFS Edges:", dfs_edges_sequence)

# Perform DFS starting from an arbitrary node (explores one component)
dfs_edges_arbitrary = list(nx.dfs_edges(G))
print("DFS Edges (arbitrary start):", dfs_edges_arbitrary)