import networkx as nx
from typing import List, Set, Dict
from collections import defaultdict

def find_odd_degree_vertices(G: nx.Graph) -> List[str]:
    """Find vertices with odd degree."""
    return [node for node in G.nodes() if G.degree(node) % 2 != 0]

def find_shortest_paths(G: nx.Graph, odd_vertices: List[str]) -> Dict[tuple, float]:
    """Find shortest paths between all pairs of odd degree vertices."""
    paths = {}
    for i in range(len(odd_vertices)):
        for j in range(i + 1, len(odd_vertices)):
            v1, v2 = odd_vertices[i], odd_vertices[j]
            try:
                path_length = nx.shortest_path_length(G, v1, v2)
                paths[(v1, v2)] = path_length
            except nx.NetworkXNoPath:
                paths[(v1, v2)] = float('inf')
    return paths

def add_augmenting_paths(G: nx.Graph) -> nx.Graph:
    """Add necessary edges to make the graph Eulerian."""
    augmented_G = G.copy()
    odd_vertices = find_odd_degree_vertices(G)
    
    if not odd_vertices:
        return augmented_G
    
    # Find shortest paths between odd degree vertices
    paths = find_shortest_paths(G, odd_vertices)
    
    # Create a graph for minimum weight perfect matching
    matching_graph = nx.Graph()
    for (v1, v2), weight in paths.items():
        matching_graph.add_edge(v1, v2, weight=weight)
    
    # Perform minimum weight perfect matching
    matching = nx.min_weight_matching(matching_graph)
    
    # Add the matched paths to the original graph
    for v1, v2 in matching:
        path = nx.shortest_path(G, v1, v2)
        for i in range(len(path) - 1):
            augmented_G.add_edge(path[i], path[i + 1])
    
    return augmented_G

def find_eulerian_trail(G: nx.Graph, start_node: str) -> List[str]:
    """Find Eulerian trail starting from given node."""
    if not G.edges:
        return [start_node]
    
    trail = []
    current = start_node
    edges_used = set()
    
    def get_next_edge(node: str) -> tuple:
        """Get next available edge from current node."""
        for neighbor in G.neighbors(node):
            edge = tuple(sorted([node, neighbor]))
            if edge not in edges_used:
                return edge
        return None
    
    # Follow edges until no unused edges remain
    while True:
        edge = get_next_edge(current)
        if edge is None:
            break
            
        edges_used.add(edge)
        next_node = edge[1] if edge[0] == current else edge[0]
        
        if not trail:
            trail.extend([current, next_node])
        else:
            trail.append(next_node)
            
        current = next_node
    
    return trail

def solve_cpp(G: nx.Graph, start_node: str) -> List[str]:
    """
    Solve the Chinese Postman Problem for an unweighted, undirected graph.
    Returns a trail that visits all edges at least once.
    
    Args:
        G: NetworkX undirected graph
        start_node: Starting vertex for the trail
    
    Returns:
        List of vertices representing the trail
    """
    if not G.has_node(start_node):
        raise ValueError("Start node not in graph")
    
    if not nx.is_connected(G):
        raise ValueError("Graph must be connected")
    
    # Create augmented graph that has Eulerian properties
    augmented_G = add_augmenting_paths(G)
    
    # Find Eulerian trail starting from start_node
    trail = find_eulerian_trail(augmented_G, start_node)
    
    return trail

def main():
    # Create example graph
    G = nx.Graph()
    G.add_edge('A', 'B')
    G.add_edge('B', 'C')
    G.add_edge('B', 'D')
    G.add_edge('D', 'E')
    G.add_edge('D', 'F')
    G.add_edge('F', 'G')
    G.add_edge('F', 'H')
    
    start_node = "A"
    trail = solve_cpp(G, start_node)
    print("Eulerian Trail (CPP solution):", trail)

if __name__ == "__main__":
    main()
