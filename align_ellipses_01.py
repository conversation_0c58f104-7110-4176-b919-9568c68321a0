import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Polygon, Point
from shapely.affinity import rotate as shapely_rotate, translate
from scipy.optimize import minimize_scalar

# --- 1. Helper Function to Generate Ellipse Points ---
def get_ellipse_points(center_x, center_y, semi_major, semi_minor, angle_deg, num_points=100):
    """
    Generates boundary points for an ellipse.
    angle_deg: rotation angle of the ellipse in degrees.
    """
    t = np.linspace(0, 2 * np.pi, num_points, endpoint=False) # endpoint=False for Polygon
    ellipse_x_unrotated = semi_major * np.cos(t)
    ellipse_y_unrotated = semi_minor * np.sin(t)

    angle_rad = np.deg2rad(angle_deg)
    cos_angle = np.cos(angle_rad)
    sin_angle = np.sin(angle_rad)

    ellipse_x_rotated = ellipse_x_unrotated * cos_angle - ellipse_y_unrotated * sin_angle
    ellipse_y_rotated = ellipse_x_unrotated * sin_angle + ellipse_y_unrotated * cos_angle

    ellipse_x = center_x + ellipse_x_rotated
    ellipse_y = center_y + ellipse_y_rotated

    return np.column_stack((ellipse_x, ellipse_y))

# --- 2. Define Ellipses and Pivot Point ---

# Green Ellipse (Target, larger)
green_center_x, green_center_y = 5, 5
green_semi_major = 10
green_semi_minor = 7
green_angle_deg = 25  # Orientation of the green ellipse
green_points = get_ellipse_points(green_center_x, green_center_y,
                                  green_semi_major, green_semi_minor,
                                  green_angle_deg)
green_polygon = Polygon(green_points)

# Pivot Point (Pink Dot) - This point MUST be ON the green ellipse boundary.
# For simulation, let's pick a point on the green ellipse.
# e.g., the point corresponding to parametric angle t_pivot_param on the unrotated, uncentered ellipse.
t_pivot_param_on_green = np.pi + 1 # Arbitrary angle to select a point on green ellipse

# Calculate local coordinates of this pivot point on an unrotated green ellipse at origin
pivot_local_x_g = green_semi_major * np.cos(t_pivot_param_on_green) + 0.5
pivot_local_y_g = green_semi_minor * np.sin(t_pivot_param_on_green)

# Rotate this local point by green_angle_deg
green_angle_rad = np.deg2rad(green_angle_deg)
cos_g_angle = np.cos(green_angle_rad)
sin_g_angle = np.sin(green_angle_rad)
pivot_rotated_x_g = pivot_local_x_g * cos_g_angle - pivot_local_y_g * sin_g_angle
pivot_rotated_y_g = pivot_local_x_g * sin_g_angle + pivot_local_y_g * cos_g_angle

# Translate to final green ellipse center
pivot_x = green_center_x + pivot_rotated_x_g
pivot_y = green_center_y + pivot_rotated_y_g
pivot_coords = (pivot_x, pivot_y) # This is the fixed "pink dot"

# Red Ellipse (Smaller, to be rotated around pivot_coords)
red_semi_major = 1.5
red_semi_minor = 4.5
initial_red_orientation_angle_deg = 10 # An initial arbitrary orientation for the red ellipse

# Construct the initial red ellipse.
# A specific point on its boundary (its "attachment point") will be placed at pivot_coords.
# Let's choose the point on the red ellipse that corresponds to its parametric angle t=0
# (i.e., (red_semi_major, 0) if it were centered at origin and unrotated).

# 1. Define the local coordinates of the attachment point on the red ellipse's own frame:
attachment_point_local_red_x = red_semi_major * np.cos(0)
attachment_point_local_red_y = red_semi_minor * np.sin(0)

# 2. Create a template red ellipse centered at origin, unrotated (angle=0).
red_points_template_at_origin = get_ellipse_points(0, 0,
                                                   red_semi_major, red_semi_minor,
                                                   0) # angle 0 for template
red_poly_template_at_origin = Polygon(red_points_template_at_origin)

# 3. Translate this template so its attachment point moves to (0,0).
#    The center of the ellipse moves to (-attachment_point_local_red_x, -attachment_point_local_red_y).
red_poly_attachment_at_origin = translate(red_poly_template_at_origin,
                                          xoff=-attachment_point_local_red_x,
                                          yoff=-attachment_point_local_red_y)

# 4. Rotate this ellipse (which has its attachment point at (0,0)) by its initial orientation.
#    The rotation is around (0,0), which is the current location of the attachment point.
red_poly_oriented_attachment_at_origin = shapely_rotate(red_poly_attachment_at_origin,
                                                        initial_red_orientation_angle_deg,
                                                        origin=(0,0))

# 5. Translate this entire construct so the attachment point (still at (0,0) in its local system)
#    moves to the global pivot_coords.
initial_red_polygon = translate(red_poly_oriented_attachment_at_origin,
                                xoff=pivot_coords[0],
                                yoff=pivot_coords[1])
# Now, `initial_red_polygon` has its designated attachment point at `pivot_coords`
# and has an overall initial orientation. All subsequent rotations will be around `pivot_coords`.

# --- 3. Objective Function ---
# We want to minimize the area of the red ellipse that is OUTSIDE the green ellipse.
def objective_function(rotation_angle_to_apply_deg):
    """
    Calculates the area of the red ellipse outside the green ellipse
    after rotating the initial_red_polygon around pivot_coords.
    """
    # The `initial_red_polygon` is the one we just carefully constructed.
    # We apply an *additional* rotation to it.
    rotated_red_polygon = shapely_rotate(initial_red_polygon,
                                         rotation_angle_to_apply_deg,
                                         origin=pivot_coords) # Rotate around the fixed pivot

    # Calculate the area of the (rotated) red ellipse that is outside the green ellipse.
    area_of_red_outside_green = rotated_red_polygon.difference(green_polygon).area
    return area_of_red_outside_green

# --- 4. Optimization ---
# Search for the rotation angle (from -180 to 180 degrees) that minimizes the objective function.
optimization_result = minimize_scalar(objective_function, bounds=(-180, 180), method='bounded')

optimal_additional_rotation_deg = optimization_result.x
min_area_outside = optimization_result.fun

print(f"Chosen pivot on green ellipse: ({pivot_coords[0]:.2f}, {pivot_coords[1]:.2f})")
print(f"Optimal additional rotation for red ellipse: {optimal_additional_rotation_deg:.2f} degrees")
print(f"Resulting minimum area of red ellipse outside green: {min_area_outside:.4e}")

# --- 5. Get Final Rotated Ellipse and Visualization ---
final_rotated_red_polygon = shapely_rotate(initial_red_polygon,
                                           optimal_additional_rotation_deg,
                                           origin=pivot_coords)

# Plotting
fig, ax = plt.subplots(figsize=(11, 11))

# Green ellipse (target)
x_g, y_g = green_polygon.exterior.xy
ax.plot(x_g, y_g, color='green', linewidth=2.5, label='Green Ellipse (Target)', zorder=1)
ax.fill(x_g, y_g, color='green', alpha=0.2, zorder=1)

# Initial Red ellipse
x_r_init, y_r_init = initial_red_polygon.exterior.xy
ax.plot(x_r_init, y_r_init, color='salmon', linestyle='--', linewidth=2, label='Initial Red Ellipse', zorder=2)
ax.fill(x_r_init, y_r_init, color='salmon', alpha=0.25, zorder=2)

# Final Rotated Red ellipse
x_r_final, y_r_final = final_rotated_red_polygon.exterior.xy
ax.plot(x_r_final, y_r_final, color='purple', linewidth=2.5, label=f'Final Rotated Red Ellipse\n(Rotated by {optimal_additional_rotation_deg:.2f}°)', zorder=3)
ax.fill(x_r_final, y_r_final, color='purple', alpha=0.35, zorder=3)

# Pivot point (Pink Dot) - should be on green ellipse boundary and on initial red & final red.
ax.plot(pivot_coords[0], pivot_coords[1], 'mo', markersize=10, label='Pivot Point (Pink Dot)', zorder=4, markeredgecolor='k')

ax.set_aspect('equal', adjustable='box')
ax.legend(loc='best')
ax.set_title('Rotating Smaller Ellipse (Red) around a Pivot on Larger Ellipse (Green)')
ax.set_xlabel('X-coordinate')
ax.set_ylabel('Y-coordinate')
ax.grid(True, linestyle=':', alpha=0.7)

# Set plot limits to focus on the ellipses
all_x_coords = list(x_g) + list(x_r_init) + list(x_r_final) + [pivot_coords[0]]
all_y_coords = list(y_g) + list(y_r_init) + list(y_r_final) + [pivot_coords[1]]
ax.set_xlim(min(all_x_coords) - 2, max(all_x_coords) + 2)
ax.set_ylim(min(all_y_coords) - 2, max(all_y_coords) + 2)

plt.show()

# Verification print statements
pivot_point_shapely = Point(pivot_coords)
dist_pivot_to_green_boundary = green_polygon.exterior.distance(pivot_point_shapely)
dist_pivot_to_initial_red_boundary = initial_red_polygon.exterior.distance(pivot_point_shapely)
dist_pivot_to_final_red_boundary = final_rotated_red_polygon.exterior.distance(pivot_point_shapely)

print(f"\nVerification of Pivot Point Location (distances should be near zero):")
print(f"  Distance from pivot to Green ellipse boundary: {dist_pivot_to_green_boundary:.2e}")
print(f"  Distance from pivot to Initial Red ellipse boundary: {dist_pivot_to_initial_red_boundary:.2e}")
print(f"  Distance from pivot to Final Rotated Red ellipse boundary: {dist_pivot_to_final_red_boundary:.2e}")

print(f"\nEllipse Areas:")
print(f"  Green ellipse area: {green_polygon.area:.2f}")
print(f"  Red ellipse area: {initial_red_polygon.area:.2f} (invariant)")
print(f"  Overlap area (Green & Final Red): {green_polygon.intersection(final_rotated_red_polygon).area:.2f}")
print(f"  Area of Final Red outside Green (should match minimized value): {final_rotated_red_polygon.difference(green_polygon).area:.2f}")