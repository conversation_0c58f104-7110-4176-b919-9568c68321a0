import numpy as np
import matplotlib.pyplot as plt
# from scipy.optimize import fsolve # Option 1
from scipy.optimize import least_squares # Option 2 - often more robust

# Helper for 2D cross product (scalar result)
def cross_2d(v1, v2):
    return v1[0]*v2[1] - v1[1]*v2[0]

# Arc definition and properties
class Arc2D:
    def __init__(self, center, radius, start_angle, end_angle, ccw=True):
        self.center = np.array(center)
        self.radius = radius
        self.start_angle = start_angle # In radians
        self.end_angle = end_angle   # In radians
        self.ccw = ccw

    def get_point(self, angle):
        return self.center + self.radius * np.array([np.cos(angle), np.sin(angle)])

    @property
    def start_point(self):
        return self.get_point(self.start_angle)

    @property
    def end_point(self):
        return self.get_point(self.end_angle)

    def get_tangent(self, angle): # Unit tangent
        if self.ccw:
            return np.array([-np.sin(angle), np.cos(angle)])
        else:
            return np.array([np.sin(angle), -np.cos(angle)])
            
    def get_normal(self, angle): # Unit normal, pointing towards center
        return np.array([-np.cos(angle), -np.sin(angle)])

    @property
    def curvature(self): # Unsigned curvature
        return 1.0 / self.radius if self.radius > 1e-9 else np.inf

    def get_params_at_start(self):
        p = self.start_point
        t = self.get_tangent(self.start_angle)
        n = self.get_normal(self.start_angle)
        k_unsigned = self.curvature
        k_signed = k_unsigned * cross_2d(t, n)
        return p, t, n, k_unsigned, k_signed

    def get_params_at_end(self):
        p = self.end_point
        t = self.get_tangent(self.end_angle)
        n = self.get_normal(self.end_angle)
        k_unsigned = self.curvature
        k_signed = k_unsigned * cross_2d(t, n)
        return p, t, n, k_unsigned, k_signed

    def get_plot_points(self, num_points=50):
        sa, ea = self.start_angle, self.end_angle
        if self.ccw:
            if ea < sa: ea += 2 * np.pi 
        else: 
            if ea > sa: ea -= 2 * np.pi 
        angles = np.linspace(sa, ea, num_points)
        points = np.array([self.get_point(angle) for angle in angles])
        return points

# Bezier curve functions
def bezier_point_calc(t, P0, P1, P2, P3):
    return ( (1-t)**3 * P0 +
             3*(1-t)**2 * t * P1 +
             3*(1-t) * t**2 * P2 +
             t**3 * P3 )

def bezier_plot_points(P0, P1, P2, P3, num_points=50):
    t_values = np.linspace(0, 1, num_points)
    points = np.array([bezier_point_calc(t, P0, P1, P2, P3) for t in t_values])
    return points

# --- Define Arcs and Point X ---
arc1 = Arc2D(center=[0,0], radius=2.0, start_angle=np.deg2rad(0), end_angle=np.deg2rad(90), ccw=True)
arc2 = Arc2D(center=[6,3], radius=1.0, start_angle=np.deg2rad(180), end_angle=np.deg2rad(270), ccw=True)
# arc2 = Arc2D(center=[6,1], radius=1.0, start_angle=np.deg2rad(90), end_angle=np.deg2rad(0), ccw=False) # Test CW arc

X = np.array([3.0, 2.5]) # The intermediate point

# Get G2 parameters from arcs
A, T_A, N_A, kappa_A_unsigned, kappa_A_signed = arc1.get_params_at_end()
B, T_B, N_B, kappa_B_unsigned, kappa_B_signed = arc2.get_params_at_start()

# System of equations to solve for L_A and h
def residuals_func(vars_LA_h, A_pt, T_A_vec, N_A_vec, kappa_A_unsign, B_pt, T_B_vec, N_B_vec, kappa_B_sign, X_pt):
    L_A, h = vars_LA_h

    # Using least_squares with bounds is better, but keep a basic check for extreme invalid values
    if L_A <= 1e-6 or h <= 1e-6: 
        return [1e6, 1e6] 

    # Bezier B1 (A to X)
    P0_B1 = A_pt
    P1_B1 = A_pt + L_A * T_A_vec
    P2_B1 = P1_B1 + (1.5 * kappa_A_unsign * L_A**2) * N_A_vec 
    P3_B1 = X_pt

    # Bezier B2 (X to B) - control points derived from B1 and h for G2 at X
    P0_B2 = X_pt
    
    X_minus_P2B1 = X_pt - P2_B1
    norm_X_minus_P2B1 = np.linalg.norm(X_minus_P2B1)
    if norm_X_minus_P2B1 < 1e-9: # P2_B1 is at X, can lead to issues
        return [1e6, 1e6] 

    P1_B2 = X_pt + (1.0/h) * X_minus_P2B1 # G1 continuity at X
    
    # G2 continuity at X using the standard formula:
    # P2_B2 = 2*P1_B2 - X_pt + (1/h^2) * (X_pt - 2*P2_B1 + P1_B1)
    # (where h in my code corresponds to s_Tai = ||X-P2_B1||/||P1_B2-X||)
    # My h is such that X-P2_B1 = h * (P1_B2-X), so ||X-P2_B1|| = h * ||P1_B2-X||
    # s_Tai (ratio of arm lengths ||Q1-X|| / ||X-P2||) = 1/h (in my notation)
    # So, factor is s_Tai^2 = (1/h)^2
    term_for_P2_B2 = X_pt - 2*P2_B1 + P1_B1
    P2_B2 = 2*P1_B2 - X_pt + (1.0/(h**2)) * term_for_P2_B2
    P3_B2 = B_pt

    # Check G2 conditions for B2 at point B
    V_end_arm_B2 = P3_B2 - P2_B2 
    L_B_val = np.linalg.norm(V_end_arm_B2)

    if L_B_val < 1e-9: 
        return [1e6, 1e6] # Degenerate: P2_B2 is at B

    T_B_bezier_unit = V_end_arm_B2 / L_B_val

    res1 = cross_2d(T_B_bezier_unit, T_B_vec) # Tangent alignment (want 0)
    
    kappa_B2_end_signed = (2.0/3.0) * cross_2d(P2_B2-P1_B2, V_end_arm_B2) / (L_B_val**3)
    res2 = kappa_B2_end_signed - kappa_B_sign # Curvature match (want 0)
    
    # Check for NaN/Inf in residuals (can happen if L_B_val is tiny and L_B_val**3 underflows or is near zero)
    if not (np.isfinite(res1) and np.isfinite(res2)):
        return [1e6, 1e6]
        
    return [res1, res2]

# Initial guess for L_A and h
dist_AX = np.linalg.norm(X - A)
dist_XB = np.linalg.norm(B - X)

# Heuristics for initial guess: L_A and L_B (implicit length for Bezier2)
# are often around 1/3 of the chord length for good shape.
# L_A: length of A to P1_B1
# h: ratio ||X-P2_B1|| / ||P1_B2-X||. If P2_B1 is roughly (A+X)/2 and P1_B2 is (X+B)/2,
# then X-P2_B1 ~ (X-A)/2 and P1_B2-X ~ (B-X)/2. So h ~ ||X-A||/||B-X|| = dist_AX/dist_XB
initial_L_A = max(0.1, dist_AX * 0.33) 
initial_h = max(0.1, dist_AX / dist_XB if dist_XB > 1e-3 else 1.0)

initial_guess = [initial_L_A, initial_h]
# Bounds for L_A and h (must be positive, and not excessively large to avoid numerics)
# Looser bounds first
# bounds_param = ([1e-3, 1e-3], [100.0, 100.0])
# Tighter bounds based on distances might be more stable
bounds_param = ([dist_AX * 0.05, 0.05 * initial_h], # min L_A, min h
                [dist_AX * 3.0,  20.0 * initial_h]) # max L_A, max h
bounds_param = ([1e-2, 1e-2], [max(10.0, dist_AX * 5), max(10.0, initial_h * 5)]) # A general set of bounds


print(f"Initial guess: L_A={initial_guess[0]:.4f}, h={initial_guess[1]:.4f}")
initial_residuals = residuals_func(initial_guess, A, T_A, N_A, kappa_A_unsigned, B, T_B, N_B, kappa_B_signed, X)
print(f"Residuals at initial guess: {initial_residuals}")


# Solve using least_squares
result = least_squares(
    residuals_func,
    initial_guess,
    args=(A, T_A, N_A, kappa_A_unsigned, B, T_B, N_B, kappa_B_signed, X),
    bounds=bounds_param,
    method='trf', # Trust Region Reflective, good for bounds
    ftol=1e-9,    # Tolerance for sum of squares
    xtol=1e-9,    # Tolerance for change in x
    gtol=1e-9,    # Tolerance for norm of gradient
    verbose=0     # Change to 1 or 2 for more solver output
)

if result.success:
    L_A_sol, h_sol = result.x
    print(f"Solution found: L_A = {L_A_sol:.4f}, h = {h_sol:.4f}")
    
    P0_B1_sol = A
    P1_B1_sol = A + L_A_sol * T_A
    P2_B1_sol = P1_B1_sol + (1.5 * kappa_A_unsigned * L_A_sol**2) * N_A
    P3_B1_sol = X

    P0_B2_sol = X
    X_minus_P2B1_sol = X - P2_B1_sol
    P1_B2_sol = X + (1.0/h_sol) * X_minus_P2B1_sol
    term_for_P2_B2_sol = X - 2*P2_B1_sol + P1_B1_sol
    P2_B2_sol = 2*P1_B2_sol - X + (1.0/(h_sol**2)) * term_for_P2_B2_sol
    P3_B2_sol = B

    fig, ax = plt.subplots(figsize=(12,9))
    arc1_pts = arc1.get_plot_points()
    arc2_pts = arc2.get_plot_points()
    ax.plot(arc1_pts[:,0], arc1_pts[:,1], 'b-', linewidth=2, label='Arc 1')
    ax.plot(arc2_pts[:,0], arc2_pts[:,1], 'g-', linewidth=2, label='Arc 2')
    ax.plot(X[0], X[1], 'ko', markersize=8, label='Point X')

    bez1_pts = bezier_plot_points(P0_B1_sol, P1_B1_sol, P2_B1_sol, P3_B1_sol)
    bez2_pts = bezier_plot_points(P0_B2_sol, P1_B2_sol, P2_B2_sol, P3_B2_sol)
    ax.plot(bez1_pts[:,0], bez1_pts[:,1], 'r--', linewidth=1.5, label='Bezier 1 (A to X)')
    ax.plot(bez2_pts[:,0], bez2_pts[:,1], 'm--', linewidth=1.5, label='Bezier 2 (X to B)')

    cp_b1 = np.array([P0_B1_sol, P1_B1_sol, P2_B1_sol, P3_B1_sol])
    cp_b2 = np.array([P0_B2_sol, P1_B2_sol, P2_B2_sol, P3_B2_sol])
    ax.plot(cp_b1[:,0], cp_b1[:,1], 'ro:', markersize=5, alpha=0.7, label='CPs B1')
    ax.plot(cp_b2[:,0], cp_b2[:,1], 'mo:', markersize=5, alpha=0.7, label='CPs B2')
    
    arrow_scale = 0.5
    ax.arrow(A[0], A[1], T_A[0]*arrow_scale, T_A[1]*arrow_scale, head_width=0.1, fc='cyan', ec='black', length_includes_head=True)
    ax.arrow(B[0], B[1], T_B[0]*arrow_scale, T_B[1]*arrow_scale, head_width=0.1, fc='cyan', ec='black', length_includes_head=True)
    
    T_B1_X_vec = (P3_B1_sol - P2_B1_sol); T_B1_X_unit = T_B1_X_vec / np.linalg.norm(T_B1_X_vec)
    ax.arrow(X[0], X[1], T_B1_X_unit[0]*arrow_scale, T_B1_X_unit[1]*arrow_scale, head_width=0.1, fc='yellow', ec='black', ls=':', length_includes_head=True, label='T_B1 at X')
    
    T_B2_X_vec = (P1_B2_sol - P0_B2_sol); T_B2_X_unit = T_B2_X_vec / np.linalg.norm(T_B2_X_vec)
    ax.arrow(X[0], X[1], T_B2_X_unit[0]*arrow_scale, T_B2_X_unit[1]*arrow_scale, head_width=0.1, fc='orange', ec='black', ls='--', length_includes_head=True, label='T_B2 at X')

    ax.set_aspect('equal', adjustable='box')
    ax.legend(fontsize='small')
    ax.set_title('G2 Continuous Curve: Arc1 -> Bezier1 -> X -> Bezier2 -> Arc2', fontsize='medium')
    plt.grid(True)
    plt.show()

    final_res = residuals_func((L_A_sol, h_sol), A, T_A, N_A, kappa_A_unsigned, B, T_B, N_B, kappa_B_signed, X)
    print(f"Final residuals at solution: res1 (tangent)={final_res[0]:.2e}, res2 (curvature)={final_res[1]:.2e}")
    print(f"Solver message: {result.message}")
else:
    print(f"Solver failed to find a solution. Status: {result.status}")
    print(f"Solver message: {result.message}")
    print(f"Final cost (sum of squares of residuals): {result.cost:.2e}")
    print(f"Variables at termination: L_A={result.x[0]:.4f}, h={result.x[1]:.4f}")
    final_residuals_fail = residuals_func(result.x, A, T_A, N_A, kappa_A_unsigned, B, T_B, N_B, kappa_B_signed, X)
    print(f"Residuals at termination: {final_residuals_fail}")