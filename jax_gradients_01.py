import jax
import jax.numpy as jnp
import matplotlib.pyplot as plt  # For visualization

# Hyperparams (tune to your real setup)
ELLIPSE_MAJOR = 2.0  # Semi-major axis (fixed for 1D)
ELLIPSE_MINOR = 1.0  # Semi-minor axis
NUM_SAMPLES = 100  # Must be >0
POS_BOUNDS = [0.9, 1.0]
LEARNING_RATE = 0.001  # Smaller to avoid overshoot
MAX_ITERS = 500
TOL = 1e-6

# Mock polylines: Wider gap for no protrusion at optimal
LEFT_POLYLINE_X = -1.0
RIGHT_POLYLINE_X = 4.0

def distance_to_polyline(points, polyline_x):
    return polyline_x - points[:, 0]  # Signed raw

def distance_to_left_polyline(points):
    return points[:, 0] - LEFT_POLYLINE_X  # Positive if inside (right of left line)

def distance_to_right_polyline(points):
    return RIGHT_POLYLINE_X - points[:, 0]  # Positive if inside (left of right line)

# Generate ellipse points
def generate_ellipse_points(pos, num_samples=NUM_SAMPLES):
    theta = jnp.linspace(0, 2 * jnp.pi, num_samples)
    x = ELLIPSE_MAJOR * jnp.cos(theta)
    y = ELLIPSE_MINOR * jnp.sin(theta)
    
    # Translation tuned for optimal at pos=0.95
    center_x = 10 * pos - 7.5  # At 0.9:1.5, at 0.95:2.0? Wait, calc: 10*0.9-7.5=9-7.5=1.5, 10*0.95-7.5=9.5-7.5=2.0, 10*1.0-7.5=10-7.5=2.5
    # Midpoint 1.5 at pos=0.9 (adjust if your real optimal is higher)
    center_y = 0.0
    
    points = jnp.stack([x + center_x, y + center_y], axis=-1)
    return points

# Approx farthest outside
def approx_farthest_outside(points, polyline_dist_func):
    distances = polyline_dist_func(points)  # (num_samples,)
    
    # Mask ~1 if outside (dist <0)
    outside_mask = jax.nn.sigmoid(-2 * distances)
    
    masked_dists = jnp.where(distances < 0, -distances, 0) * outside_mask  # Only negative dists, absolute
    
    if masked_dists.size == 0 or jnp.all(masked_dists == 0):
        return 0.0
    
    softened = jax.nn.softmax(5 * masked_dists)  # Softer for smaller grads
    soft_max = jnp.sum(masked_dists * softened, axis=-1)
    
    return soft_max

# Objective
def objective(pos):
    points = generate_ellipse_points(pos)
    
    signed_left = distance_to_left_polyline(points)
    signed_right = distance_to_right_polyline(points)
    
    dist_left = jnp.min(signed_left)  # Min signed
    dist_right = jnp.min(signed_right)  # Min signed
    
    centering_loss = jnp.abs(dist_left - dist_right)
    
    penalty_left = approx_farthest_outside(points, distance_to_left_polyline)
    penalty_right = approx_farthest_outside(points, distance_to_right_polyline)
    penalty = penalty_left + penalty_right
    
    res = centering_loss + 2.0 * penalty  # Tuned weight
    
    print(f"Debug: pos={pos}, dist_left={dist_left}, dist_right={dist_right}, penalty={penalty}, res={res}")
    
    return res

# JAX gradient
grad_objective = jax.grad(objective)

# Gradient descent with improved convergence check
def optimize(initial_pos, lr=LEARNING_RATE, max_iters=MAX_ITERS, tol=TOL):
    pos = jnp.array(initial_pos)
    history = [float(pos)]
    
    for i in range(max_iters):
        grad = grad_objective(pos)
        print(f"Iter {i}: grad={grad:.4f}")
        new_pos = pos - lr * grad
        new_pos = jnp.clip(new_pos, POS_BOUNDS[0], POS_BOUNDS[1])
        
        delta = jnp.abs(new_pos - pos)
        if delta < tol and jnp.abs(grad) < tol:  # Added grad check to avoid false stop at bound
            print(f"Converged after {i} iterations (delta={delta:.6f}, grad={grad:.6f})")
            break
        
        pos = new_pos
        history.append(float(pos))
    
    return pos, objective(pos), history

# Run it! Start at 1.0 to see movement to optimal ~0.95
initial_pos = 1.0
optimal_pos, optimal_res, history = optimize(initial_pos)

print(f"Optimal position: {optimal_pos:.4f}")
print(f"Minimum res: {optimal_res:.4f} (should be ~0 if centered)")

# Visualize convergence
plt.plot(history, label='Position over iterations')
plt.xlabel('Iteration')
plt.ylabel('Position')
plt.title('Convergence to Optimal Position')
plt.legend()
plt.show()

# Visualize ellipse
optimal_points = generate_ellipse_points(optimal_pos)
plt.scatter(optimal_points[:, 0], optimal_points[:, 1], label='Ellipse Points')
plt.axvline(x=LEFT_POLYLINE_X, color='r', label='Left Polyline')
plt.axvline(x=RIGHT_POLYLINE_X, color='g', label='Right Polyline')
center_x = jnp.mean(optimal_points[:, 0])
plt.axvline(x=center_x, color='b', linestyle='--', label='Ellipse Center')
plt.title('Ellipse at Optimal Position')
plt.legend()
plt.show()