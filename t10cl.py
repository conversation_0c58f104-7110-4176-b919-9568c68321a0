import numpy as np
from scipy.spatial import cKDTree
import time

def detect_line_intersections(lines):
    """
    Efficiently detect intersections between 2D lines using spatial partitioning.
    
    Parameters:
    lines: np.ndarray of shape (N, 2, 2) where each line is [[x1, y1], [x2, y2]]
    
    Returns:
    np.ndarray: Boolean array of length N indicating which lines intersect with any other
    """
    N = len(lines)
    
    # Calculate bounding boxes for each line
    mins = np.minimum(lines[:, 0], lines[:, 1])  # Shape: (N, 2)
    maxs = np.maximum(lines[:, 0], lines[:, 1])  # Shape: (N, 2)
    
    # Create a KD-tree using the centers of bounding boxes
    centers = (mins + maxs) / 2
    tree = cKDTree(centers)
    
    # Find potential neighbors based on maximum line length
    max_lengths = np.linalg.norm(lines[:, 1] - lines[:, 0], axis=1)
    max_length = max_lengths.max()
    
    # Query the tree for potential neighbors
    potential_pairs = tree.query_pairs(max_length, output_type='ndarray')
    
    if len(potential_pairs) == 0:
        return np.zeros(N, dtype=bool)
    
    # Extract lines for potential intersections
    lines1 = lines[potential_pairs[:, 0]]
    lines2 = lines[potential_pairs[:, 1]]
    
    # Vectorized line intersection test
    def line_intersect_vectorized(lines1, lines2):
        # Extract points
        p1 = lines1[:, 0]
        p2 = lines1[:, 1]
        p3 = lines2[:, 0]
        p4 = lines2[:, 1]
        
        # Calculate denominator
        denom = (p4[:, 1] - p3[:, 1]) * (p2[:, 0] - p1[:, 0]) - \
                (p4[:, 0] - p3[:, 0]) * (p2[:, 1] - p1[:, 1])
        
        # Calculate ua and ub
        ua = ((p4[:, 0] - p3[:, 0]) * (p1[:, 1] - p3[:, 1]) - \
              (p4[:, 1] - p3[:, 1]) * (p1[:, 0] - p3[:, 0])) / (denom + 1e-8)
        
        ub = ((p2[:, 0] - p1[:, 0]) * (p1[:, 1] - p3[:, 1]) - \
              (p2[:, 1] - p1[:, 1]) * (p1[:, 0] - p3[:, 0])) / (denom + 1e-8)
        
        # Check if lines intersect
        intersect = (ua >= 0) & (ua <= 1) & (ub >= 0) & (ub <= 1)
        
        return intersect
    
    # Find actual intersections
    intersections = line_intersect_vectorized(lines1, lines2)
    
    # Create result array
    result = np.zeros(N, dtype=bool)
    np.logical_or.at(result, potential_pairs[intersections, 0], True)
    np.logical_or.at(result, potential_pairs[intersections, 1], True)
    
    return result

def detect_line_intersections2(lines): 
    """ Given an array 'lines' of shape (N, 2, 2) where each line is represented by two endpoints, returns an (M, 2) array of index pairs (i, j) for which line i and line j intersect. The computation is fully vectorized: bounding boxes are computed and used to prefilter candidate pairs, and then line intersection parameters are computed for the remaining candidate pairs.

    The intersection test for two segments with endpoints:
        p1 = (x1, y1), p2 = (x2, y2) for the first segment, and
        p3 = (x3, y3), p4 = (x4, y4) for the second
    uses the parameterization:

        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        t = ((x1-x3)*(y1-y2) - (y1-y3)*(x1-x2)) / denom
        u = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        
    and the segments intersect if 0<=t<=1 and 0<=u<=1, provided that |denom| > epsilon.

    This function is optimized for large numbers of line segments.
    """
    pts1 = lines[:, 0]
    pts2 = lines[:, 1]
    x1, y1 = pts1[:, 0], pts1[:, 1]
    x2, y2 = pts2[:, 0], pts2[:, 1]

    # Compute bounding boxes for each line segment.
    min_x = np.minimum(x1, x2)
    max_x = np.maximum(x1, x2)
    min_y = np.minimum(y1, y2)
    max_y = np.maximum(y1, y2)

    N = len(lines)
    # Get all unique candidate pairs (i,j) with i < j
    i_idx, j_idx = np.triu_indices(N, k=1)

    # Prefilter candidate pairs using bounding box overlap
    valid_bb = ((min_x[i_idx] <= max_x[j_idx]) & (max_x[i_idx] >= min_x[j_idx]) &
                (min_y[i_idx] <= max_y[j_idx]) & (max_y[i_idx] >= min_y[j_idx]))
    cand_i = i_idx[valid_bb]
    cand_j = j_idx[valid_bb]

    # Get candidate coordinates
    x1_c = x1[cand_i]
    y1_c = y1[cand_i]
    x2_c = x2[cand_i]
    y2_c = y2[cand_i]

    x3_c = x1[cand_j]
    y3_c = y1[cand_j]
    x4_c = x2[cand_j]
    y4_c = y2[cand_j]

    # Compute denominator for the intersection formulas.
    denom = (x1_c - x2_c) * (y3_c - y4_c) - (y1_c - y2_c) * (x3_c - x4_c)

    # Compute t and u numerators
    t_num = (x1_c - x3_c) * (y1_c - y2_c) - (y1_c - y3_c) * (x1_c - x2_c)
    u_num = (x1_c - x3_c) * (y3_c - y4_c) - (y1_c - y3_c) * (x3_c - x4_c)

    # Avoid division by very small numbers
    eps = 1e-10
    valid_denom = np.abs(denom) > eps
    # Initialize t and u to -1 (indicating no valid intersection)
    t = np.full_like(denom, -1, dtype=np.float64)
    u = np.full_like(denom, -1, dtype=np.float64)
    t[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    # Check for intersection parameters within [0, 1]
    intersect = (t >= 0) & (t <= 1) & (u >= 0) & (u <= 1)

    # Return the intersecting pairs (indices) in an (M, 2) array.
    intersect_pairs = np.vstack((cand_i[intersect], cand_j[intersect])).T
    return intersect_pairs


def create_sample_data(n=12000):
    """Create sample line data for testing"""
    rng = np.random.default_rng(42)
    lines = rng.uniform(-100, 100, (n, 2, 2))
    return lines

if __name__ == "__main__":
    lines = create_sample_data()
    lines = np.array([
        [[0, 0], [0, 1]],        
        [[0.5, 0.5], [-0.5, -0.5]]
        ])    
    start_time = time.time()
    # result = line_intersection(lines) #grok
    result = detect_line_intersections2(lines)  #o3
    end_time = time.time()
    print(f"Execution time: {end_time - start_time:.4f} seconds")
    print(result)