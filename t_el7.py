import numpy as np
import jax
import jax.numpy as jnp
# --- MODIFICATION HERE ---
# Import minimize with a unique alias to prevent name collisions.
from jax.scipy.optimize import minimize as jax_minimize
from scipy.optimize import Bounds
import time


# JAX Configuration for 64-bit precision
jax.config.update("jax_enable_x64", True)

# --- 1. User Input Data ---
points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

bounds_tuple = ([-18170, -9679, 30, 20, np.deg2rad(320)],
                [-18130, -9625, 60, 40, np.deg2rad(340)])

initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)


# --- 2. The Core JAX Fitting Function ---

def fit_ellipse_jax(points: jnp.ndarray,
                    initial_guess: jnp.ndarray,
                    bounds: Bounds) -> any:
    """
    Fits an ellipse to a set of 2D points using JAX and L-BFGS-B.
    """
    def loss_fn(params, points):
        h, k, a, b, theta = params
        x, y = points[:, 0], points[:, 1]

        a = jnp.abs(a)
        b = jnp.abs(b)

        cos_theta = jnp.cos(theta)
        sin_theta = jnp.sin(theta)
        
        x_translated = x - h
        y_translated = y - k
        
        x_rotated = x_translated * cos_theta + y_translated * sin_theta
        y_rotated = -x_translated * sin_theta + y_translated * cos_theta

        algebraic_dist = (x_rotated / a)**2 + (y_rotated / b)**2 - 1
        return jnp.sum(algebraic_dist**2)

    value_and_grad_fn = jax.jit(jax.value_and_grad(loss_fn))

    # --- MODIFICATION HERE ---
    # Call the aliased function 'jax_minimize' to be certain we use the right one.
    result = jax_minimize(
        fun=value_and_grad_fn,
        x0=initial_guess,
        args=(points,),
        method='L-BFGS-B',
        bounds=bounds,
        jac=True,
        options={'maxiter': 200, 'ftol': 1e-10}
    )
    return result

# --- (The rest of the script is identical to the first one) ---


# --- 4. Main Execution Block ---
if __name__ == '__main__':
    jnp_points = jnp.array(points)
    jnp_initial_guess = jnp.array(initial_guess)
    scipy_bounds = Bounds(bounds_tuple[0], bounds_tuple[1])

    print("--- Ellipse Fitting with JAX (Robust Import) ---")
    fit_result = fit_ellipse_jax(jnp_points, jnp_initial_guess, scipy_bounds)
    final_params = fit_result.x
    
    print("\n--- Results ---")
    print(f"Success: {fit_result.success}")
    print(f"Message: {fit_result.message}")
    print(f"Number of iterations: {fit_result.nit}")
    print(f"Final Loss (sum of squares): {fit_result.fun:.6e}")
    print("\nFitted Parameters:")
    print(f"  Center (h, k): ({final_params[0]:.4f}, {final_params[1]:.4f})")
    print(f"  Semi-axes (a, b): ({final_params[2]:.4f}, {final_params[3]:.4f})")
    print(f"  Angle (deg): {np.rad2deg(final_params[4]):.4f}")
