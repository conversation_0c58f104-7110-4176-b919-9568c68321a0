# pixel_engine_history.py
import numpy as np
from PIL import Image
from shapely.geometry import Polygon
from shapely.affinity import translate
from skimage.draw import polygon as sk_polygon
import math

# ───────────────────────── helpers ────────────────────────────────
def poly_to_mask(poly, H, W):
    xs, ys = poly.exterior.coords.xy
    rr, cc = sk_polygon(ys, xs, (H, W))
    m = np.zeros((H, W), dtype=bool)
    m[rr, cc] = True
    return m

# ───────────────────────── simulator ──────────────────────────────
class PixelSim:
    def __init__(self, pocket_mask, r):
        # for motion direction
        self._prev_cx = self._prev_cy = None    # initialise        
        self.material = pocket_mask.copy()       # True = stock present
        self.r = r
        Y, X = np.mgrid[-r:r+1, -r:r+1]      # y, x  →  shape (k, k)
        r_sq = r**2
        r_inner_sq = (r-1)**2
        self.tool = (X**2 + Y**2 <= r_sq)
        self.edge_mask = (X**2 + Y**2 <= r_sq) & (X**2 + Y**2 >= r_inner_sq)
        self._Y = Y
        self._X = X
        self._edge_coords = np.stack((Y, X), axis=0)   # -> shape (2, k, k)
        self.edge_tot = int(np.sum(self.edge_mask))
        self.kH, self.kW = self.tool.shape
        self.H, self.W = pocket_mask.shape

    def _slice(self, cx, cy):
        x0 = max(cx - self.r, 0)
        y0 = max(cy - self.r, 0)
        x1 = min(x0 + self.kW, self.W)
        y1 = min(y0 + self.kH, self.H)
        kx0 = max(self.r - cx, 0)
        ky0 = max(self.r - cy, 0)
        kx1 = self.kW - max(cx + self.r + 1 - self.W, 0)
        ky1 = self.kH - max(cy + self.r + 1 - self.H, 0)
        return (slice(y0,y1), slice(x0,x1)), (slice(ky0,ky1), slice(kx0,kx1))

    def engage(self, cx, cy):
        mat_slc, ker_slc = self._slice(cx, cy)
        mview = self.material[mat_slc]
        kview = self.tool[ker_slc]

        removed_local = mview & kview
        mview[removed_local] = False            # commit cut

        # ---------- motion vector ----------
        if self._prev_cx is None:               # first call (plunge)
            ux, uy = 0.0, 1.0                   # arbitrary
        else:
            dx, dy = cx - self._prev_cx, cy - self._prev_cy
            L = math.hypot(dx, dy) or 1.0
            ux, uy = dx/L, dy/L
        self._prev_cx, self._prev_cy = cx, cy

        # ---------- forward half‑edge ----------
        Yloc = self._Y[ker_slc]                 # correct 2‑D slice
        Xloc = self._X[ker_slc]
        front = (Xloc*ux + Yloc*uy) > 0       # cosine > 0
        edge_loc = self.edge_mask[ker_slc] & front

        engaged = np.count_nonzero(removed_local & edge_loc)
        total = np.count_nonzero(edge_loc)
        alpha_e = 180.0 * engaged/total if total else 0.0

        return removed_local, mat_slc, alpha_e

# ──────────────────────── bitmap renderer ─────────────────────────
class BitmapRenderer:
    """Maintains the RGB frame and keeps track of 'previous red'."""
    STOCK = (0.4, 0.4, 0.4)
    AIR = (1.0, 1.0, 1.0)
    RED = (1.0, 0.0, 0.0)

    def __init__(self, material_mask):
        h, w = material_mask.shape
        self.img = np.zeros((h, w, 3), dtype=np.float32)
        mask = material_mask.copy()
        self.img[mask] = self.STOCK
        self.img[~mask] = self.AIR
        self.prev_mask = None      # last step's red mask (view)
        self.prev_slc = None

    def apply(self, removed_local, slc):
        # 1) age‑out previous red => white (air)
        if self.prev_mask is not None:
            self.img[self.prev_slc][self.prev_mask] = self.AIR

        # 2) paint current removal red
        self.img[slc][removed_local] = self.RED

        # 3) save references for next step
        self.prev_mask = removed_local
        self.prev_slc = slc

    def save(self, fname):
        Image.fromarray((self.img*255).astype(np.uint8), "RGB").save(fname)

# ───────────────────────── demo sequence ──────────────────────────
if __name__ == "__main__":
    H = W = 480
    pocket_poly = translate(Polygon([(40,40),(440,40),(440,440),(40,440)]), 0, 0)
    pocket = poly_to_mask(pocket_poly, H, W)

    sim = PixelSim(pocket, r=32)
    view = BitmapRenderer(sim.material)

    steps = [(240,240), #plunge (step 0)
            #  (245,240),
            #  (250,240),
             (241,241)]

    for i, (cx, cy) in enumerate(steps):
        removed, slc, alpha = sim.engage(cx, cy)
        if removed.any():                      # plunge will be empty
            print(f"step {i}: αₑ ≈ {alpha:.1f}°")
        view.apply(removed, slc)
        # view.save(f"step_{i:02d}.bmp")

    view.save(f"milling.bmp")
    
