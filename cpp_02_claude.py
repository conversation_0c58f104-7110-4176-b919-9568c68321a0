import networkx as nx
from itertools import combinations, permutations

def chinese_postman_open(G, start_node):
    # Check if graph is connected
    if not nx.is_connected(G):
        raise nx.NetworkXError("Graph must be connected")

    # Check if start_node exists in graph
    if start_node not in G.nodes():
        raise nx.NetworkXError(f"Start node {start_node} not in graph")

    # Find nodes with odd degree
    odd_degree_nodes = [v for v, d in G.degree() if d % 2 == 1]

    # For an open path, we need exactly 0 or 2 odd-degree nodes
    if len(odd_degree_nodes) not in [0, 2]:
        # Add a virtual edge between start_node and all other nodes
        # This transforms the problem into finding an Eulerian path
        augmented = nx.MultiGraph(G)

        # Find nodes with odd degree (excluding start_node if it's odd)
        odd_nodes = [n for n in odd_degree_nodes if n != start_node]

        # If start_node has even degree, make it odd by adding a virtual node
        if start_node not in odd_degree_nodes:
            odd_nodes.append(start_node)

        # Create a complete graph of odd nodes with shortest paths as edges
        odd_graph = nx.Graph()
        for u, v in combinations(odd_nodes, 2):
            shortest_path = nx.shortest_path(G, u, v, weight='weight')
            path_weight = sum(G[shortest_path[i]][shortest_path[i+1]]['weight']
                            for i in range(len(shortest_path)-1))
            odd_graph.add_edge(u, v, weight=path_weight, path=shortest_path)

        # Find minimum weight perfect matching
        matching = list(nx.algorithms.matching.min_weight_matching(odd_graph))

        # Add duplicate edges from matching
        for u, v in matching:
            path = odd_graph[u][v]['path']
            for i in range(len(path) - 1):
                augmented.add_edge(path[i], path[i+1], weight=G[path[i]][path[i+1]]['weight'])

        # Find Eulerian path starting at start_node
        path = list(nx.eulerian_path(augmented, source=start_node))

        # Calculate total weight
        total_weight = sum(augmented[u][v][0]['weight'] for u, v in path)

        # Convert edge list to node list
        node_path = [start_node]
        for u, v in path:
            node_path.append(v)

        return node_path, total_weight

    else:
        # If we already have 0 or 2 odd nodes, we can find an Eulerian path directly
        # For 0 odd nodes, start at start_node
        # For 2 odd nodes, start at one of them (preferably start_node if it's odd)
        augmented = nx.MultiGraph(G)

        if len(odd_degree_nodes) == 2 and start_node not in odd_degree_nodes:
            # If start_node is not one of the odd nodes, add a virtual edge
            # from start_node to one of the odd nodes
            odd_node = odd_degree_nodes[0]
            shortest_path = nx.shortest_path(G, start_node, odd_node, weight='weight')
            for i in range(len(shortest_path) - 1):
                augmented.add_edge(shortest_path[i], shortest_path[i+1],
                                  weight=G[shortest_path[i]][shortest_path[i+1]]['weight'])

        # Find Eulerian path
        path = list(nx.eulerian_path(augmented, source=start_node))

        # Calculate total weight
        total_weight = sum(augmented[u][v][0]['weight'] for u, v in path)

        # Convert edge list to node list
        node_path = [start_node]
        for u, v in path:
            node_path.append(v)

        return node_path, total_weight
    

def main():
    # Create sample graph
    G = nx.Graph()
    G.add_edge('A', 'B', weight=10)
    G.add_edge('B', 'C', weight=1)
    G.add_edge('C', 'D', weight=1)
    G.add_edge('D', 'A', weight=20)
    G.add_edge('B', 'D', weight=1)

    # Solve
    path, weight = chinese_postman_open(G, start_node='D')
    print(f"Path: {' -> '.join(path)}")
    print(f"Total weight: {weight}")

if __name__ == "__main__":
    main()
