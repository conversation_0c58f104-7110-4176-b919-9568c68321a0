import numpy as np
from shapely.geometry import LineString, Point
from shapely.affinity import rotate, translate
from scipy.spatial.distance import cdist

def linestring_ellipse_distance(linestring, ellipse_center, ellipse_a, ellipse_b, ellipse_angle=0):
    """
    Compute the distance of linestring points relative to an ellipse boundary.
    
    Parameters:
    -----------
    linestring : shapely.geometry.LineString or array-like
        The linestring to check
    ellipse_center : tuple or array-like
        Center of ellipse (x, y)
    ellipse_a : float
        Semi-major axis length
    ellipse_b : float  
        Semi-minor axis length
    ellipse_angle : float, optional
        Rotation angle of ellipse in degrees (default: 0)
    
    Returns:
    --------
    float
        If any points are outside: maximum distance outside ellipse (positive)
        If all points are inside: negative of minimum distance to boundary (negative)
        Normalized by ellipse size for scale-invariance
    """
    # Convert linestring to coordinates
    if hasattr(linestring, 'coords'):
        coords = np.array(list(linestring.coords))
    else:
        coords = np.array(linestring)
    
    # Translate points to ellipse-centered coordinate system
    cx, cy = ellipse_center
    translated_coords = coords - np.array([cx, cy])
    
    # Rotate points to align with ellipse axes
    if ellipse_angle != 0:
        angle_rad = np.radians(-ellipse_angle)  # Negative for inverse rotation
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
        rotated_coords = translated_coords @ rotation_matrix.T
    else:
        rotated_coords = translated_coords
    
    # Compute ellipse equation values: (x/a)² + (y/b)² 
    x, y = rotated_coords[:, 0], rotated_coords[:, 1]
    ellipse_values = (x / ellipse_a) ** 2 + (y / ellipse_b) ** 2
    
    # Points outside ellipse have ellipse_value > 1
    outside_mask = ellipse_values > 1
    
    if np.any(outside_mask):
        # Some points are outside - return maximum distance outside
        outside_distances = np.sqrt(ellipse_values[outside_mask]) - 1
        return np.max(outside_distances)
    else:
        # All points inside - return negative of minimum distance to boundary
        # 1 - sqrt(ellipse_value) gives distance to boundary for inside points
        inside_distances = 1 - np.sqrt(ellipse_values)
        return -np.min(inside_distances)  # Negative to indicate "inside"


def vectorized_linestring_ellipse_distance(linestrings, ellipse_params):
    """
    Vectorized version for multiple linestrings and/or ellipses.
    
    Parameters:
    -----------
    linestrings : list of LineString/arrays
        List of linestrings to evaluate
    ellipse_params : array-like
        Parameters [cx, cy, a, b, angle] or array of such parameters
    
    Returns:
    --------
    numpy.ndarray
        Array of distances for each configuration
        Positive: max distance outside ellipse
        Negative: negative of min distance to boundary (all points inside)
    """
    ellipse_params = np.atleast_2d(ellipse_params)
    
    if len(ellipse_params) == 1:
        # Single ellipse, multiple linestrings
        cx, cy, a, b, angle = ellipse_params[0]
        results = []
        for ls in linestrings:
            dist = linestring_ellipse_distance(ls, (cx, cy), a, b, angle)
            results.append(dist)
        return np.array(results)
    
    else:
        # Multiple ellipses (assuming single linestring)
        linestring = linestrings[0] if isinstance(linestrings, list) else linestrings
        results = []
        for params in ellipse_params:
            cx, cy, a, b, angle = params
            dist = linestring_ellipse_distance(linestring, (cx, cy), a, b, angle)
            results.append(dist)
        return np.array(results)


def create_cmaes_objective(target_linestring, penalty_type='outside_only'):
    """
    Create an objective function for CMA-ES optimization.
    
    Parameters:
    -----------
    target_linestring : LineString or array
        The linestring to fit
    penalty_type : str
        'outside_only': Only penalize points outside (0 if all inside)
        'distance': Use signed distance (negative if inside, positive if outside)
        'abs_distance': Use absolute distance to boundary
    
    Returns:
    --------
    function
        Objective function that takes ellipse parameters and returns penalty
    """
    def objective(ellipse_params):
        """
        Objective function for CMA-ES.
        
        Parameters:
        -----------
        ellipse_params : array-like
            [center_x, center_y, semi_major, semi_minor, angle_degrees]
        
        Returns:
        --------
        float or array
            Penalty value based on penalty_type
        """
        if len(ellipse_params.shape) == 1:
            # Single parameter set
            cx, cy, a, b, angle = ellipse_params
            distance = linestring_ellipse_distance(target_linestring, (cx, cy), a, b, angle)
        else:
            # Multiple parameter sets (vectorized)
            distance = vectorized_linestring_ellipse_distance([target_linestring], ellipse_params)
        
        if penalty_type == 'outside_only':
            # Only penalize if points are outside (positive distance)
            return np.maximum(0, distance)
        elif penalty_type == 'distance':
            # Use raw signed distance
            return distance
        elif penalty_type == 'abs_distance':
            # Minimize absolute distance to boundary
            return np.abs(distance)
        else:
            raise ValueError(f"Unknown penalty_type: {penalty_type}")
    
    return objective


# Example usage and testing
if __name__ == "__main__":
    from shapely.geometry import LineString    
    
    # Create test linestring
    points = [(0, 0), (3, 1), (5, 3), (4, 5), (2, 4), (-1, 3)]
    linestring = LineString(points)
    
    # Test ellipse parameters [cx, cy, a, b, angle]
    ellipse_params = [1.5, 2.5, 3.0, 2.0, 30]  # center, semi-axes, rotation
    
    # Compute distance
    distance = linestring_ellipse_distance(
        linestring, 
        (ellipse_params[0], ellipse_params[1]), 
        ellipse_params[2], 
        ellipse_params[3], 
        ellipse_params[4]
    )
    
    print(f"Distance to ellipse boundary: {distance:.4f}")
    if distance > 0:
        print("  -> Some points are OUTSIDE the ellipse")
    else:
        print("  -> All points are INSIDE the ellipse")
    
    # Test with a smaller ellipse (points will be outside)
    small_ellipse = [1.5, 2.5, 1.5, 1.0, 30]
    distance_small = linestring_ellipse_distance(
        linestring,
        (small_ellipse[0], small_ellipse[1]),
        small_ellipse[2],
        small_ellipse[3], 
        small_ellipse[4]
    )
    print(f"Distance with smaller ellipse: {distance_small:.4f}")
    
    # Test with a large ellipse (all points inside)
    large_ellipse = [1.5, 2.5, 8.0, 6.0, 30]
    distance_large = linestring_ellipse_distance(
        linestring,
        (large_ellipse[0], large_ellipse[1]),
        large_ellipse[2],
        large_ellipse[3],
        large_ellipse[4]
    )
    print(f"Distance with large ellipse: {distance_large:.4f}")
    
    # Test vectorized version
    multiple_ellipses = [
        [1.5, 2.5, 3.0, 2.0, 30],  # Medium ellipse
        [2.0, 2.0, 1.5, 1.0, 0],   # Small ellipse (points outside)
        [1.0, 3.0, 4.79578, 3.53373, 45]   # Large ellipse (points inside)
    ]
    
    distances = vectorized_linestring_ellipse_distance([linestring], multiple_ellipses)
    print(f"Vectorized distances: {distances}")
    
    # Create CMA-ES objective functions with different penalty types
    objective_outside_only = create_cmaes_objective(linestring, 'outside_only')
    objective_distance = create_cmaes_objective(linestring, 'distance') 
    objective_abs_distance = create_cmaes_objective(linestring, 'abs_distance')
    
    # Test different objective types
    test_params = np.array(ellipse_params)
    print(f"\nObjective comparisons for params {ellipse_params}:")
    print(f"Outside only: {objective_outside_only(test_params):.4f}")
    print(f"Signed distance: {objective_distance(test_params):.4f}")
    print(f"Absolute distance: {objective_abs_distance(test_params):.4f}")
    
    # Test vectorized evaluation
    penalties = objective_distance(np.array(multiple_ellipses))
    print(f"Vectorized signed distances: {penalties}")