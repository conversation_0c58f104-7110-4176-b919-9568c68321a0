import numpy as np

import bpy
import numpy as np
import networkx as nx
import shapely
from mathutils import Vector
import time
from shapely.geometry import LineString, box
import geopandas as gpd
# Use Shapely for efficient nearest point calculation
from shapely.geometry import Point, MultiPoint
from shapely.ops import nearest_points

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None


def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None


def geometry_to_shapely(geometry):
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = False
    return contour, islands


def create_ring_object(coords, name):
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i+1)%n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects


def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.

    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects

    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []

    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))

    return blender_objects


def decompose_to_polygons(geom):
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def sort_containment(edges, polygons):
    # Initialize a directed graph
    G = nx.DiGraph()

    # Add edges from the array
    for container, contained in zip(edges[0], edges[1]):
        G.add_edge(contained, container)

    # Ensure the graph is a DAG and compute transitive reduction
    if nx.is_directed_acyclic_graph(G):
        containment_graph = nx.transitive_reduction(G)
    # else:
    #     containment_graph = G.copy()
    #     containment_graph.remove_edges_from(nx.find_cycle(containment_graph))

    # # Create a new graph for the containment (direct edges)
    # directed_containment_graph = nx.DiGraph()
    # directed_containment_graph.add_edges_from(containment_graph.edges())

    # for node in directed_containment_graph.nodes():
    #     directed_containment_graph.nodes[node]['polygon'] = polygons[node]

    # # Add identifiers for nodes
    # for i, polygon in enumerate(polygons):
    #     directed_containment_graph.nodes[polygon]['id'] = i

    return containment_graph


def create_graph_containment(polygons_list):
    tree = shapely.STRtree(polygons_list)
    contain_information = tree.query(polygons_list, predicate='contains_properly')
    # print(f'contain_information: {contain_information}')
    return sort_containment(contain_information, polygons_list)


def traverse_dag_postorder(graph: nx.DiGraph, leaf_order: list) -> list:
    """ Traverse the DAG starting from nodes in leaf_order (expected to be leaf nodes) and traverse upward (post-order: predecessors are visited before their parent).
    If a node is reached from multiple leaves, it will only appear once.
    """
    visited = set()
    result = []

    def dfs(node):
        if node in visited:
            return
        # First traverse all predecessors (i.e. nodes that feed into this node)
        for parent in graph.predecessors(node):
            dfs(parent)
        visited.add(node)
        result.append(node)

    for leaf in leaf_order:
        if leaf not in graph:
            raise ValueError(f"Leaf node {leaf} not found in graph")
        dfs(leaf)
    return reversed(result)


def traverse_dag_pairs(graph: nx.DiGraph, leaf_order: list) -> list:
    """Directly traverses the DAG starting from nodes in leaf_order (expected to be leaf nodes) and yields a list of tuple pairs of nodes from the DFS traversal.
    Each tuple represents a consecutive pair of nodes in the DFS path. Additionally, each node is paired with its 'id' attribute from the graph.
    Returns a list of tuples of the form ((node1, id1), (node2, id2)).
    """
    visited = set()
    pairs = []

    def dfs(node, last=None):
        if node in visited:
            return last
        # Traverse all predecessors, passing along the last visited node
        for parent in graph.predecessors(node):
            last = dfs(parent, last)
            if last is not None:
                # Append the pair as ((node, id), (node, id))
                pairs.append(((last, graph.nodes[last]['id']), (node, graph.nodes[node]['id'])))
            last = node
        visited.add(node)
        return node

    for leaf in leaf_order:
        if leaf not in graph:
            raise ValueError(f"Leaf node {leaf} not found in graph")
        dfs(leaf)
    return pairs


def solve_tsp_for_nodes(nodes):
    """
    Solve Traveling Salesman Problem for a list of nodes with geometric properties.

    Args:
        nodes: List of objects that have centroid property (Shapely geometries)

    Returns:
        List of indices representing the optimal route through the nodes
    """
    # Get centroids
    centroids = [node.centroid for node in nodes]
    # Convert Shapely points to coordinate tuples
    coords = [(c.x, c.y) for c in centroids]

    # Create a complete graph with Euclidean distances as edge weights
    tsp_graph = nx.Graph()
    for i, (x1, y1) in enumerate(coords):
        for j, (x2, y2) in enumerate(coords[i+1:], start=i+1):
            distance = shapely.geometry.Point(x1, y1).distance(shapely.geometry.Point(x2, y2))
            tsp_graph.add_edge(i, j, weight=distance)

    # Solve TSP using NetworkX's Christofides approximation
    return nx.approximation.traveling_salesman_problem(tsp_graph, cycle=False)


def segmentize(coords: np.ndarray, min_segment_length: float) -> tuple[np.ndarray, np.ndarray]:
    """
    Re-samples the input polyline or polygon by preserving original vertices and adding intermediate points
    on edges longer than min_segment_length, ensuring each resulting segment has a length of at least
    min_segment_length. Edges shorter than min_segment_length are left untouched.

    Parameters:
        coords (np.ndarray): Array of shape (N, 2) representing the polyline or polygon boundary.
        min_segment_length (float): Minimum length for each resulting segment.
        is_closed (bool): If True, treats coords as a closed polygon; if False, as an open polyline.

    Returns:
        tuple:
            - np.ndarray: Array of points with original vertices preserved and additional points added where needed.
            - np.ndarray: Array of indices where original vertices appear in the resulting array.
    """
    coords = np.asarray(coords)
    if coords.shape[0] < 2:
        return coords.copy(), np.arange(coords.shape[0])

    n_vertices = coords.shape[0]
    loop_range = n_vertices
    result = []  # List to collect all points
    original_indices = []  # List to track indices of original vertices in the result

    current_index = 0  # Track the current index in the result array

    for i in range(loop_range):
        # Get start and end points of the current edge
        start = coords[i]
        end = coords[(i + 1) % n_vertices]
        diff = end - start
        # Calculate edge length
        edge_length = np.sqrt((diff ** 2).sum())

        # Always add the start point (original vertex)
        result.append(start[np.newaxis, :])
        original_indices.append(current_index)
        current_index += 1

        if edge_length > min_segment_length:
            # Calculate the number of segments needed
            n_segments = int(np.floor(edge_length / min_segment_length))
            if n_segments < 1:
                n_segments = 1  # Minimum one segment
            segment_length = edge_length / n_segments

            # Since edge_length > min_segment_length and n_segments is floor(edge_length / min_segment_length),
            # segment_length will always be >= min_segment_length
            t = np.linspace(0, 1, n_segments + 1)[1:-1]  # Intermediate points, excluding start and end
            new_points = start + np.outer(t, diff)
            result.append(new_points)
            current_index += len(t)

    # Concatenate all points into a single array
    return np.concatenate(result), np.array(original_indices)


def compute_edge_metrics(radial_edges, inner, outer):
    """
    Compute edge metrics for specific pairs of inner and outer polygon vertices defined by radial_edges.

    Args:
        radial_edges: numpy array of shape (k, 2) where each row contains (inner_idx, outer_idx)
        inner: numpy array of shape (n_inner, 2) containing inner polygon vertices
        outer: numpy array of shape (n_outer, 2) containing outer polygon vertices

    Returns:
        A dictionary with keys:
        - 'angle_inner': angles (in radians) at inner vertices for each edge in radial_edges
        - 'angle_outer': angles (in radians) at outer vertices for each edge in radial_edges
        - 'edge_length': distances between connected inner and outer vertices
        - 'inner_idx': inner polygon indices
        - 'outer_idx': outer polygon indices
    """
    # Extract inner and outer indices from radial_edges
    inner_idx = radial_edges[:, 0]
    outer_idx = radial_edges[:, 1]

    # Get the vertices
    inner_current = inner[inner_idx]
    outer_current = outer[outer_idx]

    # Get previous vertex for each inner point (with wrap-around)
    n_inner = inner.shape[0]
    inner_prev = inner[(inner_idx - 1) % n_inner]

    # Get next vertex for each outer point (with wrap-around)
    n_outer = outer.shape[0]
    outer_next = outer[(outer_idx + 1) % n_outer]

    # --- Compute inner angle at each inner vertex ---
    # Vector along inner polygon edge ending at inner_current
    v_inner = inner_prev - inner_current
    # Vector from inner_current to outer vertex
    v_to_outer = outer_current - inner_current

    # Calculate cosine of inner angle for each radial edge
    dot_inner = np.sum(v_inner * v_to_outer, axis=1)
    norm_v_inner = np.linalg.norm(v_inner, axis=1)
    norm_v_to_outer = np.linalg.norm(v_to_outer, axis=1)
    cos_angle_inner = dot_inner / (norm_v_inner * norm_v_to_outer)
    cos_angle_inner = np.clip(cos_angle_inner, -1, 1)
    angle_inner = np.arccos(cos_angle_inner)
    diff_inner = np.pi - angle_inner
    # diff_inner = np.maximum(angle_inner, 2 * np.pi - angle_inner)

    # --- Compute outer angle at each outer vertex ---
    # For each pair, compute the vector from outer to inner
    v_from_outer_to_inner = inner_current - outer_current
    # For each outer vertex, compute the polygon edge leading out
    v_outer = outer_next - outer_current

    dot_outer = np.sum(v_from_outer_to_inner * v_outer, axis=1)
    norm_v_from_outer_to_inner = np.linalg.norm(v_from_outer_to_inner, axis=1)
    norm_v_outer = np.linalg.norm(v_outer, axis=1)
    cos_angle_outer = dot_outer / (norm_v_from_outer_to_inner * norm_v_outer)
    cos_angle_outer = np.clip(cos_angle_outer, -1, 1)
    angle_outer = np.arccos(cos_angle_outer)
    diff_outer = np.pi - angle_outer
    # diff_outer = np.maximum(angle_outer, 2 * np.pi - angle_outer)

    # --- Compute length of each radial edge ---
    edge_length = norm_v_to_outer

    return {
        'diff_inner': diff_inner,
        'diff_outer': diff_outer,
        'edge_length': edge_length,
        'inner_idx': inner_idx,
        'outer_idx': outer_idx
    }


def compute_offset_points_straight(inner: np.ndarray, outer: np.ndarray, offset: float, straight_idx: np.ndarray, search_distance: float) -> np.ndarray:
    """
    For each index in 'straight_idx', computes a query point based on the inner polygon edge defined by (indice-1, indice).
    The query point is generated by moving from inner[indice] in a direction perpendicular to the edge, scaled by 'offset'.
    Then, for each query point, finds and returns the closest point from the outer polygon.

    Parameters:
        inner (np.ndarray): An (N,2) array of inner polygon points.
        outer (np.ndarray): An (M,2) array of outer polygon points.
        offset (float): The length of the perpendicular offset vector.
        straight_idx (np.ndarray): Array of indices (in inner) where the angle is straight.
        search_distance (float): Maximum distance to search for closest point.

    Returns:
        np.ndarray: An array of closest points from the outer polygon corresponding to each query point.
    """
    # Calculate the directional vectors from the previous point to the current point
    vectors = inner[straight_idx] - inner[(straight_idx - 1) % len(inner)]

    # Compute the perpendicular vectors using a counter-clockwise rotation (-dy, dx)
    perp_vectors = np.column_stack((-vectors[:, 1], vectors[:, 0]))

    # Normalize the perpendicular vectors to get unit vectors
    norms = np.linalg.norm(perp_vectors, axis=1, keepdims=True)
    unit_perp = perp_vectors / norms

    # Compute the query points: move from inner point at straight_idx by offset along the perpendicular direction
    query_points = inner[straight_idx] + offset * unit_perp

    # Compute squared distances between each query point and all points in the outer polygon using broadcasting
    # query_points has shape (K, 2) and outer has shape (M, 2), result shape (K, M)
    diff = outer[np.newaxis, :, :] - query_points[:, np.newaxis, :]
    squared_dists = np.sum(diff**2, axis=2)

    # For each query point, get the index of the closest outer point if within search_distance, else use -1
    min_dists = np.min(squared_dists, axis=1)
    argmin_indices = np.argmin(squared_dists, axis=1)
    result = np.where(min_dists <= search_distance**2, argmin_indices, -1)
    return result


def compute_offset_points_acute(inner: np.ndarray, outer: np.ndarray, offset: float, acute_idx: np.ndarray, search_distance: float) -> tuple:
    """
    For each index in 'acute_idx', computes two query points based on the two edges adjacent to the vertex.
    For the edge (i-1,i), computes the query point as inner[i] + offset * unit_perp, where unit_perp is the unit perpendicular vector computed from inner[i] - inner[i-1].
    For the edge (i+1,i), computes the query point using the edge reversed: inner[i] - inner[i+1], then flips the perpendicular vector 180 degrees.
    Returns a tuple of two arrays containing the closest points from 'outer' corresponding to each query point.
    """
    # First segment: from edge (i-1, i)
    vec_prev = inner[acute_idx] - inner[(acute_idx - 1) % len(inner)]
    perp_prev = np.column_stack((-vec_prev[:, 1], vec_prev[:, 0]))
    norm_prev = np.linalg.norm(perp_prev, axis=1, keepdims=True)
    unit_perp_prev = perp_prev / norm_prev
    query_points_prev = inner[acute_idx] + offset * unit_perp_prev

    # Second segment: from edge (i+1, i); note: use reversed edge
    vec_next = inner[acute_idx] - inner[(acute_idx + 1) % len(inner)]
    perp_next = np.column_stack((-vec_next[:, 1], vec_next[:, 0]))
    norm_next = np.linalg.norm(perp_next, axis=1, keepdims=True)
    unit_perp_next = perp_next / norm_next
    # Flip the direction by multiplying by -1
    query_points_next = inner[acute_idx] + offset * (-unit_perp_next)

    # Find closest points in outer for both sets of query points
    diff_prev = outer[np.newaxis, :, :] - query_points_prev[:, np.newaxis, :]
    sq_dists_prev = np.sum(diff_prev**2, axis=2)
    min_dists_prev = np.min(sq_dists_prev, axis=1)
    argmin_prev = np.argmin(sq_dists_prev, axis=1)
    result_prev = np.where(min_dists_prev <= search_distance**2, argmin_prev, -1)

    diff_next = outer[np.newaxis, :, :] - query_points_next[:, np.newaxis, :]
    sq_dists_next = np.sum(diff_next**2, axis=2)
    min_dists_next = np.min(sq_dists_next, axis=1)
    argmin_next = np.argmin(sq_dists_next, axis=1)
    result_next = np.where(min_dists_next <= search_distance**2, argmin_next, -1)

    return result_prev, result_next


def compute_offset_points_obtuse(inner: np.ndarray, outer: np.ndarray, offset: float, obtuse_idx: np.ndarray, search_distance: float) -> np.ndarray:
    """
    For each index in 'obtuse_idx', computes the query point based on the angle bisector of the two adjacent edges at the vertex.

    For a vertex at index i, the two edges are defined by (i-1, i) and (i+1, i). The function computes the unit vectors in the direction of these edges (from inner[i] towards inner[i-1] and inner[i+1] respectively), sums them to get the bisector, normalizes the bisector, and then computes the query point as inner[i] - offset * bisector_unit.

    Parameters:
        inner (np.ndarray): (N,2) array of inner polygon points.
        outer (np.ndarray): (M,2) array of outer polygon points.
        offset (float): The length of the offset vector.
        obtuse_idx (np.ndarray): Array of indices in inner corresponding to obtuse vertices.
        search_distance (float): Maximum distance to search for closest point.

    Returns:
        np.ndarray: An array of closest points from the outer polygon corresponding to each query point.
    """
    # For each obtuse vertex at index i, compute the two edge vectors
    v1 = inner[obtuse_idx] - inner[(obtuse_idx - 1) % len(inner)]
    v2 = inner[obtuse_idx] - inner[(obtuse_idx + 1) % len(inner)]

    # Normalize the edge vectors
    u1 = v1 / np.linalg.norm(v1, axis=1, keepdims=True)
    u2 = v2 / np.linalg.norm(v2, axis=1, keepdims=True)

    # Compute the bisector as the sum of the unit vectors and normalize it
    bisector = u1 + u2
    bisector_unit = bisector / np.linalg.norm(bisector, axis=1, keepdims=True)

    # Compute the query points by offsetting from the vertex in the opposite direction of the bisector
    query_points = inner[obtuse_idx] - offset * bisector_unit

    # Find the closest points in outer using vectorized numpy operations
    diff = outer[np.newaxis, :, :] - query_points[:, np.newaxis, :]
    sq_dists = np.sum(diff**2, axis=2)
    min_dists = np.min(sq_dists, axis=1)
    argmin_indices = np.argmin(sq_dists, axis=1)
    result = np.where(min_dists <= search_distance**2, argmin_indices, -1)

    return result


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_polygon_angles(vertices, tolerance=0.1):
    """
    Classify vertices of a polygon based on their angles from outside perspective.

    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    tolerance: float, tolerance for angle classification

    Returns:
    tuple of three arrays containing indices of acute, straight, and obtuse angles
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)

    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices

    # Normalize vectors
    v1_norm = v1 / np.linalg.norm(v1, axis=1)[:, np.newaxis]
    v2_norm = v2 / np.linalg.norm(v2, axis=1)[:, np.newaxis]

    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)

    # Clip dot products to [-1, 1] to avoid numerical errors
    dot_products = np.clip(dot_products, -1, 1)

    # Calculate angles in radians
    angles = np.arccos(dot_products)

    # Calculate 2D cross product manually (z-component of 3D cross product)
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]

    # Flip angles where cross product is negative (inside angles)
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)

    # Classify angles with tolerance
    acute_mask = angles < np.pi - tolerance
    straight_mask = np.abs(angles - np.pi) <= tolerance
    obtuse_mask = angles > np.pi + tolerance
    return np.where(straight_mask)[0], np.where(acute_mask)[0], np.where(obtuse_mask)[0]


def generate_radial_edges(inner, outer, polygon_length, window):
    offsets = np.arange(window)
    outer_offsets = (outer[:, np.newaxis] + offsets) % polygon_length
    inner_expanded = np.repeat(inner, window)
    return np.column_stack((inner_expanded, outer_offsets.flatten()))


def geopandas_crosses_shapely_numpy(lines, polygon_coords):
    boundary = shapely.geometry.Polygon(polygon_coords).boundary
    # Vectorize the crosses operation
    crosses_vec = np.vectorize(lambda line: not line.crosses(boundary))
    crossing_mask = crosses_vec(lines)
    return crossing_mask

## slighly faster than shapely
# def geopandas_crosses_geoseries(lines, polygon_coords):
#     polygon = shapely.geometry.Polygon(polygon_coords)
#     s = gpd.GeoSeries(lines)
#     crossing = s.crosses(polygon.boundary)
#     return s.index[crossing].to_numpy()


def compute_polygon_edge_lengths(polygon):
    """
    Calculate the length of each edge in a polygon.

    Args:
        polygon: numpy array of shape (n, 2) containing polygon vertices

    Returns:
        tuple containing:
        - numpy array of shape (n,) containing the length of each edge,
          where edge i connects vertices i and (i+1)%n
        - numpy array of shape (n, 2) containing the indices of vertices
          that form each edge [(0,1), (1,2), ..., (n-1,0)]
    """
    n = len(polygon)

    # Roll the vertices to get the next point for each vertex
    next_vertices = np.roll(polygon, -1, axis=0)

    # Calculate vectors for each edge
    edge_vectors = next_vertices - polygon

    # Calculate the length of each edge
    edge_lengths = np.sqrt(np.sum(edge_vectors**2, axis=1))

    # Create array of edge indices
    start_indices = np.arange(n)
    end_indices = (start_indices + 1) % n
    edge_indices = np.column_stack((start_indices, end_indices))

    return edge_lengths, edge_indices


def compute_polygon_edge_angles(polygon):
    """
    Calculate the angle differences between consecutive edges in a polygon.

    Args:
        polygon: numpy array of shape (n, 2) containing polygon vertices

    Returns:
        tuple containing:
        - numpy array of shape (n,) containing the angle differences in radians
          between each edge and the next edge. Value is 0 for parallel edges.
        - numpy array of shape (n, 2) containing the unit direction vectors of each edge
    """
    n = len(polygon)

    # Get the next vertices for each vertex (with wrap-around)
    next_vertices = np.roll(polygon, -1, axis=0)

    # Calculate edge vectors
    edge_vectors = next_vertices - polygon

    # Normalize to get direction vectors
    edge_lengths = np.sqrt(np.sum(edge_vectors**2, axis=1))
    # Avoid division by zero
    edge_lengths = np.maximum(edge_lengths, 1e-10)
    edge_directions = edge_vectors / edge_lengths[:, np.newaxis]

    # Get the next edge directions (with wrap-around)
    next_edge_directions = np.roll(edge_directions, -1, axis=0)

    # Calculate dot products between consecutive edges
    dot_products = np.sum(edge_directions * next_edge_directions, axis=1)

    # Clip to avoid numerical errors
    dot_products = np.clip(dot_products, -1.0, 1.0)

    # Calculate angle differences (in radians)
    # Note: this gives the smallest angle between the vectors (0 to π)
    angle_diffs = np.arccos(dot_products)

    return angle_diffs


def process_inner_outer_pair(inner, outer, offset, seg_val=0.005, window=5, tolerance=0.05):
    inner_coords = inner.exterior.coords[:-1]
    outer_coords = outer.exterior.coords[:-1]

    inner_polygon, inner_original_indices = segmentize(inner_coords, seg_val)
    outer_polygon, outer_original_indices = segmentize(outer_coords, seg_val)

    # inner_polygon = np.asarray(inner_coords)
    # outer_polygon = np.asarray(outer_coords)

    polygon_length = outer_polygon.shape[0]

    inner_straight, inner_acute, inner_obtuse = classify_polygon_angles(inner_polygon, tolerance)
    outer_straight = compute_offset_points_straight(inner_polygon, outer_polygon, offset, inner_straight, seg_val)
    outer_acute_prev, outer_acute_next = compute_offset_points_acute(inner_polygon, outer_polygon, offset, inner_acute, seg_val*0.75)
    outer_obtuse = compute_offset_points_obtuse(inner_polygon, outer_polygon, offset, inner_obtuse, seg_val)

    straight_mask = outer_straight != -1
    acute_mask_prev, acute_mask_next = outer_acute_prev != -1, outer_acute_next != -1
    obtuse_mask = outer_obtuse != -1

    inner_straight, outer_straight = inner_straight[straight_mask], outer_straight[straight_mask]
    inner_acute_prev, outer_acute_prev = inner_acute[acute_mask_prev], outer_acute_prev[acute_mask_prev]
    inner_acute_next, outer_acute_next = inner_acute[acute_mask_next], outer_acute_next[acute_mask_next]
    inner_obtuse, outer_obtuse = inner_obtuse[obtuse_mask], outer_obtuse[obtuse_mask]

    radial_edges_straight = generate_radial_edges(inner_straight, outer_straight, polygon_length, window)
    radial_edges_acute_prev = generate_radial_edges(inner_acute_prev, outer_acute_prev, polygon_length, window)
    radial_edges_acute_next = generate_radial_edges(inner_acute_next, outer_acute_next, polygon_length, window)
    radial_edges_obtuse = generate_radial_edges(inner_obtuse, outer_obtuse, polygon_length, window)

    radial_edges_straight = np.unique(radial_edges_straight, axis=0)
    radial_edges_acute = np.unique(np.concatenate((radial_edges_acute_prev, radial_edges_acute_next), axis=0), axis=0)
    radial_edges_obtuse = np.unique(radial_edges_obtuse, axis=0)
    radial_edges = np.concatenate((radial_edges_straight, radial_edges_acute, radial_edges_obtuse), axis=0)
    radial_edges = radial_edges[np.lexsort((radial_edges[:, 1], radial_edges[:, 0]))]

    if len(radial_edges) == 0:
        return None

    lines = np.array([inner_polygon[radial_edges[:, 0]], outer_polygon[radial_edges[:, 1]]]).transpose(1, 0, 2)
    lines = list(map(LineString, lines))
    crosses = geopandas_crosses_shapely_numpy(lines, outer_polygon)
    radial_edges = radial_edges[crosses]


    connection_metrics = compute_edge_metrics(radial_edges, inner_polygon, outer_polygon)

    inner_lengths, inner_edges = compute_polygon_edge_lengths(inner_polygon)
    inner_angles = compute_polygon_edge_angles(inner_polygon)

    return {
        'radial_edges': radial_edges,
        'connection_metrics': connection_metrics,
        'inner_coords': inner_polygon,
        'outer_coords': outer_polygon,
        'inner_lengths': inner_lengths,
        'inner_edges': inner_edges,
        'inner_angles': inner_angles,
        'original_inner_coords': inner_coords,
        'original_outer_coords': outer_coords,
        'original_inner_indices': inner_original_indices,
        'original_outer_indices': outer_original_indices
    }


def create_inner_edges(inner_number, data_pair):
    inner_edges = []
    for indice, next_indice in zip(data_pair['inner_edges'][:, 0], data_pair['inner_edges'][:, 1]):
        indice_prev = (inner_number, indice)
        next_indice = (inner_number, next_indice)

        # weight = abs(data_pair['inner_lengths'][indice])
        weight = 0.01
        # weight = abs(data_pair['inner_angles'][indice])
        inner_edges.append((indice_prev, next_indice, {'weight': weight}))
    return inner_edges


def create_inner_outer_edges(inner_number, outer_number, data_pair):
    inner_outer_edges = []
    for i, (inner_indice, outer_indice) in enumerate(data_pair['radial_edges']):
        inner_node = (inner_number, inner_indice)
        outer_node = (outer_number, outer_indice)

        weight = calculate_edge_weight(data_pair, i)
        inner_outer_edges.append((inner_node, outer_node, {'weight': weight}))
    return inner_outer_edges


def calculate_edge_weight(data_pair, i):
    # connection_metrics dict:
    # 'angle_inner': angle_inner,
    # 'angle_outer': angle_outer,
    # 'edge_length': edge_length,
    # 'inner_idx': inner_idx,
    # 'outer_idx': outer_idx

    # weight = data_pair['connection_metrics']['edge_length'][i]
    max_weight =  max(data_pair['connection_metrics']['diff_inner'][i], data_pair['connection_metrics']['diff_outer'][i])
    max_weight = max_weight*2 if max_weight > np.pi/3 else max_weight

    # print(max_weight)
    return max_weight


def process_edge_geometry(edge, data_pair_dict, polygons, path, is_last_edge=False):
    """
    Process geometry for a single edge in the spiral path.

    Args:
        edge (tuple): Tuple of (inner, outer) polygon indices
        data_pair_dict (dict): Dictionary containing geometry data for polygon pairs
        polygons (np.ndarray): Array of polygon indices
        path (np.ndarray): Array containing path information
        is_last_edge (bool): Whether this is the last edge in the path

    Returns:
        np.ndarray: Processed geometry for this edge
    """
    inner, outer = edge
    data_pair = data_pair_dict[(inner, outer)]

    # Select appropriate geometry based on whether it's the last edge
    if is_last_edge:
        segmentized_geometry = data_pair['outer_coords']
        original_geometry = data_pair['original_outer_coords']
        original_indices = data_pair['original_outer_indices']
    else:
        segmentized_geometry = data_pair['inner_coords']
        original_geometry = data_pair['original_inner_coords']
        original_indices = data_pair['original_inner_indices']

    # Get vertices for current polygon
    masked = outer if is_last_edge else inner
    mask = np.isin(polygons, masked)
    vertices = path[mask, 1]

    # Filter out vertices that are not in original_indices
    if len(vertices) > 2:
        vertices_in_original = vertices[np.isin(vertices, original_indices)]

        # Ensure first vertex is included
        if len(vertices_in_original) == 0 or vertices_in_original[0] != vertices[0]:
            vertices_in_original = np.concatenate([[vertices[0]], vertices_in_original])

        # Ensure last vertex is included
        if vertices_in_original[-1] != vertices[-1]:
            vertices = np.concatenate([vertices_in_original, [vertices[-1]]])

    if vertices[0] in original_indices:
        # Find where in the original indices our vertex is located
        start_index = np.where(original_indices == vertices[0])[0][0]

        # Roll the geometry to start from vertices[0]
        reordered = np.roll(original_geometry, -start_index, axis=0)
        final_geometry = np.vstack((reordered, reordered[0:1]))
    else:
        # Find the closest index in original_indices where to insert the new point
        insert_idx = np.searchsorted(original_indices, vertices[0])

        # Get the point from segmentized geometry
        start_point = segmentized_geometry[vertices[0]]
        new_points = segmentized_geometry[vertices[1:]]

        # Insert the new point into original_geometry
        original_geometry = np.insert(original_geometry, insert_idx, start_point, axis=0)

        # Roll the geometry to start from the newly inserted point
        reordered = np.roll(original_geometry, -insert_idx, axis=0)

        # Create closed geometry
        closed_geometry = np.vstack((reordered, reordered[0:1]))

        # Concatenate the new points (part of the path)
        final_geometry = np.concatenate((closed_geometry, new_points))

    return final_geometry


def main():
    offset = 5
    simplify = False
    simplify_threshold = 0.001
    segmentize_start = offset*2 #* 3
    passes = 50
    window = 5
    buffer_attributes = {
        "join_style": 'mitre', # 'mitre', 'round', 'bevel'
        "mitre_limit": 1.1
        }

    send_to_blender = True

    geometry = get_geometry()
    contour, islands = geometry_to_shapely(geometry)
    polygon = geometry_to_polygon(geometry)
    exterior_list = []
    interior_list = []

    for pass_num in range(passes):
        buffer_offset = offset * (pass_num + 1)
        buffer = polygon.buffer(-buffer_offset, **buffer_attributes)
        if buffer.is_empty:
            # print(f'{pass_num} passes completed')
            break

        if simplify:
            s_threshold = (simplify_threshold * pass_num)/4
            print(f'simplify threshold: {s_threshold}')
            buffer = buffer.simplify(s_threshold)

        exteriors, interiors = decompose_to_polygons(buffer)
        exterior_list.extend(exteriors)
        interior_list.extend(interiors)

    # For more human readability, the first elements are the innermost [[0]]
    exterior_list = exterior_list[::-1]
    interior_list = interior_list[::-1]

    for i, polygon in enumerate(exterior_list):
        shapely_polygon = shapely.geometry.Polygon(polygon)
        shapely_to_blender(shapely_polygon, f"exterior_{i}", interiors=False)

    for i, polygon in enumerate(interior_list):
        shapely_polygon = shapely.geometry.Polygon(polygon)
        shapely_to_blender(shapely_polygon, f"interior_{i}", interiors=False)
    '''
    contour_graph = create_graph_containment(exterior_list)

    # Find sink and source nodes (nodes with no outgoing and incoming edges respectively)
    sink_nodes_contour = np.array([node for node in contour_graph.nodes if contour_graph.out_degree(node) == 0])
    sources_nodes_contour = np.array([node for node in contour_graph.nodes if contour_graph.in_degree(node) == 0])

    print("Sources contour:", sources_nodes_contour)
    print("Sinks contour :", sink_nodes_contour)

    contour_paths = []

    # Traverse graph from all sources to all sinks
    for source in sources_nodes_contour:
        for sink in sink_nodes_contour:
            if nx.has_path(contour_graph, source, sink):
                path = nx.shortest_path(contour_graph, source, sink)
                contour_paths.append(np.array(path))

    points_graph = nx.DiGraph()
    #Collect the data pairs
    data_pair_dict = {}
    additional_sinks = []

    for pair in contour_graph.edges():
        inner_number, outer_number = pair
        # print(inner_number, outer_number)

        # shapely_polygon = shapely.geometry.Polygon(exterior_list[inner_number])
        # shapely_to_blender(shapely_polygon, f"inner_{inner_number}", interiors=False)

        # shapely_polygon = shapely.geometry.Polygon(exterior_list[outer_number])
        # shapely_to_blender(shapely_polygon, f"outer_{outer_number}", interiors=False)

        data_pair = process_inner_outer_pair(exterior_list[inner_number], exterior_list[outer_number], offset=offset, seg_val=segmentize_start, window=window)
        # If no valid pair is found, skip the rest, and treat the inner polygon as a sink
        if data_pair is None:
            additional_sinks.append(inner_number)
            continue

        data_pair_dict.update({pair: data_pair})

        # Edges between inner and outer polygons (based on radial_edges)
        inner_outer_edges = create_inner_outer_edges(inner_number, outer_number, data_pair)
        points_graph.add_edges_from(inner_outer_edges)

        # Edges within the inner polygon
        inner_edges = create_inner_edges(inner_number, data_pair)
        points_graph.add_edges_from(inner_edges)

    path_list = []

    for contour_path in contour_paths:
        # Add super source and super target nodes
        super_source = f'super_source'
        super_target = f'super_target'

        points_graph.add_node(super_source)
        points_graph.add_node(super_target)

        # Collect unique sources and sinks from contour paths
        source_list = np.array([contour[0] for contour in contour_paths])
        sink_list = np.array([contour[-1] for contour in contour_paths])

        # Get unique values using np.unique
        source_list = np.unique(source_list)
        sink_list = np.unique(sink_list)

        sources_nodes_points = []
        sink_nodes_points = []

        # Select actual sources and sinks nodes
        for key in data_pair_dict.keys():
            data_pair = data_pair_dict[key]
            inner_polygon_num = key[0]
            outer_polygon_num = key[1]

            if inner_polygon_num in source_list:
                sources_nodes_points = [(inner_polygon_num, i) for i in np.arange(len(data_pair['inner_coords']))]
                for source in sources_nodes_points:
                    points_graph.add_edge(super_source, source, weight=0)

            if outer_polygon_num in sink_list:
                sink_nodes_points = [(outer_polygon_num, i) for i in np.arange(len(data_pair['outer_coords']))]
                for sink in sink_nodes_points:
                    points_graph.add_edge(sink, super_target, weight=0)

        if nx.has_path(points_graph, super_source, super_target):
            # Compute the shortest path
            path = nx.dijkstra_path(points_graph, source=super_source, target=super_target, weight='weight')
            # Extract the relevant part of the path (excluding dummy nodes)
            path_list.append(np.asarray(path[1:-1]))
            np_path = np.asarray(path[1:-1])[:,0]
            # Remove processed path from contour_paths
            contour_paths = [arr for arr in contour_paths if not np.array_equal(arr, np_path)]
            # Remove processed elements from contour_paths
            contour_paths = [
                arr[~np.isin(arr, np_path)]
                for arr in contour_paths
                if len(arr[~np.isin(arr, np_path)]) > 0
            ]
        else:
            break

        # Cleanup: Remove temporary nodes before next iteration
        points_graph.remove_node(super_source)
        points_graph.remove_node(super_target)

    if additional_sinks:
        # Check if additional_sinks exist in the path_list
        for path in path_list:
            additional_sinks = [sink for sink in additional_sinks if sink not in path[:, 0]]

        # Add additional sinks to path_list
        path_list.extend([np.array([sink, 0]) for sink in additional_sinks])

    if send_to_blender:
        for path in path_list:
            if len(path) < 2:
                ...
            else:
                polygons = path[:, 0]
                unique_polygons = np.unique(polygons)
                edges = [(unique_polygons[i], unique_polygons[i+1]) for i in range(len(unique_polygons) - 1)]

                # Initialize as empty array with correct shape (0,2)
                spiral_geometry = np.empty((0,2))

                for edge in edges:
                    edge_geometry = process_edge_geometry(edge, data_pair_dict, polygons, path, is_last_edge=False)
                    spiral_geometry = np.concatenate((spiral_geometry, edge_geometry))

                last_edge_geometry = process_edge_geometry(edges[-1], data_pair_dict, polygons, path, is_last_edge=True)
                spiral_geometry = np.concatenate((spiral_geometry, last_edge_geometry))

                create_line_object(spiral_geometry, f"spiral_path_{path[0, 0]}")

                # original_geometry_line = np.concatenate((original_geometry, original_geometry[0:1]))
                # create_line_object(original_geometry_line, f"inner_polygon_{inner}")

                # if len(vertices) > 1:
                #     ...
                # # else:
                #     if np.any(np.isin(vertices, original_indices)):
                #         # Find where in the original indices our vertex is located
                #         start_index = np.where(original_indices == vertices[0])[0][0]

                #         # Roll the geometry
                #         original_geometry = np.roll(original_geometry, -start_index, axis=0)

                #         # Close the geometry by appending the first point
                #         closed_geometry = np.vstack((original_geometry, original_geometry[0:1]))

                #         create_line_object(closed_geometry, f"inner_polygon_{edge[0]}")
                #     else:
                #         # print(f'vert are not on original geometry\n')
                #         continue

                    # print(f'vertex indice: {vertices[0]}')
                    # original_indices = next(
                    #     (data_pair_dict[k][f'original_{"inner" if k[0] == unique_polygon else "outer"}_indices']
                    #      for k in data_pair_dict if unique_polygon in k),
                    #     None
                    # )
                    # if original_indices is None:
                    #     print(f'No original indices found for polygon {unique_polygon}')
                    #     continue
                    # print(f'original indices: {original_indices}')
                    # if np.any(np.isin(vertices, original_indices)):
                    #     print(f'vert are on original geometry\n')

                    #     continue
                    # else:
                    #     print(f'vert are not on original geometry\n')
                    #     continue


            # Create an empty array to store coordinates for this path
            path_coords = []

            # Additional sinks are 1d arrays
            if path.ndim == 1:
                # shapely_polygon = shapely.geometry.Polygon(exterior_list[path[0]])
                # shapely_to_blender(shapely_polygon, f"inner_polygon_{path[0]}", interiors=False)
                ...
            else:
                for polygon_idx, vertex_idx in path:
                    # Create a Shapely Polygon from the corresponding exterior
                    original_polygon = exterior_list[polygon_idx]
                    # shapely_polygon = shapely.geometry.Polygon(original_polygon)
                    # shapely_to_blender(shapely_polygon, f"inner_polygon_{polygon_idx}", interiors=False)

                    # Find the key in data_pair_dict where the first element is polygon_idx
                    # segmentized_inner_coords = data_pair_dict[key]['inner_coords']
                    for key in data_pair_dict.keys():
                        if key[0] == polygon_idx:
                            ...
                            # print(key)
                            # # # Create a Shapely Polygon from the corresponding exterior
                            shapely_polygon = shapely.geometry.Polygon(data_pair_dict[key]['inner_coords'])
                            shapely_to_blender(shapely_polygon, f"inner_polygon_{key[0]}", interiors=False)


                            # # Assuming vertex_idx references a point in either inner_coords or outer_coords
                            if vertex_idx < len(data_pair_dict[key]['inner_coords']):
                                path_coords.append(data_pair_dict[key]['inner_coords'][vertex_idx])
                            else:
                                # Adjust index for outer_coords if needed
                                outer_idx = vertex_idx - len(data_pair_dict[key]['inner_coords'])
                                path_coords.append(data_pair_dict[key]['outer_coords'][outer_idx])
                            break

            # Convert to numpy array and create line object
            path_coords = np.array(path_coords)
            create_line_object(path_coords, "path")
            '''

    if additional_sinks:
        print("additional_sinks:", np.array(additional_sinks))

    print("path_list:", path_list)
    # for path in path_list:
    #     polygons = np.unique(path[:, 0])
    #     for polygon_idx in polygons:
    #         coords = exterior_list[polygon_idx].exterior.coords
    #         create_ring_object(coords, f"inner_polygon_{polygon_idx}")
    # print("Additional sinks:", np.array(additional_sinks))
    # for i, path in enumerate(path_list):
    #     p = path[:,0]
    #     print(f"path:[{i}]:", p)

if __name__ == "__main__":
    main()
