import numpy as np

def project_with_matrix(v2, v1):
    """Projects v2 onto the basis of v1 using a change of basis matrix."""
    v1 = np.asarray(v1)
    v2 = np.asarray(v2)
    
    # 1. Create the new orthonormal basis
    u_x = v1 / np.linalg.norm(v1)
    u_y = np.array([-u_x[1], u_x[0]])

    # 2. Create the transformation matrix from the new basis.
    # The columns of the matrix are the basis vectors.
    # This is the matrix that converts from the new basis TO the standard basis.
    P_transpose = np.array([u_x, u_y])

    # 3. The inverse of an orthonormal matrix is its transpose.
    # To convert FROM the standard basis TO the new basis, we use P_transpose.
    P = P_transpose.T 
    
    # In our construction, P_transpose is actually the inverse matrix (P⁻¹)
    # we need to transform v2.
    # new_coords = P⁻¹ @ v2
    v2_in_v1_coords = P_transpose @ v2
    
    return v2_in_v1_coords

# --- Using the same example vectors ---
angle_v1 = np.deg2rad(30)
v1 = np.array([np.cos(angle_v1), np.sin(angle_v1)])
print(f'v1: {v1}')
angle_v2 = np.deg2rad(75)
v2 = np.array([np.cos(angle_v2), np.sin(angle_v2)])
print(f'v2: {v2}')

v2_in_v1_matrix = project_with_matrix(v2, v1)

# --- Verification ---
# The angle between v2 and v1 is 75 - 30 = 45 degrees.
# So in the new system, v2 should be a vector at 45 degrees.
# Its coordinates should be (cos(45), sin(45)) because v2 is also normalized.
angle_diff = np.deg2rad(45)
expected_coords = np.array([np.cos(angle_diff), np.sin(angle_diff)])

print("\n--- Using Matrix Method ---")
print(f"Coordinates of v2 in v1's system: {v2_in_v1_matrix}")
assert np.allclose(v2_in_v1_matrix, expected_coords)
print("✅ Result matches expectation.")