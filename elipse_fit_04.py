import numpy as np
import scipy.optimize
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse, Polygon

# --- Ellipse and Geometry Helper Functions ---

def get_line_coeffs(p1, p2):
    """
    Returns (l, m, n) for the line lx + my + n = 0 passing through p1 and p2.
    p1 = (x1, y1), p2 = (x2, y2)
    """
    l = p1[1] - p2[1]  # y1 - y2
    m = p2[0] - p1[0]  # x2 - x1
    n = p1[0] * p2[1] - p2[0] * p1[1]  # x1*y2 - x2*y1
    return l, m, n

def get_q_matrix(params):
    """
    params = (A, B, C, D, E), F is fixed to -1
    Returns the 3x3 matrix Q_mat for the conic.
    """
    A, B, C, D, E = params
    F = -1.0
    return np.array([
        [A, B / 2, D / 2],
        [B / 2, C, E / 2],
        [D / 2, E / 2, F]
    ])

def get_adjugate_q_matrix(Q_mat):
    """
    Calculates the adjugate of a 3x3 matrix Q_mat.
    For a non-singular matrix, adj(Q) = det(Q) * inv(Q).
    This is more stable if det(Q) is close to zero.
    """
    # Minors
    M11 = Q_mat[1,1]*Q_mat[2,2] - Q_mat[1,2]*Q_mat[2,1]
    M12 = Q_mat[1,0]*Q_mat[2,2] - Q_mat[1,2]*Q_mat[2,0]
    M13 = Q_mat[1,0]*Q_mat[2,1] - Q_mat[1,1]*Q_mat[2,0]

    M21 = Q_mat[0,1]*Q_mat[2,2] - Q_mat[0,2]*Q_mat[2,1]
    M22 = Q_mat[0,0]*Q_mat[2,2] - Q_mat[0,2]*Q_mat[2,0]
    M23 = Q_mat[0,0]*Q_mat[2,1] - Q_mat[0,1]*Q_mat[2,0]

    M31 = Q_mat[0,1]*Q_mat[1,2] - Q_mat[0,2]*Q_mat[1,1]
    M32 = Q_mat[0,0]*Q_mat[1,2] - Q_mat[0,2]*Q_mat[1,0]
    M33 = Q_mat[0,0]*Q_mat[1,1] - Q_mat[0,1]*Q_mat[1,0]

    # Cofactors
    C_mat = np.array([
        [ M11, -M12,  M13],
        [-M21,  M22, -M23],
        [ M31, -M32,  M33]
    ])
    return C_mat.T # Adjugate is transpose of cofactor matrix

def ellipse_area(params):
    A, B, C, D, E = params
    F = -1.0
    q_mat = get_q_matrix(params)
    det_q = np.linalg.det(q_mat)
    
    # Discriminant for ellipse type: B^2 - 4AC
    # For an ellipse, B^2 - 4AC < 0, so 4AC - B^2 > 0
    val_4ac_b2 = 4 * A * C - B**2
    
    if val_4ac_b2 <= 1e-8: # Not an ellipse or degenerate
        return 0 # Or a very small number / large penalty

    # Area = 2 * pi * abs(det(Q)) / (4AC - B^2)^(3/2)
    # The sign of det(Q) with F=-1 indicates if origin is inside (negative) or outside (positive)
    # For an ellipse containing the origin, det(Q) is negative.
    # If params define a real ellipse, det(Q) should be non-zero.
    # If F=-1, typical ellipses (like x^2+y^2=1 -> x^2+y^2-1=0 -> A=1,C=1,F=-1) have det(Q_mat) < 0
    # So abs(det(Q)) is appropriate.
    area_val = 2 * np.pi * np.abs(det_q) / (val_4ac_b2)**(1.5)
    return area_val if np.isfinite(area_val) else 0


def get_ellipse_center(params):
    A, B, C, D, E = params
    den = 4 * A * C - B**2
    if abs(den) < 1e-8: # Avoid division by zero (degenerate case)
        return np.array([np.nan, np.nan])
    cx = (B * E - 2 * C * D) / den
    cy = (B * D - 2 * A * E) / den
    return np.array([cx, cy])

def get_ellipse_plot_params(params_opt, center_offset):
    """Converts Ax^2+...-1=0 params to matplotlib Ellipse patch params"""
    A, B, C, D, E = params_opt
    F = -1.0

    # Center (in translated system, before adding offset)
    cx_local, cy_local = get_ellipse_center(params_opt)
    
    # Translate actual center back
    cx = cx_local + center_offset[0]
    cy = cy_local + center_offset[1]

    # Eigenvalues of the quadratic part [[A, B/2], [B/2, C]] determine axes lengths
    # The equation for the ellipse centered at its origin (cx_local, cy_local) is
    # A(x-cx_local)^2 + B(x-cx_local)(y-cy_local) + C(y-cy_local)^2 + F_prime = 0
    # where F_prime = D*cx_local/2 + E*cy_local/2 + F (this is from a specific derivation, can be tricky)
    # A simpler approach for semi-axes and angle from A,B,C,D,E,F:
    # See https://en.wikipedia.org/wiki/Ellipse#General_ellipse
    # Or https://math.stackexchange.com/questions/872739/finding-the-angle-of-rotation-of-an-ellipse-from-its-general-equation-and-vicev
    
    # For Ax^2 + Bxy + Cy^2 + Dx + Ey + F = 0
    # Angle of rotation
    if abs(A - C) < 1e-8: # A approx C
        angle_rad = np.pi / 4 if B != 0 else 0
    else:
        angle_rad = 0.5 * np.arctan2(B, A - C)
    
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)

    # Semi-axes lengths squared (can be derived from eigenvalues or formulas)
    # For F=-1, the constant term on the RHS of canonical form involves F and D,E,cx,cy
    # Let's use a common formula for semi-axes a_len, b_len:
    den = B**2 - 4*A*C
    if abs(den) < 1e-8: return cx,cy,0,0,0 # Degenerate
    
    # Term that appears in numerator for semi-axes squared
    # This is 2 * (A*E^2 + C*D^2 - B*D*E + (B^2-4AC)*F) / (B^2-4AC)
    # which for F=-1 becomes: 2 * (A*E^2 + C*D^2 - B*D*E - (B^2-4AC)) / (B^2-4AC)
    # Or, with F=-1, we are scaling to get constant 1 on RHS.
    # The term Q_mat[2,2] - (D^2/(4A) + E^2/(4C) - BD/(4AC)) type formulas are for specific cases or hard to use.
    # The formula from Wikipedia for semi-axes directly:
    num = 2 * (A*E**2 + C*D**2 - B*D*E + den*F) # den = B^2-4AC, F=-1

    # Check for real ellipse. num/den should be positive for semi-axes^2 to be positive.
    # Since den = B^2-4AC < 0 for ellipse, num should be negative if F=-1 (origin inside ellipse).
    if num / den <= 0: # This case implies ellipse does not exist or is degenerate
        # This might happen if the F=-1 convention is violated (e.g. ellipse passes through origin)
        # or if parameters are far from defining an ellipse.
        # A robust check is if `A*cx_local^2 + B*cx_local*cy_local + C*cy_local^2 + D*cx_local + E*cy_local + F` is < 0.
        # This is -det(Q_mat) / (AC - B^2/4) if Q_mat is for ellipse centered at origin with general F.
        # For our F=-1, Q_mat is already defined.
        # -det(Q_mat) / (AC - B^2/4) should be positive for radii to be real.
        # det(Q_mat) should be negative, (AC-B^2/4) should be positive.
        Q_mat = get_q_matrix(params_opt)
        det_Q = np.linalg.det(Q_mat)
        ac_b2_4 = A*C - (B/2)**2 # This is (4AC-B^2)/4
        
        if ac_b2_4 <= 1e-8 : return cx,cy,0,0,0 # Degenerate quadratic part
        const_term_for_radii = -det_Q / ac_b2_4 # This should be positive
        if const_term_for_radii <= 0: return cx,cy,0,0,0 # Degenerate

        # Eigenvalues of [[A, B/2],[B/2,C]] are lambda1, lambda2
        # Radii squared are const_term_for_radii / lambda1 and const_term_for_radii / lambda2
        M_quad = np.array([[A, B/2],[B/2, C]])
        eigenvals = np.linalg.eigvalsh(M_quad)
        
        # Eigenvals must be positive if A, C > 0 and 4AC-B^2 > 0 for F=-1 ellipse containing origin
        if eigenvals[0] <= 1e-8 or eigenvals[1] <= 1e-8: return cx,cy,0,0,0

        r1_sq = const_term_for_radii / eigenvals[0]
        r2_sq = const_term_for_radii / eigenvals[1]
        
        if r1_sq <= 0 or r2_sq <=0 : return cx,cy,0,0,0 # Should not happen if const_term_for_radii > 0 and eigenvals > 0

        # width = 2 * major_axis, height = 2 * minor_axis
        width = 2 * np.sqrt(max(r1_sq, r2_sq))
        height = 2 * np.sqrt(min(r1_sq, r2_sq))
        
        # Angle needs to correspond to the axis associated with width (major axis)
        # Eigenvector for A-C gives direction of major/minor axis
        if abs(r1_sq - r2_sq) < 1e-8: # Circle
             pass # angle_rad is fine
        elif r1_sq > r2_sq: # r1 is major axis
            if abs(eigenvals[0] - (A*cos_a**2 + B*sin_a*cos_a + C*sin_a**2)) > abs(eigenvals[0] - (A*sin_a**2 - B*sin_a*cos_a + C*cos_a**2)):
                 # The angle_rad corresponds to the other eigenvalue (minor axis for this assignment)
                 # This means the first eigenvector aligns with what became the 'height'
                 # Need to ensure angle corresponds to width (semi-major axis)
                 # A' = A c^2 + Bsc + C s^2, C' = A s^2 - Bsc + C c^2
                 # If A' is for major axis, angle_rad is correct.
                 # If C' is for major axis, angle_rad needs adjustment by pi/2.
                 # The derived angle_rad is typically for the x-axis of the ellipse's coordinate system.
                 # We need to associate sqrt(const_term_for_radii / eigenvals[0]) with an axis direction.
                 # The first eigenvector of M_quad gives the direction for eigenvals[0].
                 # This part can be tricky; the angle from atan2(B, A-C) is usually correct
                 # for the first axis in the rotated coordinate system.
                 # Matplotlib's Ellipse takes angle for its 'width' (first semi-axis parameter).
                 pass # Angle from atan2(B, A-C) typically refers to the x-axis of the canonical form.
                     # The semi-axis along this x-axis is sqrt(const_term / A_canonical)
                     # Let's assume width corresponds to what A transforms to and height to C.
                     # If A_can < C_can (meaning major axis along y_can), then swap width/height or adjust angle by 90 deg.
                     # The calculated angle is for the x-axis of the rotated system.
                     # A_prime = (A+C)/2 + sqrt(((A-C)/2)^2 + (B/2)^2) (or minus for C_prime)
                     # The formula for width/height below ensures width > height.
                     # The angle_rad from atan2(B,A-C) aligns with the axis corresponding to A' (first one in rotated form)
                     # If A' makes the ellipse fatter then it is width, if C' makes it fatter then C' is width.

    else: # Original formulas for a_sq, b_sq from Wikipedia (often needs F to be chosen carefully)
        # These formulas are more direct if they work:
        # For Ax^2 + Bxy + Cy^2 + Dx + Ey + F = 0
        # den = B^2 - 4AC
        # num = 2 * (A*E^2 + C*D^2 - B*D*E + den*F)
        # a_sq = -num / (den * (A+C + sqrt((A-C)^2+B^2)))
        # b_sq = -num / (den * (A+C - sqrt((A-C)^2+B^2)))
        # This assumes (A+C - sqrt((A-C)^2+B^2)) corresponds to major axis.
        # Simpler: use the derived const_term_for_radii and eigenvals above, it's more robust.
        # Let's re-use the robust calculation
        Q_mat = get_q_matrix(params_opt)
        det_Q = np.linalg.det(Q_mat)
        ac_b2_4 = A*C - (B/2)**2
        if ac_b2_4 <= 1e-8 : return cx,cy,0,0,0
        const_term_for_radii = -det_Q / ac_b2_4
        if const_term_for_radii <= 0: return cx,cy,0,0,0
        M_quad = np.array([[A, B/2],[B/2, C]])
        eigenvals = np.linalg.eigvalsh(M_quad)
        if eigenvals[0] <= 1e-8 or eigenvals[1] <= 1e-8: return cx,cy,0,0,0
        r1_sq = const_term_for_radii / eigenvals[0]
        r2_sq = const_term_for_radii / eigenvals[1]
        if r1_sq <= 0 or r2_sq <=0 : return cx,cy,0,0,0

        width = 2 * np.sqrt(max(r1_sq, r2_sq))
        height = 2 * np.sqrt(min(r1_sq, r2_sq))

        # Adjust angle if major axis (width) corresponds to the second eigenvalue/eigenvector
        if (r1_sq < r2_sq and eigenvals[0] < eigenvals[1]) or \
           (r1_sq > r2_sq and eigenvals[0] > eigenvals[1]): # max radius from first eigenval
            pass # angle_rad is fine
        else: # max radius from second eigenval
            angle_rad += np.pi/2

    return cx, cy, width, height, np.rad2deg(angle_rad)


# --- Optimization Setup ---
def solve_ellipse_problem(v_a, v_b, v_c, tangent_edges_indices, cross_edge_indices):
    """
    Finds the maximizing inscribed ellipse.
    tangent_edges_indices: list of tuples, e.g., [(0,1), (1,2)] meaning edge va-vb and vb-vc
    cross_edge_indices: list of tuples, e.g., [(2,0)] meaning edge vc-va
    Vertices are passed as np.arrays.
    """
    vertices = [v_a, v_b, v_c]

    # --- 1. Translate triangle ---
    centroid = (v_a + v_b + v_c) / 3.0
    v_a_t = v_a - centroid
    v_b_t = v_b - centroid
    v_c_t = v_c - centroid
    vertices_t = [v_a_t, v_b_t, v_c_t]

    # --- 2. Define lines for tangency and side constraints ---
    tangent_lines_coeffs = []
    for i1, i2 in tangent_edges_indices:
        coeffs = get_line_coeffs(vertices_t[i1], vertices_t[i2])
        tangent_lines_coeffs.append(coeffs)

    # For side constraints: find the third vertex for each tangent line
    # to determine the "inside" of the triangle.
    third_vertex_for_tangent_edge = []
    all_indices = {0, 1, 2}
    for i1, i2 in tangent_edges_indices:
        remaining_indices = list(all_indices - {i1, i2})
        if not remaining_indices:
            raise ValueError("Could not find third vertex for tangent edge.")
        third_idx = remaining_indices[0]
        third_vertex_for_tangent_edge.append(vertices_t[third_idx])
        
    # --- 3. Objective Function ---
    def objective(params):
        area = ellipse_area(params)
        # Penalize if not an ellipse or if area is not positive
        A,B,C,_,_ = params
        if 4*A*C - B**2 <= 1e-7 or area <= 1e-7:
             return 1e12 # Large penalty
        return -area # Minimize negative area

    # --- 4. Constraint Functions ---
    constraints = []

    # 4.1 Ellipse condition: 4AC - B^2 > epsilon
    epsilon = 1e-7
    constraints.append({
        'type': 'ineq',
        'fun': lambda params: 4 * params[0] * params[2] - params[1]**2 - epsilon
    })

    # 4.2 Tangency constraints
    for l_coeffs in tangent_lines_coeffs:
        l, m, n = l_coeffs
        def tangency_constraint(params, l=l, m=m, n=n): # Capture l,m,n
            q_mat = get_q_matrix(params)
            try:
                # Check if Q_mat is singular - if so, adjugate can be problematic
                # but det(Q_mat) will be 0 for tangency.
                # For stability, use a robust adjugate calculation.
                adj_q = get_adjugate_q_matrix(q_mat)
            except np.linalg.LinAlgError:
                return 1e6 # Penalize if matrix is badly conditioned for inversion
            
            tangency_val = (l**2 * adj_q[0,0] + m**2 * adj_q[1,1] + n**2 * adj_q[2,2] +
                            2*l*m*adj_q[0,1] + 2*l*n*adj_q[0,2] + 2*m*n*adj_q[1,2])
            return tangency_val # For 'eq' constraint, should be func = 0
        
        constraints.append({
            'type': 'eq',
            'fun': tangency_constraint
        })

    # 4.3 Center on "inside" of tangent edges
    for i, (l_coeffs, third_v_t) in enumerate(zip(tangent_lines_coeffs, third_vertex_for_tangent_edge)):
        l, m, n = l_coeffs
        # Value of line eq for the third vertex (determines "inside" sign)
        sign_inside = np.sign(l * third_v_t[0] + m * third_v_t[1] + n)
        if abs(sign_inside) < 1e-8: # third vertex is on the line (degenerate triangle)
             # This is problematic, make a default assumption or error
             print(f"Warning: Third vertex {third_v_t} is collinear with tangent edge {i}. Problem might be ill-defined.")
             sign_inside = 1.0 # Or handle as error


        def center_side_constraint(params, l=l, m=m, n=n, sign_inside=sign_inside): # Capture vars
            center_t = get_ellipse_center(params)
            if np.isnan(center_t[0]): # Degenerate ellipse params
                return -1.0 # Fail constraint
            
            # Value of line eq for ellipse center
            val_center = l * center_t[0] + m * center_t[1] + n
            # Product should be non-negative if center is on correct side or on the line
            return val_center * sign_inside # Must be >= 0
        
        constraints.append({
            'type': 'ineq',
            'fun': center_side_constraint
        })

    # --- 5. Initial Guess (for translated system) ---
    # Start with a small circle at the origin of the translated system
    # (x/r)^2 + (y/r)^2 - 1 = 0  => A=1/r^2, C=1/r^2, B=D=E=0, F=-1
    # Estimate r based on triangle size
    s1 = np.linalg.norm(v_a_t - v_b_t)
    s2 = np.linalg.norm(v_b_t - v_c_t)
    s3 = np.linalg.norm(v_c_t - v_a_t)
    avg_side_length = (s1 + s2 + s3) / 3.0
    r_guess = avg_side_length / 4.0 # A smaller radius
    if r_guess < 1e-3: r_guess = 0.1 # Avoid division by zero for tiny triangles

    # A0 = 1.0 / r_guess**2 # Can be very large if r_guess is small
    # C0 = 1.0 / r_guess**2
    # More stable: A, C around 1 implies radii around 1 (if F=-1)
    A0 = 1.0 
    C0 = 1.0
    x0 = np.array([A0, 0.0, C0, 0.0, 0.0]) # A, B, C, D, E

    # --- 6. Run Optimizer ---
    # Bounds can sometimes help, but here mostly rely on constraints
    # A > 0, C > 0 implicitly by 4AC-B^2 > 0
    # bounds = [(1e-6, None), (None, None), (1e-6, None), (None, None), (None, None)]

    print("Starting optimization...")
    res = scipy.optimize.minimize(
        objective,
        x0,
        method='SLSQP',
        constraints=constraints,
        options={'disp': True, 'maxiter': 1000, 'ftol': 1e-9} # Increased maxiter
    )

    print("\nOptimization result:")
    print(res)

    if not res.success:
        print("Optimization FAILED.")
        print(f"Message: {res.message}")
        # Try to get plot params even if failed, for debugging
        try:
            cx_plot, cy_plot, w_plot, h_plot, ang_plot = get_ellipse_plot_params(res.x, centroid)
            return res.x, (cx_plot, cy_plot, w_plot, h_plot, ang_plot), vertices, res.success
        except Exception as e:
            print(f"Could not get plot params after fail: {e}")
            return None, None, vertices, False


    # --- 7. Get final ellipse parameters for plotting (in original coords) ---
    params_opt = res.x
    cx_plot, cy_plot, w_plot, h_plot, ang_plot = get_ellipse_plot_params(params_opt, centroid)
    
    final_area = ellipse_area(params_opt)
    print(f"Maximized Area (approx): {final_area:.4f}")
    print(f"Optimal Ellipse Parameters (A,B,C,D,E for F=-1, translated system): {params_opt}")
    print(f"Plot Ellipse: Center=({cx_plot:.2f},{cy_plot:.2f}), W={w_plot:.2f}, H={h_plot:.2f}, Angle={ang_plot:.2f}")
    
    return params_opt, (cx_plot, cy_plot, w_plot, h_plot, ang_plot), vertices, res.success


# --- Main Execution ---
if __name__ == '__main__':
    # Define triangle vertices (a,b,c)
    # Example 1: Equilateral-ish
    # v_a = np.array([0.0, 0.0])
    # v_b = np.array([4.0, 0.0])
    # v_c = np.array([2.0, 3.0])

    # Example 2: Scalene
    v_a = np.array([1.0, 1.0])
    v_b = np.array([5.0, 2.0])
    v_c = np.array([2.0, 6.0])

    # Example 3: A more challenging one, might need ftol/maxiter adjustment
    # v_a = np.array([-3.0, -2.0])
    # v_b = np.array([4.0, -1.0])
    # v_c = np.array([1.0, 5.0])


    # Edges are (v_a, v_b), (v_b, v_c), (v_c, v_a)
    # Let's say ellipse must be tangent to (v_a, v_b) and (v_b, v_c)
    # and can cross (v_c, v_a)
    
    # Indices refer to vertices_t = [v_a_t, v_b_t, v_c_t]
    # Edge (v_a,v_b) -> (0,1)
    # Edge (v_b,v_c) -> (1,2)
    # Edge (v_c,v_a) -> (2,0)
    
    tangent_edges = [(0,1), (1,2)] 
    cross_edges = [(2,0)] # Not used directly in constraints, just for problem def

    opt_params, plot_ellipse_params, original_vertices, success = solve_ellipse_problem(
        v_a, v_b, v_c, tangent_edges, cross_edges
    )

    # --- Plotting ---
    fig, ax = plt.subplots(figsize=(8,8))
    
    # Plot triangle
    triangle_patch = Polygon(original_vertices, closed=True, edgecolor='blue', facecolor='lightblue', alpha=0.4)
    ax.add_patch(triangle_patch)
    ax.plot(v_a[0], v_a[1], 'bo', label='v_a')
    ax.plot(v_b[0], v_b[1], 'bo', label='v_b')
    ax.plot(v_c[0], v_c[1], 'bo', label='v_c')
    for i, v_name in enumerate(['v_a', 'v_b', 'v_c']):
        ax.text(original_vertices[i][0]+0.1, original_vertices[i][1]+0.1, v_name)


    if success and plot_ellipse_params:
        cx, cy, width, height, angle = plot_ellipse_params
        if width > 0 and height > 0: # Check if ellipse is valid for plotting
            ellipse = Ellipse(xy=(cx, cy), width=width, height=height, angle=angle,
                              edgecolor='red', facecolor='salmon', alpha=0.6, label='Maximized Ellipse')
            ax.add_patch(ellipse)
            ax.plot(cx, cy, 'ro', markersize=5, label='Ellipse Center')
        else:
            print("Resulting ellipse is degenerate, not plotting.")
    else:
        print("Optimization failed or no valid ellipse found to plot.")


    all_x = [v[0] for v in original_vertices]
    all_y = [v[1] for v in original_vertices]
    if plot_ellipse_params and width > 0 and height > 0:
        # Add ellipse extent to plot limits
        # Crude estimation of ellipse bounding box for plot limits
        el_max_r = max(width, height) / 2.0
        all_x.extend([cx - el_max_r, cx + el_max_r])
        all_y.extend([cy - el_max_r, cy + el_max_r])


    ax.set_xlabel("x")
    ax.set_ylabel("y")
    ax.set_title("Maximizing Ellipse (tangent to 2 edges, can cross 1)")
    ax.legend()
    ax.axis('equal')
    ax.grid(True)
    
    # Set plot limits based on content
    if all_x and all_y:
        ax.set_xlim(min(all_x) - 1, max(all_x) + 1)
        ax.set_ylim(min(all_y) - 1, max(all_y) + 1)
    
    plt.show()