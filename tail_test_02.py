import numpy as np
from shapely.geometry import LineString

def add_fish_tails(triangle, length=0.2):
    """
    Adds fish tails to each vertex of a triangle.
    
    Parameters:
    - triangle: NumPy array of shape (3, 2) with triangle vertices.
    - length: Length of the fish tails.
    
    Returns:
    - List of Shapely LineStrings representing the fish tails.
    """
    fish_tails = []
    n_verts = len(triangle)
    
    for i in range(n_verts):
        a = triangle[i]
        next_idx = (i + 1) % n_verts
        prev_idx = (i - 1) % n_verts
        b = triangle[next_idx]
        c = triangle[prev_idx]
        
        # Vectors from a to next and previous vertices
        ab = b - a
        ac = c - a
        
        # Compute outward normals (90-degree clockwise rotation)
        normal_ab = np.array([ab[1], -ab[0]])
        normal_ac = np.array([ac[1], -ac[0]])
        
        # Normalize and scale
        normal_ab = normal_ab / np.linalg.norm(normal_ab) * length
        normal_ac = normal_ac / np.linalg.norm(normal_ac) * length
        
        # New points along the normals
        point1 = a + normal_ab
        point2 = a + normal_ac
        
        # Create LineStrings for the fish tail
        tail1 = LineString([tuple(a), tuple(point1)])
        tail2 = LineString([tuple(a), tuple(point2)])
        
        fish_tails.extend([tail1, tail2])
    
    return fish_tails

# Example usage
triangle = np.array([[0, 0], [1, 0], [0.5, np.sqrt(3)/2]])  # Equilateral triangle
tails = add_fish_tails(triangle, length=0.2)

# Visualize or process the tails and original triangle
from shapely.geometry import Polygon
original_poly = Polygon(triangle)

print("Original triangle:", original_poly)
for i, tail in enumerate(tails):
    print(f"Fish tail {i+1}:", tail)