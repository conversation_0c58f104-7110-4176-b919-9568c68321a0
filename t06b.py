#!/usr/bin/env python3
import networkx as nx
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt


def solve_constrained_tsp(points, required_paths):
    """
    Solves the TSP for the given points using a TSP approximation and enforces
    that certain required segments appear in the specified order.

    Args:
        points (dict): A dictionary mapping node IDs to (x, y) coordinates.
        required_paths (list): A list of tuples; each tuple (start, end) specifies that
                               the TSP route should immediately go from start to end.

    Returns:
        current_path (list): The TSP route (open path) adjusted to meet the constraints.
        total_distance (float): The total distance of the route.
    """
    # Create a complete graph
    G = nx.Graph()
    for node, pos in points.items():
        G.add_node(node, pos=pos)
    for i in points:
        for j in points:
            if i != j:
                dist = np.sqrt((points[i][0] - points[j][0])**2 + (points[i][1] - points[j][1])**2)
                G.add_edge(i, j, weight=dist)

    # Get an initial TSP solution (cycle) using an approximation
    try:
        initial_path = nx.approximation.traveling_salesman_problem(G, cycle=False, nodes=list(points.keys()))
    except Exception as e:
        print("Error in TSP calculation:", e)
        return None, None

    # Remove duplicate starting node if present
    if initial_path and initial_path[0] == initial_path[-1]:
        initial_path = initial_path[:-1]

    # Define a helper function to adjust the tour so that required edge (s -> e) is enforced
    def enforce_required_edge(tour, s, e):
        if s not in tour or e not in tour:
            print(f"Warning: One of the required nodes {s} or {e} is not in the tour.")
            return tour
        # Rotate the tour so that s is at index 0.
        idx = tour.index(s)
        tour = tour[idx:] + tour[:idx]
        # If the node following s is not e, remove e from its current position and insert at index 1.
        if len(tour) > 1 and tour[1] != e:
            try:
                tour.remove(e)
            except ValueError:
                pass
            tour.insert(1, e)
        return tour

    # Adjust the tour for each required segment in order
    current_path = list(initial_path)
    for s, e in required_paths:
        current_path = enforce_required_edge(current_path, s, e)

    # For an open path, do not append the start node.
    final_path = current_path

    # Recompute the total route length for open path (edges between consecutive nodes)
    total_distance = 0
    for i in range(len(final_path) - 1):
        if final_path[i+1] in G[final_path[i]]:
            total_distance += G[final_path[i]][final_path[i+1]]['weight']
        else:
            print(f"Warning: Edge {final_path[i]} -> {final_path[i+1]} not found")
            total_distance = float('inf')
            break

    return final_path, total_distance


def visualize_solution(points, path):
    """
    Visualizes the TSP route using matplotlib and NetworkX. Saves the plot to a file.

    Args:
        points (dict): A dictionary mapping node IDs to (x, y) coordinates.
        path (list): The TSP route as a list of node IDs.
    """
    if path is None:
        return

    G = nx.Graph()
    for node, pos in points.items():
        G.add_node(node, pos=pos)
    for i in range(len(path)):
        j = (i + 1) % len(path)
        G.add_edge(path[i], path[j])
    pos = nx.get_node_attributes(G, 'pos')
    plt.figure(figsize=(8, 8))
    nx.draw_networkx(G, pos, node_color='lightblue', node_size=500, with_labels=True)
    plt.title("TSP Solution with Required Segments")
    plt.axis('equal')
    plt.savefig("tsp_solution.png")
    print("Solution plot saved to tsp_solution.png")


if __name__ == "__main__":
    # Example usage with 20 sample points
    points = {
        0: (2, 7), 1: (2, 4), 2: (1, 0), 3: (5, 3),
        4: (1, 2), 5: (4, 2), 6: (6, 3), 7: (3, 5),
        8: (2, 2), 9: (4, 4), 10: (7, 2), 11: (1, 5),
        12: (5, 1), 13: (3, 1), 14: (6, 5), 15: (0, 3),
        16: (7, 7), 17: (2, 6), 18: (4, 7), 19: (6, 0)
    }

    # Required segments: must travel from 4 -> 5 and from 8 -> 2
    required_paths = [(4, 5), (8, 2)]

    path, distance = solve_constrained_tsp(points, required_paths)

    if path is not None:
        print("TSP Path with required segments:", path)
        print("Total route length: {:.2f}".format(distance))
        visualize_solution(points, path)
    else:
        print("Failed to generate a valid TSP solution.")