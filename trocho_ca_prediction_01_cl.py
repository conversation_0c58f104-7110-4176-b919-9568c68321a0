import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize_scalar

def max_cutting_angle(cutter_radius, prev_radius, next_radius, center_offset):
    """
    Calculate the maximum cutting angle when a cutter transitions between two trochoidal loops.
    """
    # Define a function to calculate cutting angle at a specific position
    def calculate_angle(theta):
        # Position of cutter center
        cutter_x = center_offset + next_radius * np.cos(theta)
        cutter_y = next_radius * np.sin(theta)
        
        # Distance from cutter center to previous loop center
        dist = np.sqrt(cutter_x**2 + cutter_y**2)
        
        # No intersection cases
        if dist > prev_radius + cutter_radius:
            # Cutter completely outside the previously machined area
            return 180.0  # Maximum cutting angle (half circle)
        if dist < abs(prev_radius - cutter_radius):
            # Cutter completely inside/outside the previously machined area
            if prev_radius > cutter_radius:
                # Cutter completely inside previously machined area
                return 0.0  # No cutting
            else:
                # Previously machined area completely inside cutter
                return 180.0  # Maximum cutting
        
        # Calculate intersection angle using the law of cosines
        cos_angle = (dist**2 + cutter_radius**2 - prev_radius**2) / (2 * dist * cutter_radius)
        cos_angle = np.clip(cos_angle, -1, 1)  # Ensure within valid range
        
        # The central angle in the cutter circle
        central_angle = 2 * np.arccos(cos_angle)
        
        # Calculate the cutting angle based on the intersection
        if dist < prev_radius:
            # Cutter mostly inside previously machined area
            cutting_angle = 180.0 - np.degrees(central_angle)
        else:
            # Cutter mostly outside previously machined area
            cutting_angle = np.degrees(central_angle)
        
        return cutting_angle
    
    # Find the position with maximum cutting angle
    def negative_angle(theta):
        return -calculate_angle(theta)
    
    # Search for maximum with multiple starting points
    max_angle = 0
    for start in np.linspace(0, 2*np.pi, 36):
        result = minimize_scalar(negative_angle, bounds=(start-np.pi/18, start+np.pi/18), method='bounded')
        angle = -result.fun
        max_angle = max(max_angle, angle)
    
    return max_angle

def calculate_max_engagements(cutter_radius, loop_radii, center_distances):
    """
    Calculate maximum cutting angles for all transitions between trochoidal loops.
    """
    max_engagements = []
    
    for i in range(len(loop_radii) - 1):
        offset = center_distances[i+1] - center_distances[i]
        angle = max_cutting_angle(cutter_radius, loop_radii[i], loop_radii[i+1], offset)
        max_engagements.append(angle)
    
    return np.array(max_engagements)

# Example data provided by user
loop_radii = np.array([40, 44])/2
center_distances = np.array([0.0, 5.0]) # Example distances

# Calculate with a cutter radius of 2.0 units
cutter_radius = 10.0
max_angles = calculate_max_engagements(cutter_radius, loop_radii, center_distances)

# Print results
print(f"Cutter radius: {cutter_radius} units")
print("\nLoop radii:", loop_radii)
print("Center distances:", center_distances)
print("\nMaximum cutting angles for each transition (degrees):")
for i, angle in enumerate(max_angles):
    print(f"Transition {i+1} → {i+2}: {angle:.2f}°")

# Return the array of maximum engagement angles
print("\nArray of maximum engagement angles:")
print(max_angles)