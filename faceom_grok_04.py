import numpy as np
import bpy
from mathutils import Vector
# Assume faceom_grok_03.py content (like rotate_vector, create_interpolation_functions etc.)
# is included here or imported
# from faceom_grok_03 import * # Or copy functions

# --- Functions from your faceom_grok_03.py (or imported) ---
# rotate_vector(vec, angle_deg)
# create_contour_points(points)
# create_interpolation_functions(points, t)
# calculate_tool_path(c, tangent, t, theta, r_tool, delta_t, beta, is_closed)
# create_line_object(coords, name)
# --- End of FACEOM functions ---

def rotate_vector(vec, angle_deg):
    """Rotate a 2D vector by angle_deg counterclockwise."""
    angle_rad = np.deg2rad(angle_deg)
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)
    rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
    return rotation_matrix @ vec


def create_contour_points(points):
    """Create a 2D contour from points."""    
    # Compute cumulative distance for parameterization
    diffs = np.diff(points, axis=0)    
    distances = np.sqrt((diffs**2).sum(axis=1))    
    t = np.zeros(points.shape[0])    
    t[1:] = np.cumsum(distances)    
    return points, t


def create_interpolation_functions(points, t):
    """Create interpolation functions for the contour."""
    def cs_x(t_val):
        return np.interp(t_val, t, points[:, 0])

    def cs_y(t_val):
        return np.interp(t_val, t, points[:, 1])
    
    def c(t_val):
        """Return the [x, y] position on the contour at parameter t."""
        return np.array([cs_x(t_val), cs_y(t_val)])
    
    def tangent(t_val, delta=1e-5):
        """Return the unit tangent vector at parameter t using finite difference."""
        # Use finite difference to approximate derivative
        t_plus = min(t_val + delta, t[-1])
        t_minus = max(t_val - delta, t[0])
        
        p_plus = np.array([cs_x(t_plus), cs_y(t_plus)])
        p_minus = np.array([cs_x(t_minus), cs_y(t_minus)])
        
        # Calculate tangent vector
        deriv = (p_plus - p_minus) / (t_plus - t_minus)
        norm = np.linalg.norm(deriv)
        
        # Return normalized tangent vector
        return deriv / norm if norm > 0 else deriv
    
    return c, tangent


def calculate_tool_path(c, tangent, t, theta=60, r_tool=8, delta_t=0.1, beta=0, is_closed=True):
    """Calculate the tool path using the FACEOM algorithm."""
    t_min = 0
    t_max = t[-1]
    
    # Initialization
    t_i = t_min
    C_i = c(t_i)          # Initial contour point
    T_i = tangent(t_i)    # Unit tangent vector at C_i
    omega = beta + theta + 90
    U_i = rotate_vector(T_i, omega)  # Direction for initial tool position
    P_i = C_i + r_tool * U_i         # Initial tool position
    PC = C_i - P_i
    alpha = 90 - theta
    V_i = rotate_vector(PC, alpha)   # Initial feed direction

    tool_path = [P_i]  # List to store tool path points
    start_point = P_i.copy()  # Store starting point for closed loops

    # Iterative Tool Path Calculation
    while t_i < t_max:
        t_i += delta_t
        if t_i > t_max:
            t_i = t_max
        C_next = c(t_i)
        
        # Find intersection of half-line P_i + s * V_i with circle at C_next
        D = P_i - C_next
        a = np.dot(V_i, V_i)
        b = 2 * np.dot(D, V_i)
        c_coeff = np.dot(D, D) - r_tool**2
        discriminant = b**2 - 4 * a * c_coeff
        
        if discriminant < 0:
            print("No intersection found")
            break
        
        sqrt_disc = np.sqrt(discriminant)
        s1 = (-b - sqrt_disc) / (2 * a)
        s2 = (-b + sqrt_disc) / (2 * a)
        positive_s = [s for s in [s1, s2] if s > 0]
        
        if not positive_s:
            print("No positive intersection found")
            break
        
        s = min(positive_s)  # Choose smallest positive s
        P_next = P_i + s * V_i
        
        # Update feed direction
        PC_next = C_next - P_next
        V_next = rotate_vector(PC_next, alpha)
        
        # Update current position and direction
        P_i = P_next
        V_i = V_next
        tool_path.append(P_i)
    
    # For closed contours, add a smooth connection back to the start
    if is_closed and len(tool_path) > 1:
        # Calculate distance between last point and first point
        dist = np.linalg.norm(tool_path[-1] - start_point)
        
        # If the gap is significant, add points to close the loop
        if dist > delta_t:
            # Create a simple linear interpolation to close the loop
            num_points = max(2, int(dist / delta_t))
            for i in range(1, num_points):
                t_param = i / num_points
                interp_point = tool_path[-1] * (1 - t_param) + start_point * t_param
                tool_path.append(interp_point)
    
    return np.array(tool_path)


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    edges = [(i, i + 1) for i in range(len(vertices) - 1)]
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_segment(p1, p2, num_points):
    """Creates points for a line segment between p1 and p2."""
    return np.array([p1 + (p2 - p1) * i / (num_points - 1) for i in range(num_points)])

def create_circular_arc(center, radius, start_angle_deg, end_angle_deg, num_points):
    """Creates points for a circular arc."""
    angles = np.linspace(np.deg2rad(start_angle_deg), np.deg2rad(end_angle_deg), num_points)
    points = np.zeros((num_points, 2))
    points[:, 0] = center[0] + radius * np.cos(angles)
    points[:, 1] = center[1] + radius * np.sin(angles)
    return points

def generate_constant_engagement_trochoidal(
    c_left, tangent_left, t_left,
    c_right, tangent_right, t_right,
    theta_engage, r_tool, delta_t_faceom,
    trochoidal_step_w, num_loops, slot_length
):
    """
    Generates a trochoidal path aiming for constant engagement.
    NOTE: This is a conceptual implementation and needs refinement.
    """
    print("Generating FACEOM guide path for Left Wall...")
    # Assume calculate_tool_path gives climb milling path for left wall
    # Beta=0 assumes path starts parallel to contour tangent at t=0
    # Need to adjust alpha calculation inside FACEOM or handle conventions carefully
    alpha_climb_left = 90 - theta_engage
    # You might need to modify calculate_tool_path to accept alpha directly,
    # or ensure its internal calculation aligns with wall/direction.
    # Let's assume it works correctly for the left wall with standard alpha.
    path_left = calculate_tool_path(c_left, tangent_left, t_left, theta_engage, r_tool, delta_t_faceom, beta=0, is_closed=False)

    print("Generating FACEOM guide path for Right Wall...")
    # For climb milling the right wall, the orientation is flipped.
    # Option 1: Reverse t_right parameterization.
    # Option 2: Adjust alpha. alpha_climb_right = -(90 - theta_engage)
    # This requires modifying calculate_tool_path or ensuring it handles this.
    # For simplicity, let's assume reversing t_right and using standard alpha works,
    # or that a modified FACEOM handles alpha appropriately.
    # We might need to run FACEOM on the right wall geometry but adjust parameters.
    # Placeholder: Generate right path - *requires careful geometric validation*
    # This step is complex and depends heavily on FACEOM's internal angle logic.
    # We might need a mirrored version or parameter adjustment.
    # As a simplification, let's mirror the left path for a straight slot:
    slot_width = np.linalg.norm(c_left(0) - c_right(0)) # Approximate width
    # This mirroring is ONLY valid for a perfectly straight slot aligned with an axis
    # path_right = path_left + np.array([0, slot_width - 2 * (r_tool * np.cos(np.deg2rad(theta_engage/2)))]) # Highly simplified approximation

    # For a proper approach, run FACEOM on the right wall contour.
    # Let's assume we have a correctly calculated path_right.
    # Example: Rerun FACEOM with adjusted angle logic if needed.
    # For now, we proceed conceptually assuming path_right exists.
    # path_right = calculate_tool_path(c_right, tangent_right, t_right, theta_engage, r_tool, delta_t_faceom, beta=180, is_closed=False) # Beta might need adjust
    # Due to complexity of right path generation, using placeholder:
    print("WARNING: Right path generation is simplified/placeholder.")
    offset_vector = c_right(t_right[len(t_right)//2]) - c_left(t_left[len(t_left)//2]) # Vector from left to right approx
    path_right = path_left + offset_vector # Very rough approximation for parallel straight slot


    # --- Step 3: Build Trochoidal Path (NEW Algorithm) ---
    final_toolpath = []
    
    # Estimate points per loop based on FACEOM path density
    points_per_faceom_segment = max(1, len(path_left) // num_loops)

    # Trochoidal loop radius (approximation, often related to w)
    # This radius affects the loop shape, needs tuning.
    loop_radius = trochoidal_step_w / 2.0 
    
    print(f"Targeting {num_loops} loops.")

    # Roll-in (simple linear for now)
    start_point = path_left[0] + np.array([-2*r_tool, 0]) # Example starting point
    final_toolpath.extend(create_line_segment(start_point, path_left[0], 10))

    current_pos = path_left[0]

    for i in range(num_loops):
        # Determine target wall (alternate)
        target_is_left = (i % 2 == 0)

        # Get ideal engagement point from FACEOM path
        idx = min(i * points_per_faceom_segment, len(path_left) - 1)
        idx_right = min(i * points_per_faceom_segment, len(path_right) - 1) # Use corresponding right index


        if target_is_left:
            p_engage_ideal = path_left[idx]
            p_opposite_ideal = path_right[idx_right] # Corresponding point near opposite wall
            # Loop starts near right, arcs left to engage left wall near p_engage_ideal
            # Center calculation needs refinement - place center so arc passes p_engage_ideal
            center_offset_dir = (p_opposite_ideal - p_engage_ideal)
            center_offset_dir = center_offset_dir / np.linalg.norm(center_offset_dir) if np.linalg.norm(center_offset_dir) > 0 else np.array([0,1])
            
            # Simplified center placement - halfway between ideal points
            loop_center = (p_engage_ideal + p_opposite_ideal) / 2.0 
            
            # Calculate arc angles (needs precise geometry)
            # Vector from center to start of cut (approx p_engage_ideal)
            vec_engage = p_engage_ideal - loop_center
            engage_angle = np.rad2deg(np.arctan2(vec_engage[1], vec_engage[0]))

            start_angle = engage_angle + 90 # Example: start 180 deg before engagement midpoint
            end_angle = engage_angle - 90 # Example: end after engagement midpoint

            arc_points = create_circular_arc(loop_center, loop_radius, start_angle, end_angle, 20) # Arc leftwards
            final_toolpath.extend(arc_points)
            current_pos = arc_points[-1]

        else: # Target is right
            p_engage_ideal = path_right[idx_right]
            p_opposite_ideal = path_left[idx] # Corresponding point near opposite wall
            # Loop starts near left, arcs right to engage right wall near p_engage_ideal
            center_offset_dir = (p_opposite_ideal - p_engage_ideal)
            center_offset_dir = center_offset_dir / np.linalg.norm(center_offset_dir) if np.linalg.norm(center_offset_dir) > 0 else np.array([0,-1])

            # Simplified center placement
            loop_center = (p_engage_ideal + p_opposite_ideal) / 2.0

             # Calculate arc angles (needs precise geometry)
            vec_engage = p_engage_ideal - loop_center
            engage_angle = np.rad2deg(np.arctan2(vec_engage[1], vec_engage[0]))

            start_angle = engage_angle - 90 # Example arc rightwards
            end_angle = engage_angle + 90 # Example arc rightwards

            arc_points = create_circular_arc(loop_center, loop_radius, start_angle, end_angle, 20) # Arc rightwards
            final_toolpath.extend(arc_points)
            current_pos = arc_points[-1]

    # Roll-out (simple linear for now)
    end_point = final_toolpath[-1] + np.array([2*r_tool, 0]) # Example end point
    final_toolpath.extend(create_line_segment(final_toolpath[-1], end_point, 10))

    return np.array(final_toolpath)


# --- Main Execution ---
def main():
    # --- Parameters ---
    tool_diameter = 10.0
    r_tool = tool_diameter / 2.0
    theta_engage = 60.0  # Desired constant engagement angle with wall
    trochoidal_step_w = 0.125 * tool_diameter # Step 'w' from Fig 9a
    slot_width = 15.0
    slot_length = 50.0
    num_loops = 10 # Number of trochoidal loops desired
    delta_t_faceom = 0.5 # Step size for FACEOM calculation

    # --- Define Slot Geometry (Simple Straight Slot) ---
    # Left Wall
    left_wall_points = np.array([[0, slot_width / 2.0], [slot_length, slot_width / 2.0]])
    points_left, t_left = create_contour_points(left_wall_points)
    c_left, tangent_left = create_interpolation_functions(points_left, t_left)

    # Right Wall
    right_wall_points = np.array([[0, -slot_width / 2.0], [slot_length, -slot_width / 2.0]])
    # For consistency or potential alpha adjustment, parameterize in same direction
    points_right, t_right = create_contour_points(right_wall_points)
    c_right, tangent_right = create_interpolation_functions(points_right, t_right)

    # --- Generate Path ---
    final_path = generate_constant_engagement_trochoidal(
        c_left, tangent_left, t_left,
        c_right, tangent_right, t_right,
        theta_engage, r_tool, delta_t_faceom,
        trochoidal_step_w, num_loops, slot_length
    )

    # --- Visualize in Blender ---
    print(f"Generated path with {len(final_path)} points.")
    create_line_object(final_path, "Constant_Engagement_Trochoidal")

    # Visualize guide paths (optional)
    # Need to run FACEOM properly inside generate_constant_engagement_trochoidal
    # to get these paths correctly.
    # create_line_object(path_left, "FACEOM_Guide_Left")
    # create_line_object(path_right, "FACEOM_Guide_Right")


if __name__ == "__main__":
    # You would run this script within Blender's scripting environment
    main()