import bpy
from mathutils import Vector
import math

import numpy as np
import trimesh # Assuming trimesh is used for PathSample
import shapely.geometry # Assuming shapely is used for Polygon

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) == 2:
        contour = shapely.geometry.Polygon(shell=geometry[0])
        path = shapely.geometry.LineString(geometry[1])
    else:
        print("Please select a valid mesh object.")
        return []
    return contour, path


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def create_circle_object(center, radius, name):
    """Create a circle object in Blender to visualize a maximally inscribed disc.

    Args:
        center: [x, y] coordinates of the circle center
        radius: Radius of the circle
        name: Name for the created circle object

    Returns:
        The created Blender object
    """
    # Create a mesh for the circle
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Number of vertices in the circle
    vertices_count = 32

    # Generate vertices around the circle
    verts = []
    for i in range(vertices_count):
        angle = 2.0 * math.pi * i / vertices_count
        x = center[0] + radius * math.cos(angle)
        y = center[1] + radius * math.sin(angle)
        verts.append(Vector((x, y, 0)))

    # Create edges around the circle (connect vertices to form a loop)
    edges = [(i, (i+1) % vertices_count) for i in range(vertices_count)]

    # Create the mesh from vertices and edges (no faces)
    mesh.from_pydata(verts, edges, [])
    mesh.update()

    # Set display properties
    mat = bpy.data.materials.new(name=f"{name}_material")
    mat.diffuse_color = (0.2, 0.8, 0.2, 1.0)  # Solid green for better visibility as a line
    obj.data.materials.append(mat)

    return obj


def boundary_distance(polygon, points):
    """
    Find the distance between a polygon's boundary and an
    array of points.

    Parameters
    -------------
    polygon : shapely.geometry.Polygon
      Polygon to query
    points : (n, 2) float
      2D points

    Returns
    ------------
    distance : (n,) float
      Minimum distance from each point to polygon boundary
    """     
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front(path, polygon, step, min_radius=5):
    path = np.asanyarray(path)
    if not trimesh.util.is_shape(path, (-1, 2)):
         raise ValueError("Path should be N Gons x 2 dimensions")
    if not isinstance(polygon, shapely.geometry.Polygon):
         raise ValueError("Polygon must be a shapely Polygon object")
    if not path.shape[0] > 1:
         raise ValueError("Path must have more than one point")

    # create a sampler object which can sample the path by distance
    sampler = trimesh.path.traversal.PathSample(path)
    # sample finely relative to the desired step size
    sample_step = step / 25.0
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)

    # get the points and boundary distance (radius) for each sample
    points = sampler.sample(sample_distances)
    radii = boundary_distance(polygon=polygon, points=points)
    print(points[:10])
    print(radii[:10])

    # store results: distance along path, and radius
    # always include the first point
    result_distances = [0.0]
    result_radii = [radii[0]]
    # store the index of the last point added
    last_idx = 0

    # loop through sampled points
    for i in range(1, len(points)):
        if radii[i] < min_radius:
            break
        # vector between candidate point and last point added
        vector = points[i] - points[last_idx]
        # calculate the edge-to-edge distance between circles
        front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[i]
        # if the distance exceeds threshold add point to results
        if front_distance >= step:
            result_distances.append(sample_distances[i])
            result_radii.append(radii[i])
            last_idx = i

    # check to make sure the last point isn't identical to the second-to-last
    # this can happen on tiny paths if points are duplicated
    if (len(result_distances) > 1 and
            result_distances[-1] - result_distances[-2] < 1e-8):
        result_distances.pop()
        result_radii.pop()

    return np.array(result_distances), np.array(result_radii)


# --- Bezier and Arc functions (keep as in the previous 'tangent fix' version) ---
def cubic_bezier(t, p0, p1, p2, p3):
    p0, p1, p2, p3 = map(np.array, [p0, p1, p2, p3])
    t = np.clip(t, 0.0, 1.0)
    return ((1 - t)**3 * p0 + 3 * (1 - t)**2 * t * p1 + 3 * (1 - t) * t**2 * p2 + t**3 * p3)


def generate_bezier_transition(p0, t0, p3, t3, num_points=20, default_alpha_beta_factor=0.5, alpha=None, beta=None):
    p0, p3 = np.array(p0), np.array(p3)
    t0_norm, t3_norm = np.linalg.norm(t0), np.linalg.norm(t3)
    _t0 = np.array(t0) / t0_norm if t0_norm > 1e-9 else np.array([1.0, 0.0])
    _t3 = np.array(t3) / t3_norm if t3_norm > 1e-9 else np.array([1.0, 0.0])
    if t0_norm <= 1e-9: print("Warning: Zero T0.")
    if t3_norm <= 1e-9: print("Warning: Zero T3.")

    if alpha is None or beta is None:
        dist = np.linalg.norm(p3 - p0)
        alpha_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        beta_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        _alpha = alpha if alpha is not None else alpha_calc
        _beta = beta if beta is not None else beta_calc
    else:
        _alpha, _beta = alpha, beta
    _alpha, _beta = max(0, _alpha), max(0, _beta)
    p1, p2 = p0 + _alpha * _t0, p3 - _beta * _t3
    if num_points < 2: return np.array([p0]) if num_points == 1 else np.empty((0, 2))
    t_values = np.linspace(0, 1, num_points)
    return np.array([cubic_bezier(t, p0, p1, p2, p3) for t in t_values])


def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, num_points=30):
    # Simplified arc generator, tangents handled outside
    center = np.array(center)
    if radius < 1e-9 or num_points < 2:
       return np.array([center]) if num_points >= 1 else np.empty((0,2))
    angles = np.linspace(angle_start_rad, angle_end_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


def normalize_angle(angle):
    # Normalize angle to be within [-pi, pi]
    return (angle + np.pi) % (2 * np.pi) - np.pi


def cubic_bezier(t, p0, p1, p2, p3):
    p0, p1, p2, p3 = map(np.array, [p0, p1, p2, p3])
    t = np.clip(t, 0.0, 1.0)
    return ((1 - t)**3 * p0 + 3 * (1 - t)**2 * t * p1 + 3 * (1 - t) * t**2 * p2 + t**3 * p3)


def generate_bezier_transition(p0, t0, p3, t3, num_points=20, default_alpha_beta_factor=0.5, alpha=None, beta=None):
    p0, p3 = np.array(p0), np.array(p3)
    t0_norm, t3_norm = np.linalg.norm(t0), np.linalg.norm(t3)
    _t0 = np.array(t0) / t0_norm if t0_norm > 1e-9 else np.array([1.0, 0.0])
    _t3 = np.array(t3) / t3_norm if t3_norm > 1e-9 else np.array([1.0, 0.0])
    # Optional warnings removed for brevity
    if alpha is None or beta is None:
        dist = np.linalg.norm(p3 - p0)
        alpha_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        beta_calc = dist * default_alpha_beta_factor if dist > 1e-9 else 0.1
        _alpha = alpha if alpha is not None else alpha_calc
        _beta = beta if beta is not None else beta_calc
    else: _alpha, _beta = alpha, beta
    _alpha, _beta = max(0, _alpha), max(0, _beta)
    p1, p2 = p0 + _alpha * _t0, p3 - _beta * _t3
    if num_points < 2: return np.array([p0]) if num_points == 1 else np.empty((0, 2))
    t_values = np.linspace(0, 1, num_points)
    return np.array([cubic_bezier(t, p0, p1, p2, p3) for t in t_values])



def main():
    geometry = get_geometry()
    islands = None    

    if len(geometry) == 1:
        polygon = geometry_to_polygon(geometry)
    elif len(geometry) == 2:
        polygon, path = geometry_to_trocho_path(geometry)
        path = np.array(path.coords, dtype=np.float64)
        # polygon, islands = geometry_to_shapely(geometry)
    else:
        print("Please select a valid mesh object.")
        exit()
    
    polygon_boundary = polygon.buffer(-5)

    edge_step = 2             # Desired distance between circle FRONTS
    min_path_radius = 5       # Minimum radius of trochoid circes
    points_per_arc = 30
    points_per_transition = 30
    # Factor for alpha/beta based on CENTER-to-CENTER distance
    transition_alpha_beta_factor = 0.01

    # --- Generate Loop Centers and Radii using advancing_front ---
    print("Running advancing_front...")
    try:
        result_distances, result_radii = advancing_front(path, polygon_boundary, edge_step, min_path_radius)
        print(result_distances)
        print(f"Found {len(result_distances)} loop points.")

        # Get the actual 2D center coordinates for the selected loops
        path_sampler = trimesh.path.traversal.PathSample(path)
        selected_centers = path_sampler.sample(result_distances)

        if len(selected_centers) < 2:
            raise ValueError("Advancing front did not generate enough points for loops.")

    except Exception as e:
        print(f"Error during advancing_front or sampling: {e}")
        # Exit or handle error appropriately
        selected_centers = np.empty((0,2)) # Ensure variable exists

    # for selected_center, radius in zip(selected_centers, result_radii):
    #     create_circle_object(selected_center, radius, "circle")

    # --- Toolpath Generation based on selected_centers ---
    full_toolpath = []
    if len(selected_centers) >= 3: # Need at least 3 points now
        # Initialize based on the *first* arc's angles
        C0, R0 = selected_centers[0], max(1e-6, result_radii[0])
        C1, R1 = selected_centers[1], max(1e-6, result_radii[1])
        C2 = selected_centers[2] # Need C2 for first arc angle calc

        Init_Start_Angle, _ = calculate_arc_angles(C0, R0, C1, R1, C2)
        start_pos = C0 + R0 * np.array([np.cos(Init_Start_Angle), np.sin(Init_Start_Angle)])
        full_toolpath.append(start_pos)
        last_pos = start_pos

        # Initial tangent: Need direction C0->C1
        dir0_vec = C1 - C0
        dir0_norm = np.linalg.norm(dir0_vec)
        dir0 = dir0_vec / dir0_norm if dir0_norm > 1e-9 else np.array([1.0, 0.0])
        # Tangent entering the first arc start point
        radius_vec_start0 = start_pos - C0
        last_tangent_out = np.array([-radius_vec_start0[1], radius_vec_start0[0]]) # Rotated +90
        norm_t0 = np.linalg.norm(last_tangent_out)
        last_tangent_out = last_tangent_out / norm_t0 if norm_t0 > 1e-9 else dir0

    # Loop through segments, calculating arc i and transition i
    # Iteration i generates arc at C_i and transition from C_i arc end to C_{i+1} arc start
    for i in range(len(selected_centers) - 1): # Stop one early, as we need i+2 inside
        # --- Define points and radii for calculation ---
        C_i = selected_centers[i]
        R_i = max(1e-6, result_radii[i])
        C_iplus1 = selected_centers[i+1]
        R_iplus1 = max(1e-6, result_radii[i+1])
        # Look ahead for angle calculation and next segment direction
        C_iplus2 = selected_centers[i+2] if i+2 < len(selected_centers) else C_iplus1 + (C_iplus1 - C_i) # Extrapolate if last point

        # --- Calculate Angles and Points for Arc i ---
        Actual_Start_Angle_i, Actual_End_Angle_i = calculate_arc_angles(C_i, R_i, C_iplus1, R_iplus1, C_iplus2)
        arc_start_pos_i = C_i + R_i * np.array([np.cos(Actual_Start_Angle_i), np.sin(Actual_Start_Angle_i)])
        arc_end_pos_i = C_i + R_i * np.array([np.cos(Actual_End_Angle_i), np.sin(Actual_End_Angle_i)])

        # Calculate tangent out of Arc i based on its actual end angle
        radius_vec_end_i = arc_end_pos_i - C_i
        actual_tangent_out_i = np.array([radius_vec_end_i[1], -radius_vec_end_i[0]]) # Rotated -90
        norm_to_i = np.linalg.norm(actual_tangent_out_i)
        actual_tangent_out_i = actual_tangent_out_i / norm_to_i if norm_to_i > 1e-9 else -(C_iplus1 - C_i) / np.linalg.norm(C_iplus1 - C_i) # Fallback tangent

        # --- Ensure path reaches start of Arc i ---
        # (Transition from previous state to arc_start_pos_i)
        if np.linalg.norm(last_pos - arc_start_pos_i) > 1e-6:
            # Tangent entering arc_start_pos_i
            radius_vec_start_i = arc_start_pos_i - C_i
            actual_tangent_in_i = np.array([-radius_vec_start_i[1], radius_vec_start_i[0]]) # Rotated +90
            norm_ti_i = np.linalg.norm(actual_tangent_in_i)
            actual_tangent_in_i = actual_tangent_in_i / norm_ti_i if norm_ti_i > 1e-9 else (C_iplus1 - C_i) / np.linalg.norm(C_iplus1 - C_i) # Fallback

            trans_to_arc_start = generate_bezier_transition(
                last_pos, last_tangent_out, # From previous state
                arc_start_pos_i, actual_tangent_in_i, # To current arc start
                num_points=max(2, points_per_transition // 2 + 1),
                default_alpha_beta_factor=transition_alpha_beta_factor
            )
            if trans_to_arc_start.shape[0] > 1: full_toolpath.extend(trans_to_arc_start[1:])
            # last_pos = arc_start_pos_i # Update position if transition added

        # --- Generate Arc i Points ---
        arc_points = generate_arc_points(C_i, R_i, Actual_Start_Angle_i, Actual_End_Angle_i, points_per_arc)
        if arc_points.shape[0] > 1:
            full_toolpath.extend(arc_points[1:]) # Skip first point (arc_start_pos_i)

        # Update position after Arc i
        last_pos = arc_end_pos_i
        last_tangent_out = actual_tangent_out_i # Tangent leaving Arc i

        # --- Calculate Target for Transition i (Start of Arc i+1) ---
        # Need C_i+3 for lookahead if possible
        C_iplus3 = selected_centers[i+3] if i+3 < len(selected_centers) else C_iplus2 + (C_iplus2 - C_iplus1) # Extrapolate
        R_iplus2 = max(1e-6, result_radii[i+2]) if i+2 < len(result_radii) else R_iplus1 # Approx radius

        # Calculate angles for Arc i+1 using its lookahead (C_i+1, R_i+1 -> C_i+2, R_i+2 using C_i+3)
        Actual_Start_Angle_iplus1, _ = calculate_arc_angles(C_iplus1, R_iplus1, C_iplus2, R_iplus2, C_iplus3)

        # Target P3: Actual start point of Arc i+1
        target_P3 = C_iplus1 + R_iplus1 * np.array([np.cos(Actual_Start_Angle_iplus1), np.sin(Actual_Start_Angle_iplus1)])

        # Target T3: Tangent entering target_P3
        radius_vec_start_iplus1 = target_P3 - C_iplus1
        target_T3 = np.array([-radius_vec_start_iplus1[1], radius_vec_start_iplus1[0]]) # Rotated +90
        norm_t3 = np.linalg.norm(target_T3)
        # Fallback T3: direction C_iplus1 -> C_iplus2
        dir_iplus1 = C_iplus2 - C_iplus1
        norm_dir_iplus1 = np.linalg.norm(dir_iplus1)
        fallback_T3 = dir_iplus1 / norm_dir_iplus1 if norm_dir_iplus1 > 1e-9 else last_tangent_out # Last resort

        target_T3 = target_T3 / norm_t3 if norm_t3 > 1e-9 else fallback_T3

        # --- Generate Transition i ---
        segment_len_i = np.linalg.norm(C_iplus1 - C_i) # Distance for alpha/beta scaling
        alpha = segment_len_i * transition_alpha_beta_factor
        beta = segment_len_i * transition_alpha_beta_factor

        transition_points = generate_bezier_transition(
            last_pos,        # P0 = End of arc i
            last_tangent_out,# T0 = Tangent leaving arc i
            target_P3,       # P3 = Actual start of arc i+1
            target_T3,       # T3 = Tangent entering arc i+1
            num_points=points_per_transition,
            alpha=alpha, beta=beta
        )
        if transition_points.shape[0] > 1: full_toolpath.extend(transition_points[1:])

        # --- Update State for Next Iteration ---
        last_pos = target_P3 # End of transition is start of next arc
        last_tangent_out = target_T3 # Tangent leaving transition = tangent entering next arc


    # --- Convert path and Plot ---
    full_toolpath = np.array(full_toolpath)
    
    # --- Convert path and Plot ---
    full_toolpath = np.array(full_toolpath)
    create_line_object(full_toolpath, "AAAAAAAAtrochoid")


if __name__ == "__main__":
    main()
