import numpy as np
import math
import time

# --- Simulation Parameters ---
# Tool
tool_diameter = 10.0  # mm (D from Fig 6)
tool_radius = tool_diameter / 2.0
num_teeth = 2      # z (from Fig 6)

# Process
axial_depth_of_cut = 5.0 # mm (ap from Fig 6)
spindle_speed_rpm = 24000 # RPM (n from Fig 6)
feed_rate_vf = 800 # mm/min (Example, use calculated if preferred)

# Simulation Grid
pixel_size = 0.1  # mm per pixel (Delta in paper discussion)
workpiece_width_mm = 100.0
workpiece_height_mm = 50.0
padding_mm = tool_diameter
sim_width_mm = workpiece_width_mm + 2 * padding_mm
sim_height_mm = workpiece_height_mm + 2 * padding_mm

# Simulation Steps
sim_step_distance = 0.2 # mm (ds - how far tool moves each sim step)
# <<< Add check for step distance >>>
if sim_step_distance <= 1e-9:
    raise ValueError("Simulation step distance must be positive.")
steps_per_mm = 1.0 / sim_step_distance
steps_per_min = feed_rate_vf / sim_step_distance
# vf_check = axial_depth_of_cut * feed_rate_vf # No longer needed for ae calculation

# --- Helper Functions ---
# (Keep helper functions world_to_pixel, create_tool_matrix,
#  create_workpiece_matrix, generate_tool_path_straight,
#  generate_tool_path_corner as they were)
def world_to_pixel(xy_mm, origin_xy_mm, p_size):
    """Converts world coordinates (mm) to pixel indices."""
    px = int(round((xy_mm[0] - origin_xy_mm[0]) / p_size))
    py = int(round((xy_mm[1] - origin_xy_mm[1]) / p_size))
    return px, py

def create_tool_matrix(diameter, p_size):
    """Creates a binary matrix representing the circular tool."""
    radius = diameter / 2.0
    tool_matrix_size_pixels = int(math.ceil(diameter / p_size))
    if tool_matrix_size_pixels % 2 == 0:
        tool_matrix_size_pixels += 1
    center_pixel = tool_matrix_size_pixels // 2
    tool_matrix = np.zeros((tool_matrix_size_pixels, tool_matrix_size_pixels), dtype=np.uint8)
    radius_pixels_sq = (radius / p_size) ** 2
    for r in range(tool_matrix_size_pixels):
        for c in range(tool_matrix_size_pixels):
            dist_sq = (r - center_pixel)**2 + (c - center_pixel)**2
            if dist_sq <= radius_pixels_sq:
                tool_matrix[r, c] = 1
    return tool_matrix

def create_workpiece_matrix(sim_w_mm, sim_h_mm, wp_w_mm, wp_h_mm, pad_mm, p_size):
    """Creates the simulation grid with the workpiece material marked."""
    sim_width_pixels = int(round(sim_w_mm / p_size))
    sim_height_pixels = int(round(sim_h_mm / p_size))
    workpiece_matrix = np.zeros((sim_height_pixels, sim_width_pixels), dtype=np.uint8)
    wp_origin_mm = (pad_mm, pad_mm)
    wp_start_px, wp_start_py = world_to_pixel((wp_origin_mm[0], wp_origin_mm[1]), (0,0), p_size)
    wp_end_px = wp_start_px + int(round(wp_w_mm / p_size))
    wp_end_py = wp_start_py + int(round(wp_h_mm / p_size))
    workpiece_matrix[wp_start_py:wp_end_py, wp_start_px:wp_end_px] = 1
    sim_origin_mm = (0.0, 0.0)
    return workpiece_matrix, sim_origin_mm

def generate_tool_path_straight(start_xy, end_xy, step_ds):
    """Generates points for a straight path with consistent step size."""
    start_vec = np.array(start_xy)
    end_vec = np.array(end_xy)
    total_dist = np.linalg.norm(end_vec - start_vec)
    
    path = [tuple(start_vec)]
    if total_dist <= 0:
        return path
        
    direction = (end_vec - start_vec) / total_dist
    for dist in np.arange(step_ds, total_dist, step_ds):
        path.append(tuple(start_vec + direction * dist))
    
    # Add end point if not already included
    if not np.allclose(path[-1], end_xy):
        path.append(tuple(end_xy))
    return path

def generate_tool_path_corner(start_xy, corner_xy, end_xy, step_ds):
    """Generates points for a path with one corner."""
    path1 = generate_tool_path_straight(start_xy, corner_xy, step_ds)
    path2 = generate_tool_path_straight(corner_xy, end_xy, step_ds)
    return path1[:-1] + path2  # Avoid duplicating the corner point
# --- Simulation Core ---
print("Creating Tool Matrix...")
tool_matrix = create_tool_matrix(tool_diameter, pixel_size)
tool_matrix_center_offset = tool_matrix.shape[0] // 2
print(f"Tool matrix shape: {tool_matrix.shape}")

print("Creating Workpiece Matrix...")
workpiece, sim_origin = create_workpiece_matrix(
    sim_width_mm, sim_height_mm,
    workpiece_width_mm, workpiece_height_mm,
    padding_mm, pixel_size
)
initial_workpiece = workpiece.copy()  # Keep initial state for reference
sim_height_pixels, sim_width_pixels = workpiece.shape
print(f"Workpiece matrix shape: {workpiece.shape}")

# --- Define Tool Path (Example: Corner Turn - Inside corner cut) ---
path_start = (padding_mm + workpiece_width_mm / 2.0, padding_mm - tool_radius)  # Start below workpiece
path_corner = (padding_mm + workpiece_width_mm / 2.0, padding_mm + workpiece_height_mm / 2.0)  # Move up to center
path_end = (padding_mm + workpiece_width_mm + tool_radius, padding_mm + workpiece_height_mm / 2.0)  # Move right out of workpiece
tool_path_mm = generate_tool_path_corner(path_start, path_corner, path_end, sim_step_distance)

print(f"Generated tool path with {len(tool_path_mm)} steps.")

# --- Run Simulation ---
results = {
    'mrr': [],
    'theta': [],
    'ae': [],
    'path_length': []
}
current_path_length = 0.0

start_time = time.time()

for i, tool_center_mm in enumerate(tool_path_mm):
    # Calculate path length
    if i > 0:
        dist_moved = np.linalg.norm(np.array(tool_center_mm) - np.array(tool_path_mm[i-1]))
        step_dist_actual = max(dist_moved, 1e-9) if dist_moved > 0 else sim_step_distance
        current_path_length += dist_moved
    else:
        step_dist_actual = sim_step_distance  # First step uses nominal distance

    results['path_length'].append(current_path_length)

    # Convert tool center mm to pixel coordinates
    tool_center_px, tool_center_py = world_to_pixel(tool_center_mm, sim_origin, pixel_size)

    # Calculate tool overlap region
    y_start = tool_center_py - tool_matrix_center_offset
    y_end = y_start + tool_matrix.shape[0]
    x_start = tool_center_px - tool_matrix_center_offset
    x_end = x_start + tool_matrix.shape[1]

    # Handle boundary conditions
    y_start_clip = max(0, y_start)
    y_end_clip = min(sim_height_pixels, y_end)
    x_start_clip = max(0, x_start)
    x_end_clip = min(sim_width_pixels, x_end)

    # Get corresponding slices
    tool_y_start_offset = y_start_clip - y_start
    tool_y_end_offset = tool_matrix.shape[0] - (y_end - y_end_clip)
    tool_x_start_offset = x_start_clip - x_start
    tool_x_end_offset = tool_matrix.shape[1] - (x_end - x_end_clip)
    
    workpiece_slice = workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip]
    tool_slice = tool_matrix[tool_y_start_offset:tool_y_end_offset,
                            tool_x_start_offset:tool_x_end_offset]

    # Initialize metrics
    mrr_inst = 0.0
    area_removed_inst_mm2 = 0.0
    ae_estimated = 0.0
    theta_deg = 0.0

    # Process overlap if valid slices
    if workpiece_slice.shape == tool_slice.shape and workpiece_slice.size > 0:
        overlap = workpiece_slice & tool_slice
        overlap_pixels = np.sum(overlap)

        if overlap_pixels > 0:
            # Calculate material removal metrics
            area_removed_inst_mm2 = overlap_pixels * (pixel_size ** 2)
            mrr_inst = area_removed_inst_mm2 * axial_depth_of_cut * feed_rate_vf / step_dist_actual
            ae_estimated = area_removed_inst_mm2 / step_dist_actual

            # Update workpiece by removing material
            workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip][overlap == 1] = 0

            # Calculate engagement angle (theta)
            if tool_radius > 1e-9:
                ae_clamped = min(max(0.0, ae_estimated), tool_diameter * 1.01)
                arg = (tool_radius - ae_clamped) / tool_radius
                arg_clamped = max(-1.0, min(arg, 1.0))
                theta_deg = math.degrees(math.acos(arg_clamped))

    # Store results
    results['mrr'].append(mrr_inst)
    results['ae'].append(ae_estimated)
    results['theta'].append(theta_deg)

end_time = time.time()
print(f"\nSimulation finished in {end_time - start_time:.2f} seconds.")

print(results['theta'][149:170])
print(tool_path_mm[149:170])
