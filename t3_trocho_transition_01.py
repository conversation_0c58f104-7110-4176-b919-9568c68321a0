from shapely.geometry import LineString
import numpy as np

# Define the LineString
P0 = (0, 0)  # Example coordinates
P1 = (10, 0)
line = LineString([P0, P1])

# Parameters
A = 1.0  # Radius at P0
B = 2.0  # Radius at P1
N = 2    # Number of oscillations

# Extract coordinates
x0, y0 = P0
x1, y1 = P1

# Compute vectors
L = np.sqrt((x1 - x0)**2 + (y1 - y0)**2)
u = [(x1 - x0) / L, (y1 - y0) / L]
n = [-(y1 - y0) / L, (x1 - x0) / L]
R = L / (2 * np.pi * N)

# Parameterize the path
t_values = np.linspace(0, 1, 100)  # 100 points for smoothness
path_coords = []
for t in t_values:
    D_t = A + (B - A) * t
    theta = 2 * np.pi * N * t
    x_t = x0 + (R * theta - D_t * np.sin(theta)) * u[0] + (-D_t * (np.cos(theta) - 1)) * n[0]
    y_t = y0 + (R * theta - D_t * np.sin(theta)) * u[1] + (-D_t * (np.cos(theta) - 1)) * n[1]
    path_coords.append((x_t, y_t))

# Create the trochoidal path as a LineString
trochoid_path = LineString(path_coords)
