import numpy as np
from scipy.optimize import root_scalar
import time

# Define parameters
a = 10
b = 5
px = 14
py = -2.3
pivot_y = -b  # -5, the bottom point

# Function to solve: lhs - 1 = 0
def f(k):
    sx = 1 + k * abs(px)
    sy = 1 + k * abs(py)
    cy = pivot_y + (0 - pivot_y) * sy  # New center y: -5 + 5 * sy
    a_new = a * sx
    b_new = b * sy
    term_x = (px / a_new) ** 2
    term_y = ((py - cy) / b_new) ** 2
    return term_x + term_y - 1

# Find root; adjust bracket if needed (expect k > 0, f(0) > 0, f large < 0)
# Test f at some points to find suitable bracket
k_low = 0
f_low = f(k_low)
k_high = 1
f_high = f(k_high)
time1 = time.time()
while f_high > 0 and k_high < 100:
    k_high *= 2
    f_high = f(k_high)

sol = root_scalar(f, bracket=[k_low, k_high], method='brentq')
k = sol.root
time2 = time.time()
print(f'Time: {time2 - time1}')

# Compute sx, sy, and new center for reference
sx = 1 + k * abs(px)
sy = 1 + k * abs(py)
cy_new = pivot_y + (0 - pivot_y) * sy

print(f"k: {k}")
print(f"sx: {sx}")
print(f"sy: {sy}")
print(f"new_center_y: {cy_new}")