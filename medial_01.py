from shapely.geometry import Polygon, LineString
import pyvoronoi

# Define a sample polygon
polygon = Polygon([(0, 0), (2, 0), (2, 2), (1, 3), (0, 2)])



# Step 1: Extract edges
coords = list(polygon.exterior.coords)[:-1]  # Exclude the repeated closing point
edges = [(coords[i], coords[i + 1]) for i in range(len(coords) - 1)]
edges.append((coords[-1], coords[0]))  # Close the polygon

pv = pyvoronoi.Pyvoronoi(100)
for start, end in edges:
    pv.AddSegment([start, end])

pv.Construct()
edges = pv.GetEdges()
vertices = pv.GetVertices()
cells = pv.GetCells()
print("Cell Count: {0}".format(len(cells)))
for c in cells:
    print("Cell contains point: {0}. Contains segment: {1}. Is open: {2}, Site Index: {3}".format(c.contains_point, c.contains_segment, c.is_open, c.site))
    print(",".join(map(str,c.vertices)))
    for sIndex in c.edges:
        print("Start Index: {0}, End Index = {1}".format(edges[sIndex].start, edges[sIndex].end))