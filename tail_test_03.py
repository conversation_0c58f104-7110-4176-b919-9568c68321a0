import numpy as np

# Define lines
l0 = np.array([[0, 0], [-1, 1]])
l1 = np.array([[0, 0], [1, 1]])

# Calculate angle and radius
v0 = l0[1] - l0[0]
v1 = l1[1] - l1[0]
angle = np.arccos(np.dot(v0, v1) / (np.linalg.norm(v0) * np.linalg.norm(v1)))
radius = 1 / (1 + np.cos(angle))

# Arc parameters
center = np.array([0, 1])
n_segments = 4
theta_start = np.pi
theta_end = 0
theta = np.linspace(theta_start, theta_end, n_segments + 1)

# Generate arc points
arc_points = np.zeros((n_segments + 1, 2))
arc_points[:, 0] = center[0] + radius * np.cos(theta)
arc_points[:, 1] = center[1] + radius * np.sin(theta)

# Output
print("Arc points:")
for i, point in enumerate(arc_points):
    print(f"Point {i}: {point}")