import numpy as np
from shapely.geometry import Point, LineString, MultiLineString
from shapely import STR<PERSON>

def points_share_endpoints(start, end, linestrings, tolerance=1e-6):
    # Check if start or end is near any start/end point of the linestrings
    for ls in linestrings:
        start = np.array(ls.coords[0])
        end = np.array(ls.coords[-1])

        if (np.linalg.norm(start_coords - start) <= tolerance or
            np.linalg.norm(start_coords - end) <= tolerance or
            np.linalg.norm(end_coords - start) <= tolerance or
            np.linalg.norm(end_coords - end) <= tolerance):
            return True

    return False

# Example usage
p0 = Point(0, 0)
p1 = Point(0.23, 0.4)

linestrings = [
    LineString([(0, 0), (1, 1)]),
    LineString([(0, 0), (3, 0)])
]

tree = STRtree(linestrings)

result = tree.query(p1)

# result = points_share_endpoints(p0, p1, linestrings)
print(result)
