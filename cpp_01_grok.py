import networkx as nx

def chinese_postman_path(G):
    """
    Solve the Chinese Postman Problem for a weighted, undirected graph.
    Returns the Eulerian circuit (list of edges) and total length.
    
    Args:
        G (nx.Graph): A connected, undirected graph with 'weight' attributes on edges.
    
    Returns:
        tuple: (circuit, total_length) where circuit is a list of (u, v) edges,
               and total_length is the sum of edge weights in the circuit.
    
    Raises:
        ValueError: If the graph is not connected.
    """
    # Ensure the graph is connected
    if not nx.is_connected(G):
        raise ValueError("Graph must be connected for the Chinese Postman Problem")
    
    # Step 1: Check if the graph is Eulerian
    if nx.is_eulerian(G):
        circuit = list(nx.eulerian_circuit(G))
        total_length = sum(G[u][v]['weight'] for u, v in circuit)
        return circuit, total_length
    
    # Step 2: Find vertices with odd degree
    odd_vertices = [v for v in G.nodes() if G.degree(v) % 2 != 0]
    
    # Step 3: Compute shortest path distances between all pairs of odd-degree vertices
    dist = {
        v: {u: nx.shortest_path_length(G, v, u, weight='weight') 
            for u in odd_vertices if u != v} 
        for v in odd_vertices
    }
    
    # Step 4: Create a complete graph K with negated weights for minimum weight matching
    K = nx.Graph()
    for u in odd_vertices:
        for v in odd_vertices:
            if u < v:  # Avoid duplicate edges since K is undirected
                K.add_edge(u, v, weight=-dist[u][v])
    
    # Step 5: Find minimum weight perfect matching by maximizing negated weights
    matching = nx.algorithms.matching.max_weight_matching(K, maxcardinality=True)
    
    # Step 6: Create a MultiGraph starting with the original graph
    MG = nx.MultiGraph(G)
    
    # Step 7: For each pair in the matching, duplicate edges along the shortest path
    for u, v in matching:
        path = nx.shortest_path(G, u, v, weight='weight')
        for i in range(len(path) - 1):
            a, b = path[i], path[i + 1]
            weight = G[a][b]['weight']
            MG.add_edge(a, b, weight=weight)
    
    # Step 8: Find the Eulerian circuit in the modified MultiGraph
    circuit = list(nx.eulerian_circuit(MG))
    
    # Step 9: Compute the total length of the circuit
    total_length = sum(MG[u][v][0]['weight'] for u, v in circuit)  # Use index [0] for first edge between vertices
    
    return circuit, total_length

# Example usage
def main():
    # Create a sample weighted graph
    G = nx.Graph()
    G.add_edge('A', 'B', weight=10)
    G.add_edge('B', 'C', weight=1)
    G.add_edge('C', 'D', weight=1)
    G.add_edge('D', 'A', weight=20)
    G.add_edge('B', 'D', weight=1)
    
    # Solve the CPP
    print(">>> circuit, total_length = chinese_postman_path(G)")
    circuit, total_length = chinese_postman_path(G)
    print(f"Circuit (edges): {circuit}")
    print(f"Total length: {total_length}")
    
    # Convert circuit to a node sequence (optional)
    path = [circuit[0][0]] + [v for u, v in circuit]  # Modified this line too
    print(f"Path (nodes): {path}")

if __name__ == "__main__":
    main()
