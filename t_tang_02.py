import numpy as np
from scipy.special import ellipe, ellipeinc
from scipy.optimize import brentq

def find_t_for_arc_length_fraction_optimized(a, b, fraction, tol=1e-8, max_iter=50):
    """Optimized version with better initial guess and precomputed values"""
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * ellipe(e_sq)
    target_arc_length = fraction * circumference
    
    # Better initial guess using empirical approximation
    # This accounts for the non-linear distribution of arc length
    if fraction <= 0.5:
        # For first half, arc length grows faster initially
        t = fraction * np.pi * (1.1 - 0.2 * fraction)
    else:
        # For second half, symmetric about pi
        t = np.pi + (fraction - 0.5) * np.pi * (0.9 + 0.2 * (fraction - 0.5))
    
    # Precompute constants
    sqrt_factor = np.sqrt(e_sq)
    
    for i in range(max_iter):
        sin_t = np.sin(t)
        sqrt_term = np.sqrt(1 - e_sq * sin_t**2)
        
        f = a * ellipeinc(t, e_sq) - target_arc_length
        
        if abs(f) < tol:
            return t
            
        # Derivative: a * sqrt(1 - e_sq * sin(t)**2)
        df_dt = a * sqrt_term
        
        # Newton step with adaptive damping for stability
        step = f / df_dt
        if abs(step) > np.pi/4:  # Limit large steps
            step = np.sign(step) * np.pi/4
            
        t -= step
        
        # Keep t in valid range [0, 2π]
        t = t % (2 * np.pi)
    
    return t

def find_t_for_arc_length_fraction_scipy(a, b, fraction):
    """Using scipy's root finding (often faster for single evaluations)"""
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * ellipe(e_sq)
    target_arc_length = fraction * circumference
    
    def objective(t):
        return a * ellipeinc(t, e_sq) - target_arc_length
    
    # Use Brent's method with good bounds
    try:
        result = brentq(objective, 0, 2*np.pi, xtol=1e-8)
        return result
    except ValueError:
        # Fallback to original method if bounds are problematic
        return find_t_for_arc_length_fraction_optimized(a, b, fraction)

def find_t_for_arc_length_fraction_vectorized(a, b, fractions, tol=1e-8, max_iter=50):
    """Vectorized version for multiple fractions at once"""
    fractions = np.asarray(fractions)
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * ellipe(e_sq)
    target_arc_lengths = fractions * circumference
    
    # Vectorized initial guess
    t = np.where(fractions <= 0.5, 
                 fractions * np.pi * (1.1 - 0.2 * fractions),
                 np.pi + (fractions - 0.5) * np.pi * (0.9 + 0.2 * (fractions - 0.5)))
    
    converged = np.zeros(len(fractions), dtype=bool)
    
    for i in range(max_iter):
        # Vectorized function evaluation
        f = a * ellipeinc(t, e_sq) - target_arc_lengths
        
        # Check convergence
        newly_converged = (np.abs(f) < tol) & ~converged
        converged |= newly_converged
        
        if np.all(converged):
            break
            
        # Only update non-converged values
        active_mask = ~converged
        if np.any(active_mask):
            sin_t = np.sin(t[active_mask])
            sqrt_term = np.sqrt(1 - e_sq * sin_t**2)
            df_dt = a * sqrt_term
            
            step = f[active_mask] / df_dt
            # Limit step size
            step = np.clip(step, -np.pi/4, np.pi/4)
            
            t[active_mask] -= step
            t[active_mask] = t[active_mask] % (2 * np.pi)
    
    return t

# Lookup table approach for very frequent calls
class EllipseArcLengthLUT:
    """Lookup table for very fast approximate solutions"""
    
    def __init__(self, a, b, n_points=1000):
        self.a = a
        self.b = b
        self.e_sq = 1.0 - b**2 / a**2
        
        # Precompute lookup table
        t_values = np.linspace(0, 2*np.pi, n_points)
        arc_lengths = a * ellipeinc(t_values, self.e_sq)
        total_circumference = arc_lengths[-1]
        
        # Normalize to fractions
        self.fractions = arc_lengths / total_circumference
        self.t_values = t_values
        
    def find_t(self, fraction, refine=True):
        """Find t using lookup table with optional refinement"""
        # Interpolate from lookup table
        t_approx = np.interp(fraction, self.fractions, self.t_values)
        
        if not refine:
            return t_approx
            
        # Refine with 1-2 Newton steps
        target_arc_length = fraction * 4.0 * self.a * ellipe(self.e_sq)
        
        t = t_approx
        for _ in range(2):  # Just 2 iterations for refinement
            f = self.a * ellipeinc(t, self.e_sq) - target_arc_length
            if abs(f) < 1e-10:
                break
            df_dt = self.a * np.sqrt(1 - self.e_sq * np.sin(t)**2)
            t -= f / df_dt
            
        return t

# Example usage and timing comparison
if __name__ == "__main__":
    import time
    
    a, b = 53.900938025541485, 53.900938025541485
    fraction = 0.3
    
    # Time original method
    start = time.time()
    result1 = find_t_for_arc_length_fraction_optimized(a, b, fraction)
    time1 = time.time() - start
    
    # Time scipy method
    start = time.time()
    result2 = find_t_for_arc_length_fraction_scipy(a, b, fraction)
    time2 = time.time() - start
    
    # Time vectorized method for multiple values
    fractions = np.linspace(0.1, 0.9, 100)
    start = time.time()
    results3 = find_t_for_arc_length_fraction_vectorized(a, b, fractions)
    time3 = time.time() - start
    
    # Time lookup table method
    lut = EllipseArcLengthLUT(a, b)
    start = time.time()
    result4 = lut.find_t(0.9851263405853155)
    time4 = time.time() - start
    
    print(f"Optimized Newton: {result1:.8f}, Time: {time1*1000:.3f}ms")
    print(f"Scipy Brent: {result2:.8f}, Time: {time2*1000:.3f}ms")
    print(f"Vectorized (100 values): Time: {time3*1000:.3f}ms")
    print(f"Lookup table: {result4:.8f}, Time: {time4*1000:.3f}ms")