import bpy
import numpy as np
import networkx as nx
import shapely
from mathutils import Vector
import time
from shapely.geometry import LineString, box
from shapely.strtree import STRtree

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None


def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None


def geometry_to_shapely(geometry):
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = False
    return contour, islands


def create_ring_object(coords, name):
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i+1)%n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects
    

def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.
    
    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects
    
    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []
    
    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))
    
    return blender_objects


def decompose_to_polygons(geom):    
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def sort_containment(arr, exterior_list):
    # Initialize a directed graph
    G = nx.DiGraph()

    # Add edges from the array
    for container, contained in zip(arr[0], arr[1]):
        G.add_edge(exterior_list[container], exterior_list[contained])

    # Ensure the graph is a DAG and compute transitive reduction
    if nx.is_directed_acyclic_graph(G):
        containment_graph = nx.transitive_reduction(G)
    else:
        containment_graph = G.copy()
        containment_graph.remove_edges_from(nx.find_cycle(containment_graph))

    # Create a new graph for the containment (direct edges)
    directed_containment_graph = nx.DiGraph()
    directed_containment_graph.add_edges_from(containment_graph.edges())
    return directed_containment_graph


def create_graph_containment(polygons_list):
    tree = shapely.STRtree(polygons_list)
    contain_information = tree.query(polygons_list, predicate='contains_properly')            
    return sort_containment(contain_information, polygons_list)


def traverse_dag_postorder(graph: nx.DiGraph, leaf_order: list) -> list:
    """ Traverse the DAG starting from nodes in leaf_order (expected to be leaf nodes) and traverse upward (post-order: predecessors are visited before their parent).
    If a node is reached from multiple leaves, it will only appear once.
    """
    visited = set()
    result = []

    def dfs(node):
        if node in visited:
            return
        # First traverse all predecessors (i.e. nodes that feed into this node)
        for parent in graph.predecessors(node):
            dfs(parent)
        visited.add(node)
        result.append(node)

    for leaf in leaf_order:
        if leaf not in graph:
            raise ValueError(f"Leaf node {leaf} not found in graph")
        dfs(leaf)
    return reversed(result)


def solve_tsp_for_nodes(nodes):
    """
    Solve Traveling Salesman Problem for a list of nodes with geometric properties.
    
    Args:
        nodes: List of objects that have centroid property (Shapely geometries)
        
    Returns:
        List of indices representing the optimal route through the nodes
    """
    # Get centroids
    centroids = [node.centroid for node in nodes]    
    # Convert Shapely points to coordinate tuples
    coords = [(c.x, c.y) for c in centroids]

    # Create a complete graph with Euclidean distances as edge weights
    tsp_graph = nx.Graph()
    for i, (x1, y1) in enumerate(coords):
        for j, (x2, y2) in enumerate(coords[i+1:], start=i+1):
            distance = shapely.geometry.Point(x1, y1).distance(shapely.geometry.Point(x2, y2))
            tsp_graph.add_edge(i, j, weight=distance)

    # Solve TSP using NetworkX's Christofides approximation
    return nx.approximation.traveling_salesman_problem(tsp_graph, cycle=False)


def segmentize(coords: np.ndarray, min_segment_length: float) -> np.ndarray:
    """
    Re-samples the input polyline or polygon by preserving original vertices and adding intermediate points
    on edges longer than min_segment_length, ensuring each resulting segment has a length of at least
    min_segment_length. Edges shorter than min_segment_length are left untouched.

    Parameters:
        coords (np.ndarray): Array of shape (N, 2) representing the polyline or polygon boundary.
        min_segment_length (float): Minimum length for each resulting segment.
        is_closed (bool): If True, treats coords as a closed polygon; if False, as an open polyline.

    Returns:
        np.ndarray: Array of points with original vertices preserved and additional points added where needed.
    """
    coords = np.asarray(coords)
    if coords.shape[0] < 2:
        return coords.copy()

    n_vertices = coords.shape[0]    
    loop_range = n_vertices
    result = []  # List to collect all points

    for i in range(loop_range):
        # Get start and end points of the current edge
        start = coords[i]
        end = coords[(i + 1) % n_vertices]
        diff = end - start
        # Calculate edge length
        edge_length = np.sqrt((diff ** 2).sum())

        # Always add the start point (original vertex)
        result.append(start[np.newaxis, :])

        if edge_length > min_segment_length:
            # Calculate the number of segments needed
            n_segments = int(np.floor(edge_length / min_segment_length))
            if n_segments < 1:
                n_segments = 1  # Minimum one segment
            segment_length = edge_length / n_segments

            # Since edge_length > min_segment_length and n_segments is floor(edge_length / min_segment_length),
            # segment_length will always be >= min_segment_length
            t = np.linspace(0, 1, n_segments + 1)[1:-1]  # Intermediate points, excluding start and end
            new_points = start + np.outer(t, diff)
            result.append(new_points)    

    # Concatenate all points into a single array
    return np.concatenate(result)


def compute_edge_metrics(inner, outer): 
    """ Given two polygons (inner and outer) as numpy arrays of shape (n_points, 2), compute for each pairing (ipX, opX): - angle_inner: between vector from inner[ipX-1] to inner[ipX] and vector from inner[ipX] to outer[opX] - angle_outer: between vector from outer[opX] to inner[ipX] and vector from outer[opX] to outer[opX+1] - edge_length: Euclidean distance from inner[ipX] to outer[opX]

    The vertices indices wrap-around (i.e., ipX-1 for the first vertex is the last vertex; opX+1 for the
    last vertex is the first vertex).

    Returns:
        A dictionary with keys:
        - 'angle_inner': a (m, n) numpy array of angles (in radians) at inner vertices.
        - 'angle_outer': a (m, n) numpy array of angles (in radians) at outer vertices.
        - 'edge_length': a (m, n) numpy array of distances.
        where m = number of vertices in inner and n = number of vertices in outer.
    """
    m = inner.shape[0]
    n = outer.shape[0]

    # Get previous vertex for each inner point (with wrap-around)
    inner_prev = inner[np.arange(m) - 1]

    # Get next vertex for each outer point (with wrap-around)
    outer_next = outer[(np.arange(n) + 1) % n]

    # Reshape inner and outer for broadcasting over all combinations
    inner_current = inner.reshape(m, 1, 2)  # shape (m, 1, 2)
    outer_points = outer.reshape(1, n, 2)     # shape (1, n, 2)

    # --- Compute inner angle at each inner vertex ---
    # Vector along inner polygon edge ending at inner_current (same for all corresponding outer points)
    v_inner = inner - inner_prev             # shape (m,2)
    # Vector from inner_current to each outer point
    v_to_outer = outer_points - inner_current # shape (m, n, 2)

    # Calculate cosine of inner angle for each (inner, outer) pair.
    dot_inner = np.sum(v_inner.reshape(m, 1, 2) * v_to_outer, axis=-1)  # shape (m, n)
    norm_v_inner = np.linalg.norm(v_inner, axis=1).reshape(m, 1)         # shape (m, 1)
    norm_v_to_outer = np.linalg.norm(v_to_outer, axis=-1)                # shape (m, n)
    cos_angle_inner = dot_inner / (norm_v_inner * norm_v_to_outer)
    cos_angle_inner = np.clip(cos_angle_inner, -1, 1)
    angle_inner = np.arccos(cos_angle_inner)  # shape (m, n)

    # --- Compute outer angle at each outer vertex ---
    # For each pair, compute the vector from outer to inner (which is the negative of above)
    v_from_outer_to_inner = inner_current - outer_points  # shape (m, n, 2)
    # For each outer vertex, compute the polygon edge leading out (constant over inner index)
    v_outer = outer_next - outer                           # shape (n, 2)
    v_outer = v_outer.reshape(1, n, 2)                       # shape (1, n, 2)

    dot_outer = np.sum(v_from_outer_to_inner * v_outer, axis=-1)  # shape (m, n)
    norm_v_from_outer_to_inner = np.linalg.norm(v_from_outer_to_inner, axis=-1)  # shape (m, n)
    norm_v_outer = np.linalg.norm(v_outer, axis=-1)   # shape (1, n)
    cos_angle_outer = dot_outer / (norm_v_from_outer_to_inner * norm_v_outer)
    cos_angle_outer = np.clip(cos_angle_outer, -1, 1)
    angle_outer = np.arccos(cos_angle_outer)  # shape (m, n)

    # --- Compute length of the imagined edge ---
    edge_length = norm_v_to_outer.copy()  # shape (m, n)

    return {
        'angle_inner': angle_inner,
        'angle_outer': angle_outer,
        'edge_length': edge_length
    }


def detect_line_intersections(lines): 
    """ Given an array 'lines' of shape (N, 2, 2) where each line is represented by two endpoints, returns an (M, 2) array of index pairs (i, j) for which line i and line j intersect. The computation is fully vectorized: bounding boxes are computed and used to prefilter candidate pairs, and then line intersection parameters are computed for the remaining candidate pairs.

    The intersection test for two segments with endpoints:
        p1 = (x1, y1), p2 = (x2, y2) for the first segment, and
        p3 = (x3, y3), p4 = (x4, y4) for the second
    uses the parameterization:

        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        t = ((x1-x3)*(y1-y2) - (y1-y3)*(x1-x2)) / denom
        u = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        
    and the segments intersect if 0<=t<=1 and 0<=u<=1, provided that |denom| > epsilon.

    This function is optimized for large numbers of line segments.
    """
    pts1 = lines[:, 0]
    pts2 = lines[:, 1]
    x1, y1 = pts1[:, 0], pts1[:, 1]
    x2, y2 = pts2[:, 0], pts2[:, 1]

    # Compute bounding boxes for each line segment.
    min_x = np.minimum(x1, x2)
    max_x = np.maximum(x1, x2)
    min_y = np.minimum(y1, y2)
    max_y = np.maximum(y1, y2)

    N = len(lines)
    # Get all unique candidate pairs (i,j) with i < j
    i_idx, j_idx = np.triu_indices(N, k=1)

    # Prefilter candidate pairs using bounding box overlap
    valid_bb = ((min_x[i_idx] <= max_x[j_idx]) & (max_x[i_idx] >= min_x[j_idx]) &
                (min_y[i_idx] <= max_y[j_idx]) & (max_y[i_idx] >= min_y[j_idx]))
    cand_i = i_idx[valid_bb]
    cand_j = j_idx[valid_bb]

    # Get candidate coordinates
    x1_c = x1[cand_i]
    y1_c = y1[cand_i]
    x2_c = x2[cand_i]
    y2_c = y2[cand_i]

    x3_c = x1[cand_j]
    y3_c = y1[cand_j]
    x4_c = x2[cand_j]
    y4_c = y2[cand_j]

    # Compute denominator for the intersection formulas.
    denom = (x1_c - x2_c) * (y3_c - y4_c) - (y1_c - y2_c) * (x3_c - x4_c)

    # Compute t and u numerators
    t_num = (x1_c - x3_c) * (y1_c - y2_c) - (y1_c - y3_c) * (x1_c - x2_c)
    u_num = (x1_c - x3_c) * (y3_c - y4_c) - (y1_c - y3_c) * (x3_c - x4_c)

    # Avoid division by very small numbers
    eps = 1e-10
    valid_denom = np.abs(denom) > eps
    # Initialize t and u to -1 (indicating no valid intersection)
    t = np.full_like(denom, -1, dtype=np.float64)
    u = np.full_like(denom, -1, dtype=np.float64)
    t[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    # Check for intersection parameters within [0, 1]
    intersect = (t >= 0) & (t <= 1) & (u >= 0) & (u <= 1)

    # Return the intersecting pairs (indices) in an (M, 2) array.
    intersect_pairs = np.vstack((cand_i[intersect], cand_j[intersect])).T
    return intersect_pairs


def valid_inner_outer_connections(inner, outer):
    """ Given two 2D polygons, inner and outer, represented as numpy arrays of shape (M,2) and (N,2), 
    this function returns an (K,2) array of index pairs (i, j) such that the straight connection from inner[i] to outer[j] 
    does not intersect any edge of the outer polygon (except at the outer vertex endpoint). The computation is fully vectorized 
    using bounding box pre-filtering and vectorized intersection tests.

    Parameters:
      inner: np.ndarray with shape (M,2)
      outer: np.ndarray with shape (N,2) representing vertices of a closed polygon (ordered), 
             where edges are defined between consecutive vertices and the last vertex connects to the first.

    Returns:
      np.ndarray of shape (K,2) where each row is a pair [i, j] indicating a valid connection from inner[i] to outer[j].
    """
    M = inner.shape[0]
    N = outer.shape[0]

    # Outer polygon edges: each edge from outer[k] to outer[(k+1)%N]
    outer_edges = np.stack([outer, np.roll(outer, -1, axis=0)], axis=1)  # shape: (N, 2, 2)

    # Build candidate connection segments from each inner vertex to each outer vertex
    candidate_P = np.repeat(inner, N, axis=0)     # shape: (M*N, 2)
    candidate_Q = np.tile(outer, (M, 1))            # shape: (M*N, 2)

    # Reshape for broadcasting
    P = candidate_P[:, None, :]   # shape: (M*N, 1, 2)
    Q = candidate_Q[:, None, :]   # shape: (M*N, 1, 2)

    # Outer edges endpoints
    A = outer_edges[:, 0][None, :, :]  # shape: (1, N, 2)
    B = outer_edges[:, 1][None, :, :]  # shape: (1, N, 2)

    # Bounding box pre-filtering
    cand_min = np.minimum(P, Q)   # shape: (M*N, 1, 2)
    cand_max = np.maximum(P, Q)
    edge_min = np.minimum(A, B)   # shape: (1, N, 2)
    edge_max = np.maximum(A, B)
    bbox_overlap = (cand_min[..., 0] <= edge_max[..., 0]) & (cand_max[..., 0] >= edge_min[..., 0]) & \
                   (cand_min[..., 1] <= edge_max[..., 1]) & (cand_max[..., 1] >= edge_min[..., 1])

    # Vectorized intersection using cross product method
    diff_PQ = Q - P           # shape: (M*N, 1, 2)
    diff_AB = B - A           # shape: (1, N, 2)
    cross = lambda a, b: a[..., 0]*b[..., 1] - a[..., 1]*b[..., 0]

    denom = cross(diff_PQ, diff_AB)   # shape: (M*N, N)
    eps = 1e-10

    diff_AP = A - P   # shape: (M*N, N, 2)
    t_num = cross(diff_AP, diff_AB)   # shape: (M*N, N)
    u_num = cross(diff_AP, diff_PQ)   # shape: (M*N, N)
    valid_denom = np.abs(denom) > eps
    t_val = np.full_like(denom, -1, dtype=np.float64)
    u_val = np.full_like(denom, -1, dtype=np.float64)
    t_val[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u_val[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    intersect = bbox_overlap & valid_denom & (t_val >= 0) & (t_val <= 1) & (u_val >= 0) & (u_val <= 1)

    # Exclude intersections due to shared endpoint (i.e., when the outer vertex equals A or B)
    shared = np.all(np.isclose(Q, A), axis=-1) | np.all(np.isclose(Q, B), axis=-1)
    intersect = intersect & (~shared)

    # A candidate connection is invalid if it intersects any outer edge
    invalid_conn = np.any(intersect, axis=1)
    valid_conn = ~invalid_conn

    valid_idx = np.nonzero(valid_conn)[0]
    inner_idx = valid_idx // N
    outer_idx = valid_idx % N
    return np.stack((inner_idx, outer_idx), axis=1)


def valid_inner_outer_connections_raycast(inner, outer, chunk_size=1000, eps=1e-8):
    """ Given two 2D polygons, inner and outer, represented as numpy arrays of shape (M,2) and (N,2), 
    this function returns an (K,2) array of index pairs (i, j) such that the ray from inner[i] to outer[j] 
    does not encounter any outer polygon edge before reaching the outer vertex (except when the outer vertex is a shared endpoint). 
    This implementation uses a raycasting approach with vectorized intersection tests and chunking for memory efficiency.

    Parameters:
      inner: np.ndarray, shape (M,2)
      outer: np.ndarray, shape (N,2), where the vertices define a closed polygon with edges between consecutive vertices and the last vertex connecting to the first.
      chunk_size: int, size of chunks to process at once to reduce memory usage
      eps: float, epsilon value for numerical comparisons

    Returns:
      np.ndarray of shape (K,2) containing valid connections as pairs [i, j].
    """
    import numpy as np
    
    # Handle empty or single-vertex polygons
    if len(inner) == 0 or len(outer) == 0 or len(inner) == 1 or len(outer) == 1:
        return np.zeros((0, 2), dtype=np.int32)
        
    # Adjust chunk size based on array sizes
    chunk_size = min(chunk_size, len(inner) * len(outer))
    M = inner.shape[0]
    N = outer.shape[0]
    
    # Pre-compute outer edges once
    outer_edges = np.stack([outer, np.roll(outer, -1, axis=0)], axis=1)  # shape: (N, 2, 2)
    edge_min = np.minimum(outer_edges[:, 0], outer_edges[:, 1])  # shape: (N, 2)
    edge_max = np.maximum(outer_edges[:, 0], outer_edges[:, 1])  # shape: (N, 2)
    
    valid_pairs = []
    
    # Process in chunks to reduce memory usage
    for i in range(0, M * N, chunk_size):
        chunk_end = min(i + chunk_size, M * N)
        chunk_inner_idx = (np.arange(i, chunk_end) // N).astype(np.int32)
        chunk_outer_idx = (np.arange(i, chunk_end) % N).astype(np.int32)
        
        # Ensure chunk indices are valid
        chunk_inner_idx = np.clip(chunk_inner_idx, 0, len(inner) - 1)
        chunk_outer_idx = np.clip(chunk_outer_idx, 0, len(outer) - 1)
        
        # Build rays for this chunk
        P = inner[chunk_inner_idx]  # shape: (chunk_size, 2)
        Q = outer[chunk_outer_idx]  # shape: (chunk_size, 2)
        
        # Skip if we don't have enough points
        if len(P) == 0 or len(Q) == 0:
            continue
            
        # Quick bounding box check before detailed intersection
        cand_min = np.minimum(P, Q)[:, None, :]  # shape: (chunk_size, 1, 2)
        cand_max = np.maximum(P, Q)[:, None, :]
        
        # Vectorized bounding box overlap test
        bbox_overlap = (cand_min[..., 0] <= edge_max[None, :, 0]) & \
                      (cand_max[..., 0] >= edge_min[None, :, 0]) & \
                      (cand_min[..., 1] <= edge_max[None, :, 1]) & \
                      (cand_max[..., 1] >= edge_min[None, :, 1])
        
        # Only process rays that have any potential intersections
        potential_intersect = np.any(bbox_overlap, axis=1)
        if not np.any(potential_intersect):
            continue
            
        # Filter to only process rays with potential intersections
        active_rays = np.nonzero(potential_intersect)[0]
        if len(active_rays) == 0:
            continue
            
        # Ensure we have valid indices
        if len(active_rays) > len(P):
            active_rays = active_rays[:len(P)]
        
        P = P[active_rays][:, None, :]
        Q = Q[active_rays][:, None, :]
        bbox_overlap = bbox_overlap[active_rays]
        
        # Compute ray-edge intersections only for potentially intersecting pairs
        diff_PQ = Q - P
        diff_AB = outer_edges[:, 1] - outer_edges[:, 0]
        
        # Vectorized cross product calculations
        cross_PQ_AB = diff_PQ[..., 0] * diff_AB[None, :, 1] - diff_PQ[..., 1] * diff_AB[None, :, 0]
        valid_denom = np.abs(cross_PQ_AB) > eps
        
        # Only compute parameters where denominator is valid and bbox overlaps
        valid_tests = bbox_overlap & valid_denom
        if not np.any(valid_tests):
            continue
            
        # Compute intersection parameters only where needed
        diff_AP = outer_edges[:, 0][None, :, :] - P
        t = np.full(valid_tests.shape, np.inf, dtype=np.float64)
        u = np.full(valid_tests.shape, np.inf, dtype=np.float64)
        
        mask = valid_tests
        if np.any(mask):
            # Ensure all arrays have matching dimensions
            mask_indices = np.where(mask)
            t[mask_indices] = (diff_AP[..., 0][mask_indices] * diff_AB[None, :, 1][mask_indices] - \
                              diff_AP[..., 1][mask_indices] * diff_AB[None, :, 0][mask_indices]) / cross_PQ_AB[mask_indices]
            u[mask_indices] = (diff_AP[..., 0][mask_indices] * diff_PQ[..., 1][mask_indices] - \
                              diff_AP[..., 1][mask_indices] * diff_PQ[..., 0][mask_indices]) / cross_PQ_AB[mask_indices]
        
        # Check for valid intersections - ensure all arrays have matching shapes
        t_check = (t >= 0) & (t < 1 - eps)
        u_check = (u >= 0) & (u <= 1)
        
        if valid_tests.shape != t_check.shape or valid_tests.shape != u_check.shape:
            continue
            
        intersect = valid_tests & t_check & u_check
        
        # Handle shared endpoints - ensure shapes match
        Q_matches_A = np.all(np.abs(Q - outer_edges[:, 0][None, :, :]) < eps, axis=-1)
        Q_matches_B = np.all(np.abs(Q - outer_edges[:, 1][None, :, :]) < eps, axis=-1)
        
        if intersect.shape != Q_matches_A.shape or intersect.shape != Q_matches_B.shape:
            continue
            
        intersect = intersect & (~(Q_matches_A | Q_matches_B))
        
        # A ray is valid if it has no intersections
        valid_ray = ~np.any(intersect, axis=1)
        if np.any(valid_ray):
            valid_chunk_idx = active_rays[valid_ray]
            chunk_inner = chunk_inner_idx[valid_chunk_idx]
            chunk_outer = chunk_outer_idx[valid_chunk_idx]
            valid_pairs.append(np.stack((chunk_inner, chunk_outer), axis=1))
    
    if not valid_pairs:
        return np.zeros((0, 2), dtype=np.int32)
    
    return np.unique(np.concatenate(valid_pairs, axis=0), axis=0)


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_polygon_angles(vertices, tolerance=0.1):
    """
    Classify vertices of a polygon based on their angles from outside perspective.
    
    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    tolerance: float, tolerance for angle classification
    
    Returns:
    tuple of three arrays containing indices of acute, straight, and obtuse angles
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)
    
    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices
    
    # Normalize vectors
    v1_norm = v1 / np.linalg.norm(v1, axis=1)[:, np.newaxis]
    v2_norm = v2 / np.linalg.norm(v2, axis=1)[:, np.newaxis]
    
    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)
    
    # Clip dot products to [-1, 1] to avoid numerical errors
    dot_products = np.clip(dot_products, -1, 1)
    
    # Calculate angles in radians
    angles = np.arccos(dot_products)
    
    # Calculate 2D cross product manually (z-component of 3D cross product)
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]
    
    # Flip angles where cross product is negative (inside angles)
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)
    
    # Classify angles with tolerance
    acute_mask = angles < np.pi - tolerance
    straight_mask = np.abs(angles - np.pi) <= tolerance
    obtuse_mask = angles > np.pi + tolerance
    
    return (
        np.where(acute_mask)[0],
        np.where(straight_mask)[0],
        np.where(obtuse_mask)[0]
    )
    

def main():
    offset = 0.005
    passes = 100
    island_passes = 1
    base_name = "OffsetObject"
    buffer_attributes = {
        "join_style": 'mitre',
        "mitre_limit": 1.1
        }

    geometry = get_geometry()    
    contour, islands = geometry_to_shapely(geometry)
    polygon = geometry_to_polygon(geometry)
    exterior_list = []
    interior_list = []    
     
    # new_coords = segmentize(geometry[0], 0.000005)
    # print(new_coords.shape)    
    # # shapely_to_blender(shapely.geometry.Polygon(new_coords), "segmentized", interiors=False)    
    # time_start = time.time()
    # ac, rt, obt = classify_vertex_angles(new_coords, tolerance=2.0)       
    # print("Classification time:", time.time() - time_start)
    # # print("Acute vertices:\n", np.sort(ac))
    # # print("Straight vertices:\n", np.sort(rt))
    # # print("Obtuse vertices:\n", np.sort(obt))
    # time_start = time.time()
    # clas = classify_polygon_angles(new_coords)    
    # print("Classification time:", time.time() - time_start)
    # # print("Classified angles:\n", clas[2])

    # print(geometry)
    # metrics = compute_edge_metrics(geometry[1], geometry[0])
    # # print(metrics['angle_inner'])
    # print(metrics['angle_outer'].shape)
    # print(metrics['edge_length'])
    time_start = time.time()
    connetions = valid_inner_outer_connections_raycast(geometry[1], geometry[0], chunk_size=500)
    print("valid_inner_outer_connections time:", time.time() - time_start)
    # # print(connetions[connetions[:, 0] == 12])
    # time_start = time.time()
    # connections_raycast = valid_inner_outer_connections_raycast(geometry[1], geometry[0])
    # print("valid_inner_outer_connections_raycast time:", time.time() - time_start)
       
    # print(np.diff(connections_raycast, connetions))
    # print(connetions[connetions[:, 0] == 12])
    # print(connections_raycast[connections_raycast[:, 0] == 12])

    
    # for pass_num in range(passes):
    #     buffer_offset = offset * (pass_num + 1)
    #     buffer = polygon.buffer(-buffer_offset, **buffer_attributes)
    #     if buffer.is_empty:
    #         print(f'{pass_num} passes completed')
    #         break

    #     exteriors, interiors = decompose_to_polygons(buffer)
    #     exterior_list.extend(exteriors)
    #     interior_list.extend(interiors)

    # contour_graph = create_graph_containment(exterior_list)    
    # deepest_nodes = [node for node in contour_graph.nodes if contour_graph.out_degree(node) == 0]    

    # if len(deepest_nodes) < 2:
    #     shapely_list_to_blender(contour_graph, "order")
    #     return

    # tsp_indices = solve_tsp_for_nodes(deepest_nodes)
    # tsp_route = [deepest_nodes[i] for i in tsp_indices]    
    
    # # post_order = traverse_dag_postorder(contour_graph, tsp_route)
    # shapely_list_to_blender(post_order, "order")
    # coords = np.array(contour.exterior.coords)
    # min_length = 0.05
    # new_coords = segmentize(coords, min_length)    
    # new_contour = shapely.geometry.Polygon(new_coords)
    # shapely_to_blender(new_contour, "segmentized", interiors=False)


if __name__ == "__main__":
    main()