import numpy as np

class CachedEllipseFitter:
    """
    Cached ellipse fitter that pre-computes expensive operations on points array.
    Use this when the same points array is used many times with different parameters.
    """
    
    def __init__(self, points):
        """
        Initialize with points array and pre-compute what we can.
        
        Args:
            points: Nx2 array of points
        """
        self.points = points
        self.n_points = len(points)
        
        # Pre-extract x and y coordinates (avoid repeated indexing)
        self.x_coords = points[:, 0].copy()  # Make contiguous copy
        self.y_coords = points[:, 1].copy()
        
        # Pre-allocate arrays for intermediate computations
        self._dx = np.empty(self.n_points, dtype=np.float64)
        self._dy = np.empty(self.n_points, dtype=np.float64)
        self._x_rot = np.empty(self.n_points, dtype=np.float64)
        self._y_rot = np.empty(self.n_points, dtype=np.float64)
        self._val = np.empty(self.n_points, dtype=np.float64)
        
        # Cache for frequently used computations
        self._last_center = None
        self._last_theta = None
        self._cached_cos_theta = None
        self._cached_sin_theta = None
        self._translation_cached = False
        self._rotation_cached = False
    
    def objective_function(self, center, theta, semi_major, semi_minor, front=True):
        """
        Optimized objective function using cached computations.
        
        Args:
            center: 2-element array of ellipse center
            theta: rotation angle in radians
            semi_major: semi-major axis length
            semi_minor: semi-minor axis length
            front: if True, use mean squared error; if False, use min-based error
        
        Returns:
            Objective function value
        """
        # Cache trigonometric functions if theta hasn't changed
        if self._last_theta != theta:
            self._cached_cos_theta = np.cos(theta)
            self._cached_sin_theta = np.sin(theta)
            self._last_theta = theta
            self._rotation_cached = False
        
        # Cache translation if center hasn't changed
        if self._last_center is None or not np.array_equal(self._last_center, center):
            np.subtract(self.x_coords, center[0], out=self._dx)
            np.subtract(self.y_coords, center[1], out=self._dy)
            self._last_center = center.copy()
            self._translation_cached = True
            self._rotation_cached = False
        
        # Apply rotation using cached values and pre-allocated arrays
        if not self._rotation_cached:
            # x_rot = dx * cos_theta + dy * sin_theta
            np.multiply(self._dx, self._cached_cos_theta, out=self._x_rot)
            np.multiply(self._dy, self._cached_sin_theta, out=self._val)  # temp use
            np.add(self._x_rot, self._val, out=self._x_rot)
            
            # y_rot = -dx * sin_theta + dy * cos_theta
            np.multiply(self._dx, -self._cached_sin_theta, out=self._y_rot)
            np.multiply(self._dy, self._cached_cos_theta, out=self._val)  # temp use
            np.add(self._y_rot, self._val, out=self._y_rot)
            
            self._rotation_cached = True
        
        # Pre-compute squared axis ratios
        inv_major_sq = 1.0 / (semi_major * semi_major)
        inv_minor_sq = 1.0 / (semi_minor * semi_minor)
        
        # Compute ellipse equation values using pre-allocated array
        np.multiply(self._x_rot, self._x_rot, out=self._val)
        np.multiply(self._val, inv_major_sq, out=self._val)
        
        # Add y component
        y_rot_sq = self._y_rot * self._y_rot  # temporary
        y_rot_sq *= inv_minor_sq
        np.add(self._val, y_rot_sq, out=self._val)
        
        if front:
            # Use pre-allocated array for difference computation
            np.subtract(self._val, 1.0, out=self._val)
            np.multiply(self._val, self._val, out=self._val)
            return np.mean(self._val)
        else:
            return (1.0 - np.min(self._val)) ** 2
    
    def clear_cache(self):
        """Clear all cached values."""
        self._last_center = None
        self._last_theta = None
        self._translation_cached = False
        self._rotation_cached = False

# Factory function for backward compatibility
def create_cached_fitter(points):
    """Create a cached fitter instance."""
    return CachedEllipseFitter(points)

def optimized_objective_function(points, center, theta, semi_major, semi_minor, front=True):
    """
    Optimized ellipse fitting objective function with multiple performance improvements.
    
    Args:
        points: Nx2 array of points
        center: 2-element array of ellipse center
        theta: rotation angle in radians
        semi_major: semi-major axis length
        semi_minor: semi-minor axis length
        front: if True, use mean squared error; if False, use min-based error
    
    Returns:
        Objective function value
    """
    # Pre-compute trigonometric values (avoid recomputation)
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    
    # Vectorized translation and rotation in one step
    # Avoid creating intermediate arrays when possible
    dx = points[:, 0] - center[0]
    dy = points[:, 1] - center[1]
    
    # Apply rotation directly without matrix multiplication
    x_rot = dx * cos_theta + dy * sin_theta
    y_rot = -dx * sin_theta + dy * cos_theta
    
    # Pre-compute squared axis ratios to avoid repeated division
    inv_major_sq = 1.0 / (semi_major * semi_major)
    inv_minor_sq = 1.0 / (semi_minor * semi_minor)
    
    # Compute ellipse equation values
    val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
    
    if front:
        # Use more numerically stable computation
        diff = val - 1.0
        return np.mean(diff * diff)
    else:
        return (1.0 - np.min(val)) ** 2

# Alternative version using einsum for very large datasets
def optimized_objective_einsum(points, center, theta, semi_major, semi_minor, front=True):
    """
    Version optimized for very large point arrays using einsum.
    """
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    
    # Translation
    translated = points - center
    
    # Rotation using einsum (can be faster for large arrays)
    rot_matrix = np.array([[cos_theta, sin_theta], [-sin_theta, cos_theta]])
    rotated = np.einsum('ij,kj->ki', rot_matrix, translated)
    
    # Ellipse equation
    inv_axes_sq = np.array([1/(semi_major**2), 1/(semi_minor**2)])
    val = np.einsum('ij,j->i', rotated**2, inv_axes_sq)
    
    if front:
        diff = val - 1.0
        return np.mean(diff * diff)
    else:
        return (1.0 - np.min(val)) ** 2

# Memory-efficient version for streaming/chunked processing
def optimized_objective_chunked(points, center, theta, semi_major, semi_minor, front=True, chunk_size=10000):
    """
    Memory-efficient version that processes points in chunks.
    Useful for very large point arrays that don't fit in memory.
    """
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    inv_major_sq = 1.0 / (semi_major * semi_major)
    inv_minor_sq = 1.0 / (semi_minor * semi_minor)
    
    n_points = len(points)
    
    if front:
        total_error = 0.0
        for i in range(0, n_points, chunk_size):
            chunk = points[i:i+chunk_size]
            dx = chunk[:, 0] - center[0]
            dy = chunk[:, 1] - center[1]
            
            x_rot = dx * cos_theta + dy * sin_theta
            y_rot = -dx * sin_theta + dy * cos_theta
            
            val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
            diff = val - 1.0
            total_error += np.sum(diff * diff)
        
        return total_error / n_points
    else:
        min_val = float('inf')
        for i in range(0, n_points, chunk_size):
            chunk = points[i:i+chunk_size]
            dx = chunk[:, 0] - center[0]
            dy = chunk[:, 1] - center[1]
            
            x_rot = dx * cos_theta + dy * sin_theta
            y_rot = -dx * sin_theta + dy * cos_theta
            
            val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
            chunk_min = np.min(val)
            if chunk_min < min_val:
                min_val = chunk_min
        
        return (1.0 - min_val) ** 2

# Numba-optimized version (requires: pip install numba)
try:
    from numba import njit
    
    @njit
    def optimized_objective_numba(points, center, theta, semi_major, semi_minor, front=True):
        """
        Numba JIT-compiled version for maximum performance.
        First call will be slower due to compilation, subsequent calls very fast.
        """
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        inv_major_sq = 1.0 / (semi_major * semi_major)
        inv_minor_sq = 1.0 / (semi_minor * semi_minor)
        
        n_points = points.shape[0]
        
        if front:
            total_error = 0.0
            for i in range(n_points):
                dx = points[i, 0] - center[0]
                dy = points[i, 1] - center[1]
                
                x_rot = dx * cos_theta + dy * sin_theta
                y_rot = -dx * sin_theta + dy * cos_theta
                
                val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
                diff = val - 1.0
                total_error += diff * diff
            
            return total_error / n_points
        else:
            min_val = np.inf
            for i in range(n_points):
                dx = points[i, 0] - center[0]
                dy = points[i, 1] - center[1]
                
                x_rot = dx * cos_theta + dy * sin_theta
                y_rot = -dx * sin_theta + dy * cos_theta
                
                val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
                if val < min_val:
                    min_val = val
            
            return (1.0 - min_val) ** 2

except ImportError:
    print("Numba not available. Install with 'pip install numba' for maximum performance.")
    optimized_objective_numba = None

# Benchmark function to test different versions
def benchmark_versions(points, center, theta, semi_major, semi_minor, front=True):
    """
    Benchmark different optimization versions.
    """
    import time
    
    versions = [
        ("Optimized", optimized_objective_function),
        ("Einsum", optimized_objective_einsum),
        ("Chunked", optimized_objective_chunked),
    ]
    
    if optimized_objective_numba is not None:
        versions.append(("Numba", optimized_objective_numba))
    
    results = {}
    for name, func in versions:
        start_time = time.time()
        result = func(points, center, theta, semi_major, semi_minor, front)
        end_time = time.time()
        
        results[name] = {
            'time': end_time - start_time,
            'result': result
        }
        print(f"{name}: {end_time - start_time:.6f}s, result: {result:.6f}")
    
    return results

# Advanced caching with partial parameter updates
class AdvancedCachedFitter:
    """
    More sophisticated caching that can handle partial parameter updates efficiently.
    """
    
    def __init__(self, points):
        self.fitter = CachedEllipseFitter(points)
        
        # Cache for axis-dependent computations
        self._last_axes = None
        self._cached_inv_major_sq = None
        self._cached_inv_minor_sq = None
    
    def objective_function(self, center, theta, semi_major, semi_minor, front=True):
        """Objective function with advanced caching."""
        
        # Cache axis calculations
        current_axes = (semi_major, semi_minor)
        if self._last_axes != current_axes:
            self._cached_inv_major_sq = 1.0 / (semi_major * semi_major)
            self._cached_inv_minor_sq = 1.0 / (semi_minor * semi_minor)
            self._last_axes = current_axes
        
        return self.fitter.objective_function_with_cached_axes(
            center, theta, self._cached_inv_major_sq, self._cached_inv_minor_sq, front
        )

# Add method to CachedEllipseFitter for axis caching
def add_axis_caching_method():
    def objective_function_with_cached_axes(self, center, theta, inv_major_sq, inv_minor_sq, front=True):
        """Version that accepts pre-computed inverse axis squares."""
        # Cache trigonometric functions
        if self._last_theta != theta:
            self._cached_cos_theta = np.cos(theta)
            self._cached_sin_theta = np.sin(theta)
            self._last_theta = theta
            self._rotation_cached = False
        
        # Cache translation
        if self._last_center is None or not np.array_equal(self._last_center, center):
            np.subtract(self.x_coords, center[0], out=self._dx)
            np.subtract(self.y_coords, center[1], out=self._dy)
            self._last_center = center.copy()
            self._rotation_cached = False
        
        # Apply rotation
        if not self._rotation_cached:
            np.multiply(self._dx, self._cached_cos_theta, out=self._x_rot)
            np.multiply(self._dy, self._cached_sin_theta, out=self._val)
            np.add(self._x_rot, self._val, out=self._x_rot)
            
            np.multiply(self._dx, -self._cached_sin_theta, out=self._y_rot)
            np.multiply(self._dy, self._cached_cos_theta, out=self._val)
            np.add(self._y_rot, self._val, out=self._y_rot)
            
            self._rotation_cached = True
        
        # Compute ellipse equation values
        np.multiply(self._x_rot, self._x_rot, out=self._val)
        np.multiply(self._val, inv_major_sq, out=self._val)
        
        y_rot_sq = self._y_rot * self._y_rot
        y_rot_sq *= inv_minor_sq
        np.add(self._val, y_rot_sq, out=self._val)
        
        if front:
            np.subtract(self._val, 1.0, out=self._val)
            np.multiply(self._val, self._val, out=self._val)
            return np.mean(self._val)
        else:
            return (1.0 - np.min(self._val)) ** 2
    
    CachedEllipseFitter.objective_function_with_cached_axes = objective_function_with_cached_axes

add_axis_caching_method()

# Example usage and testing
if __name__ == "__main__":
    # Generate test data
    np.random.seed(42)
    n_points = 100000
    points = np.random.randn(n_points, 2) * 10
    
    print("=== Cached vs Non-cached Performance Comparison ===")
    
    # Test parameters
    centers = [np.array([1.0, 2.0]), np.array([1.1, 2.1]), np.array([1.0, 2.0])]
    thetas = [0.5, 0.6, 0.5]
    semi_majors = [3.0, 3.1, 3.0]
    semi_minors = [2.0, 2.1, 2.0]
    
    import time
    
    # Test cached version
    print(f"\nTesting with {n_points} points, {len(centers)} iterations")
    
    # Cached version
    fitter = CachedEllipseFitter(points)
    start_time = time.time()
    cached_results = []
    for center, theta, major, minor in zip(centers, thetas, semi_majors, semi_minors):
        result = fitter.objective_function(center, theta, major, minor, front=True)
        cached_results.append(result)
    cached_time = time.time() - start_time
    
    # Non-cached version
    start_time = time.time()
    regular_results = []
    for center, theta, major, minor in zip(centers, thetas, semi_majors, semi_minors):
        result = optimized_objective_function(points, center, theta, major, minor, front=True)
        regular_results.append(result)
    regular_time = time.time() - start_time
    
    print(f"Cached version: {cached_time:.6f}s")
    print(f"Regular version: {regular_time:.6f}s")
    print(f"Speedup: {regular_time/cached_time:.2f}x")
    print(f"Results match: {np.allclose(cached_results, regular_results)}")
    
    # Memory usage optimization demo
    print(f"\n=== Memory Usage ===")
    print(f"Original points array: {points.nbytes / 1024 / 1024:.2f} MB")
    print(f"Cached fitter overhead: ~{(fitter._dx.nbytes + fitter._dy.nbytes + fitter._x_rot.nbytes + fitter._y_rot.nbytes + fitter._val.nbytes) / 1024 / 1024:.2f} MB")
    
    # Advanced caching demo
    print(f"\n=== Advanced Caching Demo ===")
    advanced_fitter = AdvancedCachedFitter(points)
    
    start_time = time.time()
    for center, theta, major, minor in zip(centers, thetas, semi_majors, semi_minors):
        result = advanced_fitter.objective_function(center, theta, major, minor, front=True)
    advanced_time = time.time() - start_time
    
    print(f"Advanced cached version: {advanced_time:.6f}s")
    print(f"Speedup over regular: {regular_time/advanced_time:.2f}x")