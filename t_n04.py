import numpy as np

# Function to check if a point is inside a polygon using ray casting
def is_point_in_polygon(point, polygon):
    x, y = point
    n = len(polygon)
    inside = False
    p1x, p1y = polygon[0]
    for i in range(n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    return inside

# Function to check if a point is inside an ellipse
def is_point_in_ellipse(point, h, k, a, b, theta):
    x, y = point
    # Transform point to ellipse coordinates
    cos_theta, sin_theta = np.cos(theta), np.sin(theta)
    x_prime = cos_theta * (x - h) + sin_theta * (y - k)
    y_prime = -sin_theta * (x - h) + cos_theta * (y - k)
    return (x_prime / a)**2 + (y_prime / b)**2 <= 1

# Main function to estimate relative engagement (overlap)
def estimate_engagement(polygon, ellipses, num_samples=72):
    # Compute bounding box for rejection sampling
    xs, ys = zip(*polygon)
    min_x, max_x = min(xs), max(xs)
    min_y, max_y = min(ys), max(ys)
    
    # Generate uniform random points inside the polygon
    samples = []
    while len(samples) < num_samples:
        x = np.random.uniform(min_x, max_x)
        y = np.random.uniform(min_y, max_y)
        if is_point_in_polygon((x, y), polygon):
            samples.append((x, y))
    
    # For each ellipse, count how many samples are inside it
    engagements = []
    for ellipse in ellipses:
        h, k, a, b, theta = ellipse  # Unpack ellipse params: center (h,k), semi-axes a,b, rotation theta (radians)
        count = sum(1 for point in samples if is_point_in_ellipse(point, h, k, a, b, theta))
        engagements.append(count)
    
    return engagements  # Higher value means larger relative engagement

# Example usage
# Define a concave polygon (list of (x,y) points, clockwise or counterclockwise)
polygon = [(0,0), (5,0), (5,3), (3,1), (2,3), (0,2)]  # Example concave shape

# Series of ellipses: each as (h, k, a, b, theta)
ellipses = [
    (2.5, 1.5, 2.0, 1.0, 0.0),      # Ellipse 1: no rotation
    (3.0, 2.0, 1.5, 1.0, np.pi/4),  # Ellipse 2: rotated 45 degrees
    (1.0, 1.0, 3.0, 2.0, 0.0)       # Ellipse 3: larger
]

# Compute relative engagements
import time
time1 = time.time()
relative_engagements = estimate_engagement(polygon, ellipses)
time2 = time.time()
print(f'Time: {time2-time1}')
print("Relative engagements:", relative_engagements)
# To compare: higher number means bigger engagement (overlap)