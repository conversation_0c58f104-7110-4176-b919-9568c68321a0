import numpy as np
import bpy
import shapely
from shapely.geometry import LineString, Point

def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj




def main():

    # # geometry = get_geometry()
    # # polygon = geometry_to_polygon(geometry)
    # # contour, islands = geometry_to_shapely(geometry)
    
    # # Example array of circle centers (each row: [c_x, c_y])
    # centers = np.array([
    #     [0, 0],
    #     [5, 2],
    #     [10, 0]
    # ])

    # # Parameters for the rolling circle and trochoid
    # r = 0.5   # radius of the rolling circle
    # d = 1.0   # distance from center to the traced point (d = r gives a cycloid)

    # # Parameter t for the trochoid (adjust its range as desired)
    # t = np.linspace(0, 2 * np.pi, 20)

    # # For each circle, plot the trochoid and the circle (for reference)
    # for cx, cy in centers:
    #     # Trochoid equations, offset by the circle's center
    #     x = cx + r * t - d * np.sin(t)
    #     y = cy + r - d * np.cos(t)
    #     # create_line_object(np.vstack([x, y]).T, f"AAAAtrochoid_{cx}_{cy}")        

    def cubic_bezier(P0, P1, P2, P3, t):
        return (1 - t)**3 * P0 + 3 * (1 - t)**2 * t * P1 + 3 * (1 - t) * t**2 * P2 + t**3 * P3

    # Define the circles
    center1 = np.array([0, 0])
    radius1 = 1.0
    center2 = np.array([3, 0])
    radius2 = 1.0

    # Choose angles (in radians) on the circles for the blending points:
    theta1 = np.deg2rad(45)   # 45 degrees on the first circle
    theta2 = np.deg2rad(135)  # 135 degrees on the second circle

    # Calculate points on the circles
    P0 = center1 + radius1 * np.array([np.cos(theta1), np.sin(theta1)])
    P3 = center2 + radius2 * np.array([np.cos(theta2), np.sin(theta2)])

    # Compute tangent directions at these points (perpendicular to the radius)
    tangent1 = np.array([-np.sin(theta1), np.cos(theta1)])
    tangent2 = np.array([-np.sin(theta2), np.cos(theta2)])

    # Adjust the control points with an arbitrary factor to tune the smoothness
    factor = 0.5  # You can try different values to get the best blend
    P1 = P0 + factor * tangent1
    P2 = P3 - factor * tangent2

    # Generate points on the Bézier curve
    t_values = np.linspace(0, 1, 100)
    curve_points = np.array([cubic_bezier(P0, P1, P2, P3, t) for t in t_values])

    # Create a Shapely LineString for the curve (if further geometric operations are needed)
    curve = LineString(curve_points)
    create_line_object(curve_points, "AAAAAtrochoid")


if __name__ == "__main__":
    main()