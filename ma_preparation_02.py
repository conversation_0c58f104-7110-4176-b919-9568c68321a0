import bpy
from mathutils import Vector
import numpy as np
import shapely.geometry
from shapely.ops import nearest_points
import pyvoronoi
import math
from typing import List, Tuple, Optional, Dict


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []
    
    geometry_list = []
    for obj in selected_objects:
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    edges = [(i, i + 1) for i in range(len(vertices) - 1)]
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) != 2:
        print("Please select exactly two mesh objects.")
        return None, None
    return shapely.geometry.Polygon(shell=geometry[0]), shapely.geometry.LineString(geometry[1])


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def minimum_distance_to_boundary(point, contour, islands=None):
    """
    Calculate the minimum distance from a point to the boundary of the shape,
    considering both contour and islands.

    Parameters:
    -----------
    point : array-like
        Coordinates of the point [x, y]
    contour : shapely.geometry.Polygon7
        Exterior boundary of the shape
    islands : shapely.geometry.MultiPolygon, optional
        Interior islands/holes

    Returns:
    --------
    float : Minimum distance to any part of the boundary
    tuple : Coordinates of the closest boundary point
    """
    # Convert point to shapely Point
    point_geom = shapely.geometry.Point(point)

    # Get distance to exterior boundary
    min_dist = point_geom.distance(contour.exterior)
    closest_point = nearest_points(point_geom, contour.exterior)[1].coords[0]

    # Check islands if they exist
    if islands and not islands.is_empty:
        # For each island, check if distance is smaller
        for island in islands.geoms:
            island_dist = point_geom.distance(island.exterior)
            if island_dist < min_dist:
                min_dist = island_dist
                closest_point = nearest_points(point_geom, island.exterior)[1].coords[0]

    return min_dist, closest_point


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def classify_vertices_by_angles_and_short_edges(vertices, min_edge_length=0.1):
    """
    Classify vertices of a polygon based on their angles and identify vertices
    connected to short edges.

    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    min_edge_length: float, minimum edge length threshold

    Returns:
    tuple of three arrays containing:
    - indices of convex angles
    - indices of concave angles
    - indices of vertices connected to edges shorter than min_edge_length
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)

    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices

    # Calculate edge lengths
    prev_edge_lengths = np.linalg.norm(v1, axis=1)
    next_edge_lengths = np.linalg.norm(v2, axis=1)

    # Find vertices connected to short edges
    short_edges_mask = (prev_edge_lengths < min_edge_length) | (next_edge_lengths < min_edge_length)
    short_edges_indices = np.where(short_edges_mask)[0]

    # Normalize vectors
    v1_norm = v1 / prev_edge_lengths[:, np.newaxis]
    v2_norm = v2 / next_edge_lengths[:, np.newaxis]

    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)
    dot_products = np.clip(dot_products, -1, 1)

    # Calculate angles in radians
    angles = np.arccos(dot_products)

    # Calculate 2D cross product
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]

    # Flip angles where cross product is negative
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)

    # Classify angles (from inside perspective)
    concave_mask = angles < np.pi
    convex_mask = angles > np.pi

    return (np.where(convex_mask)[0],
            np.where(concave_mask)[0],
            short_edges_indices)


def find_cut_point(p1: np.ndarray, p2: np.ndarray, polygon: object, islands: object,
                   min_radius: float, sample_step: float = 0.05) -> Optional[np.ndarray]:
    """Calculate the point where radius crosses min_radius threshold."""
    segment_length = np.linalg.norm(p2 - p1)
    num_samples = max(int(segment_length / sample_step), 2)
    
    t_range = np.linspace(0, 1, num_samples)
    sample_points = p1[None, :] + t_range[:, None] * (p2 - p1)[None, :]
    
    sample_radii = calculate_sample_radii(polygon, islands, sample_points)
    
    return find_crossing_point(p1, p2, sample_radii, min_radius, t_range)


def calculate_sample_radii(polygon: object, islands: object, sample_points: np.ndarray) -> np.ndarray:
    """Calculate radii at sample points considering both polygon and islands."""
    contour_distances = boundary_distance(polygon, sample_points)
    
    if islands and not islands.is_empty:
        island_distances = np.array([boundary_distance(island, sample_points) 
                                   for island in islands.geoms])
        island_min_distances = np.min(island_distances, axis=0)
        return np.minimum(contour_distances, island_min_distances)
    
    return contour_distances


def find_crossing_point(p1: np.ndarray, p2: np.ndarray, sample_radii: np.ndarray, 
                       min_radius: float, t_range: np.ndarray) -> Optional[np.ndarray]:
    """Find the point where radius crosses min_radius threshold."""
    crossings = np.where(np.diff(sample_radii >= min_radius))[0]
    
    if not len(crossings):
        return None
        
    idx = crossings[0]
    t = interpolate_crossing(t_range, sample_radii, min_radius, idx)
    return p1 + t * (p2 - p1)


def interpolate_crossing(t_range: np.ndarray, sample_radii: np.ndarray, 
                        min_radius: float, idx: int) -> float:
    """Interpolate the exact crossing point."""
    return (t_range[idx] + 
            (min_radius - sample_radii[idx]) / 
            (sample_radii[idx + 1] - sample_radii[idx]) * 
            (t_range[idx + 1] - t_range[idx]))


def process_edge_segment(edge: Dict, points: List[np.ndarray], radii: List[float], 
                        minimum_radius: float) -> Dict:
    """Create a new edge segment."""
    new_edge = edge.copy()
    new_edge['points'] = np.array(points)
    new_edge['radii'] = np.array(radii)
    return new_edge


def separate_edges_by_radius(edges: List[Dict], minimum_radius: float) -> Tuple[List[Dict], List[Dict]]:
    """Separates edges based on radius criteria."""
    stage1_filtered, all_small_radius = initial_edge_separation(edges, minimum_radius)
    normal_edges, small_radius_edges = separate_edge_types(stage1_filtered, minimum_radius)
    
    shortened_edges = process_small_radius_edges(small_radius_edges, minimum_radius)
    
    bigger_radius_edges = normal_edges + shortened_edges['bigger']
    smaller_radius_edges = all_small_radius + shortened_edges['smaller']
    
    return bigger_radius_edges, smaller_radius_edges


def initial_edge_separation(edges: List[Dict], minimum_radius: float) -> Tuple[List[Dict], List[Dict]]:
    """Perform initial separation of edges based on radius."""
    stage1_filtered = []
    all_small_radius = []
    
    for edge in edges:
        if any(radius >= minimum_radius for radius in edge['radii']):
            stage1_filtered.append(edge)
        else:
            all_small_radius.append(edge)
            
    return stage1_filtered, all_small_radius


def separate_edge_types(edges: List[Dict], minimum_radius: float) -> Tuple[List[Dict], List[Dict]]:
    """Separate edges that need splitting from those that don't."""
    normal_edges = []
    small_radius_edges = []
    
    for edge in edges:
        if all(radius >= minimum_radius for radius in edge['radii']):
            normal_edges.append(edge)
        else:
            small_radius_edges.append(edge)
            
    return normal_edges, small_radius_edges


def process_small_radius_edges(edges: List[Dict], minimum_radius: float) -> Dict[str, List[Dict]]:
    """Process edges that need splitting."""
    shortened_edges = {'bigger': [], 'smaller': []}
    
    for edge in edges:
        process_single_edge(edge, minimum_radius, shortened_edges)
    
    return shortened_edges


def process_single_edge(edge: Dict, minimum_radius: float, 
                       shortened_edges: Dict[str, List[Dict]]) -> None:
    """Process a single edge that needs splitting."""
    points = edge['points']
    radii = edge['radii']
    current_segment = {'points': [], 'radii': []}
    
    for i in range(len(points) - 1):
        process_edge_points(edge, points[i:i+2], radii[i:i+2], 
                          minimum_radius, current_segment, shortened_edges)

    if current_segment['points']:
        finalize_segment(edge, current_segment, minimum_radius, shortened_edges)


def process_edge_points(edge: Dict, points: List[np.ndarray], radii: List[float],
                       minimum_radius: float, current_segment: Dict, 
                       shortened_edges: Dict[str, List[Dict]]) -> None:
    """
    Process points in an edge segment and update segments based on radius criteria.
    
    Args:
        edge: Original edge dictionary
        points: List of two consecutive points to process
        radii: List of radii corresponding to the points
        minimum_radius: Threshold radius value
        current_segment: Dictionary holding current segment being built
        shortened_edges: Dictionary storing processed segments
    """
    p1, p2 = points
    r1, r2 = radii
    
    # Start new segment if needed
    if not current_segment['points']:
        current_segment['points'].append(p1)
        current_segment['radii'].append(r1)
    
    # Both points have radius >= minimum_radius
    if r1 >= minimum_radius and r2 >= minimum_radius:
        current_segment['points'].append(p2)
        current_segment['radii'].append(r2)
    
    # Both points have radius < minimum_radius
    elif r1 < minimum_radius and r2 < minimum_radius:
        if current_segment['points']:
            finalize_segment(edge, current_segment, minimum_radius, shortened_edges)
        shortened_edges['smaller'].append(
            process_edge_segment(edge, [p1, p2], [r1, r2], minimum_radius)
        )
    
    # Transition from bigger to smaller radius
    elif r1 >= minimum_radius and r2 < minimum_radius:
        cut_point = find_cut_point(p1, p2, edge.get('polygon'), edge.get('islands'), minimum_radius)
        if cut_point is not None:
            current_segment['points'].append(cut_point)
            current_segment['radii'].append(minimum_radius)
            finalize_segment(edge, current_segment, minimum_radius, shortened_edges)
    
    # Transition from smaller to bigger radius
    else:  # r1 < minimum_radius and r2 >= minimum_radius
        cut_point = find_cut_point(p2, p1, edge.get('polygon'), edge.get('islands'), minimum_radius)
        if cut_point is not None:
            current_segment['points'].append(cut_point)
            current_segment['radii'].append(minimum_radius)


def finalize_segment(edge: Dict, current_segment: Dict, 
                    minimum_radius: float, shortened_edges: Dict[str, List[Dict]]) -> None:
    """
    Finalize and store a segment in the appropriate category.
    
    Args:
        edge: Original edge dictionary
        current_segment: Dictionary containing points and radii for current segment
        minimum_radius: Threshold radius value
        shortened_edges: Dictionary storing processed segments
    """
    if len(current_segment['points']) >= 2:
        new_segment = process_edge_segment(
            edge,
            current_segment['points'],
            current_segment['radii'],
            minimum_radius
        )
        
        # Determine category based on radii
        if all(r >= minimum_radius for r in current_segment['radii']):
            shortened_edges['bigger'].append(new_segment)
        else:
            shortened_edges['smaller'].append(new_segment)
    
    # Reset current segment
    current_segment['points'] = []
    current_segment['radii'] = []


def find_max_radius_point(edges: List[Dict]) -> Tuple[np.ndarray, float]:
    """Find the point with the maximum radius from edges."""
    max_radius = float('-inf')
    max_point = None
    
    for edge in edges:
        max_radius_idx = np.argmax(edge['radii'])
        if edge['radii'][max_radius_idx] > max_radius:
            max_radius = edge['radii'][max_radius_idx]
            max_point = edge['points'][max_radius_idx]
    
    return max_point, max_radius


def voronoi(contour, islands=None, min_edge_length=0.001): #min_edge_length units: 1 = 1 millimeter
    pv = pyvoronoi.Pyvoronoi(100)

    # Process the contour (exterior polygon)
    coords = list(contour.exterior.coords)
    print(f"Adding contour with {len(coords)} points")

    # Add segments for each edge of the contour (excluding the last point which is same as first)
    for i in range(len(coords) - 1):
        start = coords[i]
        end = coords[i + 1]
        pv.AddSegment([start, end])

    # Process islands (holes) if provided
    if islands and not islands.is_empty:
        # If islands is a MultiPolygon, iterate through its polygons
        for island in islands.geoms:
            hole_coords = list(island.exterior.coords)
            print(f"Adding island with {len(hole_coords)} points")

            # Add segments for each edge of the island
            for i in range(len(hole_coords) - 1):
                start = hole_coords[i]
                end = hole_coords[i + 1]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    print("Cell Count: {0}".format(len(cells)))

    # Collect all edges data first
    linear_edges = []
    curved_edges = []

    # Use sets for efficient duplicate detection
    seen_linear_hashes = set()
    seen_curved_hashes = set()

    for cIndex, cell in enumerate(cells):
        for i, edge_idx in enumerate(cell.edges):
            e = edges[edge_idx]
            # Skip edges with invalid vertices
            if e.start == -1 or e.end == -1:
                continue

            startVertex = vertices[e.start]
            endVertex = vertices[e.end]

            # Store edge data with identifier for naming
            if e.is_linear:
                # Create tuple form of start and end points for hashing
                start_point = (startVertex.X, startVertex.Y)
                end_point = (endVertex.X, endVertex.Y)

                # create_line_object([start_point, end_point], f"{cIndex}_{i}")

                # Create hash from the edge points (sorted for direction independence)
                edge_tuple = tuple(sorted([start_point, end_point]))
                edge_hash = hash(edge_tuple)

                # Only add if not seen before
                if edge_hash not in seen_linear_hashes:
                    linear_edges.append({
                        'points': [list(start_point), list(end_point)],
                        'name': f"linear_edge_{cIndex}_{i}"
                    })
                    seen_linear_hashes.add(edge_hash)
            else:
                # Calculate distance only for curved edges
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                         (startVertex.Y - endVertex.Y)**2) / 10
                points = pv.DiscretizeCurvedEdge(edge_idx, max_distance)
                points_array = np.array([[p[0], p[1]] for p in points])
                
                # For curved edges, hash the first and last point
                # Use the points_array we already created
                if len(points_array) >= 2:
                    # Convert array points to tuples for hashing
                    start_point = tuple(points_array[0])
                    end_point = tuple(points_array[-1])
                    start_end_tuple = tuple(sorted([start_point, end_point]))
                    curve_hash = hash(start_end_tuple)

                    # Only add if not seen before
                    if curve_hash not in seen_curved_hashes:
                        curved_edges.append({
                            'points': points_array,
                            'name': f"curved_edge_{cIndex}_{i}"
                        })
                        seen_curved_hashes.add(curve_hash)

    # Extract medial axis - these are the interior edges of the Voronoi diagram
    medial_edges = []

    # Calculate convex and concave indices for the contour
    contour_coords = np.array(contour.exterior.coords[:-1])  # Exclude last point which is same as first
    concave_indices, convex_indices, short_edges = classify_vertices_by_angles_and_short_edges(contour_coords, min_edge_length=min_edge_length)
    print(f"Concave indices in contour: {concave_indices}")

    # Dictionary to store all convex and concave vertices by polygon
    convex_concave_vertices = {}
    convex_concave_vertices['contour'] = {
        'convex': convex_indices,
        'concave': concave_indices,
        'short_edges': short_edges
    }

    # If islands exist, calculate convex and concave indices for each island (since islands have reversed orientation)
    if islands and not islands.is_empty:
        convex_concave_vertices['islands'] = {}
        for i, island in enumerate(islands.geoms):
            island_coords = np.array(island.exterior.coords[:-1])
            convex_indices, concave_indices, short_edges = classify_vertices_by_angles_and_short_edges(island_coords, min_edge_length=min_edge_length)
            convex_concave_vertices['islands'][i] = {
                'convex': convex_indices,
                'concave': concave_indices,
                'short_edges': short_edges
            }

    # We'll use the polygon to determine which edges are inside
    for edge in linear_edges + curved_edges:
        points = np.array(edge['points'])
        # Skip processing if no points
        if len(points) == 0:
            continue

        # Check if midpoint of the edge is inside the contour but outside any islands
        midpoint = points.mean(axis=0) if len(points) > 1 else points[0]
        midpoint_point = shapely.geometry.Point(midpoint)

        # Edge is part of medial axis if it's inside contour but outside all islands
        is_inside = contour.contains(midpoint_point)

        # Check if point is in any island
        is_in_island = False
        if islands and not islands.is_empty:
            is_in_island = islands.contains(midpoint_point)

        # Skip if edge is not inside or is in an island
        if not (is_inside and not is_in_island):
            continue

        # Check if this edge is connected to a convex vertex in contour or islands
        # Find closest contour vertex to first and last point of edge
        start_point = points[0]
        end_point = points[-1] if len(points) > 1 else points[0]

        # Check connection to contour convex vertices
        contour_convex = convex_concave_vertices['contour']['convex']
        contour_short_edges = convex_concave_vertices['contour']['short_edges']
        # convex_short = np.concatenate((contour_convex, contour_short_edges))
        convex_short = contour_convex
        if len(convex_short) > 0:
            # Calculate distances to all contour vertices
            start_dists = np.sqrt(np.sum((contour_coords - start_point)**2, axis=1))
            end_dists = np.sqrt(np.sum((contour_coords - end_point)**2, axis=1))

            # Find closest vertex indices
            closest_to_start = np.argmin(start_dists)
            closest_to_end = np.argmin(end_dists)

            # Skip if either end is too close to a convex vertex or connected to a short edge
            # Use a small threshold distance to determine if points are "connected"
            threshold = 0.01

            if closest_to_start in convex_short and start_dists[closest_to_start] < threshold:
                continue

            if closest_to_end in convex_short and end_dists[closest_to_end] < threshold:
                continue

        # Check connection to island convex vertices (since islands have reversed orientation)
        skip_edge = False
        if islands and not islands.is_empty and 'islands' in convex_concave_vertices:
            # Check each island for convex vertices
            for i, island in enumerate(islands.geoms):
                if i not in convex_concave_vertices['islands'] or len(convex_concave_vertices['islands'][i]) == 0:
                    continue

                island_coords = np.array(island.exterior.coords[:-1])
                island_convex = convex_concave_vertices['islands'][i]['convex']  # These are now convex indices

                # Calculate distances to all island vertices
                start_dists = np.sqrt(np.sum((island_coords - start_point)**2, axis=1))
                end_dists = np.sqrt(np.sum((island_coords - end_point)**2, axis=1))

                # Find closest vertex indices
                closest_to_start = np.argmin(start_dists)
                closest_to_end = np.argmin(end_dists)

                threshold = 0.01

                # If either endpoint is too close to a convex vertex, skip this edge entirely
                if (closest_to_start in island_convex and start_dists[closest_to_start] < threshold) or \
                   (closest_to_end in island_convex and end_dists[closest_to_end] < threshold):
                    skip_edge = True
                    break  # Stop checking other islands

        # Skip this edge if it's connected to any island convex vertex
        if skip_edge:
            continue

        # Since we already filtered duplicates during initial collection,
        # we can directly calculate radius and add to medial edges
        # Calculate radius (distance to boundary) for each point on this edge
        radii = []
        boundary_points = []

        for point in points:
            radius, boundary_pt = minimum_distance_to_boundary(point, contour, islands)
            # radius, boundary_pt = (0,0)
            radii.append(radius)
            boundary_points.append(boundary_pt)

        # Add to medial edges with radius information
        medial_edges.append({
            'points': points,
            'name': edge['name'].replace('edge', 'medial'),
            'radii': radii,
            'boundary_points': boundary_points
        })

    print(f"Found {len(medial_edges)} medial axis edges")
    return medial_edges


def main():
    geometry = get_geometry()
    islands = None

    if len(geometry) == 1:
        polygon = geometry_to_polygon(geometry)
    elif len(geometry) == 2:
        polygon, path = geometry_to_trocho_path(geometry)
        path = np.array(path.coords)
    else:
        print("Please select a valid mesh object.")
        return
        
    medial_edges = voronoi(polygon, islands, min_edge_length=0.01)
    bigger_radius_edges, smaller_radius_edges = separate_edges_by_radius(medial_edges, minimum_radius=10)
    
    for i, edge in enumerate(bigger_radius_edges):
        create_line_object(edge['points'], f"edge_{i}")

if __name__ == "__main__":
    main()
