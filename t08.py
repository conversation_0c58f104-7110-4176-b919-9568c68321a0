
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from scipy.optimize import minimize, brentq
from scipy.integrate import quad

# A helper function to plot ellipses for visualization
def plot_ellipse(ax, center, width, height, angle_deg, **kwargs):
    """Plots a single ellipse on a given matplotlib axis."""
    e = Ellipse(xy=center, width=width, height=height, 
                angle=angle_deg, **kwargs)
    ax.add_patch(e)


def solve_for_circle(R, t0, t1):
    """
    Finds the inscribed ellipse for the simple circle case using a pure
    algebraic solution.
    
    Args:
        R (float): Radius of the main circle.
        t0 (float): Perimeter fraction for the start of line 0 (0.0 to 1.0).
        t1 (float): Perimeter fraction for the start of line 1 (0.0 to 1.0).
        
    Returns:
        dict: Parameters of the inscribed ellipse (center, width, height, angle).
    """
    # 1. Find the angles of the normal lines
    theta0 = 2 * np.pi * t0
    theta1 = 2 * np.pi * t1

    # 2. Find the angle of the bisector and the half-angle of the wedge
    # This defines the rotation needed to simplify the problem
    rotation_angle_rad = (theta0 + theta1) / 2.0
    alpha = (theta1 - theta0) / 2.0
    
    # Ensure alpha is the smaller angle
    if alpha > np.pi / 2:
        alpha = np.pi - alpha
        rotation_angle_rad += np.pi
        
    # Tangent of the half-angle, which is the slope of the line in rotated coords
    m = np.tan(alpha)

    # 3. Solve the system of equations derived from tangency and osculating conditions
    # See derivation in thought process: a = (R * tan^2(alpha)) / (1 + 2*tan^2(alpha))
    tan_sq_alpha = m**2
    a = (R * tan_sq_alpha) / (1 + 2 * tan_sq_alpha)
    
    # The other parameters follow from 'a'
    # b^2 = R*a (osculating condition)
    b_sq = R * a
    b = np.sqrt(b_sq)
    
    # cx' = R - a (tangency at vertex)
    cx_rotated = R - a
    
    # 4. The ellipse is defined in the rotated frame. Now rotate it back.
    center_rotated = np.array([cx_rotated, 0])
    
    # Rotation matrix to go back to the original frame
    cos_rot = np.cos(rotation_angle_rad)
    sin_rot = np.sin(rotation_angle_rad)
    rot_matrix = np.array([[cos_rot, -sin_rot], 
                           [sin_rot,  cos_rot]])
    
    center_original = rot_matrix @ center_rotated

    return {
        "center": center_original,
        "width": 2 * a,
        "height": 2 * b,
        "angle_deg": np.rad2deg(rotation_angle_rad)
    }

# --- Example Usage for the Circle ---
R = 10.0
t0, t1 = 0.0, 0.1 # Start points on perimeter (e.g., 0% and 10%)

# Calculate the parameters
inscribed_ellipse_params = solve_for_circle(R, t0, t1)

# Visualization
fig, ax = plt.subplots(figsize=(8, 8))
ax.set_aspect('equal')

# Plot main circle
main_circle = plt.Circle((0, 0), R, color='black', fill=False, lw=2, label='Main Circle')
ax.add_patch(main_circle)

# Plot the normal lines
theta0_vis = 2 * np.pi * t0
theta1_vis = 2 * np.pi * t1
p0 = R * np.array([np.cos(theta0_vis), np.sin(theta0_vis)])
p1 = R * np.array([np.cos(theta1_vis), np.sin(theta1_vis)])
ax.plot([0, p0[0]], [0, p0[1]], 'r-', lw=2, label='Line 0 (Normal)')
ax.plot([0, p1[0]], [0, p1[1]], 'b-', lw=2, label='Line 1 (Normal)')

# Plot the inscribed ellipse
plot_ellipse(ax, 
             inscribed_ellipse_params['center'],
             inscribed_ellipse_params['width'],
             inscribed_ellipse_params['height'],
             inscribed_ellipse_params['angle_deg'],
             facecolor='cyan', edgecolor='blue', alpha=0.7, label='Inscribed Ellipse')

ax.set_xlim(-R-1, R+1)
ax.set_ylim(-R-1, R+1)
ax.grid(True)
ax.legend()
ax.set_title("Algebraic Solution for a Circle")
plt.show()

print("Inscribed Ellipse Parameters (Circle Case):")
print(inscribed_ellipse_params)

# --- Helper functions for the General Ellipse case ---

def get_ellipse_point_from_angle(A, B, angle_rad):
    """Gets (x,y) point on ellipse from parametric angle."""
    return np.array([A * np.cos(angle_rad), B * np.sin(angle_rad)])

def get_ellipse_perimeter_point(A, B, t_fraction):
    """
    Finds the point (x,y) on an ellipse at a given fraction of its perimeter.
    This requires numerical integration as there's no closed-form solution.
    """
    # Integrand for arc length
    perimeter_integrand = lambda angle: np.sqrt((A * np.sin(angle))**2 + (B * np.cos(angle))**2)
    
    # Total perimeter
    total_perimeter, _ = quad(perimeter_integrand, 0, 2 * np.pi)
    target_length = t_fraction * total_perimeter
    
    # Find the parametric angle 'theta' that corresponds to this arc length
    # We want to find theta such that integral from 0 to theta equals target_length
    error_func = lambda angle: quad(perimeter_integrand, 0, angle)[0] - target_length
    
    # Use a root-finding algorithm to find the angle
    try:
        theta, r = brentq(error_func, 0, 2 * np.pi, full_output=True)
        if not r.converged:
            raise RuntimeError("Root finding for perimeter point did not converge.")
    except ValueError: # Happens if target_length is 0 or total_perimeter
        theta = 0 if t_fraction == 0 else 2*np.pi
        
    return get_ellipse_point_from_angle(A, B, theta)

def get_ellipse_normal_line(A, B, point):
    """
    Gets the normal line Ax+By+C=0 at a point (x0, y0) on the ellipse.
    The normal vector (A, B) points "outward". We will flip it to point inward.
    """
    x0, y0 = point
    if abs(x0) < 1e-9: # Point is on the y-axis
        return np.array([1.0, 0.0, -x0])
    if abs(y0) < 1e-9: # Point is on the x-axis
        return np.array([0.0, 1.0, -y0])

    # Gradient of F(x,y)=x^2/A^2 + y^2/B^2 - 1 is the normal vector
    # n = (2x0/A^2, 2y0/B^2). We can simplify by dividing by 2.
    normal_vec = np.array([x0 / A**2, y0 / B**2])
    
    # We want the normal to point "inward". The gradient points outward.
    # So we flip the sign.
    normal_vec = -normal_vec
    
    # Line equation: normal_vec[0]*(x-x0) + normal_vec[1]*(y-y0) = 0
    # A*x + B*y - (A*x0 + B*y0) = 0
    A_line, B_line = normal_vec
    C_line = -np.dot(normal_vec, point)
    
    return np.array([A_line, B_line, C_line])

def solve_for_general_ellipse(A, B, t0, t1, num_points=30):
    """
    Finds the inscribed ellipse for the general case using numerical optimization.
    
    Args:
        A (float): Semi-major axis of the main ellipse.
        B (float): Semi-minor axis of the main ellipse.
        t0 (float): Perimeter fraction for the start of line 0.
        t1 (float): Perimeter fraction for the start of line 1.
        num_points (int): Number of points to discretize the new ellipse for constraints.

    Returns:
        dict: Parameters of the inscribed ellipse from the optimizer.
    """
    # 1. Define the geometry of the container
    p0 = get_ellipse_perimeter_point(A, B, t0)
    p1 = get_ellipse_perimeter_point(A, B, t1)
    
    line0_params = get_ellipse_normal_line(A, B, p0)
    line1_params = get_ellipse_normal_line(A, B, p1)
    
    # 2. Define objective function (we want to MAXIMIZE area, so MINIMIZE -area)
    def objective(params):
        # params: [cx, cy, a, b, phi_rad]
        a, b = params[2], params[3]
        return -a * b

    # 3. Define the constraints
    def constraints(params):
        cx, cy, a, b, phi_rad = params
        
        # Avoid non-physical solutions
        if a <= 0 or b <= 0:
            return -np.inf

        # Generate points on the new ellipse's boundary
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        
        # Rotate and translate points to world coordinates
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        
        # Constraint values must be >= 0 for the solution to be valid.
        
        # Constraint from Line 0: A0*x + B0*y + C0 >= 0
        l0 = line0_params
        dist_to_line0 = ellipse_pts_world @ l0[:2] + l0[2]
        
        # Constraint from Line 1: A1*x + B1*y + C1 >= 0
        l1 = line1_params
        dist_to_line1 = ellipse_pts_world @ l1[:2] + l1[2]

        # Constraint from Main Ellipse: (x/A)^2 + (y/B)^2 - 1 <= 0
        # We flip the sign so that inside is positive, matching the lines
        x_pts, y_pts = ellipse_pts_world.T
        dist_to_main_ellipse = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        
        # Return all constraint values concatenated
        return np.concatenate([dist_to_line0, dist_to_line1, dist_to_main_ellipse])

    # 4. Create an initial guess (x0) for the optimizer
    # Midpoint of the perimeter arc is a good reference
    p_mid = get_ellipse_perimeter_point(A, B, (t0 + t1) / 2.0)
    # A point slightly inward from the midpoint
    initial_center = p_mid * 0.9
    initial_a = A * 0.1
    initial_b = B * 0.1
    # Guess the angle based on the orientation of the midpoint
    initial_phi = np.arctan2(p_mid[1], p_mid[0])
    
    x0 = np.array([initial_center[0], initial_center[1], initial_a, initial_b, initial_phi])

    # 5. Run the optimizer
    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 500, 'disp': False})

    if not res.success:
        print(f"Warning: Optimizer may not have converged. Message: {res.message}")

    final_params = res.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        "success": res.success
    }

# We will reuse the helper functions from the previous answer:
# plot_ellipse, get_ellipse_point_from_angle, get_ellipse_perimeter_point
# So make sure they are defined in your script.

def polyline_to_line_equations(polyline, ref_point):
    """
    Converts a polyline into a list of oriented line equations.
    
    Args:
        polyline (np.array): A list of vertices, shape (num_verts, 2).
        ref_point (np.array): A 2D point known to be on the "inside".
        
    Returns:
        list: A list of tuples, where each tuple is (A, B, C, sign)
              representing an oriented line segment equation.
    """
    line_eqs = []
    if len(polyline) < 2:
        return []
        
    for i in range(len(polyline) - 1):
        p1 = polyline[i]
        p2 = polyline[i+1]
        
        # Line equation from two points: (y1-y2)x + (x2-x1)y + (x1y2 - x2y1) = 0
        A = p1[1] - p2[1]
        B = p2[0] - p1[0]
        C = p1[0] * p2[1] - p2[0] * p1[1]
        
        # Normalize to prevent very large/small values
        norm = np.sqrt(A**2 + B**2)
        if norm < 1e-9: continue # Points are identical, skip segment
        A, B, C = A/norm, B/norm, C/norm

        # Determine the "inside" sign using the reference point
        ref_val = A * ref_point[0] + B * ref_point[1] + C
        # We want the constraint to be '>= 0', so we store the sign
        # that makes the reference point satisfy this.
        sign = 1.0 if ref_val >= 0 else -1.0
        
        line_eqs.append((A, B, C, sign))
        
    return line_eqs

def solve_for_polyline_boundaries(A, B, polyline0, polyline1, num_points=30):
    """
    Finds the inscribed ellipse for general polyline boundaries using
    numerical optimization.
    
    Args:
        A (float): Semi-major axis of the main ellipse.
        B (float): Semi-minor axis of the main ellipse.
        polyline0 (np.array): Vertices of the first boundary polyline.
        polyline1 (np.array): Vertices of the second boundary polyline.
        num_points (int): Discretization points for the new ellipse.

    Returns:
        dict: Parameters of the inscribed ellipse.
    """
    # 1. Define a reference point for orienting the line segments
    # A point between the start of the polylines, pushed inward.
    p0_start = polyline0[0]
    p1_start = polyline1[0]
    ref_point = (p0_start + p1_start) / 2.0 * 0.95 # Push slightly towards origin
    
    # 2. Pre-calculate the line equations for the polylines
    lines0 = polyline_to_line_equations(polyline0, ref_point)
    lines1 = polyline_to_line_equations(polyline1, ref_point)
    
    # 3. Objective function (maximize area by minimizing -area)
    def objective(params):
        # params: [cx, cy, a, b, phi_rad]
        return -params[2] * params[3]

    # 4. Constraints function
    def constraints(params):
        cx, cy, a, b, phi_rad = params
        if a <= 1e-4 or b <= 1e-4: return -np.inf

        # Generate points on the new ellipse's boundary
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        x_pts, y_pts = ellipse_pts_world.T

        all_constraints = []

        # Constraint 1: Inside the main ellipse
        # (x/A)^2 + (y/B)^2 <= 1  -->  1 - ((x/A)^2 + (y/B)^2) >= 0
        main_ellipse_constraint = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        all_constraints.append(main_ellipse_constraint)
        
        # Constraint 2: Inside Polyline 0
        for A_l, B_l, C_l, sign in lines0:
            constraint = sign * (A_l * x_pts + B_l * y_pts + C_l)
            all_constraints.append(constraint)

        # Constraint 3: Inside Polyline 1
        for A_l, B_l, C_l, sign in lines1:
            constraint = sign * (A_l * x_pts + B_l * y_pts + C_l)
            all_constraints.append(constraint)
            
        return np.concatenate(all_constraints)

    # 5. Initial Guess
    initial_center = ref_point
    dist_between_starts = np.linalg.norm(p0_start - p1_start)
    initial_a = dist_between_starts * 0.2
    initial_b = dist_between_starts * 0.1
    vec = p1_start - p0_start
    initial_phi = np.arctan2(vec[1], vec[0])

    x0 = np.array([initial_center[0], initial_center[1], initial_a, initial_b, initial_phi])

    # 6. Run Optimizer
    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 1000, 'disp': False, 'ftol': 1e-9})
    
    if not res.success:
        print(f"Warning: Optimizer may not have converged. Message: {res.message}")

    final_params = res.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        "success": res.success
    }

# --- Example Usage ---

# Define main ellipse
A, B = 10.0, 7.0
t0_frac, t1_frac = 0.95, 0.2  # Fractions of perimeter for start points

p0_start = get_ellipse_perimeter_point(A, B, t0_frac)
p1_start = get_ellipse_perimeter_point(A, B, t1_frac)

# Define the polylines. Let's make them a bit "wobbly"
# Polyline 0
polyline0 = np.array([
    p0_start,
    p0_start * 0.8 + np.array([0.5, -0.5]), # Move inward and slightly sideways
    p0_start * 0.5 + np.array([-0.2, 0.3]),
    p0_start * 0.3
])

# Polyline 1
polyline1 = np.array([
    p1_start,
    p1_start * 0.7 + np.array([-0.3, 0.6]),
    p1_start * 0.4
])


# Solve the problem
inscribed_params_poly = solve_for_polyline_boundaries(A, B, polyline0, polyline1)
'''

# --- Visualization ---
fig, ax = plt.subplots(figsize=(10, 8))
ax.set_aspect('equal')

# Plot main ellipse
plot_ellipse(ax, (0,0), 2*A, 2*B, 0, color='black', fill=False, lw=2, label='Main Ellipse')

# Plot the polylines
ax.plot(polyline0[:, 0], polyline0[:, 1], 'o-', color='red', lw=2, label='Polyline 0')
ax.plot(polyline1[:, 0], polyline1[:, 1], 'o-', color='blue', lw=2, label='Polyline 1')

# Plot the resulting inscribed ellipse
if inscribed_params_poly["success"]:
    plot_ellipse(ax,
                 inscribed_params_poly['center'],
                 inscribed_params_poly['width'],
                 inscribed_params_poly['height'],
                 inscribed_params_poly['angle_deg'],
                 facecolor='cyan', edgecolor='green', alpha=0.7, lw=2, label='Inscribed Ellipse')

ax.set_xlim(-A-1, A+1)
ax.set_ylim(-B-1, B+1)
ax.grid(True)
ax.legend()
ax.set_title("Inscribed Ellipse for Polyline Boundaries")
plt.show()
'''

print("\nInscribed Ellipse Parameters (Polyline Case):")
print(inscribed_params_poly)