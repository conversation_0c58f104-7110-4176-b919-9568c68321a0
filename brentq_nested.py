import numpy as np
from scipy.optimize import brentq

# --- 1. Define the Inner Objective Function ---
# Let's say g(y, x) = y**2 - x
# We want to find y such that g(y, x) = 0 for a given x.
def inner_objective(y, x):
    """
    The function whose root we want to find internally.
    Example: y^2 - x = 0
    """
    return y**2 - x

# --- 2. Define a Function to Find the Inner Root ---
# This function will find y for a given x.
def find_inner_root(x, y_bounds):
    """
    Finds the root of inner_objective for a given x.
    Returns the value of y that satisfies inner_objective(y, x) = 0.
    """
    # Ensure x is non-negative if y can be real
    if x < 0:
        # Depending on your problem, you might raise an error,
        # return NaN, or handle it differently.
        # For y^2 - x = 0, if x < 0, there's no real y.
        # Let's assume we're looking for a positive y here.
        # If inner_objective can have multiple roots and you need a specific one,
        # you might need to adjust the bounds or use a different solver.
        # For y^2 = x, we expect y = sqrt(x). Let's assume we want the positive root.
        if y_bounds[0] < 0 and y_bounds[1] > 0:
            # If bounds span zero and x < 0, this might lead to issues.
            # For simplicity, let's assume we're solving for y >= 0.
            pass # Or add specific handling

    try:
        # Use brentq to find the root of inner_objective with respect to y
        # The 'args=(x,)' passes the outer variable x as an argument to inner_objective
        y_root = brentq(inner_objective, y_bounds[0], y_bounds[1], args=(x,))
        return y_root
    except ValueError as e:
        # Handle cases where brentq fails (e.g., no root in bounds)
        print(f"Warning: Brentq failed to find inner root for x={x}: {e}")
        return np.nan # Or raise an exception, or return a default value

# --- 3. Define the Outer Objective Function ---
# Let's say the outer objective is f(x) = y - 2*x
# where y is the root of the inner function.
def outer_objective(x, y_bounds):
    """
    The function whose root we want to find externally.
    This function depends on the root of the inner objective.
    Example: f(x) = y - 2*x, where y is such that y^2 - x = 0.
    """
    y = find_inner_root(x, y_bounds)
    if np.isnan(y):
        return np.nan # Propagate failure

    return y - 2 * x

# --- 4. Solve the Outer Problem ---
if __name__ == "__main__":
    # Define bounds for the inner root search
    # For y^2 - x = 0, if x >= 0, y can be real. Let's look for positive y.
    y_lower_bound = 0.0
    y_upper_bound = 10.0
    y_bounds = (y_lower_bound, y_upper_bound)

    # Define bounds for the outer root search (for x)
    # We're looking for x where outer_objective(x) = 0
    x_lower_bound = 0.0
    x_upper_bound = 20.0

    print(f"Solving outer problem: finding x such that outer_objective(x) = 0")
    print(f"Inner problem: finding y such that y^2 - x = 0 (y in [{y_bounds[0]}, {y_bounds[1]}])")
    print(f"Outer objective: f(x) = y - 2*x")

    try:
        # The 'args=(y_bounds,)' passes the bounds to the outer_objective function
        outer_root = brentq(outer_objective, x_lower_bound, x_upper_bound, args=(y_bounds,))
        print(f"\nOuter root found for x: {outer_root}")

        # Verify the solution
        inner_y_at_outer_root = find_inner_root(outer_root, y_bounds)
        print(f"Inner y at outer root ({outer_root}): {inner_y_at_outer_root}")
        print(f"Outer objective value at outer root: {outer_objective(outer_root, y_bounds)}")

        # Analytical solution for verification:
        # If y^2 - x = 0, then y = sqrt(x) (assuming positive y).
        # Outer objective: sqrt(x) - 2*x = 0
        # sqrt(x) = 2*x
        # x = 4*x^2
        # 4*x^2 - x = 0
        # x(4x - 1) = 0
        # x = 0 or x = 1/4.
        # Let's check our bounds. x_lower_bound = 0.0.
        # If x=0, y=0. Outer_objective = 0 - 2*0 = 0. So x=0 is a root.
        # If x=1/4, y=sqrt(1/4) = 1/2. Outer_objective = 1/2 - 2*(1/4) = 1/2 - 1/2 = 0.
        # Our bounds are [0, 20]. Both 0 and 0.25 are within these bounds.
        # brentq will find one of them based on the initial interval.
        # For the interval [0, 20], it should find 0.25.
        # If we start with x_lower_bound = 0.1, it should find 0.25.
        # Let's test with a different interval.
        print("\nTesting with different outer bounds [0.1, 1.0]:")
        outer_root_2 = brentq(outer_objective, 0.1, 1.0, args=(y_bounds,))
        print(f"Outer root found for x: {outer_root_2}")
        inner_y_at_outer_root_2 = find_inner_root(outer_root_2, y_bounds)
        print(f"Inner y at outer root ({outer_root_2}): {inner_y_at_outer_root_2}")
        print(f"Outer objective value at outer root: {outer_objective(outer_root_2, y_bounds)}")


    except ValueError as e:
        print(f"\nError during outer root finding: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")