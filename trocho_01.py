import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Polygon, LineString
import curvey  # Ensure you have installed <PERSON><PERSON><PERSON> via pip: pip install curvey

def get_longest_branch(skeleton):
    """
    Given a skeleton (which might be a MultiLineString or LineString),
    return the branch with the maximum length.
    """
    if skeleton.geom_type == 'MultiLineString':
        # Choose the longest branch from a collection of LineStrings.
        longest = max(skeleton.geoms, key=lambda line: line.length)
        return longest
    elif skeleton.geom_type == 'LineString':
        return skeleton
    else:
        raise ValueError("Unexpected geometry type for skeleton: " + skeleton.geom_type)

# Define the polygon representing your workpiece
polygon_coords = [(50, 50), (50, 150), (120, 180), (200, 150), (200, 50)]
workpiece = Polygon(polygon_coords)

# Compute the medial axis (skeleton) using the Curvey package.
# (Assuming Curvey exposes a method like curvey.medial_axis which accepts a Shapely polygon.)
skeleton = curvey.medial_axis(workpiece)

# Extract the main branch (for example, the longest branch).
main_branch = get_longest_branch(skeleton)
print("Selected main feed path (medial axis) as WKT:", main_branch.wkt)

# Visualization of the polygon and its medial axis
plt.figure(figsize=(8, 8))
# Plot the workpiece boundary
x, y = workpiece.exterior.xy
plt.plot(x, y, 'k-', linewidth=2, label='Workpiece Boundary')

# Plot the computed medial axis branch
if main_branch.geom_type == 'LineString':
    x, y = main_branch.xy
    plt.plot(x, y, 'r-', linewidth=2, label='Main Feed Path (Medial Axis)')
else:
    # If more than one branch was returned, plot each
    for branch in main_branch.geoms:
        x, y = branch.xy
        plt.plot(x, y, 'r-', linewidth=2)
    plt.legend(["Workpiece Boundary", "Medial Axis Branches"])

plt.xlabel("X")
plt.ylabel("Y")
plt.title("Medial Axis of the Workpiece using Curvey")
plt.legend()
plt.grid(True)
plt.axis("equal")
plt.show()