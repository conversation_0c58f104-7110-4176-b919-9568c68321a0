
import numpy as np
from scipy.spatial.distance import directed_hausdorff

# Parameters for ellipse eA
aA, bA = 15, 5
centerA = (0, 0)

# Parameters for ellipse eB
aB, bB = 12, 2.5
centerB = (0, -2.5)

# Generate points for eA boundary
theta = np.linspace(0, 2 * np.pi, 10000)
xA = centerA[0] + aA * np.cos(theta)
yA = centerA[1] + bA * np.sin(theta)
pointsA = np.column_stack((xA, yA))

# Generate points for eB boundary
phi = np.linspace(0, 2 * np.pi, 10000)
xB = centerB[0] + aB * np.cos(phi)
yB = centerB[1] + bB * np.sin(phi)
pointsB = np.column_stack((xB, yB))

# Compute directed Hausdorff distance from eB to eA
d_B_to_A = directed_hausdorff(pointsB, pointsA)[0]

print(f"The directed Hausdorff distance from eB to eA is: {d_B_to_A}")