import numpy as np
from scipy.optimize import minimize, Bounds
from scipy.integrate import quad

# Params
a_main = 12  # semi-major
b_main = 8   # semi-minor
frac_start = 0.0
frac_end = 0.1
poly_segs = 2  # segments per polyline
seg_len = 2.5  # each segment length (total ~5 as before)

# Ellipse parametric (theta from 0 to 2pi)
def ell_point(theta, a=a_main, b=b_main):
    return a * np.cos(theta), b * np.sin(theta)

# Arc length function for fraction to theta
def arc_len(theta, a=a_main, b=b_main):
    return quad(lambda t: np.sqrt(a**2 * np.sin(t)**2 + b**2 * np.cos(t)**2), 0, theta)[0]

total_perim = arc_len(2 * np.pi)
theta_start = minimize(lambda t: (arc_len(t) / total_perim - frac_start)**2, 0).x[0]
theta_end = minimize(lambda t: (arc_len(t) / total_perim - frac_end)**2, theta_start).x[0]
p_start = ell_point(theta_start)
p_end = ell_point(theta_end)

# Generate polylines (example: inward with bend)
def gen_polyline(p0, n_segs, len_seg, bend_angle=10 * np.pi/180):
    poly = [np.array(p0)]
    dir = -poly[0] / np.linalg.norm(poly[0])  # inward radial
    for i in range(n_segs):
        next_pt = poly[-1] + len_seg * dir
        poly.append(next_pt)
        # Bend slightly
        rot = np.array([[np.cos(bend_angle), -np.sin(bend_angle)], [np.sin(bend_angle), np.cos(bend_angle)]])
        dir = rot @ dir
        dir /= np.linalg.norm(dir)
    return np.array(poly)  # vertices

poly1 = gen_polyline(p_start, poly_segs, seg_len)
poly2 = gen_polyline(p_end, poly_segs, seg_len)

# Distance from point to polyline (min to any segment)
def dist_to_poly(pt, poly):
    dists = []
    for i in range(len(poly) - 1):
        a, b = poly[i], poly[i+1]
        ba = b - a
        pa = pt - a
        h = np.clip(np.dot(pa, ba) / np.dot(ba, ba), 0, 1)
        dists.append(np.linalg.norm(pa - h * ba))
    return min(dists)

# Check if point is inside main ellipse
def inside_ell(cx, cy, a=a_main, b=b_main):
    return (cx / a)**2 + (cy / b)**2 <= 1

# General conic for inscribed ellipse
def get_conic(params):
    cx, cy, aa, bb, phi = params
    cos_phi = np.cos(phi)
    sin_phi = np.sin(phi)
    inv_aa2 = 1 / aa**2
    inv_bb2 = 1 / bb**2
    A = cos_phi**2 * inv_aa2 + sin_phi**2 * inv_bb2
    B = 2 * cos_phi * sin_phi * (inv_aa2 - inv_bb2)
    C = sin_phi**2 * inv_aa2 + cos_phi**2 * inv_bb2
    D = -2 * A * cx - B * cy
    E = -B * cx - 2 * C * cy
    F = A * cx**2 + B * cx * cy + C * cy**2 - 1
    return A, B, C, D, E, F

# Sample points on arc for deviation
thetas = np.linspace(theta_start, theta_end, 50)
arc_points = np.array([ell_point(th) for th in thetas])

# Cost: minimize deviation + penalties
def cost(params):
    base = 0
    penalty = 0
    # Deviation: distance from arc points to inscribed ellipse boundary
    for pt in arc_points:
        # Approximate radial dev (proj to ellipse boundary, but simplify to dist)
        dist = dist_to_ellipse(pt, params)  # You'd need a dist_to_ellipse func; placeholder
        base += dist ** 2
    # Tangency penalties (min dist ~0, but no intersection i.e. all dists >=0)
    min_d1 = min_dist_ellipse_to_poly(params, poly1)
    min_d2 = min_dist_ellipse_to_poly(params, poly2)
    penalty += (min_d1 ** 2) * 1e3  # Encourage =0 for tangency
    penalty += (min_d2 ** 2) * 1e3
    penalty += max(0, -min_d1) * 1e6  # Harsh if intersecting (<0)
    penalty += max(0, -min_d2) * 1e6
    # Inside main
    if not inside_ell(*params[:2]):
        penalty += 1e6
    return base + penalty

# Placeholder helpers (implement fully for real use)
def dist_to_ellipse(pt, params):
    # Approximate distance from pt to ellipse boundary (numeric solve)
    return np.abs(np.linalg.norm(pt) - a_main)  # Simplify for now; replace with proper

def min_dist_ellipse_to_poly(params, poly):
    # Approximate min signed distance (positive outside, negative intersect)
    return min([dist_to_poly(np.random.uniform(-1,1,2), poly) for _ in range(100)]) - 0.1  # Dummy; use better sampling or analytic

# Initial guess (from previous circle-ish)
initial = [4, 1, 3, 2, np.pi/6]  # cx, cy, a, b, phi
bounds = Bounds([-10, -10, 1, 1, -np.pi], [10, 10, 6, 6, np.pi])

res = minimize(cost, initial, method='trust-constr', bounds=bounds, options={'maxiter': 500, 'disp': True})

if res.success:
    params = res.x
    print(f'Optimal params: cx={params[0]}, cy={params[1]}, a={params[2]}, b={params[3]}, phi={params[4]}')
    # Compute devs, touch points, etc.
    devs = [dist_to_ellipse(pt, params) for pt in arc_points]
    print(f'Avg deviation: {np.mean(devs)}')
else:
    print('Optimization failed—try better initial or more iters')