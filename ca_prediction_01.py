# Need to install shapely and matplotlib if not already installed
# pip install shapely matplotlib numpy

import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString
# No need for Polygon or rotate directly for this geometric setup

import math

# --- Parameters (Based on Figure 7 and assumptions where not specified) ---
r = 3.0          # Tool radius (mm)
m = 4            # Number of teeth (assumed)
beta = np.deg2rad(30) # Helical angle (radians) (assumed)
n = 3000         # Spindle speed (r/min)
f = 960          # Path feedrate (mm/min) (F in Figure 7 text)

# Calculate feed per tooth (fz) from path feedrate (f), teeth (m), and speed (n)
# F = fz * m * n (for linear motion), or F = fz * m * n * (circumference / pitch) for circular
# Standard definition: F = fz * m * n (mm/min) for linear feed.
# Let's use fz in the chip thickness formula directly as mm/tooth.
# Based on F=960, m=4, n=3000 -> fz = F / (m * n) = 960 / (4 * 3000) = 0.08 mm/tooth
fz = 0.08        # Feed per tooth (mm/tooth)

a = 3.0          # Axial depth of cut (mm) (Ap in Figure 7)
Ar = 1.0         # Radial depth of cut (mm) (Assumed as increment for outer boundary)

# Workpiece boundaries and tool path (Interpretation based on Fig 2 and Sec 3.1 text)
# Cur1: Inner workpiece boundary (e.g., island fillet)
O1 = (0.0, 0.0)  # Center of Cur1 (mm) - Example coordinate
R1 = 5.0         # Radius of Cur1 (mm)

# Cur2: Tool center path (as labeled in Figure 2 for milling force prediction)
# The paper says O1O2 distance is l=0.5. Let's place O2 relative to O1.
O2 = (O1[0] + 0.5, O1[1]) # Center of Cur2 (mm) - Based on |O1O2|=0.5 and O1 at origin
R2 = 5.0         # Radius of Cur2 (mm) (Radius of tool center path around O2, R2 in Fig 7 text)

# Outer workpiece boundary - Assume Cur2 offset outwards by Ar for this pass's material
# Center is O2, radius is R2 + Ar

# Cutting force coefficients (Mechanistic model, K_edge assumed 0 for simplicity)
Ktc = 5325       # Tangential mechanistic coefficient (N/mm^2)
Krc = 4902       # Radial mechanistic coefficient (N/mm^2)
Kac = 1823       # Axial mechanistic coefficient (N/mm^2)
Kte = 0.0        # Tangential edge coefficient (N/mm) (Assumed 0)
Kre = 0.0        # Radial edge coefficient (N/mm) (Assumed 0)
Kae = 0.0        # Axial edge coefficient (N/mm) (Assumed 0)

# Simulation parameters
K2 = 20          # Number of axial micro units (slices) - Increased for smoother axial integration
p = 100          # Number of time steps per tooth period (for calculating dt)

# Path simulation parameters
# Simulate tool movement along an arc of the tool path Cur2
path_start_angle_deg = 0   # Start angle on Cur2 for tool center (degrees) - Relative to O2->(+x)
path_end_angle_deg = 180   # End angle on Cur2 for tool center (degrees)
path_angle_sweep_rad = np.deg2rad(path_end_angle_deg - path_start_angle_deg)

# Calculate time steps
feedrate_path = f # Use F=960 mm/min as the path feedrate
path_length = path_angle_sweep_rad * R2 # Length of the path segment
total_time = path_length / (feedrate_path / 60.0) # Total time for this path segment (seconds)

# Time step dt based on tooth period divided by p
# Tooth period in seconds = 60 / (n * m)
# dt = (60.0 / (n * m)) / p
# Paper says dt = 60 / (n*m*p), which is the same.
dt = 60.0 / (n * m * p)

# Number of time steps for the entire path segment
K1 = int(total_time / dt)
if K1 == 0:
    K1 = 1 # Ensure at least one step

# Re-calculate total_time and dt based on integer K1 for consistency
total_time = K1 * dt
# dbeta1 per time step: total angle sweep / K1
dbeta1 = path_angle_sweep_rad / K1

# Spin angle change per time step (tool rotation)
dbeta2 = (n / 60.0) * dt * (2 * np.pi)

# Axial slice height
dz = a / K2

# Initial angles
beta1s = np.deg2rad(path_start_angle_deg) # Start orbit angle on Cur2 (relative to O2->+x)
beta2s = 0.0 # Start spin angle for tooth 1 at z=0 (relative to global +x, say)


# --- Helper Functions ---

def normalize_angle_0_to_2pi(angle):
    """Normalize angle to [0, 2pi)"""
    return angle % (2 * np.pi)

def normalize_angle_negpi_to_pi(angle):
    """Normalize angle to [-pi, pi)"""
    angle = normalize_angle_0_to_2pi(angle)
    if angle >= np.pi:
        angle -= 2 * np.pi
    return angle

def is_angle_in_sector(angle, phis, phie, tol=1e-9):
    """
    Check if 'angle' is within the sector [phis, phie] (inclusive),
    handling wrapping around 0/2pi. Angles are assumed normalized [0, 2pi).
    """
    angle = normalize_angle_0_to_2pi(angle)
    phis = normalize_angle_0_to_2pi(phis)
    phie = normalize_angle_0_to_2pi(phie)

    if phis <= phie:
        # Standard sector [phis, phie]
        return phis - tol <= angle <= phie + tol
    else:
        # Wrapped sector [phis, 2pi) U [0, phie]
        return angle >= phis - tol or angle <= phie + tol

def get_angle_relative_to_point(p1, center_pt):
    """Calculate angle of p1 relative to center_pt, measured from positive x-axis, in [-pi, pi)"""
    return np.atan2(p1.y - center_pt.y, p1.x - center_pt.x)

def get_engagement_angles(C_pt, r, O1_pt, R1, O2_pt, R2, Ar):
    """
    Find the start and end angles (global, [0, 2pi)) of the cutting arc
    on the tool boundary for the current tool center position C.
    Returns [phis, phie] or None if no engagement.
    Assumes a single continuous engagement sector.
    """
    tool_circle_boundary = C_pt.buffer(r).boundary
    inner_boundary_line = O1_pt.buffer(R1).boundary
    outer_boundary_line = O2_pt.buffer(R2 + Ar).boundary

    # Find intersections with both boundaries
    intersections_inner = tool_circle_boundary.intersection(inner_boundary_line)
    intersections_outer = tool_circle_boundary.intersection(outer_boundary_line)

    # Collect all intersection points from both boundaries
    intersection_points = []
    for intersections in [intersections_inner, intersections_outer]:
        if isinstance(intersections, Point):
            intersection_points.append(intersections)
        elif hasattr(intersections, 'geoms'): # MultiPoint, LineString, GeometryCollection
            for geom in intersections.geoms:
                if isinstance(geom, Point):
                    intersection_points.append(geom)
                # Handle LineStrings? In simple circle-circle intersection, LineString means tangential contact or overlap.
                # For simplicity here, we only use point intersections to define sector boundaries.

    if not intersection_points:
        # Check if the tool is entirely inside or outside the material band (between inner and outer)
        # Pick a test point on the tool boundary, e.g., C_pt + (r, 0)
        test_point_angle = 0 # Test point at angle 0 relative to tool center
        test_x = C_pt.x + r * np.cos(test_point_angle)
        test_y = C_pt.y + r * np.sin(test_point_angle)
        test_pt = Point(test_x, test_y)

        dist_to_O1 = test_pt.distance(O1_pt)
        dist_to_O2 = test_pt.distance(O2_pt)

        is_outside_cur1 = dist_to_O1 > R1 - 1e-9
        is_inside_cur2_outer = dist_to_O2 <= R2 + Ar + 1e-9

        if is_outside_cur1 and is_inside_cur2_outer:
             # Tool is fully engaged
             return [0.0, 2 * np.pi] # Full circle engagement
        else:
             return None # No engagement

    # Get angles of intersection points relative to tool center C_pt
    angles = [normalize_angle_0_to_2pi(get_angle_relative_to_point(p, C_pt)) for p in intersection_points]
    angles = sorted(list(set(angles))) # Unique and sorted angles

    # The angles divide the tool circle into segments. We need to check which segment(s) are cutting.
    # A segment is cutting if a test point within it is in the material zone.
    # Material zone: outside Cur1 (O1, R1) and inside Cur2_outer (O2, R2+Ar).
    cutting_segments = []
    num_angles = len(angles)
    for i in range(num_angles):
        start_angle = angles[i]
        end_angle = angles[(i + 1) % num_angles] # Wrap around

        # Calculate midpoint angle of the segment
        mid_angle = (start_angle + end_angle) / 2.0
        if start_angle > end_angle: # Segment wraps around 2pi/0
             mid_angle = (start_angle + end_angle + 2 * np.pi) / 2.0

        # Get a point on the tool boundary at the midpoint angle
        mid_x = C_pt.x + r * np.cos(mid_angle)
        mid_y = C_pt.y + r * np.sin(mid_angle)
        mid_point = Point(mid_x, mid_y)

        # Check if the midpoint is inside the material band
        dist_to_O1 = mid_point.distance(O1_pt)
        dist_to_O2 = mid_point.distance(O2_pt) # Distance to tool path center

        is_outside_cur1 = dist_to_O1 > R1 - 1e-9
        is_inside_cur2_outer = dist_to_O2 <= R2 + Ar + 1e-9

        if is_outside_cur1 and is_inside_cur2_outer:
             cutting_segments.append((start_angle, end_angle))

    # Assuming a single continuous cutting segment for simplicity in this implementation
    if cutting_segments:
        # If there are multiple segments, this picks the first one.
        # A more robust implementation would merge adjacent/overlapping segments.
        phis, phie = cutting_segments[0]
        # Ensure phis < phie for the angle_from_start calculation later
        # If phis > phie, the segment wraps around 0/2pi. Handle this in is_angle_in_sector.
        # For calculating the angle *within* the segment from the start (theta_cut),
        # we need a continuous angle range. If phis > phie, add 2*pi to phie.
        if phis > phie:
             phie += 2 * np.pi # This makes the sector continuous for easy math

        return [phis, phie]
    else:
        return None # No cutting segment found


# --- Simulation ---

O1_pt = Point(O1)
O2_pt = Point(O2)

# Store forces over time
Fx_history = []
Fy_history = []
Fz_history = []
F_res_history = []
time_history = []
engagement_angle_history = [] # Store total engagement angle (phie - phis)

print(f"Starting simulation with {K1} time steps...")
print(f"dt: {dt:.6f} s")
print(f"dbeta1: {np.rad2deg(dbeta1):.4f} deg")
print(f"dbeta2: {np.rad2deg(dbeta2):.2f} deg")
print(f"dz: {dz:.4f} mm")


for k in range(K1):
    # Current time
    current_time = k * dt
    time_history.append(current_time)

    # Current tool center position on Cur2 (path)
    current_path_angle = beta1s + k * dbeta1
    xc = O2[0] + R2 * np.cos(current_path_angle)
    yc = O2[1] + R2 * np.sin(current_path_angle)
    C_pt = Point(xc, yc)

    # Find radial engagement angles [phis_global, phie_global] relative to global +x
    engagement_sector = get_engagement_angles(C_pt, r, O1_pt, R1, O2_pt, R2, Ar)

    if engagement_sector is None:
        # No engagement at this time step
        Fx_history.append(0.0)
        Fy_history.append(0.0)
        Fz_history.append(0.0)
        F_res_history.append(0.0)
        engagement_angle_history.append(0.0)
        # print(f"Time step {k}: No engagement.")
        continue

    phis_global, phie_global = engagement_sector
    total_engagement_angle = (phie_global - phis_global) # Span might be > 2pi if phis > phie initially
    # If phis_global > phie_global, get the positive span that wraps around
    if phis_global > normalize_angle_0_to_2pi(phie_global):
         total_engagement_angle = (phie_global + 2*np.pi) - phis_global
    else:
         total_engagement_angle = phie_global - phis_global

    engagement_angle_history.append(np.rad2deg(total_engagement_angle))

    # Initialize total forces for this time step
    Fx_total = 0.0
    Fy_total = 0.0
    Fz_total = 0.0

    # Axial loop
    for i2 in range(K2):
        z = i2 * dz # Height along the tool axis, starting from 0

        # Tooth loop
        for j in range(m):
            # Current spin angle for tooth j at height z, relative to global +x
            # beta2 = beta2s + (j - 1) * 2*np.pi/m - k * dbeta2 + z * np.tan(beta) / r # Paper uses j-1
            # Use j starting from 0, so angle is for tooth 0, 1, ..., m-1
            # Angle for tooth j at z=0, time k: beta2s + j * (2*np.pi/m) - k * dbeta2
            # Add helical lag: + z * tan(beta) / r
            phi_spin_global = beta2s + j * (2*np.pi/m) - k * dbeta2 + z * np.tan(beta) / r

            # Normalize the angle for comparison with engagement sector [0, 2pi)
            phi_spin_global_norm = normalize_angle_0_to_2pi(phi_spin_global)

            # Check if this micro unit is within the radial cutting zone
            is_cutting_radially = is_angle_in_sector(phi_spin_global_norm, phis_global, phie_global)

            # Check if point is within the axial range
            is_cutting_axially = (z >= 0 - 1e-9) and (z <= a + 1e-9) # Check within axial depth

            if is_cutting_radially and is_cutting_axially:
                # This micro unit (tooth j, height z) is cutting

                # Calculate instantaneous chip thickness
                # Paper: h(phi) = fz * sin(phi) where phi is the instantaneous immersion angle.
                # We map the angle phi_spin_global within [phis_global, phie_global]
                # to the range [0, total_engagement_angle] for the sine function.
                # This maps the *entry point* of the cut to angle 0 for the sine wave.
                # angle_in_sector: angle measured from phis_global in the CCW direction.
                angle_in_sector = (phi_spin_global_norm - phis_global + 2*np.pi) % (2*np.pi)

                # Check if this angle is within the actual span, handle wrap-around case correctly
                # If phis_global > phie_global, the sector wraps.
                # angle_in_sector should be in [0, total_engagement_angle]
                if phis_global > normalize_angle_0_to_2pi(phie_global): # Wrapped sector check again
                    if phi_spin_global_norm >= phis_global:
                        angle_in_sector = phi_spin_global_norm - phis_global
                    else: # phi_spin_global_norm <= phie_global_norm
                        angle_in_sector = (phi_spin_global_norm + 2*np.pi) - phis_global
                # Else (standard sector), angle_in_sector calculated above is correct (phi_spin_global_norm - phis_global)

                # Ensure angle_in_sector is non-negative and within the total engagement angle
                angle_in_sector = max(0.0, angle_in_sector)
                angle_in_sector = min(total_engagement_angle, angle_in_sector)

                h = fz * np.sin(angle_in_sector) # Instantaneous chip thickness (mm)

                # Ensure chip thickness is non-negative (should be handled by the angle range, but safety)
                h = max(0.0, h)

                # Calculate micro unit forces in tangential, radial, axial directions
                dFt = (Ktc * h + Kte) * dz
                dFr = (Krc * h + Kre) * dz
                dFa = (Kac * h + Kae) * dz

                # Decompose forces into global X, Y, Z directions
                # Paper's formula uses phi = beta2n (spin angle in global frame).
                # Use the point's global angle phi_spin_global for decomposition as per paper's formula structure.
                # Note: This decomposition formula feels unconventional based on standard Altintas,
                # but we follow the provided formula structure.
                phi_decomp = phi_spin_global # Use the exact angle including helical lag and spin

                dFx = -dFt * np.cos(phi_decomp) - dFr * np.sin(phi_decomp)
                dFy = -dFt * np.sin(phi_decomp) + dFr * np.cos(phi_decomp)
                dFz = dFa # Axial force is dFa

                # Sum up micro unit forces for this time step
                Fx_total += dFx
                Fy_total += dFy
                Fz_total += dFz

    # Total force resultant for this time step
    F_res = np.sqrt(Fx_total**2 + Fy_total**2 + Fz_total**2)

    # Store total forces
    Fx_history.append(Fx_total)
    Fy_history.append(Fy_total)
    Fz_history.append(Fz_total)
    F_res_history.append(F_res)


print("Simulation finished.")

# --- Plotting Results ---

plt.figure(figsize=(12, 10))

# Plot Forces
plt.subplot(2, 1, 1)
plt.plot(time_history, Fx_history, label='Fx')
plt.plot(time_history, Fy_history, label='Fy')
plt.plot(time_history, Fz_history, label='Fz')
plt.plot(time_history, F_res_history, label='Resultant F')
plt.xlabel('Time (s)')
plt.ylabel('Force (N)')
plt.title('Milling Force Prediction (Section 3.1 Implementation)')
plt.legend()
plt.grid(True)

# Plot Engagement Angle (Radial)
plt.subplot(2, 1, 2)
plt.plot(time_history, engagement_angle_history, label='Engagement Angle (Radial)')
plt.xlabel('Time (s)')
plt.ylabel('Engagement Angle (degrees)')
plt.title('Radial Engagement Angle Change')
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.show()

# Optional: Print some results
# print("\nSample Results:")
# for i in range(0, K1, max(1, K1 // 10)): # Print every 10% or at least 1 step
#     print(f"Time: {time_history[i]:.4f} s, Fx: {Fx_history[i]:.2f} N, Fy: {Fy_history[i]:.2f} N, Fz: {Fz_history[i]:.2f} N, F_res: {F_res_history[i]:.2f} N, Alpha: {engagement_angle_history[i]:.2f} deg")