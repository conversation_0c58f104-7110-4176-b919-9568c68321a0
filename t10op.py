import numpy as np
import time
from shapely import Polygon, Point


def detect_line_intersections(lines): 
    """ Given an array 'lines' of shape (N, 2, 2) where each line is represented by two endpoints, returns an (M, 2) array of index pairs (i, j) for which line i and line j intersect. The computation is fully vectorized: bounding boxes are computed and used to prefilter candidate pairs, and then line intersection parameters are computed for the remaining candidate pairs.

    The intersection test for two segments with endpoints:
        p1 = (x1, y1), p2 = (x2, y2) for the first segment, and
        p3 = (x3, y3), p4 = (x4, y4) for the second
    uses the parameterization:

        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        t = ((x1-x3)*(y1-y2) - (y1-y3)*(x1-x2)) / denom
        u = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        
    and the segments intersect if 0<=t<=1 and 0<=u<=1, provided that |denom| > epsilon.

    This function is optimized for large numbers of line segments.
    """
    pts1 = lines[:, 0]
    pts2 = lines[:, 1]
    x1, y1 = pts1[:, 0], pts1[:, 1]
    x2, y2 = pts2[:, 0], pts2[:, 1]

    # Compute bounding boxes for each line segment.
    min_x = np.minimum(x1, x2)
    max_x = np.maximum(x1, x2)
    min_y = np.minimum(y1, y2)
    max_y = np.maximum(y1, y2)

    N = len(lines)
    # Get all unique candidate pairs (i,j) with i < j
    i_idx, j_idx = np.triu_indices(N, k=1)

    # Prefilter candidate pairs using bounding box overlap
    valid_bb = ((min_x[i_idx] <= max_x[j_idx]) & (max_x[i_idx] >= min_x[j_idx]) &
                (min_y[i_idx] <= max_y[j_idx]) & (max_y[i_idx] >= min_y[j_idx]))
    cand_i = i_idx[valid_bb]
    cand_j = j_idx[valid_bb]

    # Get candidate coordinates
    x1_c = x1[cand_i]
    y1_c = y1[cand_i]
    x2_c = x2[cand_i]
    y2_c = y2[cand_i]

    x3_c = x1[cand_j]
    y3_c = y1[cand_j]
    x4_c = x2[cand_j]
    y4_c = y2[cand_j]

    # Compute denominator for the intersection formulas.
    denom = (x1_c - x2_c) * (y3_c - y4_c) - (y1_c - y2_c) * (x3_c - x4_c)

    # Compute t and u numerators
    t_num = (x1_c - x3_c) * (y1_c - y2_c) - (y1_c - y3_c) * (x1_c - x2_c)
    u_num = (x1_c - x3_c) * (y3_c - y4_c) - (y1_c - y3_c) * (x3_c - x4_c)

    # Avoid division by very small numbers
    eps = 1e-10
    valid_denom = np.abs(denom) > eps
    # Initialize t and u to -1 (indicating no valid intersection)
    t = np.full_like(denom, -1, dtype=np.float64)
    u = np.full_like(denom, -1, dtype=np.float64)
    t[valid_denom] = t_num[valid_denom] / denom[valid_denom]
    u[valid_denom] = u_num[valid_denom] / denom[valid_denom]

    # Check for intersection parameters within [0, 1]
    intersect = (t >= 0) & (t <= 1) & (u >= 0) & (u <= 1)

    # Return the intersecting pairs (indices) in an (M, 2) array.
    intersect_pairs = np.vstack((cand_i[intersect], cand_j[intersect])).T
    return intersect_pairs


def find_intersecting_lines(lines):
    lines = np.asarray(lines)
    N = lines.shape[0]
    p_start = lines[:, 0, :]
    direction = lines[:, 1, :] - p_start
    inv_dir_dot = 1.0 / np.sum(direction ** 2, axis=1)

    i_idx, j_idx = np.triu_indices(N, k=1)
    M = len(i_idx)
    
    p_i = p_start[i_idx]
    p_j = p_start[j_idx]
    dir_i = direction[i_idx]
    dir_j = direction[j_idx]
    
    vec_pj_pi = p_j - p_i
    o1 = vec_pj_pi[:, 0] * dir_i[:, 1] - vec_pj_pi[:, 1] * dir_i[:, 0]
    vec_p4_pi = vec_pj_pi + dir_j
    o2 = vec_p4_pi[:, 0] * dir_i[:, 1] - vec_p4_pi[:, 1] * dir_i[:, 0]
    
    vec_pi_pj = -vec_pj_pi
    o3 = vec_pi_pj[:, 0] * dir_j[:, 1] - vec_pi_pj[:, 1] * dir_j[:, 0]
    vec_pi_dir_pj = vec_pi_pj + dir_i
    o4 = vec_pi_dir_pj[:, 0] * dir_j[:, 1] - vec_pi_dir_pj[:, 1] * dir_j[:, 0]
    
    cond1 = (o1 * o2) < 0
    cond2 = (o3 * o4) < 0
    intersects = cond1 & cond2
    
    colinear = (o1 == 0) & (o2 == 0)
    if np.any(colinear):
        col_inds = np.where(colinear)[0]
        col_i = i_idx[col_inds]
        col_j = j_idx[col_inds]
        
        vec3 = vec_pj_pi[col_inds]
        vec4 = vec3 + dir_j[col_inds]
        inv_dot = inv_dir_dot[col_i]
        
        t3 = np.sum(vec3 * dir_i[col_inds], axis=1) * inv_dot
        t4 = np.sum(vec4 * dir_i[col_inds], axis=1) * inv_dot
        
        tmin = np.minimum(t3, t4)
        tmax = np.maximum(t3, t4)
        overlap = (tmax >= 0) & (tmin <= 1)
        
        intersects_col = np.zeros(M, dtype=bool)
        intersects_col[col_inds] = overlap
        intersects |= intersects_col
    
    result = np.zeros(N, dtype=bool)
    valid_pairs = np.where(intersects)[0]
    np.logical_or.at(result, i_idx[valid_pairs], True)
    np.logical_or.at(result, j_idx[valid_pairs], True)
    
    return result


import numpy as np

def compute_edge_metrics(inner, outer): 
    """ Given two polygons (inner and outer) as numpy arrays of shape (n_points, 2), compute for each pairing (ipX, opX): - angle_inner: between vector from inner[ipX-1] to inner[ipX] and vector from inner[ipX] to outer[opX] - angle_outer: between vector from outer[opX] to inner[ipX] and vector from outer[opX] to outer[opX+1] - edge_length: Euclidean distance from inner[ipX] to outer[opX]

    The vertices indices wrap-around (i.e., ipX-1 for the first vertex is the last vertex; opX+1 for the
    last vertex is the first vertex).

    Returns:
        A dictionary with keys:
        - 'angle_inner': a (m, n) numpy array of angles (in radians) at inner vertices.
        - 'angle_outer': a (m, n) numpy array of angles (in radians) at outer vertices.
        - 'edge_length': a (m, n) numpy array of distances.
        where m = number of vertices in inner and n = number of vertices in outer.
    """
    m = inner.shape[0]
    n = outer.shape[0]

    # Get previous vertex for each inner point (with wrap-around)
    inner_prev = inner[np.arange(m) - 1]

    # Get next vertex for each outer point (with wrap-around)
    outer_next = outer[(np.arange(n) + 1) % n]

    # Reshape inner and outer for broadcasting over all combinations
    inner_current = inner.reshape(m, 1, 2)  # shape (m, 1, 2)
    outer_points = outer.reshape(1, n, 2)     # shape (1, n, 2)

    # --- Compute inner angle at each inner vertex ---
    # Vector along inner polygon edge ending at inner_current (same for all corresponding outer points)
    v_inner = inner - inner_prev             # shape (m,2)
    # Vector from inner_current to each outer point
    v_to_outer = outer_points - inner_current # shape (m, n, 2)

    # Calculate cosine of inner angle for each (inner, outer) pair.
    dot_inner = np.sum(v_inner.reshape(m, 1, 2) * v_to_outer, axis=-1)  # shape (m, n)
    norm_v_inner = np.linalg.norm(v_inner, axis=1).reshape(m, 1)         # shape (m, 1)
    norm_v_to_outer = np.linalg.norm(v_to_outer, axis=-1)                # shape (m, n)
    cos_angle_inner = dot_inner / (norm_v_inner * norm_v_to_outer)
    cos_angle_inner = np.clip(cos_angle_inner, -1, 1)
    angle_inner = np.arccos(cos_angle_inner)  # shape (m, n)

    # --- Compute outer angle at each outer vertex ---
    # For each pair, compute the vector from outer to inner (which is the negative of above)
    v_from_outer_to_inner = inner_current - outer_points  # shape (m, n, 2)
    # For each outer vertex, compute the polygon edge leading out (constant over inner index)
    v_outer = outer_next - outer                           # shape (n, 2)
    v_outer = v_outer.reshape(1, n, 2)                       # shape (1, n, 2)

    dot_outer = np.sum(v_from_outer_to_inner * v_outer, axis=-1)  # shape (m, n)
    norm_v_from_outer_to_inner = np.linalg.norm(v_from_outer_to_inner, axis=-1)  # shape (m, n)
    norm_v_outer = np.linalg.norm(v_outer, axis=-1)   # shape (1, n)
    cos_angle_outer = dot_outer / (norm_v_from_outer_to_inner * norm_v_outer)
    cos_angle_outer = np.clip(cos_angle_outer, -1, 1)
    angle_outer = np.arccos(cos_angle_outer)  # shape (m, n)

    # --- Compute length of the imagined edge ---
    edge_length = norm_v_to_outer.copy()  # shape (m, n)

    return {
        'angle_inner': angle_inner,
        'angle_outer': angle_outer,
        'edge_length': edge_length
    }


def compute_polygon_pairs(inner_points, outer_points):
    n, m = len(inner_points), len(outer_points)
    
    # Cyclic shifts
    prev_inner = np.roll(inner_points, 1, axis=0)
    next_outer = np.roll(outer_points, -1, axis=0)
    
    # Vectors
    v1_all = prev_inner - inner_points  # (n, 2)
    v2_all = outer_points[None, :, :] - inner_points[:, None, :]  # (n, m, 2)
    w2_all = next_outer - outer_points  # (m, 2)
    
    # Lengths
    lengths = np.linalg.norm(v2_all, axis=2)  # (n, m)
    
    # Angle at ipX
    dot_v1_v2 = np.sum(v1_all[:, None, :] * v2_all, axis=2)  # (n, m)
    norm_v1 = np.linalg.norm(v1_all, axis=1)  # (n,)
    norm_v2 = lengths  # (n, m)
    arg_ip = np.clip(dot_v1_v2 / (norm_v1[:, None] * norm_v2), -1, 1)
    theta_ip = np.arccos(arg_ip)  # (n, m)
    
    # Angle at opX
    dot_v2_w2 = np.sum(v2_all * w2_all[None, :, :], axis=2)  # (n, m)
    norm_w2 = np.linalg.norm(w2_all, axis=1)  # (m,)
    arg_op = np.clip(-dot_v2_w2 / (norm_v2 * norm_w2[None, :]), -1, 1)
    theta_op = np.arccos(arg_op)  # (n, m)
    
    # Records
    i_idx, j_idx = np.meshgrid(range(n), range(m), indexing='ij')
    records = np.column_stack((i_idx.ravel(), j_idx.ravel(),
                              lengths.ravel(), theta_ip.ravel(), theta_op.ravel()))
    return records
  

def calculate_angles_and_lengths(inner_poly, outer_poly):
    N = inner_poly.shape[0]
    M = outer_poly.shape[0]
    
    # Compute previous indices for inner polygon
    inner_prev_indices = (np.arange(N) - 1) % N
    inner_prev_points = inner_poly[inner_prev_indices]
    
    # Compute next indices for outer polygon
    outer_next_indices = (np.arange(M) + 1) % M
    outer_next_points = outer_poly[outer_next_indices]
    
    # Reshape for broadcasting
    inner_poly_exp = inner_poly[:, None, :]  # (N, 1, 2)
    inner_prev_exp = inner_prev_points[:, None, :]  # (N, 1, 2)
    outer_poly_exp = outer_poly[None, :, :]  # (1, M, 2)
    outer_next_exp = outer_next_points[None, :, :]  # (1, M, 2)
    
    # Compute vectors a, b, c, d
    a = inner_poly_exp - inner_prev_exp  # (N, 1, 2)
    b = outer_poly_exp - inner_poly_exp  # (N, M, 2)
    c = outer_next_exp - outer_poly_exp  # (1, M, 2)
    d = -b  # (N, M, 2)
    
    # Calculate theta1 (angle between a and b)
    dot_ab = np.sum(a * b, axis=2)
    mag_a = np.linalg.norm(a, axis=2)
    mag_b = np.linalg.norm(b, axis=2)
    cos_theta1 = np.clip(dot_ab / (mag_a * mag_b), -1.0, 1.0)
    theta1 = np.arccos(cos_theta1)
    
    # Calculate theta2 (angle between d and c)
    dot_dc = np.sum(d * c, axis=2)
    mag_d = np.linalg.norm(d, axis=2)
    mag_c = np.linalg.norm(c, axis=2)
    cos_theta2 = np.clip(dot_dc / (mag_d * mag_c), -1.0, 1.0)
    theta2 = np.arccos(cos_theta2)
    
    total_angle = theta1 + theta2
    lengths = np.linalg.norm(b, axis=2)
    
    # Flatten the results to get records
    return total_angle.ravel(), lengths.ravel()




def create_sample_data(n=12000):
    """Create sample line data for testing"""
    rng = np.random.default_rng(42)
    lines = rng.uniform(-100, 100, (n, 2, 2))
    return lines


if __name__ == "__main__":
    inner_polygon = Polygon([
        [ 39.715107, -12.91931 ],
        [ 39.759045, -12.904118],
        [ 39.765625, -12.864672],
        [ 39.789932, -12.922694]])

    outer_polygon = Polygon([
        [ 39.696907, -12.922939],
        [ 39.74685 , -12.896604],
        [ 39.76216 , -12.840488],
        [ 39.79942 , -12.929433]])


# Define two triangles 
# inner_triangle = np.array([[0, 0], [1, 0], [0.5, 1]])
# outer_triangle = np.array([[2, 0], [3, 0], [2.5, 1]])
inner_triangle = np.array([
    [ 39.715107, -12.91931 ],
    [ 39.759045, -12.904118],
    [ 39.765625, -12.864672],
    [ 39.789932, -12.922694]])
outer_triangle = np.array([
    [ 39.696907, -12.922939],
    [ 39.74685 , -12.896604],
    [ 39.76216 , -12.840488],
    [ 39.79942 , -12.929433]])

# metrics = compute_edge_metrics(inner_triangle, outer_triangle)
# records = compute_polygon_pairs(inner_triangle, outer_triangle)
# angles, lengths = calculate_angles_and_lengths(inner_triangle, outer_triangle)
# print("Angle at inner vertices (radians):\n", metrics['angle_inner'])
# print("Angle at outer vertices (radians):\n", metrics['angle_outer'])
# print("Edge lengths:\n", metrics['edge_length'])
# print("Records:\n", records)
# print("Angles:\n", angles)
# print("Lengths:\n", lengths)

lines = create_sample_data()
time_start = time.time()
# intersecting = find_intersecting_lines(lines)
intersecting = detect_line_intersections(lines)
print(intersecting)
print(time.time() - time_start)

    