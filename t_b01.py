import numpy as np
from scipy.optimize import differential_evolution, minimize
try:
    import cma
except ImportError:
    print("pycma not installed. Install with `pip install cma`.")
    cma = None
try:
    import dlib
except ImportError:
    print("Dlib not installed. Install with `pip install dlib`.")
    dlib = None
import time

def compute_ellipse_values(points, center, axes, angle):
    """Vectorized ellipse equation values for a single ellipse."""
    points = np.asarray(points)
    center = np.asarray(center)
    axes = np.asarray(axes)
    
    translated = points - center
    
    if abs(angle) > 1e-6:
        cos_theta = np.cos(angle)
        sin_theta = np.sin(angle)
        rot_matrix = np.array([[cos_theta, sin_theta],
                               [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T
    
    return (translated[:, 0] ** 2 / axes[0] ** 2) + (translated[:, 1] ** 2 / axes[1] ** 2)

def objective(params, points, obj_type='mse'):
    """Objective function: MSE or MAE of vals from 1."""
    h, k, a, b, theta = params
    if a <= 0 or b <= 0 or a < b:
        return np.inf
    vals = compute_ellipse_values(points, [h, k], [a, b], theta)
    if obj_type == 'mse':
        return np.mean((vals - 1) ** 2)
    else:  # mae
        return np.mean(np.abs(vals - 1))

def sample_arc(arc_params, n_points=200):
    """Generate points from an elliptical arc with noise."""
    center, axes, rotation, angle_range, noise_std = arc_params
    theta = np.linspace(angle_range[0], angle_range[1], n_points)
    points = np.array([
        center[0] + axes[0] * np.cos(theta) * np.cos(rotation) - axes[1] * np.sin(theta) * np.sin(rotation),
        center[1] + axes[0] * np.cos(theta) * np.sin(rotation) + axes[1] * np.sin(theta) * np.cos(rotation)
    ]).T
    if noise_std > 0:
        points += np.random.normal(0, noise_std, points.shape)
    return points

def param_error(opt_params, true_params):
    """Approximate Euclidean error, accounting for ellipse ambiguities."""
    opt = np.array(opt_params)
    true = np.array(true_params)
    # Normalize theta to [0, pi) to handle a/b swap
    opt[4] = opt[4] % np.pi
    true[4] = true[4] % np.pi
    # Consider a/b swap
    if opt[2] < opt[3]:  # Swap a,b and adjust theta
        opt[2], opt[3] = opt[3], opt[2]
        opt[4] = (opt[4] + np.pi/2) % np.pi
    return np.linalg.norm(opt - true)

# Define arcs: [center, axes, rotation, angle_range, noise_std]
arc_configs = [
    ('90deg_no_noise', ([0, 0], [2, 1], 0, [0, np.pi/2], 0)),
    ('180deg_slight_noise', ([1, -1], [3, 1.5], np.pi/6, [0, np.pi], 0.05)),
    ('45deg_rotated_noisy', ([2, 2], [2.5, 1], np.pi/4, [0, np.pi/4], 0.1)),
    ('270deg_no_rotation', ([-1, 0], [4, 2], 0, [0, 3*np.pi/2], 0)),
    ('120deg_high_noise', ([0, 1], [3, 2], np.pi/3, [0, 2*np.pi/3], 0.1))
]

# Benchmark
def run_benchmark():
    np.random.seed(42)
    bounds = [(-10, 10), (-10, 10), (0.1, 10), (0.1, 10), (0, 2 * np.pi)]
    initial_guess = [0, 0, 1, 1, 0]
    
    print("Benchmark Results:")
    for arc_name, arc_params in arc_configs:
        points = sample_arc(arc_params, n_points=200)
        true_params = arc_params[0] + arc_params[1] + [arc_params[2]]
        
        for obj_type in ['mse', 'mae']:
            print(f"Arc: {arc_name}, Objective: {obj_type}")
            
            # Differential Evolution
            start = time.time()
            result_de = differential_evolution(
                lambda x: objective(x, points, obj_type),
                bounds,
                maxiter=200,
                popsize=15,
                seed=42
            )
            time_de = time.time() - start
            param_err_de = param_error(result_de.x, true_params)
            print(f"  DE: Obj={result_de.fun:.4f}, Time={time_de:.2f}s, Param Err={param_err_de:.4f}")
            
            # SLSQP
            start = time.time()
            result_slsqp = minimize(
                lambda x: objective(x, points, obj_type),
                initial_guess,
                method='SLSQP',
                bounds=bounds
            )
            time_slsqp = time.time() - start
            param_err_slsqp = param_error(result_slsqp.x, true_params)
            print(f"  SLSQP: Obj={result_slsqp.fun:.4f}, Time={time_slsqp:.2f}s, Param Err={param_err_slsqp:.4f}")
            
            # CMA-ES
            if cma is not None:
                start = time.time()
                result_cma = cma.fmin(
                    lambda x: objective(x, points, obj_type),
                    initial_guess,
                    sigma0=0.5,
                    options={'bounds': [[b[0] for b in bounds], [b[1] for b in bounds]], 'seed': 42}
                )
                time_cma = time.time() - start
                param_err_cma = param_error(result_cma[0], true_params)
                print(f"  CMA-ES: Obj={result_cma[1]:.4f}, Time={time_cma:.2f}s, Param Err={param_err_cma:.4f}")
            
            # Dlib (commented if not installed)
            if dlib is not None:
                start = time.time()
                result_dlib = dlib.find_min_global(
                    lambda h, k, a, b, theta: objective([h, k, a, b, theta], points, obj_type),
                    [b[0] for b in bounds],
                    [b[1] for b in bounds],
                    num_function_calls=200
                )
                time_dlib = time.time() - start
                param_err_dlib = param_error(result_dlib[0], true_params)
                print(f"  Dlib: Obj={result_dlib[1]:.4f}, Time={time_dlib:.2f}s, Param Err={param_err_dlib:.4f}")
            
            print("---")

if __name__ == "__main__":
    run_benchmark()