import networkx as nx
from shapely.geometry import Point, LineString
# from itertools import pairwise

# Your list of 20 points (Shapely Point objects)
points = [
    Point(43.275 -14.809), Point(42.807 -14.386), Point(42.55 -14.299), Point(43.041 -13.931), Point(43.175 -14.548), Point(43.163 -14.223)
]

# Convert <PERSON><PERSON><PERSON><PERSON> points to coordinate tuples
coords = [(p.x, p.y) for p in points]

# Create a complete graph with Euclidean distances as edge weights
G = nx.Graph()
for i, (x1, y1) in enumerate(coords):
    for j, (x2, y2) in enumerate(coords[i+1:], start=i+1):
        distance = Point(x1, y1).distance(Point(x2, y2))  # Shapely distance
        G.add_edge(i, j, weight=distance)

# Solve TSP using NetworkX's Christofides approximation
tsp_route = nx.approximation.traveling_salesman_problem(G, cycle=False)

# Create ordered list of points using the optimal route
ordered_points = [points[i] for i in tsp_route]
shortest_path = LineString([(p.x, p.y) for p in ordered_points])

# Calculate total distance using Shapely
# total_distance = sum(p1.distance(p2) for p1, p2 in pairwise(ordered_points))

print(f"Optimal route order: {tsp_route}")
# print(f"Total distance: {total_distance:.2f}")