import numpy as np
import time

def points_in_convex_polygon(points, poly):
    if points.ndim == 2:
        points = points[None, ...]

    segs = poly[1:] - poly[:-1]
    segs_start = poly[:-1]

    vecs = points[:, :, None, :] - segs_start[None, None, :, :]
    cross = segs[None, None, :, 0] * vecs[..., 1] - segs[None, None, :, 1] * vecs[..., 0]

    return np.all(cross >= 0, axis=-1) | np.all(cross <= 0, axis=-1)

def points_in_polygon(points, poly):
    if points.ndim == 2:
        points = points[None, ...]

    x, y = points[..., 0], points[..., 1]
    x0, y0 = poly[:-1, 0], poly[:-1, 1]
    x1, y1 = poly[1:, 0], poly[1:, 1]

    cond1 = ((y0 <= y[..., None]) & (y1 > y[..., None])) | ((y1 <= y[..., None]) & (y0 > y[..., None]))
    slope = (x1 - x0) / (y1 - y0 + 1e-12)
    xinters = x0 + slope * (y[..., None] - y0)
    cond2 = x[..., None] < xinters

    crossings = np.sum(cond1 & cond2, axis=-1)
    return crossings % 2 == 1

# Generate polygons
np.random.seed(0)
M = 250

# Convex polygon: convex hull of random points
pts = np.random.rand(M, 2)
from scipy.spatial import ConvexHull
hull = ConvexHull(pts)
convex_poly = pts[hull.vertices]
convex_poly = np.vstack([convex_poly, convex_poly[0]])

# Concave polygon: just random closed walk
concave_poly = np.cumsum(np.random.randn(M, 2), axis=0)
concave_poly = np.vstack([concave_poly, concave_poly[0]])

# Test points
points = np.random.rand(30, 250, 2)

# Warmup
points_in_convex_polygon(points, convex_poly)
points_in_polygon(points, concave_poly)

# Benchmark
def bench(func, *args, n=50):
    t0 = time.time()
    for _ in range(n):
        mask = func(*args)
    t1 = time.time()
    return (t1 - t0) / n, mask.shape

concave_time, concave_shape = bench(points_in_polygon, points, concave_poly)
print(concave_time, concave_shape)


