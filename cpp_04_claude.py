import networkx as nx
import numpy as np
from itertools import combinations

def solve_chinese_postman(G, start_node):
    """
    Find a path that traverses every edge of the graph at least once,
    starting at start_node and ending after all edges are traversed.

    Args:
        G: NetworkX undirected graph
        start_node: Node to start traversal from

    Returns:
        List of nodes representing the path
    """
    # Verify graph is connected
    if not nx.is_connected(G):
        raise nx.NetworkXError("Graph must be connected")
    if start_node not in G:
        raise nx.NetworkXError(f"Start node {start_node} not in graph")

    # Make a working copy
    H = G.copy()

    # Add weight=1 to all edges
    for u, v in H.edges():
        H[u][v]['weight'] = 1

    # Find odd-degree vertices
    odd_vertices = [v for v, d in H.degree() if d % 2 == 1]

    # Case 1: All vertices have even degree (Eulerian circuit)
    if not odd_vertices:
        circuit = list(nx.eulerian_circuit(H, source=start_node))
        path = [start_node]
        for u, v in circuit:
            path.append(v)
        # Don't return to start
        return path[:-1]

    # Case 2: Exactly two odd-degree vertices
    elif len(odd_vertices) == 2:
        if start_node in odd_vertices:
            # Perfect - start at start_node, end at other odd vertex
            other_odd = odd_vertices[0] if odd_vertices[1] == start_node else odd_vertices[1]
            path = list(nx.eulerian_path(H, source=start_node))
            node_path = [start_node]
            for u, v in path:
                node_path.append(v)
            return node_path
        else:
            # Make start_node odd by adding duplicate edge
            H.add_edge(start_node, odd_vertices[0], weight=1)
            other_odd = odd_vertices[1]
            path = list(nx.eulerian_path(H, source=start_node))
            node_path = [start_node]
            for u, v in path:
                node_path.append(v)
            return node_path

    # Case 3: More than two odd-degree vertices
    else:
        # Make start_node odd if it's even
        if H.degree(start_node) % 2 == 0:
            H.add_edge(start_node, odd_vertices[0], weight=1)
            odd_vertices = [v for v, d in H.degree() if d % 2 == 1]

        # Pair remaining odd vertices
        remaining_odd = [v for v in odd_vertices if v != start_node]

        # Ensure we have even number of remaining odd vertices
        if len(remaining_odd) % 2 != 0:
            H.add_edge(start_node, remaining_odd[0], weight=1)
            remaining_odd.pop(0)
            odd_vertices = [v for v, d in H.degree() if d % 2 == 1]
            remaining_odd = [v for v in odd_vertices if v != start_node]

        # Add minimum-weight matching for remaining odd vertices
        if remaining_odd:
            # Create complete graph of odd vertices
            odds = nx.Graph()
            for u, v in combinations(remaining_odd, 2):
                path_length = nx.shortest_path_length(H, u, v, weight='weight')
                odds.add_edge(u, v, weight=path_length)

            # Find minimum-weight matching
            matching = nx.algorithms.matching.min_weight_matching(odds)

            # Add duplicate edges along shortest paths
            for u, v in matching:
                path = nx.shortest_path(H, u, v, weight='weight')
                for i in range(len(path)-1):
                    H.add_edge(path[i], path[i+1], weight=1)

        # Now find Eulerian path starting at start_node
        odd_vertices = [v for v, d in H.degree() if d % 2 == 1]
        if len(odd_vertices) == 2 and start_node in odd_vertices:
            path = list(nx.eulerian_path(H, source=start_node))
            node_path = [start_node]
            for u, v in path:
                node_path.append(v)
            return node_path
        else:
            raise nx.NetworkXError("Failed to create valid Eulerian path")
        

import networkx as nx

# Create your graph
G = nx.Graph()
G.add_edge('A', 'B')
G.add_edge('B', 'C')
G.add_edge('B', 'D')
G.add_edge('D', 'E')
G.add_edge('D', 'F')
G.add_edge('F', 'G')
G.add_edge('F', 'H')

start_node = "A"

# Solve CPP starting from node 1
path = solve_chinese_postman(G, start_node)
print(path)