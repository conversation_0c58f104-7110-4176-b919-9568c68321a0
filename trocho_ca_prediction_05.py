import numpy as np
import math

def calculate_max_engagement_angle_D_equals_M(r_paths, sample_distances, r_tool):
    """
    Calculates the front-side engagement angle based on the methodology
    suggested by the research paper (Fig 1c, Sec 2.3), specifically
    evaluating the angle at the point where intersection D (tool/Cur1)
    is assumed to coincide with intersection M (Cur1/Cur2).

    WARNING: For certain geometries (like those in Fig 6a/b), this
             condition leads to D=M=N (N is tangency point), resulting
             in a calculated angle of 0 degrees, which may not represent
             the true maximum engagement seen in simulations/practice.

    Args:
        r_paths (np.array): Array of radii of the *path* circles followed
                            by the tool center (r1_path, r2_path, ...).
        sample_distances (np.array): Array of cumulative distances/positions
                                     corresponding to the circle centers.
        r_tool (float): Radius of the cutting tool.

    Returns:
        np.array: Array of engagement angles in degrees calculated under the
                  D=M assumption for each transition.
                  Returns NaN for transitions where offset circles don't
                  intersect or other calculation issues occur.
    """
    max_angles_deg = []
    num_circles = len(r_paths)

    if len(r_paths) != len(sample_distances):
        raise ValueError("Length of r_paths and sample_distances must be equal.")
    if num_circles < 2:
        print("Warning: Need at least two circles to calculate transitions.")
        return np.array([])

    print("--- Calculating Angles (D=M assumption) ---") # Added print statement

    for n in range(num_circles - 1):
        print(f"Processing transition {n}->{n+1}...") # Added print statement
        # --- 1. Define parameters for the current pair ---
        r1_path = r_paths[n]
        r2_path = r_paths[n+1]
        # Distance between centers of circle n and n+1
        l = abs(sample_distances[n+1] - sample_distances[n])

        if l <= 1e-9: # Avoid division by zero if centers coincide
             print(f"  Warning: Centers of circle {n} and {n+1} are coincident (l={l}). Skipping.")
             max_angles_deg.append(np.nan)
             continue

        # Offset radii (paths Cur1, Cur2)
        R1 = r1_path + r_tool
        R2 = r2_path + r_tool

        # --- 2. Check if offset circles Cur1 and Cur2 intersect ---
        min_dist_intersect = abs(R1 - R2)
        max_dist_intersect = R1 + R2
        if l < min_dist_intersect - 1e-9 or l > max_dist_intersect + 1e-9:
            print(f"  Warning: Offset circles {n}(R1={R1:.2f}) and {n+1}(R2={R2:.2f}) with distance l={l:.2f} do not intersect properly (req: {min_dist_intersect:.2f} <= l <= {max_dist_intersect:.2f}). Max angle undefined.")
            max_angles_deg.append(np.nan)
            continue
        # Check for circles being identical and overlapping
        if abs(R1 - R2) < 1e-9 and abs(l) < 1e-9:
             print(f"  Warning: Offset circles {n} and {n+1} are identical and coincident. Angle undefined. Skipping.")
             max_angles_deg.append(np.nan)
             continue


        # --- 3. Calculate Coordinates of Intersection M ---
        # Place O2 at origin (0,0), O1 is at (-l, 0)
        # M is intersection of circle(O1, R1) and circle(O2, R2)
        # Corrected formula for x-coordinate relative to O2:
        try:
             # Prevent division by zero if l is extremely small but passed intersection check somehow
             if abs(l) < 1e-9:
                 raise ZeroDivisionError("l is near zero")

             xM = (R1**2 - R2**2 - l**2) / (-2 * l) # Derived from law of cosines or direct eqn subtraction: (xM+l)^2 - xM^2 = R1^2 - R2^2
             # Recalculate yM^2 based on circle O2
             yM_squared = R2**2 - xM**2
        except (ZeroDivisionError, ValueError) as e:
            print(f"  Error calculating M coordinates: {e}. l={l}, R1={R1}, R2={R2}. Skipping.")
            max_angles_deg.append(np.nan)
            continue


        if yM_squared < -1e-9: # Allow for small negative due to float errors
             # Check if it's because they just touch (|R1-R2| approx l or R1+R2 approx l)
             if abs(l - min_dist_intersect) < 1e-6 or abs(l - max_dist_intersect) < 1e-6:
                 yM_squared = 0 # Tangent case, yM is zero
             else:
                 print(f"  Warning: yM_squared negative ({yM_squared:.2e}) for pair {n}-{n+1}. Check intersection logic. l={l}, R1={R1}, R2={R2}, xM={xM}")
                 max_angles_deg.append(np.nan)
                 continue
        yM = np.sqrt(max(0, yM_squared)) # Take positive root (upper intersection)
        # M coordinates relative to O2
        M_coord = np.array([xM, yM])
        # M coordinates relative to O1
        # M_coord_O1 = np.array([xM + l, yM])
        # Sanity check distances:
        # dist_O1M = np.linalg.norm(M_coord_O1) # Should be R1
        dist_O2M = np.linalg.norm(M_coord) # Should be R2
        # print(f"  Debug: M=({xM:.3f},{yM:.3f}) rel O2. Dist O2M={dist_O2M:.3f} (R2={R2:.3f})")
        if abs(dist_O2M - R2) > 1e-4: # Increased tolerance slightly
            print(f"  Warning: M distance check failed. O2M={dist_O2M:.4f} != R2={R2:.4f}. Check M calc.")
            max_angles_deg.append(np.nan)
            continue


        # --- 4. Find Tool Center C based on D=M condition ---
        # C is on circle(O2, r2_path) and distance(C, M) = r_tool
        # Solve triangle O2CM: sides are O2C=r2_path, CM=r_tool, O2M=R2
        try:
            # Angle gamma at O2 (angle MO2C) using Law of Cosines on O2CM
            # CM^2 = O2C^2 + O2M^2 - 2*O2C*O2M*cos(gamma)
            cos_gamma_arg = (r2_path**2 + R2**2 - r_tool**2) / (2 * r2_path * R2)
        except ZeroDivisionError:
             print(f"  Warning: Zero division calculating cos_gamma. r2_path={r2_path}, R2={R2}. Skipping.")
             max_angles_deg.append(np.nan)
             continue

        if cos_gamma_arg < -1.0 - 1e-9 or cos_gamma_arg > 1.0 + 1e-9:
            print(f"  Warning: Invalid argument for acos(gamma)={cos_gamma_arg:.3f}. Check triangle O2CM inequality: sides {r2_path:.2f}, {r_tool:.2f}, {R2:.2f}. Skipping.")
            max_angles_deg.append(np.nan)
            continue

        cos_gamma_arg = max(-1.0, min(1.0, cos_gamma_arg)) # Clamp
        gamma = np.arccos(cos_gamma_arg)

        # Angle of vector O2M relative to O2's positive x-axis
        theta_M = np.arctan2(yM, xM)

        # Two possible angles for O2C: theta_M +/- gamma
        theta_C1 = theta_M + gamma
        theta_C2 = theta_M - gamma

        # Coordinates of the two possible C points
        C1_coord = np.array([r2_path * np.cos(theta_C1), r2_path * np.sin(theta_C1)])
        C2_coord = np.array([r2_path * np.cos(theta_C2), r2_path * np.sin(theta_C2)])

        # Select the correct C. Heuristic: Usually the one "further" from O1?
        # Or the one corresponding to the "outer" turn. Let's calculate angle for both
        # and take the max, assuming one might be invalid geometry.
        potential_angles = []

        for C_coord in [C1_coord, C2_coord]:
            xc, yc = C_coord

            # --- 5. Calculate Tangency Point N ---
            # N is on line O2C, distance O2N = R2
            # Unit vector u_O2C = C_coord / r2_path (if r2_path > 0)
            if r2_path < 1e-9:
                print("  Warning: r2_path is zero, cannot find N. Skipping solution.")
                continue # N undefined if C coincides with O2
                
            u_O2C = C_coord / r2_path
            N_coord = R2 * u_O2C
            # xN, yN = N_coord

            # --- 6. Calculate Angle alpha = DCN (with D=M) ---
            # Vector CD = M_coord - C_coord
            # Vector CN = N_coord - C_coord
            vec_CD = M_coord - C_coord
            vec_CN = N_coord - C_coord

            # Use dot product: vec_CD . vec_CN = |CD| * |CN| * cos(alpha)
            # We expect |CD| = r_tool and |CN| = r_tool
            norm_CD = np.linalg.norm(vec_CD)
            norm_CN = np.linalg.norm(vec_CN)

            # Check if norms are close to r_tool
            if abs(norm_CD - r_tool) > 1e-4 or abs(norm_CN - r_tool) > 1e-4:
                # This often happens due to the N=M degeneracy discussed
                # print(f"  Debug: Norm check failed for C=({xc:.3f},{yc:.3f}). |CD|={norm_CD:.3f}, |CN|={norm_CN:.3f}, r_tool={r_tool:.3f}")
                # If norms are near zero because N=M=C, angle is undefined
                if norm_CD < 1e-6 and norm_CN < 1e-6:
                     print("  Debug: N=M=C case, angle undefined.")
                     continue


            # Avoid division by zero if norms are very small
            if norm_CD < 1e-9 or norm_CN < 1e-9:
                # This indicates N=C or M=C, which implies N=M case if |CD|=|CN|=r_tool was expected
                # In the N=M case, both norms are r_tool (unless r_tool=0), but vec_CN == vec_CD
                 cos_alpha_arg = 1.0 # Vectors are identical
                 # print(f"  Debug: Detected N approx M for C=({xc:.3f},{yc:.3f}). Setting cos_alpha=1.")
            else:
                dot_product = np.dot(vec_CD, vec_CN)
                cos_alpha_arg = dot_product / (norm_CD * norm_CN)


            # Clamp argument to [-1, 1] due to potential floating point errors
            cos_alpha_arg = max(-1.0, min(1.0, cos_alpha_arg))

            alpha_rad = np.arccos(cos_alpha_arg)
            potential_angles.append(np.degrees(alpha_rad))
            # print(f"  Debug: C=({xc:.3f},{yc:.3f}), N=({xN:.3f},{yN:.3f}), M=({xM:.3f},{yM:.3f}) -> Angle={np.degrees(alpha_rad):.2f}")


        # Select the maximum valid angle calculated from the potential solutions for C
        if not potential_angles:
             print(f"  Warning: No valid engagement angle found for pair {n}-{n+1} after checking C solutions.")
             max_angles_deg.append(np.nan)
        else:
             # Filter out potential invalid 0 values if others exist? No, 0 is valid.
             calculated_angle = max(potential_angles)
             # Add note about potential 0 due to degeneracy
             if abs(calculated_angle) < 1e-3 and (abs(r2_path + r_tool - R2) < 1e-6) : # Check if potentially degenerate
                  print(f"  Note: Calculated angle is near 0. This might be due to D=M => N=M degeneracy.")
             max_angles_deg.append(calculated_angle)


    print("--- Calculation Finished ---") # Added print statement
    return np.array(max_angles_deg)

# --- Example Usage ---
# Source data from the request prompt
r_paths_input = np.array([20, 22, 24, 26, 27]) # Radii of tool center path
distances_input = np.array([0.0, 0.1, 0.2, 0.3, 0.4]) # Assume these are positions

# Assume a tool radius
tool_radius = 3.0

# Calculate the maximum engagement angles using D=M assumption
max_angles = calculate_max_engagement_angle_D_equals_M(r_paths_input, distances_input, tool_radius)

print("\n--- Results for Prompt Input ---")
print(f"Input Path Radii: {r_paths_input}")
print(f"Input Positions: {distances_input}")
print(f"Tool Radius: {tool_radius}")
print("-" * 30)
for i in range(len(max_angles)):
    l_dist = abs(distances_input[i+1] - distances_input[i])
    print(f"Transition {i}->{i+1} (r1_path={r_paths_input[i]}, r2_path={r_paths_input[i+1]}, l={l_dist:.2f}): Max Engagement Angle (D=M) = {max_angles[i]:.2f} degrees")

print("-" * 30)
print(f"Result Array: {max_angles}")

# --- Example based on Fig 6(b) ---
r_paths_fig6b = np.array([2, 2]) # r_path = r_machined
distances_fig6b = np.array([0.0, 2.0]) # l = 2.0
tool_radius_fig6b = 3.0
max_angle_fig6b = calculate_max_engagement_angle_D_equals_M(r_paths_fig6b, distances_fig6b, tool_radius_fig6b)
print("\n--- Results for Fig 6(b) parameters (r1_path=2, r2_path=2, l=2.0, r_tool=3.0) ---")
print(f"Result: {max_angle_fig6b[0]:.2f} degrees")

# --- Example based on Fig 6(a) ---
r_paths_fig6a = np.array([3, 2]) # r_path = r_machined
distances_fig6a = np.array([0.0, 2.2]) # l = 2.2
tool_radius_fig6a = 2.0
max_angle_fig6a = calculate_max_engagement_angle_D_equals_M(r_paths_fig6a, distances_fig6a, tool_radius_fig6a)
print("\n--- Results for Fig 6(a) parameters (r1_path=3, r2_path=2, l=2.2, r_tool=2.0) ---")
print(f"Result: {max_angle_fig6a[0]:.2f} degrees")