import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Polygon, Point
from shapely.affinity import rotate as shapely_rotate
from scipy.optimize import minimize_scalar

# --- 1. Define Ellipse Generation ---
def get_ellipse_points(center_x, center_y, semi_major, semi_minor, angle_deg, num_points=100):
    """
    Generates points for an ellipse.
    angle_deg: rotation angle of the ellipse in degrees.
    """
    t = np.linspace(0, 2 * np.pi, num_points)
    ellipse_x_unrotated = semi_major * np.cos(t)
    ellipse_y_unrotated = semi_minor * np.sin(t)

    angle_rad = np.deg2rad(angle_deg)
    cos_angle = np.cos(angle_rad)
    sin_angle = np.sin(angle_rad)

    ellipse_x_rotated = ellipse_x_unrotated * cos_angle - ellipse_y_unrotated * sin_angle
    ellipse_y_rotated = ellipse_x_unrotated * sin_angle + ellipse_y_unrotated * cos_angle

    ellipse_x = center_x + ellipse_x_rotated
    ellipse_y = center_y + ellipse_y_rotated

    return np.column_stack((ellipse_x, ellipse_y))

# --- 2. Simulate Input ---

# Green Ellipse (Target)
green_center = (5, 5)
green_semi_major = 8
green_semi_minor = 4
green_angle_deg = 30  # Target orientation
green_points = get_ellipse_points(green_center[0], green_center[1],
                                  green_semi_major, green_semi_minor,
                                  green_angle_deg)
green_polygon = Polygon(green_points)

# Red Ellipse (Initial - to be rotated)
# For a perfect match, it must have the same intrinsic shape as green
red_semi_major = green_semi_major
red_semi_minor = green_semi_minor

# Initial parameters for the red ellipse
initial_red_center = (2, 2) # Arbitrary initial center
initial_red_angle_deg = -15 # Arbitrary initial angle

# Define the pivot point. It MUST be on the red ellipse.
# Let's pick the point that would be at (semi_major, 0) if the ellipse were
# centered at origin and unrotated.
pivot_local_x = red_semi_major * np.cos(0) # t=0 point
pivot_local_y = red_semi_minor * np.sin(0) # t=0 point

# Transform this local pivot point according to the initial red ellipse's transformation
initial_red_angle_rad = np.deg2rad(initial_red_angle_deg)
cos_initial_red = np.cos(initial_red_angle_rad)
sin_initial_red = np.sin(initial_red_angle_rad)

pivot_rotated_x = pivot_local_x * cos_initial_red - pivot_local_y * sin_initial_red
pivot_rotated_y = pivot_local_x * sin_initial_red + pivot_local_y * cos_initial_red

pivot_x = initial_red_center[0] + pivot_rotated_x
pivot_y = initial_red_center[1] + pivot_rotated_y
pivot_coords = (pivot_x, pivot_y) # This is our fixed pink dot

# Generate the initial red ellipse polygon
initial_red_points = get_ellipse_points(initial_red_center[0], initial_red_center[1],
                                        red_semi_major, red_semi_minor,
                                        initial_red_angle_deg)
initial_red_polygon = Polygon(initial_red_points)


# --- 3. Objective Function ---
# The `initial_red_polygon` and `pivot_coords` are fixed for the objective function.
# `green_polygon` is also fixed (the target).

def objective_function(rotation_angle_to_apply_deg):
    """
    Calculates the symmetric difference area after rotating the initial_red_polygon
    around pivot_coords by rotation_angle_to_apply_deg.
    """
    # Rotate the *initial* red polygon by the *additional* rotation_angle_to_apply_deg
    # around the specified pivot point.
    rotated_red_polygon = shapely_rotate(initial_red_polygon,
                                         rotation_angle_to_apply_deg,
                                         origin=pivot_coords) # origin takes tuple (x,y) or Point

    # Calculate the area of the symmetric difference
    # symmetric_difference = (A union B) - (A intersection B)
    # If they match perfectly, this area is close to zero.
    difference_area = green_polygon.symmetric_difference(rotated_red_polygon).area
    return difference_area

# --- 4. Optimization ---
# We are looking for an additional rotation to apply to the initial red ellipse.
# The bounds for rotation are -180 to 180 degrees.
result = minimize_scalar(objective_function, bounds=(-180, 180), method='bounded')

optimal_additional_rotation_deg = result.x
min_difference_area = result.fun

print(f"Optimal additional rotation for red ellipse: {optimal_additional_rotation_deg:.2f} degrees")
print(f"Minimum symmetric difference area: {min_difference_area:.4e}")

# --- 5. Get Final Rotated Ellipse and Visualization ---
final_rotated_red_polygon = shapely_rotate(initial_red_polygon,
                                           optimal_additional_rotation_deg,
                                           origin=pivot_coords)

# Plotting
fig, ax = plt.subplots(figsize=(10, 8))

# Green ellipse (target)
x_g, y_g = green_polygon.exterior.xy
ax.plot(x_g, y_g, color='green', linewidth=2, label='Green Ellipse (Target)')
ax.fill(x_g, y_g, color='green', alpha=0.3)

# Initial Red ellipse
x_r_init, y_r_init = initial_red_polygon.exterior.xy
ax.plot(x_r_init, y_r_init, color='red', linestyle='--', linewidth=2, label='Initial Red Ellipse')
ax.fill(x_r_init, y_r_init, color='red', alpha=0.2)

# Final Rotated Red ellipse
x_r_final, y_r_final = final_rotated_red_polygon.exterior.xy
ax.plot(x_r_final, y_r_final, color='purple', linewidth=2, label=f'Final Rotated Red Ellipse (Rotated by {optimal_additional_rotation_deg:.2f}°)')
ax.fill(x_r_final, y_r_final, color='purple', alpha=0.3)

# Pivot point (Pink dot)
ax.plot(pivot_coords[0], pivot_coords[1], 'mo', markersize=10, label='Pivot Point (Pink Dot)')

ax.set_aspect('equal', adjustable='box')
ax.legend()
ax.set_title('Ellipse Rotation to Match')
ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.grid(True)
plt.show()

# Verify: The total rotation of the red ellipse that matches the green one
# (relative to an unrotated ellipse centered at origin)
# This is just for verification, not part of the core problem asked.
# The initial_red_angle_deg was how the red ellipse was "pre-rotated" from a standard
# axis-aligned position BEFORE its center was set.
# The optimal_additional_rotation_deg is applied to this already oriented ellipse.
# It's a bit complex to get an "absolute" final angle without knowing how shapely_rotate
# combines with the initial orientation.
# However, if min_difference_area is very small, we know the shapes and orientations match.
# The key is that the center of the final_rotated_red_polygon will NOT be initial_red_center,
# because we rotated around a point that is not its center.
# And its "effective" orientation angle should be close to green_angle_deg IF the pivot was
# chosen such that a simple rotation could achieve this.

print(f"\nFor verification (if match is good):")
print(f"Green ellipse centroid: {green_polygon.centroid.x:.2f}, {green_polygon.centroid.y:.2f}")
print(f"Final Red ellipse centroid: {final_rotated_red_polygon.centroid.x:.2f}, {final_rotated_red_polygon.centroid.y:.2f}")
print(f"Green ellipse area: {green_polygon.area:.2f}")
print(f"Final Red ellipse area: {final_rotated_red_polygon.area:.2f}")
print(f"Intersection area: {green_polygon.intersection(final_rotated_red_polygon).area:.2f}")