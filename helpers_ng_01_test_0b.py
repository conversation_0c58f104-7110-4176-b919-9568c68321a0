import numpy as np
import shapely
import shapely.ops
import shapely.vectorized
import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time
from dataclasses import dataclass
from typing import Tuple, Optional, Dict, List, Union
from shapely.ops import nearest_points
from scipy.spatial.distance import cdist
import cv2


@dataclass
class EllipseOptimizationResult:
    """Container for ellipse optimization results, replacing the data_box hack."""
    minor: float
    new_anchor: np.ndarray
    new_rotation: float
    ellipse_center: np.ndarray
    distances: Optional[List[float]] = None


@dataclass
class EllipseParameters:
    """Container for ellipse parameters with proper typing."""
    major: float
    minor: float
    center: np.ndarray
    anchor: np.ndarray
    rotation: float
    length: Optional[float] = None


def _normalize_position(position: float) -> float:
    """Normalize position to handle negative values consistently."""
    return position if position > 0 else 1 + position


def _calculate_ellipse_perimeter(major_axis: float, minor_axis: float) -> float:
    """Calculate exact ellipse perimeter using numerical integration."""
    eccentricity = np.sqrt(1 - (minor_axis**2 / major_axis**2))
    return 4 * major_axis * scipy.special.ellipe(eccentricity)


def _create_maximum_boundary_ellipse(ellipse_data: Dict, max_distance: float) -> shapely.geometry.Polygon:
    """Create a maximum boundary ellipse for constraint checking."""
    geometry, _ = create_ellipse(
        anchor_point=ellipse_data['center'],
        semi_major_axis=ellipse_data['major'] + max_distance,
        semi_minor_axis=ellipse_data['minor'] + max_distance,
        rotation=ellipse_data['rotation']
    )
    polygon = shapely.geometry.Polygon(geometry)
    shapely.prepare(polygon)
    return polygon


def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def find_farthest_outside_points_ng_vectorized(
    inner_ellipses: np.ndarray, 
    outer_polygon: shapely.geometry.Polygon
) -> np.ndarray:
    """
    Finds the farthest outside point for each inner ellipse relative to an outer polygon.
    This is a vectorized version that processes an array of ellipses at once.

    Args:
        inner_ellipses (np.ndarray): A NumPy array of shapely.geometry.LineString objects.
        outer_polygon (Shapely Polygon): The single outer polygon to check against.

    Returns:
        np.ndarray: A 1D array where each element is the maximum penetration distance 
                    for the corresponding ellipse. Returns 0.0 for an ellipse if no 
                    part of it is outside the polygon.
    """
    if inner_ellipses.size == 0:
        return np.array([], dtype=float)

    # 1. Get all coordinates and an index mapping them back to their original ellipse
    # This is a highly efficient way to handle coordinates from multiple geometries.
    all_coords, geom_indices = shapely.get_coordinates(inner_ellipses, return_index=True)

    # 2. Vectorized check for which points are outside the polygon
    x, y = all_coords[:, 0], all_coords[:, 1]
    is_inside = shapely.vectorized.contains(outer_polygon, x, y)
    outside_mask = ~is_inside
    
    # 3. Filter to get only the points, their original ellipse index, and create geometries
    outside_coords = all_coords[outside_mask]
    if outside_coords.shape[0] == 0:
        # No points are outside for any of the ellipses
        return np.zeros(len(inner_ellipses))
        
    outside_indices = geom_indices[outside_mask]
    outside_points_geom = shapely.points(outside_coords)
    
    # 4. Calculate the distance from the polygon boundary to each outside point
    distances_to_boundary = shapely.distance(outer_polygon, outside_points_geom)

    # 5. Find the maximum distance for each original ellipse
    # Initialize max distances for each ellipse to zero
    num_ellipses = len(inner_ellipses)
    max_distances = np.zeros(num_ellipses)

    # np.maximum.at performs a vectorized "group-by and find max" operation.
    # For each point's distance, it updates the maximum value found so far for its parent ellipse.
    np.maximum.at(max_distances, outside_indices, distances_to_boundary)

    return max_distances


def find_farthest_outside_point_ng(inner_ellipse_coords: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse_coords (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.
    """
    x = inner_ellipse_coords[:, 0]
    y = inner_ellipse_coords[:, 1]

    # Filter points outside ellipse B
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b

    outside_points_geom = shapely.points(inner_ellipse_coords[outside_mask])

    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)

    # Check if there are any outside points
    if len(distances) == 0:
        return 0.0

    # Find maximum
    return np.max(distances)




def find_farthest_outside_point(inner_ellipse: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.
    """

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]

    # Filter points outside ellipse B
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b

    outside_points_geom = shapely.points(inner_ellipse[outside_mask])

    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)

    # Check if there are any outside points
    if len(distances) == 0:
        return 0.0

    # Find maximum
    return np.max(distances)


def find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation']
    outer_center = outer_ellipse_data['center']

    t_arc = find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)
    
    # 2. Apply the angular offset to get the effective parameter
    # t_effective = t_arc + start_angle_rad
    
    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_arc), outer_semi_minor_axis * np.sin(t_arc)])
    point_vector = rotate_vector(point_vector, outer_rotation)
    point_vector += outer_center

    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_arc), outer_semi_major_axis * np.sin(t_arc)])
    normal_vector = rotate_vector(normal_vector, outer_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])

    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])

    return point_vector, normal_angle


def get_ellipse_distances_ng_vectorized(
    inner_ellipses: np.ndarray, 
    sides_data: dict
) -> np.ndarray:
    """
    Calculates distances from an array of ellipses to a set of side geometries.

    This is a vectorized version that performs the following steps:
    1. Calculates distances between all ellipses and all sides.
    2. Checks for intersections between all ellipses and all sides.
    3. For intersecting pairs, it calculates the penetration distance relative to the outer polygon.
    4. Uses np.where to combine these results into a final distance matrix.

    Args:
        inner_ellipses (np.ndarray): A NumPy array of shapely.geometry.LineString objects.
        sides_data (dict): A dictionary containing:
            - 'lines' (list or np.ndarray): Side geometries to measure distances to.
            - 'polygon' (shapely.geometry.Polygon): The outer polygon used for penetration depth.

    Returns:
        np.ndarray: A 2D array of shape (num_ellipses, num_sides) containing the distances.
                    Distances are negative if the ellipse intersects the corresponding side line.
    """
    # Ensure inputs are NumPy arrays for broadcasting
    side_lines = np.array(sides_data['lines'])
    outer_polygon = sides_data['polygon']

    if inner_ellipses.size == 0 or side_lines.size == 0:
        return np.empty((len(inner_ellipses), len(side_lines)))

    # To compare every ellipse with every side line, we need to use broadcasting.
    # Reshape inner_ellipses to a column vector (N, 1) to operate against the
    # row vector of side_lines (M,). NumPy/Shapely will broadcast this to (N, M).
    ellipses_col = inner_ellipses[:, np.newaxis]

    # 1. Calculate the intersection mask for all (ellipse, side) pairs
    # The result is a boolean matrix of shape (num_ellipses, num_sides)
    intersects_mask = shapely.intersects(ellipses_col, side_lines)
    
    # 2. Calculate the "no intersection" distances for all pairs
    # This is the default distance if there's no intersection.
    # The result is a float matrix of shape (num_ellipses, num_sides)
    positive_distances = shapely.distance(ellipses_col, side_lines)

    # 3. Calculate the "intersection" distances (penetration depth)
    # This is done for each ellipse, independent of which side it hits.
    # The result is a 1D array of shape (num_ellipses,).
    penetration_distances = find_farthest_outside_points_ng_vectorized(inner_ellipses, outer_polygon)

    # Make the penetration distances negative and reshape to a column vector (N, 1)
    # so it can be broadcast across all sides for the np.where condition.
    negative_distances = -penetration_distances[:, np.newaxis]

    # 4. Combine the results using the intersection mask
    # np.where(condition, value_if_true, value_if_false)
    # If intersects_mask[i, j] is True, use the negative_distances[i].
    # Otherwise, use the positive_distances[i, j].
    final_distances = np.where(intersects_mask, negative_distances, positive_distances)

    return final_distances


def get_ellipse_distances_ng(inner_ellipse_coords: np.ndarray, inner_ellipse: shapely.geometry.LineString, sides_data):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.

    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries

    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse

    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    distances = np.zeros(2)
    for i, side_line in enumerate(sides_data['lines']):
        if inner_ellipse.intersects(side_line):
            ## ellipse part as a result of the intersection
            dist = find_farthest_outside_point_ng(inner_ellipse_coords, sides_data['polygon'])
            distances[i] = -dist # negative, because of intersection
        else:
            distances[i] = side_line.distance(inner_ellipse)

    return distances



def find_ellipse_position_ng(inner_ellipse_data, outer_ellipse_data, sides_data, front_data, search_scale, tolerance=1e-4):
    """
    Finds the optimal scale factor where the inner and outer distances are equal
    using the efficient Brent's method.
    """
    def objective_function(position):
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)                
        new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
            outer_ellipse_data = outer_ellipse_data,
            fraction=position % 1
            )
        
        ellipse_geometry, _ = create_ellipse(
            anchor_point=new_anchor,
            semi_major_axis=inner_ellipse_data['major'],
            semi_minor_axis=inner_ellipse_data['minor'],
            rotation=new_rotation,
            anchor_on_perimeter=True
            )

        # Calculate distances to sides
        ellipse = shapely.geometry.LineString(ellipse_geometry)

        # Calculate the distances for the given position        
        distances = get_ellipse_distances_ng(ellipse_geometry, ellipse, sides_data)        
        return distances[0] - distances[1]

    front_data['fractions'][0] *= search_scale
    front_data['fractions'][1] *= search_scale
    
    return scipy.optimize.brentq(objective_function, a=front_data['fractions'][0], b=front_data['fractions'][1], xtol=tolerance, full_output=True)    


def find_minor(outer_ellipse_data, sides_data, front_fractions, minimum_minor, maximum_minor,
               outer_ellipse, test_inner_data, maximum_right_dist, tolerance=1e-4) -> Tuple[float, EllipseOptimizationResult]:
    """
    Find the optimal minor axis length for an ellipse using optimization.

    Returns:
        Tuple containing the optimal minor axis length and optimization results
    """
    # Store optimization results to return
    optimization_result = EllipseOptimizationResult(
        minor=0.0, new_anchor=np.array([0, 0]), new_rotation=0.0, ellipse_center=np.array([0, 0])
    )

    def objective_function(minor: float) -> float:
        """Objective function for minor axis optimization."""
        test_inner_data['minor'] = minor

        position = find_ellipse_position(
            test_inner_data, outer_ellipse_data, sides_data, front_fractions, tolerance=1e-3
        )
        position = _normalize_position(position)

        new_distances, new_anchor, new_rotation, ellipse_center = get_ellipse_distances(
            test_inner_data, outer_ellipse_data, sides_data, position
        )

        ellipse_geometry, _ = create_ellipse(
            anchor_point=ellipse_center,
            semi_major_axis=test_inner_data['major'],
            semi_minor_axis=test_inner_data['minor'],
            rotation=new_rotation
        )

        minor_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)

        # Apply distance corrections based on constraints
        if minor_distance < maximum_right_dist:
            minor_distance = -minor_distance
        elif minor_distance == 0.0:
            minor_distance = -1e100

        # Store results for return
        optimization_result.distances = new_distances
        optimization_result.new_anchor = new_anchor
        optimization_result.new_rotation = new_rotation
        optimization_result.ellipse_center = ellipse_center
        optimization_result.minor = minor

        return minor_distance

    optimal_minor = scipy.optimize.brentq(
        objective_function, a=minimum_minor, b=maximum_minor, xtol=tolerance
    )

    return optimal_minor, optimization_result


def intersection_span_along_line3(outer_data, inner_data, sides_data, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.15 # in mm
        poly = sides_data['poly']

        outer_ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_data['center'],
            semi_major_axis=outer_data['major'],
            semi_minor_axis=outer_data['minor'],
            rotation=outer_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)
        inter_outer = outer_linear_ring.intersection(poly)

        if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')           
            return None
        

        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter_outer.geoms]
        distances.append(
            inter_outer.geoms[np.argmin(outer_projected_distances)].distance(inter_outer.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_data['center'],
            semi_major_axis=inner_data['major'] + inner_correction,
            semi_minor_axis=inner_data['minor'] + inner_correction,
            rotation=inner_data['rotation']
            )

        inner_ellipse = shapely.geometry.LinearRing(inner_ellipse_geometry)

        # left half of the ellipse, ccw
        # anchor -----ccw---->end
        left_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[:36])        
        left_point = shapely.ops.nearest_points(left_inner_linear_ring, sides_data['lines'][0])[1]
        
        # reversed right half of the ellipse, cw
        # anchor -----cw---->end
        right_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[35:][::-1])
        right_point = shapely.ops.nearest_points(right_inner_linear_ring, sides_data['lines'][1])[1]

        distances.append(left_point.distance(right_point))

        front_fractions = [
            inner_ellipse.project(left_point, normalized=True),
            inner_ellipse.project(right_point, normalized=True)
            ]
        '''
        if idx==12:            
            create_line_object(sides_data['lines'][0].coords, "side_left", color=(1, 0, 0, 1))
            create_line_object(sides_data['lines'][1].coords, "side_right", color=(1, 0, 0, 1))
            create_line_object(left_inner_linear_ring.coords, "left_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(right_inner_linear_ring.coords, "right_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(poly.coords, "poly", color=(1, 0, 0, 1))

            create_line_object(left_point.coords, "left_point", color=(0, 1, 0, 1))
            create_line_object(right_point.coords, "right_point", color=(0, 1, 0, 1))
            for geom in inter_outer.geoms:
                create_line_object(geom.coords, "outer_inter", color=(0, 1, 0, 1))
        '''  
            

        return distances, front_fractions


def intersection_span_along_line2(outer_data, inner_data, poly, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.1 # in mm

        outer_ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_data['center'],
            semi_major_axis=outer_data['major'],
            semi_minor_axis=outer_data['minor'],
            rotation=outer_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)
        inter = outer_linear_ring.intersection(poly)

        if inter.is_empty or shapely.get_num_geometries(inter) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
            return None

        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter.geoms]
        distances.append(
            inter.geoms[np.argmin(outer_projected_distances)].distance(inter.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_data['center'],
            semi_major_axis=inner_data['major'] - inner_correction,
            semi_minor_axis=inner_data['minor'] - inner_correction,
            rotation=inner_data['rotation']
            )

        inner_points = shapely.points(inner_ellipse_geometry)
        inner_distances = shapely.distance(inner_points, poly)
        max_distance = 0.3
        mask_inner = inner_distances < max_distance
        inner_distances = inner_distances[mask_inner]
        print(f'inner_distances: {inner_distances}')

        return

        inner_projected_distances = [inner_linear_ring.project(d, normalized=True) for d in inter.geoms]
        distances.append(
            inter.geoms[np.argmin(inner_projected_distances)].distance(inter.geoms[np.argmax(inner_projected_distances)])
        )


        for i, ellipse_data in enumerate((outer_data, inner_data)):
            inner_scale_correction  = 1 - (0.01 * i)
            ellipse_geometry, _ = create_ellipse(
                anchor_point=ellipse_data['center'],
                semi_major_axis=ellipse_data['major'] * inner_scale_correction,
                semi_minor_axis=ellipse_data['minor'] * inner_scale_correction,
                rotation=ellipse_data['rotation']
                )
            outer_linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
            inter = outer_linear_ring.intersection(poly)

            if inter.is_empty or shapely.get_num_geometries(inter) < 4:
                print('intersection is empty or has less than 4 components (linear_ring is to small) -> intersection_span_along_line()')
                return None

            create_line_object(outer_linear_ring.coords, f"linear_ring_{i}", color=(1, 0, 0, 1))
            for point in inter.geoms:
                create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))

            outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter.geoms]
            distances.append(
                inter.geoms[np.argmin(outer_projected_distances)].distance(inter.geoms[np.argmax(outer_projected_distances)])
                )

            if i == 0: # outer ellipse - front fractions calculus
                front_fractions = [min(outer_projected_distances), max(outer_projected_distances)]

        # print(f'projected_distances: {projected_distances}')
        # if idx==13:
        #     for point in inter.geoms:
        #         create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))
        #     create_line_object(linear_ring.coords, f"linear_ring_{idx}", color=(1, 0, 0, 1))

        return distances, front_fractions



def intersection_span_along_line(outer_data, inner_data, poly, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None

        for i, ellipse_data in enumerate((outer_data, inner_data)):
            inner_scale_correction  = 0.5 * i ## in mm
            ellipse_geometry, _ = create_ellipse(
                anchor_point=ellipse_data['center'],
                semi_major_axis=ellipse_data['major'] + inner_scale_correction,
                semi_minor_axis=ellipse_data['minor'] + inner_scale_correction,
                rotation=ellipse_data['rotation']
                )
            linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
            inter = linear_ring.intersection(poly)

            if inter.is_empty or shapely.get_num_geometries(inter) < 4:
                print(f'intersection is empty or has less than 4 components ({shapely.get_num_geometries(inter)}) -> intersection_span_along_line()')
                print(f'inter: {inter}')
                return None

            # create_line_object(linear_ring.coords, f"linear_ring_{i}", color=(1, 0, 0, 1))
            # for point in inter.geoms:
            #     create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))

            projected_distances = [linear_ring.project(d, normalized=True) for d in inter.geoms]
            distances.append(
                inter.geoms[np.argmin(projected_distances)].distance(inter.geoms[np.argmax(projected_distances)])
                )

            if i == 0: # outer ellipse - front fractions calculus
                front_fractions = [min(projected_distances), max(projected_distances)]

            # print(f'projected_distances: {projected_distances}')
            if idx==0:
                if i in [0,1] :
                    for point in inter.geoms:
                        create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))
                    create_line_object(linear_ring.coords, f"linear_ring_{idx}", color=(1, 0, 0, 1))

        return distances, front_fractions

# def get_front_points(ellipse_data, poly):
#         """
#         Returns the front points of an ellipse that are on the boundary of a polygon.
#         """
#         points = []
#         ellipse_geometry, _ = create_ellipse(
#             anchor_point=ellipse_data['center'],
#             semi_major_axis=ellipse_data['major'],
#             semi_minor_axis=ellipse_data['minor'],
#             rotation=ellipse_data['rotation']
#             )
#         linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
#         inter = linear_ring.intersection(poly)
#         if inter.is_empty:
#             print('intersection is empty (get_front_points())')
#             return None
#         projected_distances = [linear_ring.project(d, normalized=True) for d in inter.geoms]
#         points.append(inter.geoms[np.argmin(projected_distances)])
#         points.append(inter.geoms[np.argmax(projected_distances)])

#         return points


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None


def get_ellipse_end_point(polygon_boundary, medial_axis_edge):
        center = shapely.get_coordinates(medial_axis_edge)[-1]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_semi_axis_length,
            semi_minor_axis=outer_semi_axis_length,
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                anchor = shapely.get_coordinates(
                    inter.geoms[np.argmax(distances)])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]
        else:
            print('ellipse does not intersect medial axis')
            return None

        # rotate 180 degrees (pi radians) around center
        return 2 * center - anchor


def arc_polyline(start, end, center, resolution=71):
    """
    Return a 2-D polyline approximating the circular arc from `start` to `end`
    around `center`.  Always counter-clockwise along the shorter arc.
    """
    start, end, center = map(np.asarray, (start, end, center))

    v_start = start - center
    v_end   = end   - center
    #print lenght of v_start and v_end
    # print(f'v_start: {np.linalg.norm(v_start)}')
    # print(f'v_end: {np.linalg.norm(v_end)}')

    r = np.hypot(*v_start)
    if abs(np.hypot(*v_end) - r) > 1e-1:
        print(f'start end distance: {abs(np.hypot(*v_end) - r)}')
        raise ValueError("start and end must be equidistant from center")

    angle = np.arctan2(v_end[1], v_end[0]) - np.arctan2(v_start[1], v_start[0])
    num_pts = int(abs(angle) / (2 * np.pi) * resolution) + 1
    if num_pts < 3:
        num_pts = 3

    if angle < -np.pi:
        angle += 2 * np.pi
    elif angle > np.pi:
        angle -= 2 * np.pi

    t = np.linspace(0, angle, num_pts)
    cos_t, sin_t = np.cos(t), np.sin(t)

    x = center[0] + v_start[0] * cos_t - v_start[1] * sin_t
    y = center[1] + v_start[0] * sin_t + v_start[1] * cos_t

    points = np.column_stack((x, y))
    # Replace last point with exact end coordinates
    points[-1] = end

    return points


def get_ellipse_data(polygon_boundary, medial_axis_edge, cutter_data, medial_axis_start=True):
        if medial_axis_start:
            idx = 0
        else:
            idx = -1
        center = shapely.get_coordinates(medial_axis_edge)[idx]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)
        inner_axis_length = outer_semi_axis_length - cutter_data['radius']

        outer_ellipse_data = {
            'major': outer_semi_axis_length,
            'minor': outer_semi_axis_length,
            'center': center,
            'perimeter': 2 * np.pi * outer_semi_axis_length
        }

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_ellipse_data['major'],
            semi_minor_axis=outer_ellipse_data['minor'],
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                if medial_axis_start:
                    idx = np.argmin(distances)
                else:
                    idx = np.argmax(distances)
                anchor = shapely.get_coordinates(inter.geoms[idx])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]

            outer_ellipse_data['anchor'] = anchor
        else:
            print('ellipse does not intersect medial axis')
            return None, None

        ### Initial rotation of the ellipse in radians ###
        rotation_vector = anchor - center
        outer_ellipse_data['rotation'] = np.arctan2(rotation_vector[1], rotation_vector[0])
        inner_ellipse_data = {**outer_ellipse_data, 'major': inner_axis_length, 'minor': inner_axis_length, 'perimeter': None}

        return inner_ellipse_data, outer_ellipse_data


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front2(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step

    distance_initial = np.arange(
        0.0, sampler.length, path_step)

    offsets = sampler.sample(distance_initial)
    radii = boundary_distance(polygon=polygon, points=offsets)

    return offsets, radii


def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    offsets = [offset[0]]
    radii = [radius[0]]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
            offsets.append(point)
            radii.append(r)

    return np.array(offsets), np.array(radii)


def create_circle_points(center, radius, start_angle, resolution=71, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring    
    p1 = shapely.get_coordinates(reference_ls.interpolate(0.45, normalized=True)).reshape((-1))
   
    # Get the end point of the reference linestring    
    p2 = shapely.get_coordinates(reference_ls.interpolate(0.55, normalized=True)).reshape((-1))

    # Use the first point of the target linestring as its representative point
    q = shapely.get_coordinates(target_ls.interpolate(0.5, normalized=True)).reshape((-1))

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear
    

def solve_ellipse_params2(inner_ellipse_data, outer_ellipse_data, sides_data, minimum_major, maximum_major, outer_ellipse, min_radius_of_curvature, maximum_right_dist, tolerance=1e-4):        
        def objective_function(major_minor_position):
            softness = 5
            major, minor, position = major_minor_position
            inner_ellipse_data.update({'major': major, 'minor': minor})

            distances, _, new_rotation, new_center = get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, position)        
            # sides_distance = np.abs(distances[0]-distances[1])
            # sides_distance = np.sum(np.abs(distances)) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(distances))

            ellipse_geometry, _ = create_ellipse(
                    anchor_point=new_center,
                    semi_major_axis=inner_ellipse_data['major'],
                    semi_minor_axis=inner_ellipse_data['minor'],
                    rotation=new_rotation                    
                    )
            minor_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)            
            new_minor = abs(maximum_right_dist - minor_distance) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(minor_distance))            
            new_major = np.sum(np.abs(distances)) * (1 / softness) * scipy.special.logsumexp(softness * np.abs(distances))            
            res = new_major + new_minor
            # print(f'position: {position}, major: {major}, minor: {minor}')
            # print(f'res: {res}, new_major: {new_major}, new_minor: {new_minor}, sides_distance: {sides_distance}')
            return res
        
        minimum_minor = np.sqrt(min_radius_of_curvature * minimum_major)
        
        initial_guess = np.array([minimum_major, minimum_minor, 0.95])
        bounds = [
            (minimum_major, maximum_major), # Major axis
            (minimum_minor, minimum_major), # Minor axis
            (0.95, 1.0) # possible correction of the position
        ]

        # result = scipy.optimize.differential_evolution(
        #     objective_function, 
        #     bounds, 
        #     init='latinhypercube',  # Good for bounded spaces; or use your initial as a custom init
        #     seed=42,  # For reproducibility
        #     popsize=10,  # Higher for better exploration in 3D (try 15-30)
        #     tol=1e-2,  # Tight tolerance to aim for res ~0
        #     mutation=(0.5, 1.0), 
        #     recombination=0.7,
        #     strategy='best1bin',  # Robust for non-smooth functions
        #     # workers= -1
        # )

        result = scipy.optimize.minimize(
            objective_function,
            initial_guess,
            method='Nelder-Mead', # Or 'Powell', 'COBYLA'            
            bounds=bounds,
            options={'xatol': 1e-4, 'fatol': 1e-4, 'adaptive': False, 'disp': True} # 'xatol' is the tolerance
        )        
        test_data = {
            'major': result.x[0],
            'minor': result.x[1]
        }
        test_inner_data = {**inner_ellipse_data, **test_data}
        position = result.x[2]       
        _, new_anchor, new_rotation, _  = get_ellipse_distances(test_inner_data, outer_ellipse_data, sides_data, position)
        ellipse_geometry, _ = create_ellipse(
                anchor_point=new_anchor,
                semi_major_axis=result.x[0],
                semi_minor_axis=result.x[1],
                rotation=new_rotation,
                anchor_on_perimeter=True
                )
        create_line_object(ellipse_geometry, "ellipse_minor", color=(0, 1, 0, 1))
        return result.x


def solve_ellipse_params(inner_ellipse_data, outer_ellipse_data, sides_data, front_fractions,
                      minimum_major, maximum_major, outer_maximum_ellipse, min_radius_of_curvature,
                      tolerance=1e-4) -> Tuple[float, EllipseOptimizationResult]:
    """
    Find the optimal major axis length for an ellipse using optimization.

    This function preserves the core algorithmic logic including the while loop
    and find_ellipse_position call, but with cleaner data passing.

    Returns:
        Tuple containing the optimal major axis length and optimization results
    """
    # Store optimization results to return
    optimization_result = EllipseOptimizationResult(
        minor=0.0, new_anchor=np.array([0, 0]), new_rotation=0.0, ellipse_center=np.array([0, 0])
    )

    def objective_function(major: float) -> float:
        """Objective function for major axis optimization."""
        # Calculate initial minor axis based on radius of curvature constraint
        minimum_minor = np.sqrt(min_radius_of_curvature * major)
        maximum_minor = outer_ellipse_data['minor']
        
        test_inner_data = {**inner_ellipse_data, 'major': major}
        # tolerance = 0.001

        a = minimum_minor
        b = maximum_minor
        while (b - a) / 2.0 > tolerance:
            c = (a + b) / 2.0
            test_inner_data['minor'] = c

            # Find optimal position along the ellipse path
            position = find_ellipse_position(
                test_inner_data, outer_ellipse_data, sides_data, front_fractions, tolerance=tolerance
            )
            position = _normalize_position(position)

            # Calculate ellipse geometry and distances
            distances, new_anchor, new_rotation, ellipse_center = get_ellipse_distances(
                test_inner_data, outer_ellipse_data, sides_data, position
            )

            # Create ellipse geometry for boundary checking
            ellipse_geometry, _ = create_ellipse(
                anchor_point=new_anchor,
                semi_major_axis=test_inner_data['major'],
                semi_minor_axis=test_inner_data['minor'],
                rotation=new_rotation,
                anchor_on_perimeter=True
            )

            ellipse = shapely.geometry.LinearRing(ellipse_geometry)

            # Check if ellipse fits within maximum bounds
            if ellipse.within(outer_maximum_ellipse):
                b = c
            else:
                a = c

        # Store results for return
        optimization_result.minor = c

        # Store results for return
        optimization_result.new_anchor = new_anchor
        optimization_result.new_rotation = new_rotation
        optimization_result.ellipse_center = ellipse_center

        print(f'major: {major}, objective function: {max(distances, key=abs)}')
        return max(distances, key=abs)  # maximum absolute distance from sides

    # Find optimal major axis using Brent's method
    optimal_major = scipy.optimize.brentq(
        objective_function, a=minimum_major, b=maximum_major, xtol=tolerance
    )
    print(f'optimal_major: {optimal_major}')    
    return optimal_major, optimization_result


def fit_ellipse(inner_ellipse_data, outer_ellipse_data, sides_data, cutter_data, min_radius_of_curvature=15.0, idx=0):
    """
    Fit an ellipse within given constraints using optimization.

    This function has been refactored for better readability and maintainability
    while preserving the core algorithmic logic.

    Args:
        outer_ellipse_data: Dictionary containing outer ellipse parameters
        sides_data: Dictionary containing side geometry data
        cutter_data: Dictionary containing cutter parameters
        min_radius_of_curvature: Minimum radius of curvature constraint

    Returns:
        Dictionary containing the fitted outer ellipse data
    """
    # Configuration constants
    MAXIMUM_RIGHT_DIST = 0.3
    # BOOLEAN_CORRECTION_SCALE_FACTOR = 1.00  # TODO: Quick fix for boolean operations
    TOLERANCE = 0.001

    outer_ellipse_geometry, _ = create_ellipse(
        anchor_point=outer_ellipse_data['center'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation']
        )
    
    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    shapely.prepare(outer_ellipse)
    
    inter_outer = outer_ellipse.intersection(sides_data['ring'])

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')           
        return None        
        
    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]
    front_fractions_pop = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)    
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)    
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    front_line_front = front_line.offset_curve(-cutter_data['radius'], join_style=2, mitre_limit=2.0).simplify(1)
    front_line_back = front_line.offset_curve(-MAXIMUM_RIGHT_DIST, join_style=2, mitre_limit=2.0)    
    front_poly = shapely.geometry.Polygon(list(front_line_front.coords) + list(reversed(front_line_back.coords)))
    buffered_front_line = front_line.buffer(MAXIMUM_RIGHT_DIST, join_style=2, mitre_limit=2.0, cap_style='flat')

    shapely.prepare(buffered_front_line)
    shapely.prepare(front_poly)    

    front_data = {
        'line': front_line,
        'outer_polygon': front_poly,
        'buffered_line': buffered_front_line,
        'fractions': front_fractions_pop,
        'length': front_line.length
    }

    search_scale = 0.3

    test_inner_data = inner_ellipse_data.copy()
    test_inner_data['anchor'] = outer_ellipse_data['anchor']

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=np.array([0, 0]),
        semi_major_axis=5,
        semi_minor_axis=50
    )
    ellipse = shapely.geometry.LinearRing(ellipse_geometry)

    fraction = 0.6 * 0.25 # only 1/4 of the ellipse    
    
    point_on_perimeter = ellipse.interpolate(fraction, normalized=True)
    create_line_object(point_on_perimeter.coords, "line", color=(1, 0, 0, 1))    
    point_vector = np.array(point_on_perimeter.coords[0]) - np.array(ellipse_center)
    normalized_vector = point_vector / np.linalg.norm(point_vector)
    print(f'point_vector: {normalized_vector}')
    # rotation = np.arctan2(point_vector[1], point_vector[0])
    # print(f'rotation: {rotation}')
    # rotation = find_t_for_arc_length_fraction(5, 50, fraction)
    # print(f'rotation: {rotation}')
    # r_calc = rotation / (0.5 * np.pi)    
    # print(f'r_calc: {r_calc}')
    

    return
    time1 = time.time()
    position = find_ellipse_position_ng(
                test_inner_data, outer_ellipse_data, sides_data, front_data, search_scale=search_scale, tolerance=1e-3
            )
    time2 = time.time()
    print(f'Time: {time2-time1}')
    print(f'position: {position}')


    # Create ellipse geometry for boundary checking
    ellipse_geometry, _ = create_ellipse(
        anchor_point=new_anchor,
        semi_major_axis=test_inner_data['major'],
        semi_minor_axis=test_inner_data['minor'],
        rotation=new_rotation,
        anchor_on_perimeter=True
    )

    # ellipse = shapely.geometry.LinearRing(ellipse_geometry)
    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0, 0, 1))
    return {},{}

    '''

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=optimization_result.new_anchor,
        semi_major_axis=optimal_major,
        semi_minor_axis=optimization_result.minor,
        rotation=optimization_result.new_rotation,
        anchor_on_perimeter=True
    )
    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0, 0, 1))

    # Update inner ellipse data with optimization results
    inner_ellipse_data.update({
        'major': optimal_major,
        'minor': optimization_result.minor,
        'center': ellipse_center,
        'anchor': optimization_result.new_anchor,
        'rotation': optimization_result.new_rotation
    })

    # Calculate outer ellipse parameters
    outer_major = inner_ellipse_data['major'] + cutter_data['radius']
    outer_minor = inner_ellipse_data['minor'] + cutter_data['radius']

    # Calculate ellipse perimeter using exact formula
    perimeter = _calculate_ellipse_perimeter(outer_major, outer_minor)

    # Calculate new anchor position for outer ellipse
    anchor_offset = rotate_vector(
        np.array([cutter_data['radius'], 0]),
        inner_ellipse_data['rotation']
    )

    # Construct final outer ellipse data
    outer_ellipse_result = {
        'major': outer_major,
        'minor': outer_minor,
        'center': ellipse_center,
        'anchor': inner_ellipse_data['anchor'] + anchor_offset,
        'rotation': inner_ellipse_data['rotation'],
        'perimeter': perimeter
    }

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=outer_ellipse_result['anchor'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation'],
        anchor_on_perimeter=True
    )
    # create_line_object(ellipse_geometry, "ellipse_outer", color=(0, 1, 0, 1))

    return inner_ellipse_data, outer_ellipse_result
    '''


def get_split_coords(ellipse_data):
    cx, cy = ellipse_data['center']
    angle_rad = ellipse_data['rotation']+np.pi
    x = cx + ellipse_data['major'] * np.cos(angle_rad)
    y = cy + ellipse_data['major'] * np.sin(angle_rad)
    return np.array([x, y])

def resample_linestring(line: shapely.geometry.LineString, segment_length: float) -> shapely.geometry.LineString:
    """
    Resamples a Shapely LineString to have segments of a constant length.

    The last segment will be shorter than segment_length if the total
    length is not a multiple of segment_length.

    Args:
        line (LineString): The original LineString to resample.
        segment_length (float): The desired length of each segment.

    Returns:
        LineString: The new, resampled LineString.
    """
    if segment_length <= 0:
        raise ValueError("Segment length must be positive.")

    # Get the total length of the original line
    total_length = line.length

    # Generate distances along the line at which to place new vertices
    # np.arange creates a sequence from 0 to total_length with a step of segment_length
    distances = np.arange(0, total_length, segment_length)

    # Interpolate points at these distances
    # The list comprehension is a concise way to do this
    new_points = [line.interpolate(dist) for dist in distances]

    # Always include the very last point of the original line to ensure
    # the resampled line has the same extent.
    # We can access the last coordinate directly.
    last_point = shapely.geometry.Point(line.coords[-1])
    if not new_points[-1].equals(last_point):
         new_points.append(last_point)


    # Create a new LineString from the list of points
    return shapely.geometry.LineString(new_points)


import numpy as np

def fit_ellipse(points):
    # Normalize points for numerical stability
    x, y = points[:, 0], points[:, 1]
    x_mean, y_mean = np.mean(x), np.mean(y)
    x_std, y_std = np.std(x), np.std(y)
    x = (x - x_mean) / x_std
    y = (y - y_mean) / y_std

    # Design matrix for conic: [x², xy, y², x, y, 1]
    D = np.column_stack([x**2, x*y, y**2, x, y, np.ones_like(x)])

    # Scatter matrix S = D.T @ D
    S = D.T @ D

    # Constraint matrix C (for ellipse: B² - 4AC < 0, but we enforce 4AC - B² = 1)
    C = np.zeros((6, 6))
    C[0, 2] = C[2, 0] = 2
    C[1, 1] = -1

    # Solve generalized eigenvalue problem: S @ a = lambda C @ a
    # We want the eigenvector for the positive eigenvalue
    eigenvalues, eigenvectors = np.linalg.eig(np.linalg.inv(S) @ C)
    positive = np.where(eigenvalues > 0)[0]
    if len(positive) == 0:
        raise ValueError("No ellipse fit possible—points might be degenerate.")
    a = eigenvectors[:, positive[np.argmax(eigenvalues[positive])]]

    # Denormalize coefficients
    a = a / np.sqrt(np.abs(a @ C @ a))  # Normalize to satisfy constraint


    # Now extract ellipse parameters (center, axes, angle)

    # Denormalize coefficients
    A = a[0]
    B = a[1]
    C = a[2]
    D = a[3]
    E = a[4]
    F = a[5]
    sx = x_std
    sy = y_std
    mx = x_mean
    my = y_mean
    A_orig = A / sx**2
    B_orig = B / (sx * sy)
    C_orig = C / sy**2
    D_orig = -2 * A * mx / sx**2 - B * my / (sx * sy) + D / sx
    E_orig = -2 * C * my / sy**2 - B * mx / (sx * sy) + E / sy
    F_orig = A * mx**2 / sx**2 + C * my**2 / sy**2 + B * mx * my / (sx * sy) - D * mx / sx - E * my / sy + F

    # Now extract parameters from original coefficients
    A, B, C, D, E, F = A_orig, B_orig, C_orig, D_orig, E_orig, F_orig

    # Center
    denom = B**2 - 4 * A * C
    if abs(denom) < 1e-6:
        raise ValueError("Degenerate conic.")
    cx = (2 * C * D - B * E) / denom
    cy = (2 * A * E - B * D) / denom

    # Angle
    angle = 0.5 * np.arctan2(B, A - C)

    # Semi-axes using eigenvalue approach
    Q = np.array([[A, B/2], [B/2, C]])
    eigenvalues = np.linalg.eigvals(Q)
    if np.any(eigenvalues <= 0):
        raise ValueError("Invalid ellipse parameters.")

    # Compute F_prime
    F_prime = A * cx**2 + B * cx * cy + C * cy**2 + D * cx + E * cy + F
    if F_prime >= 0:
        raise ValueError("Invalid F_prime for ellipse.")

    semi_axes = np.sqrt(-F_prime / eigenvalues)
    semi_major = np.max(semi_axes)
    semi_minor = np.min(semi_axes)

    return {'center': (cx, cy), 'semi_major': semi_major, 'semi_minor': semi_minor, 'angle': angle}

def main():
    
    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)
    points = np.array(geometry[0], dtype=np.float32).reshape(-1, 1, 2)
    print(points)

    ellipse = fit_ellipse(points)
    # print(ellipse)

    
if __name__ == "__main__":
    main()
