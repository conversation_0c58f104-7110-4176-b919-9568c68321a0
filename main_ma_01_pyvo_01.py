import numpy as np
import bpy
import networkx as nx
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point, MultiLineString
from shapely.ops import nearest_points, linemerge
import math

# Use Shapely for efficient nearest point calculation


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def create_circle_object(center, radius, name):
    """Create a circle object in Blender to visualize a maximally inscribed disc.

    Args:
        center: [x, y] coordinates of the circle center
        radius: Radius of the circle
        name: Name for the created circle object
        
    Returns:
        The created Blender object
    """
    # Create a mesh for the circle
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    
    # Number of vertices in the circle
    vertices_count = 32
    
    # Generate vertices around the circle
    verts = []
    for i in range(vertices_count):
        angle = 2.0 * math.pi * i / vertices_count
        x = center[0] + radius * math.cos(angle)
        y = center[1] + radius * math.sin(angle)
        verts.append(Vector((x, y, 0)))  
    
    # Create edges around the circle (connect vertices to form a loop)
    edges = [(i, (i+1) % vertices_count) for i in range(vertices_count)]
    
    # Create the mesh from vertices and edges (no faces)
    mesh.from_pydata(verts, edges, [])
    mesh.update()
    
    # Set display properties
    mat = bpy.data.materials.new(name=f"{name}_material")
    mat.diffuse_color = (0.2, 0.8, 0.2, 1.0)  # Solid green for better visibility as a line
    obj.data.materials.append(mat)
    
    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects


def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.

    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects

    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []

    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))

    return blender_objects


def decompose_to_polygons(geom):
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_polygon_angles(vertices, tolerance=0.1):
    """
    Classify vertices of a polygon based on their angles from outside perspective.

    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    tolerance: float, tolerance for angle classification

    Returns:
    tuple of three arrays containing indices of acute, straight, and obtuse angles
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)

    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices

    # Normalize vectors
    v1_norm = v1 / np.linalg.norm(v1, axis=1)[:, np.newaxis]
    v2_norm = v2 / np.linalg.norm(v2, axis=1)[:, np.newaxis]

    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)

    # Clip dot products to [-1, 1] to avoid numerical errors
    dot_products = np.clip(dot_products, -1, 1)

    # Calculate angles in radians
    angles = np.arccos(dot_products)

    # Calculate 2D cross product manually (z-component of 3D cross product)
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]

    # Flip angles where cross product is negative (inside angles)
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)

    # Classify angles with tolerance
    acute_mask = angles < np.pi - tolerance
    straight_mask = np.abs(angles - np.pi) <= tolerance
    obtuse_mask = angles > np.pi + tolerance
    return np.where(straight_mask)[0], np.where(acute_mask)[0], np.where(obtuse_mask)[0]


def generate_trochoidal_path(p0, p1, trochoidal_step, trochoidal_radius, resolution=1000):
    """
    Generates a trochoidal toolpath between points p0 and p1.

    Parameters:
      p0, p1 : tuple or list
          Endpoints of the main feed path (e.g. (x, y)).
      trochoidal_step : float
          The linear advance for one full trochoidal (cycloid) cycle.
          One cycle will advance by 2π * s along the feed.
      trochoidal_radius : float
          The radius of the trochoidal loop.
      resolution : int, optional
          The number of points to generate along the total cycle.

    Returns:
      global_points : ndarray
          An (N,2) array containing the X, Y coordinates of the tool path.
    """

    # Ensure points are numpy arrays
    p0 = np.array(p0, dtype=float)
    p1 = np.array(p1, dtype=float)

    # Calculate feed direction and length
    direction = p1 - p0
    L = np.linalg.norm(direction)
    d_unit = direction / L
    theta_line = np.arctan2(d_unit[1], d_unit[0])

    # Compute the "advance per radian" constant s from the trochoidal step.
    # Note: one full cycle (0 to 2π) advances by 2π * s.
    s = trochoidal_step / (2 * np.pi)

    # Estimate the total parameter range needed to cover the length L.
    theta_total = L / s  # (ignoring the oscillatory term; overestimate is fine)

    # Generate a vector of theta values between 0 and theta_total.
    theta = np.linspace(0, theta_total, resolution)

    # Compute local (uncorrected) cycloidal path:
    # x_local gradually increases while oscillating; y_local oscillates around 0.
    x_local = s * theta - trochoidal_radius * np.sin(theta)
    y_local = trochoidal_radius - trochoidal_radius * np.cos(theta)

    # Rotate the local path to align with the main feed direction.
    cos_a = np.cos(theta_line)
    sin_a = np.sin(theta_line)
    R_matrix = np.array([[cos_a, -sin_a],
                         [sin_a,  cos_a]])

    local_points = np.vstack((x_local, y_local)).T
    global_points = (R_matrix @ local_points.T).T + p0  # translate to start at p0
    return global_points


def clothoid_transition(k0, k1, L, num_points=1000):
    """
    Generates points of a clothoid (Euler spiral) transition curve.

    Parameters:
      k0 : float
          Starting curvature (1 / R1), where R1 is the radius of the first arc.
      k1 : float
          Ending curvature (1 / R2), where R2 is the radius of the second arc.
      L : float
          Total length of the transition curve.
      num_points : int
          Number of points to compute along the transition.

    Returns:
      x, y : ndarray
          Arrays containing x and y coordinates of the transition curve.
      theta_final : float
          The final tangent angle (in radians) at s = L.
    """
    ds = L / num_points
    s = np.linspace(0, L, num_points)
    theta = np.zeros(num_points)
    x = np.zeros(num_points)
    y = np.zeros(num_points)

    for i in range(1, num_points):
        # Linear variation of curvature: k(s) = k0 + (k1 - k0) * s / L
        curvature = k0 + (k1 - k0) * s[i] / L
        theta[i] = theta[i-1] + curvature * ds
        x[i] = x[i-1] + np.cos(theta[i]) * ds
        y[i] = y[i-1] + np.sin(theta[i]) * ds

    return x, y, theta[-1]


def are_edges_similar(edge1, edge2, threshold=0.005):
    """
    Check if two edges are similar (duplicates)
    
    Parameters:
    -----------
    edge1, edge2 : dict
        Edge dictionaries with 'points' key containing coordinates
    threshold : float
        Maximum average distance between points to consider edges similar
        
    Returns:
    --------
    bool : True if edges are similar, False otherwise
    """
    # Get points from edges
    points1 = np.array(edge1['points'])
    points2 = np.array(edge2['points'])
    
    # Quick check: if point counts are very different, edges are not similar
    if abs(len(points1) - len(points2)) > 5:
        return False
    
    # If either edge has only 2 points (straight line), check if they're the same line
    if len(points1) == 2 and len(points2) == 2:
        # Check if lines are the same, regardless of direction
        dist1 = np.min(np.linalg.norm(points1[0] - points2, axis=1)) + np.min(np.linalg.norm(points1[1] - points2, axis=1))
        dist2 = np.min(np.linalg.norm(points1[0] - np.flip(points2, axis=0), axis=1)) + np.min(np.linalg.norm(points1[1] - np.flip(points2, axis=0), axis=1))
        return min(dist1, dist2) < threshold * 2  # Use slightly larger threshold for endpoints
    
    # For more complex edges, check if the curves are similar
    # Calculate distance from each point in edge1 to nearest point in edge2
    min_dists1 = np.min(np.sqrt(np.sum((points1[:, np.newaxis, :] - points2[np.newaxis, :, :])**2, axis=2)), axis=1)
    
    # Calculate distance from each point in edge2 to nearest point in edge1
    min_dists2 = np.min(np.sqrt(np.sum((points2[:, np.newaxis, :] - points1[np.newaxis, :, :])**2, axis=2)), axis=1)
    
    # Average distance between curves
    avg_dist = (np.mean(min_dists1) + np.mean(min_dists2)) / 2
    
    return avg_dist < threshold


def minimum_distance_to_boundary(point, contour, islands=None):
    """
    Calculate the minimum distance from a point to the boundary of the shape,
    considering both contour and islands.
    
    Parameters:
    -----------
    point : array-like
        Coordinates of the point [x, y]
    contour : shapely.geometry.Polygon
        Exterior boundary of the shape
    islands : shapely.geometry.MultiPolygon, optional
        Interior islands/holes
        
    Returns:
    --------
    float : Minimum distance to any part of the boundary
    tuple : Coordinates of the closest boundary point
    """
    # Convert point to shapely Point
    point_geom = Point(point)    
    
    # Get distance to exterior boundary
    min_dist = point_geom.distance(contour.exterior)
    closest_point = nearest_points(point_geom, contour.exterior)[1].coords[0]
    
    # Check islands if they exist
    if islands and not islands.is_empty:
        # For each island, check if distance is smaller
        for island in islands.geoms:
            island_dist = point_geom.distance(island.exterior)
            if island_dist < min_dist:
                min_dist = island_dist
                closest_point = nearest_points(point_geom, island.exterior)[1].coords[0]
    
    return min_dist, closest_point


def hash_line(line, tolerance=1e-6):
    """Hashes a LineString for duplicate detection, considering direction."""
    coords = np.array(line.coords)
    rounded_coords = np.round(coords, int(-np.log10(tolerance)))
    # Sort coordinates to make hash direction-independent
    return hash(tuple(sorted(map(tuple, rounded_coords))))


def hash_edge(edge, tolerance=1e-6):
    """Hashes an edge (start, end) for duplicate detection."""
    start, end = edge
    rounded_start = tuple(np.round(start, int(-np.log10(tolerance))))
    rounded_end = tuple(np.round(end, int(-np.log10(tolerance))))
    return hash((rounded_start, rounded_end))


def remove_duplicate_edges(edges, tolerance=1e-6):
    unique_edges = []
    seen_hashes = set()

    for edge in edges:
        edge_hash = hash_edge(edge, tolerance)
        if edge_hash not in seen_hashes:
            unique_edges.append(edge)
            seen_hashes.add(edge_hash)

    return unique_edges


def voronoi(contour, islands=None, tolerance=0.001):
    pv = pyvoronoi.Pyvoronoi(100)

    # Process the contour (exterior polygon)
    coords = list(contour.exterior.coords)
    print(f"Adding contour with {len(coords)} points")
    
    # Add segments for each edge of the contour (excluding the last point which is same as first)
    for i in range(len(coords) - 1):
        start = coords[i]
        end = coords[i + 1]
        pv.AddSegment([start, end])
    
    # Process islands (holes) if provided
    if islands and not islands.is_empty:
        # If islands is a MultiPolygon, iterate through its polygons
        for island in islands.geoms:
            hole_coords = list(island.exterior.coords)
            print(f"Adding island with {len(hole_coords)} points")
            
            # Add segments for each edge of the island
            for i in range(len(hole_coords) - 1):
                start = hole_coords[i]
                end = hole_coords[i + 1]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()    
    cells = pv.GetCells()

    print("Cell Count: {0}".format(len(cells)))
    
    # Collect all edges data first
    linear_edges = []
    curved_edges = []    
    
    # Use sets for efficient duplicate detection
    seen_linear_hashes = set()
    seen_curved_hashes = set()
    
    for cIndex, cell in enumerate(cells):
        for i, edge_idx in enumerate(cell.edges):
            e = edges[edge_idx]
            # Skip edges with invalid vertices
            if e.start == -1 or e.end == -1:
                continue
                
            startVertex = vertices[e.start]
            endVertex = vertices[e.end]
            
            # Store edge data with identifier for naming
            if e.is_linear:
                # Create tuple form of start and end points for hashing
                start_point = (startVertex.X, startVertex.Y)
                end_point = (endVertex.X, endVertex.Y)

                # create_line_object([start_point, end_point], f"{cIndex}_{i}")
                
                # Create hash from the edge points (sorted for direction independence)
                edge_tuple = tuple(sorted([start_point, end_point]))
                edge_hash = hash(edge_tuple)
                
                # Only add if not seen before
                if edge_hash not in seen_linear_hashes:
                    linear_edges.append({
                        'points': [list(start_point), list(end_point)],
                        'name': f"linear_edge_{cIndex}_{i}"
                    })
                    seen_linear_hashes.add(edge_hash)
            else:
                # Calculate distance only for curved edges
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 + 
                                         (startVertex.Y - endVertex.Y)**2) / 10                
                points = pv.DiscretizeCurvedEdge(edge_idx, max_distance)
                points_array = np.array([[p[0], p[1]] for p in points])

                # create_line_object(points_array, f"{cIndex}_{i}")
                
                # For curved edges, hash the first and last point
                # Use the points_array we already created
                if len(points_array) >= 2:
                    # Convert array points to tuples for hashing
                    start_point = tuple(points_array[0])
                    end_point = tuple(points_array[-1])
                    start_end_tuple = tuple(sorted([start_point, end_point]))
                    curve_hash = hash(start_end_tuple)
                    
                    # Only add if not seen before
                    if curve_hash not in seen_curved_hashes:
                        curved_edges.append({
                            'points': points_array,
                            'name': f"curved_edge_{cIndex}_{i}"
                        })
                        seen_curved_hashes.add(curve_hash)

    # Extract medial axis - these are the interior edges of the Voronoi diagram
    medial_edges = []
    
    # Calculate acute indices for the contour
    contour_coords = np.array(contour.exterior.coords[:-1])  # Exclude last point which is same as first
    _, acute_indices, _ = classify_polygon_angles(contour_coords, tolerance=tolerance)    
    
    # Dictionary to store all acute vertices by polygon
    acute_vertices = {}
    acute_vertices['contour'] = acute_indices
    print(f"Acute indices in contour: {acute_indices}")
    
    # If islands exist, calculate obtuse indices for each island (since islands have reversed orientation)
    if islands and not islands.is_empty:
        acute_vertices['islands'] = {}
        for i, island in enumerate(islands.geoms):
            island_coords = np.array(island.exterior.coords[:-1])
            _, _, island_obtuse = classify_polygon_angles(island_coords, tolerance=tolerance)
            acute_vertices['islands'][i] = island_obtuse            
   
    # We'll use the polygon to determine which edges are inside
    for edge in linear_edges + curved_edges:
        points = np.array(edge['points'])
        # Skip processing if no points
        if len(points) == 0:
            continue
            
        # Check if midpoint of the edge is inside the contour but outside any islands
        midpoint = points.mean(axis=0) if len(points) > 1 else points[0]
        midpoint_point = Point(midpoint)
        
        # Edge is part of medial axis if it's inside contour but outside all islands
        is_inside = contour.contains(midpoint_point)
        
        # Check if point is in any island
        is_in_island = False
        if islands and not islands.is_empty:
            is_in_island = islands.contains(midpoint_point)
            
        # Skip if edge is not inside or is in an island
        if not (is_inside and not is_in_island):
            continue
            
        # Check if this edge is connected to an acute vertex in contour or islands
        # Find closest contour vertex to first and last point of edge
        start_point = points[0]
        end_point = points[-1] if len(points) > 1 else points[0]
        
        # Check connection to contour acute vertices
        contour_acute = acute_vertices['contour']
        if len(contour_acute) > 0:
            # Calculate distances to all contour vertices
            start_dists = np.sqrt(np.sum((contour_coords - start_point)**2, axis=1))
            end_dists = np.sqrt(np.sum((contour_coords - end_point)**2, axis=1))
            
            # Find closest vertex indices
            closest_to_start = np.argmin(start_dists)
            closest_to_end = np.argmin(end_dists)
            
            # Skip if either end is too close to an acute vertex
            # Use a small threshold distance to determine if points are "connected"
            threshold = 0.01
            
            if closest_to_start in contour_acute and start_dists[closest_to_start] < threshold:
                continue
                
            if closest_to_end in contour_acute and end_dists[closest_to_end] < threshold:
                continue
        
        # Check connection to island obtuse vertices (since islands have reversed orientation)
        skip_edge = False
        if islands and not islands.is_empty and 'islands' in acute_vertices:
            # Check each island for obtuse vertices
            for i, island in enumerate(islands.geoms):
                if i not in acute_vertices['islands'] or len(acute_vertices['islands'][i]) == 0:
                    continue
                    
                island_coords = np.array(island.exterior.coords[:-1])
                island_obtuse = acute_vertices['islands'][i]  # These are now obtuse indices
                
                # Calculate distances to all island vertices
                start_dists = np.sqrt(np.sum((island_coords - start_point)**2, axis=1))
                end_dists = np.sqrt(np.sum((island_coords - end_point)**2, axis=1))
                
                # Find closest vertex indices
                closest_to_start = np.argmin(start_dists)
                closest_to_end = np.argmin(end_dists)
                
                threshold = 0.01
                
                # If either endpoint is too close to an obtuse vertex, skip this edge entirely
                if (closest_to_start in island_obtuse and start_dists[closest_to_start] < threshold) or \
                   (closest_to_end in island_obtuse and end_dists[closest_to_end] < threshold):
                    skip_edge = True
                    break  # Stop checking other islands
        
        # Skip this edge if it's connected to any island obtuse vertex
        if skip_edge:
            continue
        
        # Since we already filtered duplicates during initial collection,
        # we can directly calculate radius and add to medial edges
        # Calculate radius (distance to boundary) for each point on this edge
        radii = []
        boundary_points = []
        
        for point in points:
            # radius, boundary_pt = minimum_distance_to_boundary(point, contour, islands)
            radius, boundary_pt = (0,0)
            radii.append(radius)
            boundary_points.append(boundary_pt)
        
        # Add to medial edges with radius information
        medial_edges.append({
            'points': points,
            'name': edge['name'].replace('edge', 'medial'),
            'radii': radii,
            'boundary_points': boundary_points
        })
    
    print(f"Found {len(medial_edges)} medial axis edges")
    
    # Visualize medial axis with a different color    
    for edge in medial_edges:
        ...
        # create_line_object(edge['points'], edge['name'])
        
        # # Visualize some sample maximally inscribed discs
        # num_samples = min(3, len(edge['points']))  # Limit the number of discs to avoid clutter
        # if num_samples > 0:
        #     sample_indices = np.linspace(0, len(edge['points'])-1, num_samples).astype(int)
            
        #     for idx in sample_indices:
        #         center = edge['points'][idx]
        #         radius = edge['radii'][idx]
        #         # Create a circle object to represent the maximally inscribed disc
        #         # Only create it if the radius is significant (not too small)
        #         if radius > 0.01:
        #             create_circle_object(center, radius, f"{edge['name']}_disc_{idx}")
        
    # Return the medial axis edges for further processing

    return medial_edges



if __name__ == "__main__":
    # Example with a three-point line segment as specified
    # line_points = [(0, 0), (100, 0), (150, 5)]
    # radii = [10, 20, 3]  # Matching the number of points

    # # Generate toolpath
    # toolpath = generate_trochoidal_toolpath(line_points, radii, point_spacing=0.5, cycles_per_segment=50)
    
    # # Print toolpath    
    # create_line_object(toolpath, "Trochoidal_path") 

    geometry = get_geometry()   
    polygon = geometry_to_polygon(geometry)

    medial_edges = voronoi(polygon)

    # Visualize medial axis
    for edge in medial_edges:
        create_line_object(edge['points'], edge['name'])
        

