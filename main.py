import bpy
import numpy as np
import networkx as nx
import shapely
from mathutils import Vector

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None


def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None


def geometry_to_shapely(geometry):    
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = False
    return contour, islands

        
# def geometry_to_multipolygon(geometry, simplify=False, simplify_exterior=0.0, simplify_interior=0.0):
    polygons = [shapely.geometry.Polygon(shell=geom) for geom in geometry]
    if simplify:
        if simplify_exterior != simplify_interior and len(polygons) > 1:
            interiors = [polygon.simplify(simplify_interior, preserve_topology=True) for polygon in polygons[1:]]
            interiors.insert(0, polygons[0].simplify(simplify_exterior, preserve_topology=True))
            polygons = interiors
        else:
            polygons = [polygon.simplify(simplify_exterior, preserve_topology=False) for polygon in polygons]

    return shapely.geometry.MultiPolygon(polygons)

# def buffered_geometry_to_multipolygon(geometry, decompose=False):
    if decompose:
        if not geometry.is_empty:
            polys = []
            if geometry.geom_type == 'MultiPolygon':
                for polygon in geometry.geoms:
                    polys.append(shapely.geometry.Polygon(shell=polygon.exterior))
                    for interior in polygon.interiors:
                        polys.append(shapely.geometry.Polygon(shell=interior))

            elif geometry.geom_type == 'Polygon':
                polys.append(shapely.geometry.Polygon(shell=geometry.exterior))
                for interior in geometry.interiors:
                    polys.append(shapely.geometry.Polygon(shell=interior))

            else:
                print(geometry.geom_type, 'shapely conversion aborted')
                return geometry.MultiPolygon()

            return shapely.geometry.MultiPolygon(polys)

    else:
        if geometry.geom_type == 'MultiPolygon':
            return geometry
        elif geometry.geom_type == 'Polygon':
            if not geometry.is_empty:
                return shapely.geometry.MultiPolygon([geometry])
            else:
                return geometry.MultiPolygon()
        else:
            print(geometry.geom_type, 'shapely conversion aborted')
            return geometry.MultiPolygon()


def shapely_to_blender(shapely_geom, base_name="OffsetObject"):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def create_ring_object(coords, name):
        mesh = bpy.data.meshes.new(name)
        obj = bpy.data.objects.new(name, mesh)
        bpy.context.collection.objects.link(obj)
        
        vertices = [Vector((x, y, 0)) for x, y in coords]
        n = len(vertices)
        edges = [(i, (i+1)%n) for i in range(n)]
        
        mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
        mesh.update()
        return obj

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            rings.append(geometry.exterior)
            rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                rings.append(poly.exterior)
                rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects


def sort_buffer(buffer_geoms, origin_islands):
    if not origin_islands:
        return {'polygons': buffer_geoms, 'islands': False}

    tree = shapely.STRtree(buffer_geoms)
    
    # Step 1: Find all candidate containers
    candidate_containers = set()
    for island in origin_islands:
        containing_indices = tree.query(island, predicate='within')
        if containing_indices.size > 0:
            candidates = [buffer_geoms[i] for i in containing_indices]
            candidate_containers.update(candidates)

    print(f"Candidate containers: {len(candidate_containers)}")
    return
    
    # Step 2: Validate candidates (must contain at least one origin island)
    valid_containers = []
    for candidate in candidate_containers:
        # Check if contains any origin island
        if any(candidate.contains_properly(island) for island in origin_islands):
            valid_containers.append(candidate)
    
    # Step 3: Filter nested containers
    sorted_candidates = sorted(valid_containers, key=lambda g: -g.area)
    final_islands = []
    for geom in sorted_candidates:
        if not any(g.contains_properly(geom) for g in final_islands):
            final_islands.append(geom)
    
    # Step 4: Ensure islands don't contain non-container polygons
    island_set = set(final_islands)
    main_polygons = [
        g for g in buffer_geoms 
        if g not in island_set and 
        not any(isl.contains_properly(g) for isl in island_set)
    ]
    
    return {
        'polygons': main_polygons,
        'islands': final_islands
    }

    
def sort_buffers_o(all_buffers, origin_islands):    
    buffer_geoms = [geom for pass_ in all_buffers for geom in pass_]
    
    if origin_islands:
        possible_islands = set()
        # Identify possible islands
        for geom in buffer_geoms:
            for island in origin_islands:
                if geom.contains_properly(island):
                    possible_islands.add(geom)
                    break

        # Filter out any polygons that are contained by other possible islands
        contained_islands = set()
        for c_island in possible_islands:
            for island in possible_islands:
                if c_island != island and c_island.contains_properly(island):
                    contained_islands.add(c_island)

        # The final islands are the possible islands minus the contained ones
        final_islands = possible_islands - contained_islands
        final_polygons = set(buffer_geoms) - final_islands

        return {'polygons': final_polygons, 'islands': final_islands}
    else:
        return {'polygons': buffer_geoms, 'islands': False}

def decompose_to_polygons(geom):
    def process_polygon(poly):
        result = []
        result.append(shapely.geometry.Polygon(poly.exterior))
        result.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
        return result

    polys = []    
    if geom.geom_type == 'Polygon':
        polys.extend(process_polygon(geom))
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            polys.extend(process_polygon(poly))
    return polys

if __name__ == "__main__":

    G = nx.DiGraph()
    
    offset = 0.00565
    passes = 14
    island_passes = 20
    base_name = "OffsetObject"
    buffer_attributes = {
        "join_style": 'mitre',
        "mitre_limit": 1.1
        }

    geometry = get_geometry()
    contour, islands = geometry_to_shapely(geometry)
    polygon = geometry_to_polygon(geometry)
    contour_buffers = []    
    islands_buffers = []

    for pass_num in range(passes):
        buffer_offset = offset * (pass_num + 1)
        buffer = contour.buffer(-buffer_offset, **buffer_attributes)
        if buffer.is_empty:
            print(f'{pass_num} passes completed')
            break
        contour_buffers.append(buffer)

    for pass_num in range(passes):
        buffer_offset = offset * (pass_num + 1)
        buffer = polygon.buffer(-buffer_offset, **buffer_attributes)
        if buffer.is_empty:
            print(f'{pass_num} passes completed')
            break
        shapely_to_blender(buffer, f"{base_name}_polygon_{pass_num:02d}")

    if islands:
        contour_mask = shapely.difference(
            contour.buffer(1, join_style = 'mitre', mitre_limit = 1.1),
            contour.buffer(-offset, **buffer_attributes)
            )

        for pass_num in range(island_passes):
            buffer_offset = offset * (pass_num + 1)
            buffer = islands.buffer(buffer_offset, **buffer_attributes)
            buffer_masked = shapely.difference(buffer, contour_mask)
            if buffer_masked.is_empty:
                print(f'{pass_num} island passes completed')
                break
            # shapely_to_blender(buffer_masked, f"{base_name}_island_{pass_num:02d}")            
            # islands_buffers.append(buffer)
        
        islands_mask = islands.buffer(island_passes * offset, **buffer_attributes)
        contour_mask = contour.difference(islands_mask)
        for pass_num in range(passes):
            buffer_offset = offset * (pass_num + 1)
            buffer = contour_mask.buffer(-buffer_offset, **buffer_attributes)
            if buffer.is_empty:
                print(f'{pass_num} passes completed')
                break
            # shapely_to_blender(buffer, f"{base_name}_{pass_num:02d}")            
            # contour_buffers.append(buffer)
        

        # decomposed_buffer = decompose_to_polygons(buffer)        
        # sorted_buffer = sort_buffer(decomposed_buffer, polygon.interiors)
        # buffers.append(decomposed_buffer)

    # res = shapely.difference(contour_buffers[0], islands_buffers[5])
    # res2 = shapely.difference(islands_buffers[-1], contour_buffers[0])
    # shapely_to_blender(islands_buffers[13], f"{base_name}_difference")
    # for buff in islands_buffers:
    #     shapely_to_blender(buff, f"{base_name}_{pass_num:02d}")
    # shapely_to_blender(res2, f"{base_name}_difference")

    # shapely_to_blender(contour_buffers[-1], f"{base_name}_{pass_num:02d}")
    # shapely_to_blender(islands_buffers[-1], f"{base_name}_{pass_num:02d}")
    

   
    # print(f'polygons {len(sorted_buffer["polygons"])}, islands {len(sorted_buffer["islands"])}')
        # sorted_buffer_o = sort_buffers_o(decomposed_buffer, polygon.interiors)
        # print(sorted_buffer)
        # print(sorted_buffer_o)
        # print(buffer.geom_type)


    # print(buffer.geom_type)
    # print(f'buffer {len(buffer.interiors)}, polygon {len(polygon.interiors)}')    