import numpy as np

def compute_max_engagement_angles_efficient(distances, radii, tool_radius):
    """
    Compute maximum engagement angles for trochoid loops at theta=0.
    
    Parameters:
    - distances: array of distances between consecutive circle centers (l)
    - radii: array of circle radii (length N+1 for N pairs)
    - tool_radius: scalar tool radius (r)
    
    Returns:
    - angles: array of max engagement angles for each pair
    """
    N = len(distances)
    r1 = radii[:-1]  # Radii of first circles in each pair
    r2 = radii[1:]   # Radii of second circles in each pair
    l = distances
    r = tool_radius
    
    # Offset radii
    R1 = r1 + r
    R2 = r2 + r
    
    # Tool center C and tangent point N at theta=0
    C_x = l + r2
    C_y = np.zeros(N)
    N_x = l + R2
    N_y = np.zeros(N)
    
    # Circle equations: solve (x - C_x)^2 + y^2 = r^2 and x^2 + y^2 = R1^2
    a = -2 * C_x
    b = C_x**2 - R1**2 + r**2
    discriminant = a**2 - 4 * b
    
    # Initialize arrays for results
    angles = np.full(N, np.nan)
    
    # Only process valid cases (where discriminant is positive)
    valid_mask = discriminant > 0
    
    if np.any(valid_mask):
        # Intersection x-coordinates (assuming O1 at origin)
        x1 = np.zeros(N)
        x2 = np.zeros(N)
        y1 = np.zeros(N)
        y2 = np.zeros(N)
        
        x1[valid_mask] = (-a[valid_mask] + np.sqrt(discriminant[valid_mask])) / 2
        x2[valid_mask] = (-a[valid_mask] - np.sqrt(discriminant[valid_mask])) / 2
        
        # Check if points are within the first circle
        valid_y1 = (R1**2 - x1**2) >= 0
        valid_y2 = (R1**2 - x2**2) >= 0
        
        y1[valid_mask & valid_y1] = np.sqrt(R1[valid_mask & valid_y1]**2 - x1[valid_mask & valid_y1]**2)
        y2[valid_mask & valid_y2] = np.sqrt(R1[valid_mask & valid_y2]**2 - x2[valid_mask & valid_y2]**2)
        
        # Intersection points D1, D2
        D1 = np.stack([x1, y1], axis=1)
        D2 = np.stack([x2, y2], axis=1)
        N = np.stack([N_x, N_y], axis=1)
        
        # Distances DN1, DN2
        DN1 = np.sqrt(np.sum((D1 - N)**2, axis=1))
        DN2 = np.sqrt(np.sum((D2 - N)**2, axis=1))
        
        # Engagement angles - clip arguments to valid range [-1, 1]
        arg1 = np.clip(1 - (DN1**2) / (2 * r**2), -1.0, 1.0)
        arg2 = np.clip(1 - (DN2**2) / (2 * r**2), -1.0, 1.0)
        
        alpha1 = np.arccos(arg1)
        alpha2 = np.arccos(arg2)
        
        # Maximum engagement angle per pair
        angles[valid_mask] = np.maximum(alpha1[valid_mask], alpha2[valid_mask])
    
    return angles

# Example usage
sample_distances = np.array([0.1, 0.1, 0.1])  # N=3 pairs
sample_radii = np.array([40.0, 32.0, 40.0, 32.0])/2
tool_radius = 10.0
angles = compute_max_engagement_angles_efficient(sample_distances, sample_radii, tool_radius)
print("Engagement angles (radians):", angles)
