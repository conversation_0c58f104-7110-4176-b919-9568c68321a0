"""
pixel_milling_sim.py
Discrete pixel‑based simulation of 2.5‑D milling operations
Ref.: <PERSON><PERSON><PERSON> et al., “A Discrete Simulation‑based Algorithm …” (2019)

Author: <your name>
"""
from __future__ import annotations
import numpy as np
import pandas as pd
from typing import List, Tuple


# ------------------------------------------------------------------
# Geometry helpers
# ------------------------------------------------------------------
def circle_mask(diameter_mm: float, delta: float) -> np.ndarray:
    """Binary mask (uint8) of a full circle with diameter 'diameter_mm' on a
    square grid whose pitch is 'delta' [mm/pixel]."""
    r_pix = int(np.ceil(diameter_mm / (2 * delta)))
    # build coordinate arrays centred at 0
    y, x = np.ogrid[-r_pix : r_pix + 1, -r_pix : r_pix + 1]
    mask = (x**2 + y**2) <= r_pix**2
    return mask.astype(np.uint8)


# ------------------------------------------------------------------
# Core data classes
# ------------------------------------------------------------------
class Workpiece:
    def __init__(self, width: float, height: float, delta: float):
        """
        width, height  [mm]  – rectangular blank parallel to axes
        delta          [mm/pixel]
        """
        self.delta = delta
        self.nx = int(np.ceil(width / delta))
        self.ny = int(np.ceil(height / delta))
        # 1 = material, 0 = already removed
        self.field = np.ones((self.ny, self.nx), dtype=np.uint8)


class Tool:
    def __init__(self, diameter: float, z_teeth: int, delta: float):
        self.D = diameter
        self.R = diameter / 2.0
        self.z = z_teeth
        self.delta = delta
        self.mask = circle_mask(diameter, delta)  # uint8
        # Convenience
        self.r_pix = self.mask.shape[0] // 2  # radius in pixels


# ------------------------------------------------------------------
# Tool‑path generators (demo only)
# ------------------------------------------------------------------
def linear_path(
    p0: Tuple[float, float],
    p1: Tuple[float, float],
    step: float,
) -> List[Tuple[float, float]]:
    """Return equally spaced points along a straight line p0→p1
    'step' is the chord length [mm]."""
    p0, p1 = np.asarray(p0, float), np.asarray(p1, float)
    length = np.linalg.norm(p1 - p0)
    n = max(1, int(np.ceil(length / step)))
    return [tuple(p0 + t * (p1 - p0)) for t in np.linspace(0, 1, n + 1)]


# ------------------------------------------------------------------
# Pixel based material‑removal simulation
# ------------------------------------------------------------------
def simulate(
    wp: Workpiece,
    tool: Tool,
    path_mm: List[Tuple[float, float]],
    f_z: float,      # feed per tooth            [mm]
    a_p: float,      # axial depth of cut        [mm]
    n_rpm: float,    # spindle speed             [rev/min]
) -> pd.DataFrame:
    """
    Returns a DataFrame with one row per tooth impact containing:
    area  – swept area per tooth               [mm^2]
    MRR   – material removal rate              [mm^3 / min]
    a_e   – actual radial immersion            [mm]
    theta – engagement angle                   [rad]
    h_max, h_avg – chip thicknesses            [mm]
    """
    R = tool.R
    dA = wp.delta**2  # area of one pixel [mm^2]
    ny, nx = wp.field.shape
    r_pix = tool.r_pix
    mask_full = tool.mask

    records: list[dict] = []

    for (x_mm, y_mm) in path_mm:
        # ------------------------------------------------------------------
        # Integer indices of mask window w.r.t. workpiece array
        cx, cy = int(round(x_mm / wp.delta)), int(round(y_mm / wp.delta))
        y0, y1 = cy - r_pix, cy + r_pix + 1
        x0, x1 = cx - r_pix, cx + r_pix + 1

        # Cut mask & work‑slice to stay inside work‑piece boundaries
        my0 = max(0, -y0)
        my1 = mask_full.shape[0] - max(0, y1 - ny)
        mx0 = max(0, -x0)
        mx1 = mask_full.shape[1] - max(0, x1 - nx)

        if my0 >= my1 or mx0 >= mx1:
            # Completely outside – skip
            continue

        mask = mask_full[my0:my1, mx0:mx1]
        w_slice = wp.field[
            max(y0, 0) : max(y0, 0) + mask.shape[0],
            max(x0, 0) : max(x0, 0) + mask.shape[1],
        ]

        # ------------------------------------------------------------------
        # Boolean operations
        removed = w_slice & mask
        area = int(removed.sum()) * dA  # mm^2 cut by this tooth

        # Update workpiece (= destructive)
        w_slice &= ~mask

        # ------------------------------------------------------------------
        # Technological indicators
        if area == 0.0:  # no cutting, still collect zeros
            records.append(
                dict(
                    x=x_mm,
                    y=y_mm,
                    area=0.0,
                    MRR=0.0,
                    a_e=0.0,
                    theta=0.0,
                    h_max=0.0,
                    h_avg=0.0,
                )
            )
            continue

        a_e = area / f_z                          # mm
        a_e = min(a_e, 2*R)                       # clamp to the DIAMETER
        theta = np.arccos(
            np.clip((R - a_e) / R, -1.0, 1.0)
        )  # Eq.(14)
        h_max = f_z * np.sin(theta)                            # Eq.(15)
        h_avg = (a_e * f_z) / (R * theta) if theta > 0 else 0  # Eq.(17)

        # Volume per tooth per rotation:  area * a_p   [mm^3]
        # Multiply by z*n for mm^3/min   (no 10^3 conv.)
        MRR = area * a_p * tool.z * n_rpm  # mm^3 / min

        records.append(
            dict(
                x=x_mm,
                y=y_mm,
                area=area,
                MRR=MRR,
                a_e=a_e,
                theta=theta,
                h_max=h_max,
                h_avg=h_avg,
            )
        )

    return pd.DataFrame.from_records(records)


# ------------------------------------------------------------------
# Example run (straight 30 mm slot)
# ------------------------------------------------------------------
if __name__ == "__main__":
    # ------------ user / process parameters -------------------------
    delta = 0.02       # mm / pixel         (simulation step in X‑Y)
    tool_D = 10.0      # mm
    z_teeth = 4
    a_p = 5.0          # mm  (axial DOC)
    f_z = 0.05         # mm / tooth
    n_rpm = 4000       # rev / min

    # Work‑piece 60 x 40 mm plate
    wp = Workpiece(width=60.0, height=40.0, delta=delta)
    tool = Tool(diameter=tool_D, z_teeth=z_teeth, delta=delta)

    # Simple G1 move: (5,20) → (35,20)
    # path = linear_path((5.0, 20.0), (35.0, 20.0), step=f_z)
    # path = linear_path((10.0, 5.0), (35.0, 5.0), step=f_z)
    path = (
        linear_path((20, 20), (35, 20), step=f_z)   +   # straight
        linear_path((35, 5), (35, 30), step=f_z)  +   # 90° corner
        linear_path((35, 30), (10, 30), step=f_z)     # return
    )

    df = simulate(wp, tool, path, f_z=f_z, a_p=a_p, n_rpm=n_rpm)

    # -------------- results -----------------------------------------
    print(df.head().to_string(index=False, float_format="%.4f", max_colwidth=10))

    print("\nPeak MRR  = {:.2f} mm³/min".format(df["MRR"].max()))
    print("Peak θ    = {:.1f} rad".format(df["theta"].max()))
    print(df["theta"][10:400])
