import numpy as np

def compute_ellipse_tangent(X, center, a, b):
    """
    Compute the tangent vector at point X on an ellipse.

    Parameters:
    - X: Point on the ellipse, as (x, y).
    - center: Center of the ellipse, as (x_c, y_c).
    - a: Semi-major axis length.
    - b: Semi-minor axis length.

    Returns:
    - Tangent vector at X.
    """
    x_c, y_c = center
    x, y = X

    # Gradient of the ellipse equation at X
    gradient = np.array([
        2 * (x - x_c) / (a ** 2),
        2 * (y - y_c) / (b ** 2)
    ])

    # Tangent vector (perpendicular to the gradient)
    tangent = np.array([
        -gradient[1],
        gradient[0]
    ])

    # Normalize the tangent vector (optional)
    tangent_unit = tangent / np.linalg.norm(tangent)

    return tangent_unit

# Example usage
center = (0, 0)
a = 10  # Semi-major axis
b = 7.5  # Semi-minor axis
X = (5.55555, 6.23626)  # Point on the ellipse (e.g., at the end of the major axis)

tangent = compute_ellipse_tangent(X, center, a, b)
print(f"Tangent vector at X: {tangent}")

#convert vector to radian
angle = np.arctan2(tangent[1], tangent[0])
print(f"Angle: {angle}")
