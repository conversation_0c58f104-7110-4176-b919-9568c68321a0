import numpy as np
import time
from shapely.geometry import Point, Polygon
import geopandas as gpd


# Function implementations
def numpy_boundary_distance(polygon, points):
    """Vectorized NumPy implementation for calculating distances from points to polygon boundary"""
    distances = []
    
    # Ensure polygon is closed
    if not np.array_equal(polygon[0], polygon[-1]):
        polygon = np.vstack([polygon, polygon[0]])
    
    # Create line segments
    segment_starts = polygon[:-1]
    segment_ends = polygon[1:]
    segment_vectors = segment_ends - segment_starts
    segment_lengths_squared = np.sum(segment_vectors**2, axis=1)
    
    for point in points:
        point_vectors = point - segment_starts
        
        # Handle very short segments
        epsilon = 1e-10
        too_short = segment_lengths_squared < epsilon
        
        # Initialize distances array for this point
        point_distances = np.zeros(len(segment_starts))
        
        # For very short segments, use distance to start point
        if np.any(too_short):
            point_distances[too_short] = np.sqrt(np.sum(point_vectors[too_short]**2, axis=1))
        
        # For normal segments, calculate point-to-segment distance
        normal_segments = ~too_short
        if np.any(normal_segments):
            t = np.sum(point_vectors[normal_segments] * segment_vectors[normal_segments], axis=1) / segment_lengths_squared[normal_segments]
            t = np.clip(t, 0, 1)
            
            closest_points = segment_starts[normal_segments] + np.expand_dims(t, axis=1) * segment_vectors[normal_segments]
            point_distances[normal_segments] = np.sqrt(np.sum((point - closest_points)**2, axis=1))
        
        distances.append(np.min(point_distances))
    
    return np.array(distances)

def shapely_boundary_distances(polygon, points):
    """Shapely implementation for calculating distances from points to polygon boundary"""
    poly = Polygon(polygon)
    boundary = poly.boundary
    
    return np.array([boundary.distance(Point(p)) for p in points])

def geopandas_boundary_distances(polygon, points):
    """GeoPandas implementation for calculating distances from points to polygon boundary"""
    poly = Polygon(polygon)
    
    # Create GeoDataFrame for the polygon boundary
    boundary = gpd.GeoDataFrame(geometry=[poly.boundary])
    
    # Create GeoDataFrame for points
    points_geom = [Point(p) for p in points]
    points_gdf = gpd.GeoDataFrame(geometry=points_geom)
    
    # Calculate distances
    distances = points_gdf.distance(boundary.iloc[0].geometry)
    
    return distances.values

# Generate a complex polygon (star shape with 100 points)
def generate_polygon(num_vertices=100):
    theta = np.linspace(0, 2*np.pi, num_vertices, endpoint=False)
    r_outer = 10.0
    r_inner = 5.0
    
    # Create a star-like shape
    r = np.zeros_like(theta)
    for i in range(len(theta)):
        r[i] = r_outer if i % 2 == 0 else r_inner
    
    x = r * np.cos(theta)
    y = r * np.sin(theta)
    
    # Close the polygon
    x = np.append(x, x[0])
    y = np.append(y, y[0])
    
    return np.column_stack((x, y))

# Generate random points
def generate_points(num_points=300, min_val=-15, max_val=15):
    return np.random.uniform(min_val, max_val, (num_points, 2))

# Main comparison
def run_comparison(num_vertices=1000, num_points=3000, num_runs=3):
    polygon = generate_polygon(num_vertices)
    points = generate_points(num_points)
    
    # # Plot the polygon and points
    # plt.figure(figsize=(10, 8))
    # plt.plot(polygon[:, 0], polygon[:, 1], 'k-')
    # plt.scatter(points[:, 0], points[:, 1], c='r', s=2)
    # plt.title(f"Polygon ({num_vertices} vertices) and {num_points} points")
    # plt.axis('equal')
    # plt.savefig('polygon_points.png')
    # plt.close()
    
    # Time each method
    methods = {
        "NumPy Vectorized": numpy_boundary_distance,
        "Shapely": shapely_boundary_distances,
        "GeoPandas": geopandas_boundary_distances
    }
    
    results = {}
    
    for name, func in methods.items():
        times = []
        for _ in range(num_runs):
            start_time = time.time()
            distances = func(polygon, points)
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = sum(times) / num_runs
        results[name] = {
            "avg_time": avg_time,
            "distances": distances  # Save the last run's distances
        }
        print(f"{name}: {avg_time:.6f} seconds (average of {num_runs} runs)")
    
    # Verify all methods produce similar results
    base_distances = results["NumPy Vectorized"]["distances"]
    for name, data in results.items():
        if name != "NumPy Vectorized":
            max_diff = np.max(np.abs(base_distances - data["distances"]))
            print(f"Maximum difference between NumPy and {name}: {max_diff:.8f}")
    
    # # Plot the timing results
    # plt.figure(figsize=(10, 6))
    # plt.bar(results.keys(), [data["avg_time"] for data in results.values()])
    # plt.title(f"Performance Comparison: {num_points} points, {num_vertices}-edge polygon")
    # plt.ylabel("Time (seconds)")
    # plt.yscale('log')  # Log scale for better visibility of differences
    # plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # # Add time values on top of bars
    # for i, (name, data) in enumerate(results.items()):
    #     plt.text(i, data["avg_time"] * 1.1, f"{data['avg_time']:.6f}s", 
    #              ha='center', va='bottom', fontweight='bold')
    
    # plt.tight_layout()
    # plt.savefig('performance_comparison.png')
    # plt.close()
    
    return results

# Run the comparison
results = run_comparison(num_vertices=100, num_points=300, num_runs=3)

# Print summary
print("\nPerformance Summary:")
for name, data in results.items():
    print(f"{name}: {data['avg_time']:.6f} seconds")