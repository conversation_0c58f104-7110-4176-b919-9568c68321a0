import math
import numpy as np
import shapely
from shapely.geometry import Point, LineString, Polygon


def find_spiral_coffset(CWE: float, inner_r: float, outer_r: float, cutter_r: float):
    """
    Find the offset value that produces the desired cutter workpiece engagement (CWE)
    using a brute force search approach.
    
    Args:
        CWE: Target engagement angle in degrees
        spiral_r: Radius of the spiral
        cutter_r: Radius of the cutter
        
    Returns:
        Offset value that produces the target engagement
    """

    def calculate_engagement(spiral_r: float, cutter_r: float, offset: float, inside: bool) -> float:
        """
        Calculate the engagement angle between a spiral and a cutter.
        
        Args:
            spiral_r: Radius of the spiral
            cutter_r: Radius of the cutter
            offset: Offset distance
            inside: Whether the cutter is inside the spiral
            
        Returns:
            Engagement angle in radians
        """
        if inside:
            offset += spiral_r - cutter_r
        else:
            new_offset = spiral_r
            spiral_r = spiral_r - offset + cutter_r    
            offset = new_offset

        # Create geometric representations
        spiral_circle = shapely.geometry.Point(0, 0).buffer(spiral_r, resolution=128)
        cutter_circle = shapely.geometry.Point(offset, 0).buffer(cutter_r, resolution=128).boundary

        # Calculate engagement as arc length divided by radius
        engagement = (cutter_circle.difference(spiral_circle).length / 2) / cutter_r
        return engagement


    def iterative_search(CWE, spiral_r, cutter_r, offset_guess, inside):
        best_offset = 0
        min_diff = float('inf')
        # Brute force search through possible offset values
        for offset in offset_guess:
            engagement = calculate_engagement(spiral_r, cutter_r, offset, inside)
            diff = abs(engagement - CWE)
            
            if diff < min_diff:
                min_diff = diff
                best_offset = offset
                
                # If we're very close to target, we can stop
                if diff < 0.001:
                    return offset
        
        return best_offset

    min_offset = 0.0  # Minimum possible offset
    max_offset = cutter_r  # Maximum reasonable offset
    step = 0.05  # Step size for brute force search
    offset_guess = np.arange(min_offset, max_offset, step)
    inside_offset = iterative_search(CWE, inner_r, cutter_r, offset_guess, inside=True)
    outside_offset = iterative_search(CWE, outer_r, cutter_r, offset_guess, inside=False)

    return inside_offset, outside_offset

def main():
    CWE = np.deg2rad(75)
    inner_r = 15.0
    outer_r = 35.0
    cutter_r = 5.0
    offset = find_spiral_coffset(CWE, inner_r, outer_r, cutter_r)
    print(f"Offset: {offset}")

if __name__ == '__main__':
    main()
