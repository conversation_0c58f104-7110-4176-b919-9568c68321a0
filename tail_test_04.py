import numpy as np


def draw_arc(center, radius, angle, num_points=3):
    # Generate theta values from 0 to the specified angle
    theta = np.linspace(0, angle, num_points)

    # Calculate the x and y coordinates of the arc
    x = center[0] + radius * np.cos(theta)
    y = center[1] + radius * np.sin(theta)

    return x, y

# Parameters
center = (0, 0)  # Center of the arc
radius = np.pi       # Radius of the arc
angle = np.pi  # Angle in radians (90 degrees)

# Draw the arc
x, y = draw_arc(center, radius, angle)

print("Arc points:")
for i, (xi, yi) in enumerate(zip(x, y)):
    print(f"Point {i}: ({xi}, {yi})")

