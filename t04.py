import numpy as np
from shapely.geometry import Point, LineString
from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp

# points = [
#     (4, 24), (15, 3), (22, 20), (9, 11), (18, 7),
#     (2, 17), (25, 13), (12, 25), (7, 5), (19, 21),
#     (1, 9), (14, 16), (23, 2), (6, 14), (10, 22),
#     (3, 8), (20, 18), (5, 12), (16, 1), (21, 19)
# ]
# shapely_points = [Point(x, y) for x, y in points]

# Your list of 20 points (converted to Shapely Points)
# points = [
#     Point(4, 24), Point(15, 3), Point(22, 20), Point(9, 11), Point(18, 7),
#     Point(2, 17), Point(25, 13), Point(12, 25), Point(7, 5), Point(19, 21),
#     Point(1, 9), Point(14, 16), Point(23, 2), Point(6, 14), Point(10, 22),
#     Point(3, 8), Point(20, 18), Point(5, 12), Point(16, 1), Point(21, 19)
# ]

points = [Point(43.275, -14.809), Point(42.807, -14.386), Point(42.55, -14.299), Point(43.041, -13.931), Point(43.175, -14.548), Point(43.163, -14.223)]

def create_distance_matrix(points):
    """Create distance matrix using numpy vectorization"""
    coords = np.array([(p.x, p.y) for p in points])
    diff = coords[:, np.newaxis, :] - coords[np.newaxis, :, :]
    return np.sqrt((diff ** 2).sum(axis=2)).astype(int)

def tsp_solver(points):
    """Solve TSP using OR-Tools"""
    # Create distance matrix
    distance_matrix = create_distance_matrix(points)
    
    # Create routing model
    manager = pywrapcp.RoutingIndexManager(
        len(points),  # number of locations
        1,            # number of vehicles
        0             # depot (starting point)
    )
    routing = pywrapcp.RoutingModel(manager)
    
    def distance_callback(from_index, to_index):
        """Callback function for distances"""
        from_node = manager.IndexToNode(from_index)
        to_node = manager.IndexToNode(to_index)
        return distance_matrix[from_node][to_node]
    
    transit_callback_index = routing.RegisterTransitCallback(distance_callback)
    routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)
    
    # Set search parameters
    search_parameters = pywrapcp.DefaultRoutingSearchParameters()
    search_parameters.first_solution_strategy = (
        routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC
    )
    search_parameters.local_search_metaheuristic = (
        routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
    )
    search_parameters.time_limit.seconds = 30
    
    # Solve the problem
    solution = routing.SolveWithParameters(search_parameters)
    
    # Extract the route
    index = routing.Start(0)
    route = []
    while not routing.IsEnd(index):
        route.append(manager.IndexToNode(index))
        index = solution.Value(routing.NextVar(index))
    route.append(manager.IndexToNode(index))
    
    return route


# Get optimal order and create LineString
optimal_order = tsp_solver(points)
ordered_points = [points[i] for i in optimal_order]
shortest_path = LineString([(p.x, p.y) for p in ordered_points])

print(f"Optimal route order: {optimal_order}")
print(f"Total distance: {shortest_path.length:.2f}")