import numpy as np
import matplotlib.pyplot as plt

def fit_ellipse_vectorized(points, bounds, grid_resolution=(20, 20, 10)):
    """
    Vectorized ellipse fitting using grid search over center and rotation angle.

    Parameters:
    points: np.array of shape (N, 2) - points to fit
    bounds: tuple ((min_x, max_x), (min_y, max_y), (min_theta, max_theta))
    grid_resolution: tuple (nx, ny, ntheta) - grid resolution for each parameter

    Returns:
    dict with ellipse parameters: center_x, center_y, a, b, theta
    """
    (x_bounds, y_bounds, theta_bounds) = bounds
    nx, ny, ntheta = grid_resolution

    # Create grid
    x_grid = np.linspace(x_bounds[0], x_bounds[1], nx)
    y_grid = np.linspace(y_bounds[0], y_bounds[1], ny)
    theta_grid = np.linspace(theta_bounds[0], theta_bounds[1], ntheta)

    # Create meshgrid for all combinations
    CX, CY, THETA = np.meshgrid(x_grid, y_grid, theta_grid, indexing='ij')

    # Flatten for vectorized operations
    cx_flat = CX.flatten()
    cy_flat = CY.flatten()
    theta_flat = THETA.flatten()

    n_combinations = len(cx_flat)
    n_points = len(points)

    # Vectorized coordinate transformation
    cos_theta = np.cos(theta_flat)  # shape: (n_combinations,)
    sin_theta = np.sin(theta_flat)  # shape: (n_combinations,)

    # Expand points for all combinations
    # points shape: (n_points, 2)
    # We want: (n_combinations, n_points, 2)
    points_expanded = np.expand_dims(points, 0).repeat(n_combinations, axis=0)

    # Expand centers for all combinations
    cx_expanded = cx_flat[:, np.newaxis]  # shape: (n_combinations, 1)
    cy_expanded = cy_flat[:, np.newaxis]  # shape: (n_combinations, 1)

    # Translate points
    dx = points_expanded[:, :, 0] - cx_expanded  # shape: (n_combinations, n_points)
    dy = points_expanded[:, :, 1] - cy_expanded  # shape: (n_combinations, n_points)

    # Rotate points (vectorized)
    cos_theta_expanded = cos_theta[:, np.newaxis]  # shape: (n_combinations, 1)
    sin_theta_expanded = sin_theta[:, np.newaxis]  # shape: (n_combinations, 1)

    u = dx * cos_theta_expanded + dy * sin_theta_expanded  # shape: (n_combinations, n_points)
    v = -dx * sin_theta_expanded + dy * cos_theta_expanded  # shape: (n_combinations, n_points)

    # Solve for semi-axes vectorized
    a_array, b_array, errors = solve_ellipse_axes_vectorized(u, v)

    # Find best solution
    valid_mask = (a_array > 0) & (b_array > 0) & np.isfinite(errors)

    if not np.any(valid_mask):
        return None

    valid_errors = errors[valid_mask]
    best_idx_in_valid = np.argmin(valid_errors)
    best_idx = np.where(valid_mask)[0][best_idx_in_valid]

    return {
        'center_x': cx_flat[best_idx],
        'center_y': cy_flat[best_idx],
        'a': a_array[best_idx],
        'b': b_array[best_idx],
        'theta': theta_flat[best_idx],
        'error': errors[best_idx]
    }

def solve_ellipse_axes_vectorized(u, v):
    """
    Vectorized solution for ellipse semi-axes.

    Parameters:
    u, v: arrays of shape (n_combinations, n_points)

    Returns:
    a, b, errors: arrays of shape (n_combinations,)
    """
    n_combinations, n_points = u.shape

    # Square the coordinates
    u2 = u * u  # shape: (n_combinations, n_points)
    v2 = v * v  # shape: (n_combinations, n_points)

    # Find minimum bounds for each combination
    u2_max = np.max(u2, axis=1)  # shape: (n_combinations,)
    v2_max = np.max(v2, axis=1)  # shape: (n_combinations,)

    # Avoid zero or negative bounds
    u2_max = np.maximum(u2_max, 1e-10)
    v2_max = np.maximum(v2_max, 1e-10)

    # Create grid for a² and b² search
    n_grid = 30  # Grid resolution for axes search

    # Expand bounds for vectorized grid search
    a2_min = u2_max  # shape: (n_combinations,)
    b2_min = v2_max  # shape: (n_combinations,)
    a2_max = a2_min * 10
    b2_max = b2_min * 10

    # Create grid for a² and b²
    a2_grid = np.linspace(0, 1, n_grid)[1:]  # exclude 0, shape: (n_grid-1,)
    b2_grid = np.linspace(0, 1, n_grid)[1:]  # exclude 0, shape: (n_grid-1,)

    A2_grid, B2_grid = np.meshgrid(a2_grid, b2_grid, indexing='ij')
    A2_flat = A2_grid.flatten()  # shape: (n_grid²,)
    B2_flat = B2_grid.flatten()  # shape: (n_grid²,)

    # Scale grids for each combination
    # Final shapes: (n_combinations, n_grid²)
    a2_min_expanded = a2_min[:, np.newaxis]  # shape: (n_combinations, 1)
    b2_min_expanded = b2_min[:, np.newaxis]  # shape: (n_combinations, 1)
    a2_range = (a2_max - a2_min)[:, np.newaxis]  # shape: (n_combinations, 1)
    b2_range = (b2_max - b2_min)[:, np.newaxis]  # shape: (n_combinations, 1)

    A2_scaled = a2_min_expanded + a2_range * A2_flat[np.newaxis, :]  # (n_combinations, n_grid²)
    B2_scaled = b2_min_expanded + b2_range * B2_flat[np.newaxis, :]  # (n_combinations, n_grid²)

    # Compute errors for all combinations and all grid points
    # We need to expand u2 and v2 to include the grid dimension
    u2_expanded = u2[:, np.newaxis, :]  # shape: (n_combinations, 1, n_points)
    v2_expanded = v2[:, np.newaxis, :]  # shape: (n_combinations, 1, n_points)

    A2_expanded = A2_scaled[:, :, np.newaxis]  # shape: (n_combinations, n_grid², 1)
    B2_expanded = B2_scaled[:, :, np.newaxis]  # shape: (n_combinations, n_grid², 1)

    # Calculate residuals for all combinations
    # Shape: (n_combinations, n_grid², n_points)
    residuals = u2_expanded / A2_expanded + v2_expanded / B2_expanded - 1

    # Calculate errors (sum of squared residuals)
    # Shape: (n_combinations, n_grid²)
    errors_grid = np.sum(residuals**2, axis=2)

    # Find best grid point for each combination
    best_grid_idx = np.argmin(errors_grid, axis=1)  # shape: (n_combinations,)

    # Extract best parameters
    combination_indices = np.arange(n_combinations)
    best_a2 = A2_scaled[combination_indices, best_grid_idx]
    best_b2 = B2_scaled[combination_indices, best_grid_idx]
    best_errors = errors_grid[combination_indices, best_grid_idx]

    return np.sqrt(best_a2), np.sqrt(best_b2), best_errors

def evaluate_ellipse_vectorized(params, points):
    """Evaluate how well the ellipse fits the points."""
    cx, cy = params['center_x'], params['center_y']
    a, b, theta = params['a'], params['b'], params['theta']

    cos_t, sin_t = np.cos(theta), np.sin(theta)

    # Transform points to ellipse coordinate system
    dx = points[:, 0] - cx
    dy = points[:, 1] - cy

    u = dx * cos_t + dy * sin_t
    v = -dx * sin_t + dy * cos_t

    # Calculate residuals from ellipse equation
    residuals = (u/a)**2 + (v/b)**2 - 1
    return np.sqrt(np.mean(residuals**2))

# Example usage
if __name__ == "__main__":
    points = np.array([[-18107.85742188,  -9668.421875  ],
                       [-18109.07421875,  -9649.95117188],
                       [-18133.55859375,  -9622.34765625],
                       [-18161.0234375,   -9615.94433594],
                       [-18180.34570312,  -9623.63476562]], dtype=np.float64)

    bounds = ((-18170, -18130), (-9679, -9625), (np.deg2rad(320), np.deg2rad(340)))

    # Time the vectorized version
    import time
    start_time = time.time()
    result = fit_ellipse_vectorized(points, bounds, grid_resolution=(50, 50, 20))
    end_time = time.time()

    if result:
        print(f"Ellipse parameters (computed in {end_time - start_time:.3f}s):")
        print(f"Center: ({result['center_x']:.2f}, {result['center_y']:.2f})")
        print(f"Semi-axes: a={result['a']:.2f}, b={result['b']:.2f}")
        print(f"Rotation: {np.rad2deg(result['theta']):.2f}°")
        print(f"Grid Error: {result['error']:.6f}")

        # Evaluate fit quality
        rms_error = evaluate_ellipse_vectorized(result, points)
        print(f"RMS residual: {rms_error:.6f}")
    else:
        print("No valid ellipse found within bounds")
