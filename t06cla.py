from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
import numpy as np

def create_distance_matrix(points):
    """
    Create distance matrix from points.
    points: list of (x, y) coordinates
    """
    size = len(points)
    matrix = np.zeros((size, size))
    for i in range(size):
        for j in range(size):
            if i != j:
                # Euclidean distance
                matrix[i][j] = np.sqrt(
                    (points[i][0] - points[j][0])**2 + 
                    (points[i][1] - points[j][1])**2
                )
    return matrix.astype(int)

def solve_tsp_with_constraints(points, mandatory_arcs, start_index):
    """
    Solve TSP with mandatory arcs and start point constraints
    
    Args:
        points: list of (x, y) coordinates
        mandatory_arcs: list of tuples (from_idx, to_idx) that must be in the solution
        start_index: index of the starting point
    """
    # Create the distance matrix
    distances = create_distance_matrix(points)
    
    # Create the routing model
    manager = pywrapcp.RoutingIndexManager(len(points), 1, start_index)
    routing = pywrapcp.RoutingModel(manager)

    # Create and register transit callback
    def distance_callback(from_index, to_index):
        from_node = manager.IndexToNode(from_index)
        to_node = manager.IndexToNode(to_index)
        return distances[from_node][to_node]

    transit_callback_index = routing.RegisterTransitCallback(distance_callback)
    routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)

    # Add mandatory arcs constraints
    for from_idx, to_idx in mandatory_arcs:
        routing.NextVar(manager.NodeToIndex(from_idx)).SetValues([manager.NodeToIndex(to_idx)])

    # Set maximum distance per vehicle
    dimension_name = 'Distance'
    routing.AddDimension(
        transit_callback_index,
        0,  # no slack
        10000,  # vehicle maximum travel distance
        True,  # start cumul to zero
        dimension_name)

    # Make all points mandatory except the final point
    for node in range(len(points)):
        if node != start_index:
            routing.AddDisjunction([manager.NodeToIndex(node)], 1000000)  # High penalty to ensure visits

    # Set search parameters
    search_parameters = pywrapcp.DefaultRoutingSearchParameters()
    search_parameters.first_solution_strategy = (
        routing_enums_pb2.FirstSolutionStrategy.CHRISTOFIDES
    )
    search_parameters.local_search_metaheuristic = (
        routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
    )
    search_parameters.time_limit.FromSeconds(5)

    # Solve the problem
    solution = routing.SolveWithParameters(search_parameters)

    if solution:
        path = []
        index = routing.Start(0)
        while not routing.IsEnd(index):
            node_index = manager.IndexToNode(index)
            path.append(node_index)
            index = solution.Value(routing.NextVar(index))
        path.append(manager.IndexToNode(index))
        
        total_distance = 0
        for i in range(len(path) - 1):
            total_distance += distances[path[i]][path[i + 1]]
            
        return path, total_distance
    return None, None

# Example usage
if __name__ == "__main__":
    # Example with 20 random points
    np.random.seed(42)
    points = [(np.random.randint(0, 100), np.random.randint(0, 100)) for _ in range(20)]
    
    # Define mandatory arcs: must go from point 4 to 1, and from 12 to 5
    mandatory_arcs = [(4, 1), (12, 5)]
    
    # Set start point (e.g., start from point 0)
    start_index = 0
    
    # Solve the problem
    path, total_distance = solve_tsp_with_constraints(points, mandatory_arcs, start_index)
    
    if path:
        print(f"Found path: {path}")
        print(f"Total distance: {total_distance}")
        print(f"Number of points visited: {len(path)}")
    else:
        print("No solution found!")