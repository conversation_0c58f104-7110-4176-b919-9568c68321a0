import numpy as np
import shapely
import shapely.ops

import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time
from functools import lru_cache

from scipy.special import ellipe, ellipeinc
from scipy.optimize import brentq

import cProfile
import pstats
import cma

class CachedEllipseFitter:
    """
    Cached ellipse fitter that pre-computes expensive operations on points array.
    Use this when the same points array is used many times with different parameters.
    """
    
    def __init__(self, points):
        """
        Initialize with points array and pre-compute what we can.
        
        Args:
            points: Nx2 array of points
        """
        self.points = points
        self.n_points = len(points)
        
        # Pre-extract x and y coordinates (avoid repeated indexing)
        self.x_coords = points[:, 0].copy()  # Make contiguous copy
        self.y_coords = points[:, 1].copy()
        
        # Pre-allocate arrays for intermediate computations
        self._dx = np.empty(self.n_points, dtype=np.float64)
        self._dy = np.empty(self.n_points, dtype=np.float64)
        self._x_rot = np.empty(self.n_points, dtype=np.float64)
        self._y_rot = np.empty(self.n_points, dtype=np.float64)
        self._val = np.empty(self.n_points, dtype=np.float64)
        
        # Cache for frequently used computations
        self._last_center = None
        self._last_theta = None
        self._cached_cos_theta = None
        self._cached_sin_theta = None
        self._translation_cached = False
        self._rotation_cached = False
    
    def objective_function(self, center, theta, semi_major, semi_minor, front=False):
        """
        Optimized objective function using cached computations.
        
        Args:
            center: 2-element array of ellipse center
            theta: rotation angle in radians
            semi_major: semi-major axis length
            semi_minor: semi-minor axis length
            front: if True, use mean squared error; if False, use min-based error
        
        Returns:
            Objective function value
        """
        # Cache trigonometric functions if theta hasn't changed
        if self._last_theta != theta:
            self._cached_cos_theta = np.cos(theta)
            self._cached_sin_theta = np.sin(theta)
            self._last_theta = theta
            self._rotation_cached = False
        
        # Cache translation if center hasn't changed
        if self._last_center is None or not np.array_equal(self._last_center, center):
            np.subtract(self.x_coords, center[0], out=self._dx)
            np.subtract(self.y_coords, center[1], out=self._dy)
            self._last_center = center.copy()
            self._translation_cached = True
            self._rotation_cached = False
        
        # Apply rotation using cached values and pre-allocated arrays
        if not self._rotation_cached:
            # x_rot = dx * cos_theta + dy * sin_theta
            np.multiply(self._dx, self._cached_cos_theta, out=self._x_rot)
            np.multiply(self._dy, self._cached_sin_theta, out=self._val)  # temp use
            np.add(self._x_rot, self._val, out=self._x_rot)
            
            # y_rot = -dx * sin_theta + dy * cos_theta
            np.multiply(self._dx, -self._cached_sin_theta, out=self._y_rot)
            np.multiply(self._dy, self._cached_cos_theta, out=self._val)  # temp use
            np.add(self._y_rot, self._val, out=self._y_rot)
            
            self._rotation_cached = True
        
        # Pre-compute squared axis ratios
        inv_major_sq = 1.0 / (semi_major * semi_major)
        inv_minor_sq = 1.0 / (semi_minor * semi_minor)
        
        # Compute ellipse equation values using pre-allocated array
        np.multiply(self._x_rot, self._x_rot, out=self._val)
        np.multiply(self._val, inv_major_sq, out=self._val)
        
        # Add y component
        y_rot_sq = self._y_rot * self._y_rot  # temporary
        y_rot_sq *= inv_minor_sq
        np.add(self._val, y_rot_sq, out=self._val)
        
        if front:
            # Use pre-allocated array for difference computation
            np.subtract(self._val, 1.0, out=self._val)
            np.multiply(self._val, self._val, out=self._val)
            return np.mean(self._val)
        else:
            return (1.0 - np.min(self._val)) ** 2
    
    def clear_cache(self):
        """Clear all cached values."""
        self._last_center = None
        self._last_theta = None
        self._translation_cached = False
        self._rotation_cached = False

def get_ellipse_center(anchor_point, semi_major_axis, semi_minor_axis=None, rotation=0.0):
    """Calculate the center of ellipses based on anchor points (vectorized).

    This function computes ellipse centers where the anchor point is the '3 o'clock'
    point on the ellipse's perimeter. The major axis is oriented along the global
    Y-axis and the minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (np.ndarray): Array of shape (N, 2) containing (x, y) coordinates
            that serve as the '3 o'clock' reference points on the ellipse perimeters.
        semi_major_axis (np.ndarray): 1D array of length N containing the semi-major
            axis values (the "main" radius) for each ellipse.
        semi_minor_axis (np.ndarray, optional): 1D array of length N containing the
            semi-minor axis values. If None, defaults to semi_major_axis values,
            creating circles. Defaults to None.
        rotation (np.ndarray, optional): 1D array of length N containing the geometric
            rotation of each ellipse in radians. If a scalar is provided, it will be
            broadcast to all ellipses. Defaults to 0.0.

    Returns:
        np.ndarray: Array of shape (N, 2) containing the (x, y) coordinates of the
            calculated ellipse centers.
    """
    # Convert inputs to numpy arrays for vectorized operations
    anchor_point = np.asarray(anchor_point)
    semi_major_axis = np.asarray(semi_major_axis)
    rotation = np.asarray(rotation)

    # Ensure anchor_point is 2D (N, 2)
    if anchor_point.ndim == 1:
        anchor_point = anchor_point.reshape(1, -1)

    # If semi_minor_axis is not provided, create circles by making it equal to semi_major_axis
    if semi_minor_axis is None:
        local_semi_minor = semi_major_axis
    else:
        local_semi_minor = np.asarray(semi_minor_axis)

    # Broadcast rotation to match the number of ellipses if it's a scalar
    if rotation.ndim == 0:
        rotation = np.full(len(anchor_point), rotation)

    # Calculate the offset from center to the '3 o'clock' anchor point
    # The anchor is the '3 o'clock' point. The vector from the center to this
    # point on our unrotated ellipse is (semi_minor_axis, 0). We rotate this
    # vector to find the offset from the true center to the anchor point.
    offset_x = local_semi_minor * np.cos(rotation)
    offset_y = local_semi_minor * np.sin(rotation)

    # The true center is the anchor_point minus this rotated offset vector
    center_x = anchor_point[:, 0] - offset_x
    center_y = anchor_point[:, 1] - offset_y

    # Stack the coordinates to create the final center array
    centers = np.column_stack((center_x, center_y))

    return centers

def create_ellipse_on_anchor(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = np.array([center_x, center_y])
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center

def create_ellipse(center, semi_major_axis_length, resolution=71, semi_minor_axis_length=None, rotation=0.0):
    
    """
    Create a 2D ellipse as a numpy array with points starting from 12 o'clock position.
    
    Parameters
    ----------
    center : array_like
        The (x, y) coordinates of the ellipse center.
    semi_major_axis_length : float
        Length of the semi-major axis.
    resolution : int, optional
        Number of points to generate around the ellipse. Default is 71.
        The last point will be identical to the first point.
    semi_minor_axis_length : float, optional
        Length of the semi-minor axis. If None, defaults to semi_major_axis_length
        (creating a circle). Default is None.
    rotation : float, optional
        Rotation angle in radians. Positive values rotate counterclockwise.
        Default is 0.0.
    
    Returns
    -------
    numpy.ndarray
        A 2D array of shape (resolution, 2) containing the (x, y) coordinates
        of points on the ellipse. The first and last points are identical.
    
    Examples
    --------
    >>> # Create a circle centered at origin with radius 5
    >>> circle = create_ellipse([0, 0], 5)
    >>> circle.shape
    (71, 2)
    
    >>> # Create an ellipse with different major and minor axes
    >>> ellipse = create_ellipse([2, 3], 4, semi_minor_axis_length=2, resolution=101)
    >>> ellipse.shape
    (101, 2)
    
    >>> # Create a rotated ellipse
    >>> rotated = create_ellipse([0, 0], 3, semi_minor_axis_length=1, rotation=np.pi/4)
    """

    center = np.array(center)
    
    # Default semi_minor_axis_length to semi_major_axis_length (circle)
    if semi_minor_axis_length is None:
        semi_minor_axis_length = semi_major_axis_length
    
    # Generate angles starting from 12 o'clock (π/2) instead of 3 o'clock (0)
    # We use resolution-1 points plus duplicate the first point at the end
    angles = np.linspace(0, 2*np.pi, resolution, endpoint=False) + np.pi/2
    
    # Generate ellipse points in standard position
    x = semi_major_axis_length * np.cos(angles)
    y = semi_minor_axis_length * np.sin(angles)
    
    # Apply rotation if specified
    if rotation != 0.0:
        cos_rot = np.cos(rotation)
        sin_rot = np.sin(rotation)
        x_rot = x * cos_rot - y * sin_rot
        y_rot = x * sin_rot + y * cos_rot
        x, y = x_rot, y_rot
    
    # Translate to center
    x += center[0]
    y += center[1]
    
    # Stack points and ensure last point equals first point
    points = np.column_stack((x, y))
    
    # Make the last point identical to the first point
    points = np.vstack([points, points[0]])
    
    return points

@lru_cache(maxsize=1024)
def _cached_find_t(a, b, fraction):
    return find_t_for_arc_length_fraction_newton(a, b, fraction)

def find_t_for_arc_length_fraction_newton(a, b, fraction, tol=1e-8, max_iter=50):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference
    
    # Initial guess (linear approximation)
    t = fraction * 2 * np.pi
    
    for _ in range(max_iter):
        f = a * scipy.special.ellipeinc(t, e_sq) - target_arc_length
        if abs(f) < tol:
            return t
        
        # Derivative: a * sqrt(1 - e_sq * sin(t)**2)
        df_dt = a * np.sqrt(1 - e_sq * np.sin(t)**2)
        t -= f / df_dt
    
    return t

def ellipse_point_and_normal(a, b, center, rotation, fraction):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a, b, center, rotation, fraction

    Returns:
        tuple: (point_vector, normal_angle)
    """
    # 1. Parametric angle for arc length fraction
    # t_arc = find_t_for_arc_length_fraction_newton(a, b, fraction)
    t_arc = _cached_find_t(a, b, fraction)    
    
    # Precompute trig values once
    ct, st = np.cos(t_arc), np.sin(t_arc)
    cr, sr = np.cos(rotation), np.sin(rotation)
    
    # 2. Point on ellipse (rotated + translated)
    x, y = a * ct, b * st
    point_vector = np.array([x * cr - y * sr, x * sr + y * cr]) + center

    # 3. Outward normal before rotation
    nx, ny = b * ct, a * st
    normal_vector = np.array([nx * cr - ny * sr, nx * sr + ny * cr])
    
    norm = np.hypot(normal_vector[0], normal_vector[1])
    if norm > 1e-9:
        normal_vector /= norm
    else:
        normal_vector[:] = 0.

    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])
    return point_vector, normal_angle

def get_ellipse_data(polygon_boundary, medial_axis_edge, medial_axis_start=True):
    if medial_axis_start:
        idx = 0
    else:
        idx = -1
    center = shapely.get_coordinates(medial_axis_edge)[idx]
    center_point = shapely.geometry.Point(center)
    outer_semi_axis_length = center_point.distance(polygon_boundary)
    
    ellipse_geometry = create_ellipse(
        center=center,
        semi_major_axis_length=outer_semi_axis_length        
        )
    
    ellipse = shapely.geometry.LinearRing(ellipse_geometry)
    if ellipse.intersects(medial_axis_edge):
        inter = ellipse.intersection(medial_axis_edge)
        if inter.geom_type == "MultiPoint":
            distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
            if medial_axis_start:
                idx = np.argmin(distances)                    
            else:
                idx = np.argmax(distances)                    
            anchor = shapely.get_coordinates(inter.geoms[idx])[0]
        elif inter.geom_type == "Point":
            anchor = shapely.get_coordinates(inter)[0]
        
    else:
        print('ellipse does not intersect medial axis')
        return None

    ### Initial rotation of the ellipse in radians ###
    rotation_vector = anchor - center
    # Calculate rotation (for an ellipse, so np.pi/2 is actually 0) and normalize to [-pi, pi] range
    rotation = np.arctan2(rotation_vector[1], rotation_vector[0]) - np.pi/2
    normalized_rotation = ((rotation + np.pi) % (2 * np.pi)) - np.pi

    ellipse_data = {
        'center': center,
        'major': outer_semi_axis_length,
        'minor': outer_semi_axis_length,
        'rotation': normalized_rotation,
        'roll' : False
    }    
    return ellipse_data

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []

def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list

def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])

def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    offsets = [offset[0]]
    radii = [radius[0]]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
            offsets.append(point)
            radii.append(r)

    return np.array(offsets), np.array(radii)

def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj

def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring
    p1 = shapely.get_coordinates(reference_ls.interpolate(0.45, normalized=True)).reshape((-1))

    # Get the end point of the reference linestring
    p2 = shapely.get_coordinates(reference_ls.interpolate(0.55, normalized=True)).reshape((-1))

    # Use the first point of the target linestring as its representative point
    q = shapely.get_coordinates(target_ls.interpolate(0.5, normalized=True)).reshape((-1))

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear

def calculate_all_boundary_points(l_points, r_points, center, a, b, rotation, cutter_radius):
    """
    Calculate all four boundary points (inside_l, inside_r, outside_l, outside_r) in one vectorized call.

    Based on the logic from relative_side_distance(), this function efficiently computes
    all boundary intersections for both left and right point sets with both inside and outside ellipses.

    Parameters:
    - l_points: array-like, shape (n_points, 2) - left side points
    - r_points: array-like, shape (n_points, 2) - right side points
    - center: array-like, shape (2,) - ellipse center
    - a: float - semi-major axis (before cutter adjustment)
    - b: float - semi-minor axis (before cutter adjustment)
    - rotation: float - rotation angle in radians
    - cutter_radius: float - cutter radius for inside ellipse adjustment

    Returns:
    - dict: {
        'inside_point_l': ndarray,
        'inside_point_r': ndarray,
        'outside_point_l': ndarray,
        'outside_point_r': ndarray
      }
    """    
    center = np.asarray(center)

    # Pre-compute rotation matrix
    cos_theta = np.cos(rotation)
    sin_theta = np.sin(rotation)
    rot_matrix = np.array([[cos_theta, sin_theta],
                          [-sin_theta, cos_theta]])

    # Define ellipse parameters for inside and outside
    inside_axes = np.array([a - cutter_radius + 0.15, b - cutter_radius + 0.15])
    outside_axes = np.array([a, b])

    def find_boundary_point(points, axes):
        """Helper function to find boundary point for given points and ellipse axes"""
        translated = points - center
        translated_rot = translated @ rot_matrix.T

        val = (translated_rot[:, 0] ** 2 / axes[0] ** 2) + (translated_rot[:, 1] ** 2 / axes[1] ** 2)
        boundary_indices = np.where(np.isclose(val, 1.0, atol=0.01))[0]

        if len(boundary_indices) > 0:
            return points[boundary_indices[-1]]
        else:
            return None

    # Calculate all four points
    inside_point_l = find_boundary_point(l_points, inside_axes)
    inside_point_r = find_boundary_point(r_points, inside_axes)
    outside_point_l = find_boundary_point(l_points, outside_axes)
    outside_point_r = find_boundary_point(r_points, outside_axes)

    return  [inside_point_l, 
            inside_point_r,
            outside_point_l,
            outside_point_r]
    

def fit_ellipse(ellipse_data, front_data, cutter_data, l_points, r_points, min_radius_of_curvature=15.0, l_fitter=None, r_fitter=None):
    """
    Fit an ellipse within given constraints using optimization.

    This function performs ellipse fitting by minimizing an objective function that combines
    distances to front, left, and right boundaries. It uses scipy.optimize.minimize with
    SLSQP method to find optimal ellipse parameters (position along front, major axis,
    minor axis) while respecting curvature constraints.

    Args:
        ellipse_data (dict): Initial ellipse parameters with keys:
            - 'center': [x, y] center coordinates
            - 'major': semi-major axis length
            - 'minor': semi-minor axis length
            - 'rotation': rotation angle in radians
            - 'roll': boolean for ellipse rolling (unused in this version)
        front_data (dict): Front boundary data with key:
            - 'line': LineString representing the front boundary
        cutter_data (dict): Cutter parameters with key:
            - 'radius': cutter radius for offset calculations
        l_points (np.ndarray): Left side points for fitting (N, 2)
        r_points (np.ndarray): Right side points for fitting (N, 2)
        min_radius_of_curvature (float): Minimum allowed radius of curvature
        l_fitter (CachedEllipseFitter): Pre-computed fitter for left points
        r_fitter (CachedEllipseFitter): Pre-computed fitter for right points

    Returns:
        dict: Updated ellipse_data with optimized parameters

    Note:
        The optimization minimizes: front_distances + 4*left_distances + 4*right_distances
        Weights are chosen to balance front vs side constraints.
    """
    def _constraint_curvature(x):
        """
        Constraint: minor - sqrt(k * major) >= 0
        Returns a positive value for a feasible point, negative for infeasible.
        x[1] is major, x[2] is minor
        """         
        return x[2] - np.sqrt(min_radius_of_curvature * x[1])

    def _objective_all(params, a,b,c,t):
        position, major, minor = params        
        local_pos = outer_ellipse.project(front_data['line'].interpolate(position, normalized=True), normalized=True)
        
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
        new_anchor, new_rotation = ellipse_point_and_normal(a=a, b=b, center=c, rotation=t, fraction=local_pos)
    
        center = create_ellipse_on_anchor(
            anchor_point=new_anchor,
            semi_major_axis=major,
            semi_minor_axis=minor,
            rotation=new_rotation,
            anchor_on_perimeter=True,
            only_center=True
            )
        
        front_distances = front_fitter.objective_function(center, new_rotation+np.pi/2, major, minor, front=True)
        l_distances = l_fitter.objective_function(center, new_rotation+np.pi/2, major, minor) * 4
        r_distances = r_fitter.objective_function(center, new_rotation+np.pi/2, major, minor) * 4
        return front_distances + l_distances + r_distances
    
    # data of outer ellipse    
    a = ellipse_data['minor']   # semi-major axis
    b = ellipse_data['major']   # semi-minor axis
    center = np.asarray(ellipse_data['center'])
    rotation = (ellipse_data['rotation'] + np.pi/2) % (2*np.pi)
    cutter_radius = cutter_data['radius']

    boundary_points = calculate_all_boundary_points(l_points, r_points, center, a, b, rotation, cutter_radius)

    #euclidean distance between point l/r
    inside_distance = np.linalg.norm(boundary_points[0] - boundary_points[1])
    outside_distance = np.linalg.norm(boundary_points[2] - boundary_points[3])
    distances = [inside_distance, outside_distance]

    ## 0- outer, 1- inner    
    if distances[0] >= distances[1]:
        # print('bigger than previous')
        minimum_major = distances[1]
        maximum_major = distances[0]
    else:
        # print('smaller than previous')
        minimum_major = distances[0]
        maximum_major = distances[1]

    min_max_ratio = minimum_major / maximum_major

    if min_max_ratio > 0.8: ## TO-DO: make it dynamic / try optimization
        min_max_correction = 0.25 * min_max_ratio
    else:
        min_max_correction = 0.0

    # print(f'min_max_ratio: {min_max_ratio}, min_max_correction: {min_max_correction}')

    minimum_major /= 2 + min_max_correction
    maximum_major /= 2 - min_max_correction
    minimum_minor = np.sqrt(min_radius_of_curvature * maximum_major)

    bnds = scipy.optimize.Bounds(
        [0.2, minimum_major, minimum_minor],  # Lower bounds for position, major, minor
        [0.8, maximum_major, maximum_major]   # Upper bounds
    )

    # print(f'i {i}, minimum_major: {minimum_major}, maximum_major: {maximum_major}')

    project_distances = np.linspace(0.1, 0.9, 20)    
    front_points = np.asarray([front_data['line'].interpolate(d, normalized=True).coords[0] for d in project_distances])    
    front_fitter = CachedEllipseFitter(front_points)

    initial_guess = [0.5, minimum_major, minimum_minor]    
    constraints = [
        {'type': 'ineq', 'fun': _constraint_curvature}
    ]    

    outer_ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major'],
            semi_minor_axis_length=ellipse_data['minor'],
            rotation=ellipse_data['rotation']
            )
    
    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    
    result = scipy.optimize.minimize(
        _objective_all,
        args=(a,b,center,rotation),
        x0=initial_guess,
        method = 'SLSQP',
        bounds=bnds,
        constraints=constraints,
        tol=5e-8,        
    )
    position, major, minor = result.x

    # major_range = (major - minimum_major) / (maximum_major - minimum_major)
    # print(f'major (0-1): {major_range:.3f}')       

    local_pos = outer_ellipse.project(front_data['line'].interpolate(position, normalized=True), normalized=True)    
    
    # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
    new_anchor, new_rotation = ellipse_point_and_normal(a=a, b=b, center=center, rotation=rotation, fraction=local_pos)
    
    center = create_ellipse_on_anchor(
        anchor_point=new_anchor,
        semi_major_axis=major,
        semi_minor_axis=minor,
        rotation=new_rotation,
        anchor_on_perimeter=True,
        only_center=True
        )
    
    # Update ellipse data with optimization results
    ellipse_data.update({
        'center': center,
        'major': major+cutter_radius,
        'minor': minor+cutter_radius,        
        'rotation': new_rotation-np.pi/2,
    })
    return ellipse_data


def get_split_coords(ellipse_data):
    cx, cy = ellipse_data['center']
    angle_rad = ellipse_data['rotation'] - np.pi/2
    x = cx + ellipse_data['major'] * np.cos(angle_rad)
    y = cy + ellipse_data['major'] * np.sin(angle_rad)
    return np.array([x, y])


def resample_linestring(line: shapely.geometry.LineString, segment_length: float) -> shapely.geometry.LineString:
    """
    Resamples a Shapely LineString to have segments of a constant length.

    The last segment will be shorter than segment_length if the total
    length is not a multiple of segment_length.

    Args:
        line (LineString): The original LineString to resample.
        segment_length (float): The desired length of each segment.

    Returns:
        LineString: The new, resampled LineString.
    """
    if segment_length <= 0:
        raise ValueError("Segment length must be positive.")

    # Get the total length of the original line
    total_length = line.length

    # Generate distances along the line at which to place new vertices
    # np.arange creates a sequence from 0 to total_length with a step of segment_length
    distances = np.arange(0, total_length, segment_length)

    # Interpolate points at these distances
    # The list comprehension is a concise way to do this
    new_points = [line.interpolate(dist) for dist in distances]

    # Always include the very last point of the original line to ensure
    # the resampled line has the same extent.
    # We can access the last coordinate directly.
    last_point = shapely.geometry.Point(line.coords[-1])
    if not new_points[-1].equals(last_point):
         new_points.append(last_point)


    # Create a new LineString from the list of points
    return shapely.geometry.LineString(new_points)


def get_init_helper_geoms(ellipse_data, sides_data, cutter_data):
    outer_ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=ellipse_data['major'],
        semi_minor_axis_length=ellipse_data['minor'],
        rotation=ellipse_data['rotation']
    )

    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    outer_ellipse_polygon = shapely.geometry.Polygon(outer_ellipse_geometry)
    shapely.prepare(outer_ellipse)
    shapely.prepare(outer_ellipse_polygon)

    inter_outer = outer_ellipse.intersection(sides_data['ring'])    

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
        return None

    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]

    sides_fractions = [
        sides_data['lines'][0].project(inter_outer.geoms[np.argmin(outer_projected_distances)], normalized=True),
        sides_data['lines'][1].project(inter_outer.geoms[np.argmax(outer_projected_distances)], normalized=True)
    ]    
    
    front_fractions = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    
    front_data = {
        'line': front_line,        
        'fractions': front_fractions,
        'sides_fractions': sides_fractions        
    }

    ## works only on the first iteration. TO-DO: move from loop.
    ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=(ellipse_data['major'] - cutter_data['radius'])+0.25,        
        semi_minor_axis_length=(ellipse_data['minor'] - cutter_data['radius'])+0.25,
        rotation=ellipse_data['rotation']
    )
    if ellipse_data['roll']:
        ellipse_geometry = np.roll(ellipse_geometry, int(len(ellipse_geometry)/2), axis=0)

    ellipse = shapely.geometry.LinearRing(ellipse_geometry)    
  
    sides_fractions_inner = []
    for side in sides_data['lines']:
        inter = ellipse.intersection(side)
        if inter.geom_type == "Point":
            sides_fractions_inner.append(side.project(inter, normalized=True))       
        else:
            print(f'ellipse intersection with side is not a point: {inter.geom_type}')            

    sides_parts = []
    for inner, outer, side in zip(
            sides_fractions_inner,
            front_data['sides_fractions'],
            sides_data['lines']
        ):
        sides_parts.append(shapely.ops.substring(side, inner, outer, normalized=True))

    opposite_lines = [
        shapely.geometry.LineString([outer_ellipse_geometry[0], outer_ellipse_geometry[29]]), #left
        shapely.geometry.LineString([outer_ellipse_geometry[0], outer_ellipse_geometry[43]]) #right        
    ]

    helper_geoms = {
        'outer_ellipse': outer_ellipse,
        'outer_ellipse_polygon': outer_ellipse_polygon,
        'front_data': front_data,
        'sides_parts': sides_parts,
        'opposite_lines': opposite_lines
    }
    if inter.geom_type == "MultiPoint":
        for part in helper_geoms['sides_parts']:
            create_line_object(part.coords, "side_part", color=(1, 0, 0, 1))

    return front_data


def get_front_data(ellipse_data, sides_data):    
    #outer part
    outer_ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=ellipse_data['major'],
        semi_minor_axis_length=ellipse_data['minor'],
        rotation=ellipse_data['rotation']
    )
    
    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)    
    outer_ellipse = shapely.remove_repeated_points(outer_ellipse)        
    shapely.prepare(outer_ellipse)    

    inter_outer = outer_ellipse.intersection(sides_data['ring'])

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
        return None    
    
    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]

    front_fractions = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    
    front_data = {
        'line': front_line,        
        'fractions': front_fractions
    }

    return front_data


def get_sides_and_ellipse_data(base_geoms):
    ## Sides per path
    trochos_offsets, trochos_radii = advancing_front(base_geoms['medial_axis_coords'], base_geoms['polygon_buffered'], 1) # 5mm between trochoids
    
    points_array = shapely.points(trochos_offsets)
    buffered_points_array = shapely.buffer(points_array, trochos_radii, quad_segs=18) #about 72 points per circle
    buffered_points_array[0] = shapely.buffer(points_array[0], trochos_radii[0], quad_segs=36)
    buffered_points_array[-1] = shapely.buffer(points_array[-1], trochos_radii[-1], quad_segs=36)
    
    trochos_poly = shapely.unary_union(buffered_points_array).simplify(0.05)
    shapely.prepare(trochos_poly)
    trochos_poly_coords = shapely.get_coordinates(trochos_poly.exterior)
    
    boundary = base_geoms['polygon'].boundary
    
    ##ellipse data
    ellipse_data = get_ellipse_data(boundary, base_geoms['medial_axis'], medial_axis_start=True)    
    end_ellipse_data = get_ellipse_data(boundary, base_geoms['medial_axis'], medial_axis_start=False)
    
    start_split_coords = get_split_coords(ellipse_data)
    end_split_coords = get_split_coords(end_ellipse_data)
    distances =scipy.spatial.distance.cdist([start_split_coords, end_split_coords], trochos_poly_coords)  # Shape: (len(P), len(points))
    split0, split1 = np.argmin(distances, axis=1)
    
    # Ensure proper ordering
    start, end = min(split0, split1), max(split0, split1)

    # Create two continuous segments
    side0 = shapely.geometry.LineString(trochos_poly_coords[start:end+1]).simplify(0.001)
    side1_coords = np.concatenate((trochos_poly_coords[end:], trochos_poly_coords[:start+1]))
    side1 = shapely.geometry.LineString(side1_coords).simplify(0.001)
    sides = [side0, side1]
    # sides = [resample_linestring(side, 0.1) for side in sides]

    ### Determine sides, the first side is the one that is on the "left" of the medial axis
    if get_side_of_medial_axis(sides[0], base_geoms['medial_axis']) == -1:
        sides.reverse()

    # reverse right side for match direction with left: left -->, right -->
    sides[1] = sides[1].reverse()

    for side in sides:
        shapely.prepare(side)

    trochos_ring = shapely.geometry.LinearRing(trochos_poly.exterior.coords)
    shapely.prepare(trochos_ring)

    sides_data = {
        'ring': trochos_ring,
        'polygon': trochos_poly,
        'lines': sides
    }
    return sides_data, ellipse_data


def main():
    cutter_dim = 10 # in mm

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_dim / 2
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes).normalize() ## reorder rings if they are not in the expected orientation (exterior ccw, interiors cw)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])
    medial_middle = medial_axis.interpolate(0.5, normalized=True)

    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]

    # tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    # active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]
    
    base_geoms = {
        'polygon': polygon,
        'polygon_buffered': polygon_buffered,
        'medial_axis': medial_axis,
        'medial_axis_coords': geometry[1] # medial axis coords in original shape
    }
   
    sides_data, ellipse_data = get_sides_and_ellipse_data(base_geoms)
    front_data = get_init_helper_geoms(ellipse_data, sides_data, cutter_data)

    create_line_object(sides_data['lines'][0].coords, "side_left", color=(0, 1, 0, 1))
    create_line_object(sides_data['lines'][1].coords, "side_right", color=(1, 0, 0, 1))   
    
    min_radius_of_curvature = 10.0
    time1 = time.time() 

    # prof = cProfile.Profile()
    # prof.enable()  

    # # Profile just one call of the objective
    # ellipse_data = fit_ellipse(ellipse_data, sides_data, front_data, cutter_data, min_radius_of_curvature, 0)

    # # Look at results
    # stats = pstats.Stats("prof_stats")
    # stats.sort_stats("cumtime").print_stats(20)   # top 20 slowest

    # prof.disable()
    # stats = pstats.Stats(prof).sort_stats("cumtime")
    # stats.print_stats(20)   # top 20 slowest
    # return

    l_l = shapely.segmentize(sides_data['lines'][0], 0.5)
    l_r = shapely.segmentize(sides_data['lines'][1], 0.5)
    l_points = shapely.get_coordinates(l_l).reshape(-1, 2)
    r_points = shapely.get_coordinates(l_r).reshape(-1, 2)

    l_fitter = CachedEllipseFitter(l_points)
    r_fitter = CachedEllipseFitter(r_points)

    for i in range(25):        
        # create_line_object(front_data['line'].coords, "front_line", color=(0, 0.3, 1, 1))
        ellipse_data = fit_ellipse(ellipse_data, front_data, cutter_data, l_points, r_points, min_radius_of_curvature, l_fitter, r_fitter)        
        ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major']-cutter_data['radius'],
            semi_minor_axis_length=ellipse_data['minor']-cutter_data['radius'],
            rotation=ellipse_data['rotation']
            )
        
        # ell_part0 = ellipse_geometry[0:25]
        # ell_part1 = ellipse_geometry[-25:]
        # ell_merged = np.concatenate((ell_part1, ell_part0))
        # create_line_object(ell_merged, "ellipse_inner", color=(0.8, 0.2, 0.2, 1))
        create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0.2, 0.8, 1))
        front_data = get_front_data(ellipse_data, sides_data)
    time2 = time.time()
    print(f'Time: {time2-time1}')

if __name__ == "__main__":
    main()
