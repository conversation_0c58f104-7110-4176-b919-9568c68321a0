import shapely
from shapely import LineString, box, affinity
import time
import numpy as np

stock_poly = box(-1, -1, 1, 1)  # Define your stock polygon here
arc_radius = 1.0  # Define your arc radius here

def angle_engagement(center, p_center, r, stock_poly, arc_resolution=16):
    
    # Calculate rotation angle based on vector from center to p_center
    vec = [p_center[0] - center[0], p_center[1] - center[1]]
    rotation_angle = np.arctan2(vec[1], vec[0])    
    
    # Create a 180-degree arc as a LineString
    arc_points = [(center[0] + r * np.cos(rotation_angle + np.pi/2 - t), 
                  center[1] + r * np.sin(rotation_angle + np.pi/2 - t)) 
                 for t in np.linspace(0, np.pi, arc_resolution)]
    
    # Create the arc as a LineString
    arc = shapely.LineString(arc_points)
    
    intersection = shapely.intersection(stock_poly, arc, grid_size=None)
    if intersection:    
        if intersection.geom_type == "LineString":
            # Convert line length to angle width (in radians)
            return intersection.length / arc_radius
        elif intersection.geom_type == "MultiLineString":
            section_lengths = [line.length for line in intersection.geoms]
            return sum(section_lengths) / arc_radius
    else:
        return None
    
