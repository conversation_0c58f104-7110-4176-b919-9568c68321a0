import numpy as np
import matplotlib.pyplot as plt

# Define the contour function
A = 10
w = 0.1
def c(t):
    return np.array([t, A * np.sin(w * t)])

def tangent(t, delta=1e-5):
    """Approximate tangent vector at t."""
    c_plus = c(t + delta)
    c_minus = c(t - delta)
    deriv = (c_plus - c_minus) / (2 * delta)
    norm = np.linalg.norm(deriv)
    return deriv / norm if norm > 0 else deriv

def rotate_vector(vec, angle_deg):
    """Rotate a 2D vector by angle_deg counterclockwise."""
    angle_rad = np.deg2rad(angle_deg)
    cos_a = np.cos(angle_rad)
    sin_a = np.sin(angle_rad)
    rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
    return rotation_matrix @ vec

# Parameters
theta = 60          # Engagement angle (degrees)
r_tool = 10         # Tool radius (mm)
t_min = 0           # Start parameter
t_max = 100         # End parameter
delta_t = 0.1       # Step size
beta = 0            # Initial angle

# Initialization
t_i = t_min
C_i = c(t_i)
T_i = tangent(t_i)
omega = beta + theta + 90
U_i = rotate_vector(T_i, omega)
P_i = C_i + r_tool * U_i
PC = C_i - P_i
alpha = 90 - theta
V_i = rotate_vector(PC, alpha)
tool_path = [P_i]

# Main loop
while t_i < t_max:
    t_i += delta_t
    C_next = c(t_i)  # This should now work    
    # Intersection calculation
    D = P_i - C_next
    quad_a = np.dot(V_i, V_i)
    quad_b = 2 * np.dot(D, V_i)
    quad_c = np.dot(D, D) - r_tool**2  # Renamed to quad_c
    discriminant = quad_b**2 - 4 * quad_a * quad_c
    
    if discriminant < 0:
        print("No intersection found")
        break
    
    sqrt_disc = np.sqrt(discriminant)
    s1 = (-quad_b - sqrt_disc) / (2 * quad_a)
    s2 = (-quad_b + sqrt_disc) / (2 * quad_a)
    positive_s = [s for s in [s1, s2] if s > 0]
    
    if not positive_s:
        print("No positive intersection")
        break
    
    s = min(positive_s)
    P_next = P_i + s * V_i
    
    # Update variables
    PC_next = C_next - P_next
    V_next = rotate_vector(PC_next, alpha)
    P_i = P_next
    V_i = V_next
    tool_path.append(P_i)

# Plotting
tool_path = np.array(tool_path)
t_values = np.arange(t_min, t_max, delta_t)
contour_points = np.array([c(t) for t in t_values])

print(tool_path)

# plt.plot(contour_points[:, 0], contour_points[:, 1], 'b-', label='Contour')
# plt.plot(tool_path[:, 0], tool_path[:, 1], 'r-', label='Tool Path')
# plt.legend()
# plt.xlabel('X (mm)')
# plt.ylabel('Y (mm)')
# plt.title('Tool Path with Constant Engagement')
# plt.axis('equal')
# plt.grid(True)
# plt.show()