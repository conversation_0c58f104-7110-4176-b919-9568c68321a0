"""
demo_spiral.py
==============
Spiral pocket – pixel simulation of MRR & engagement.

Requires:
    sim_pixel_milling.py   (from the previous answer, same folder)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sim_pixel_milling import simulate                         # ← our core engine

# ----------------------------------------------------------------------
# 0.  Process & tool data ----------------------------------------------
D  = 10.0       # tool Ø  [mm]
p  = 32         # pixels per Ø
R  = D/2
ap = 5.0        # axial depth [mm]
fz = 0.05       # feed per tooth [mm]
z  = 4          # teeth
n  = 4_000.     # rpm

# ----------------------------------------------------------------------
# 1.  Spiral generator --------------------------------------------------
def spiral_path(r_max: float, pitch: float, fz: float) -> np.ndarray:
    """
    Archimedean spiral  r = b·theta      (a = 0)
    radial pitch = 2π b   (distance between successive turns)

    Returns positions sampled every fz of arc‑length.
    """
    b = pitch / (2*np.pi)           # pitch = 2π b
    # crude upper bound for θ_max
    theta_max = r_max / b

    # adaptive stepping along θ to hit ds ≈ fz
    path = [(0.0, 0.0)]
    s_acc = 0.      # accumulated arc length
    theta = 0.0
    while True:
        # local radius and derivative
        r  = b * theta
        if r > r_max: break
        dr = b
        # local differential length ds/dθ
        ds_dtheta = np.hypot(r, dr)
        # choose dθ so that ds ≈ fz
        dtheta = fz / ds_dtheta
        theta += dtheta
        r = b * theta
        x = r * np.cos(theta)
        y = r * np.sin(theta)
        path.append((x, y))
    return np.asarray(path)

# Spiral parameters
r_max  = 50.0          # final radius of spiral centreline [mm]
pitch  = R             # step‑over = tool radius

path = spiral_path(r_max, pitch, fz)

print("Spiral path length :", path[10:30])

# ----------------------------------------------------------------------
# 2.  Work‑piece bounding box  (square around the spiral) --------------
margin = D
bbox = (-r_max-margin, r_max+margin,
        -r_max-margin, r_max+margin)

# ----------------------------------------------------------------------
# 3.  Simulation --------------------------------------------------------
# df = simulate(D, p, bbox, path, ap, fz, z, n)

# print("Samples :", len(df))
# print("Peak MRR :", df.MRR.max(), "mm³/min")
# print("Peak θ   :", df.theta.max(), "deg")
# print("\nFirst five rows:\n", df.head())

# ----------------------------------------------------------------------
# 4.  Quick plot --------------------------------------------------------
# fig, ax = plt.subplots(3, 1, figsize=(6,6), sharex=True)
# s_coord = np.arange(len(df))*fz
# ax[0].plot(s_coord, df.MRR)
# ax[0].set_ylabel("MRR [mm³/min]")
# ax[1].plot(s_coord, df.a_e)
# ax[1].set_ylabel(r"$a_e$ [mm]")
# ax[2].plot(s_coord, df.theta)
# ax[2].set_ylabel(r"$\theta$ [°]")
# ax[2].set_xlabel("Spiral arc‑length s  [mm]")
# fig.suptitle("Spiral pocket – step‑over = R")
# fig.tight_layout()
# fig.savefig("spiral_results.png", dpi=150)

# # Store full trace
# df.to_csv("spiral_results.csv", index=False)

# Created/Modified files during execution:
print("spiral_results.csv")
print("spiral_results.png")