import numpy as np
import shapely
import shapely.ops
import shapely.vectorized
import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time
from dataclasses import dataclass
from typing import Tuple, Optional, Dict, List, Union
from shapely.ops import nearest_points
from scipy.spatial.distance import cdist
import optuna


@dataclass
class EllipseOptimizationResult:
    """Container for ellipse optimization results, replacing the data_box hack."""
    minor: float
    new_anchor: np.ndarray
    new_rotation: float
    ellipse_center: np.ndarray
    distances: Optional[List[float]] = None


@dataclass
class EllipseParameters:
    """Container for ellipse parameters with proper typing."""
    major: float
    minor: float
    center: np.ndarray
    anchor: np.ndarray
    rotation: float
    length: Optional[float] = None


def _normalize_position(position: float) -> float:
    """Normalize position to handle negative values consistently."""
    return position if position > 0 else 1 + position


def _calculate_ellipse_perimeter(major_axis: float, minor_axis: float) -> float:
    """Calculate exact ellipse perimeter using numerical integration."""
    eccentricity = np.sqrt(1 - (minor_axis**2 / major_axis**2))
    return 4 * major_axis * scipy.special.ellipe(eccentricity)


def _create_maximum_boundary_ellipse(ellipse_data: Dict, max_distance: float) -> shapely.geometry.Polygon:
    """Create a maximum boundary ellipse for constraint checking."""
    geometry, _ = create_ellipse(
        anchor_point=ellipse_data['center'],
        semi_major_axis=ellipse_data['major'] + max_distance,
        semi_minor_axis=ellipse_data['minor'] + max_distance,
        rotation=ellipse_data['rotation']
    )
    polygon = shapely.geometry.Polygon(geometry)
    shapely.prepare(polygon)
    return polygon


def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):    
    
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def find_farthest_outside_point(inner_ellipse: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.
    """

    x = inner_ellipse[:, 0]
    y = inner_ellipse[:, 1]

    # Filter points outside ellipse B
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b

    outside_points_geom = shapely.points(inner_ellipse[outside_mask])

    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)

    # Check if there are any outside points
    if len(distances) == 0:
        return 0.0

    # Find maximum
    return np.max(distances)


def _find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, start_angle_rad=0.0, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation']
    outer_center = outer_ellipse_data['center']

    t_arc = _find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)

    # 2. Apply the angular offset to get the effective parameter
    t_effective = t_arc + start_angle_rad

    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_effective), outer_semi_minor_axis * np.sin(t_effective)])
    point_vector = rotate_vector(point_vector, outer_rotation)
    point_vector += outer_center

    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_effective), outer_semi_major_axis * np.sin(t_effective)])
    normal_vector = rotate_vector(normal_vector, outer_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])

    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])

    return point_vector, normal_angle


def get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, local_pos):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.

    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries

    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse

    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
    new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
        outer_ellipse_data = outer_ellipse_data,
        fraction=local_pos
        )

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=new_anchor,
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=new_rotation,
        anchor_on_perimeter=True
        )

    # create_line_object(ellipse_geometry, "ellipse_new_anchor", color=(0, 1, 0, 1))

    # Calculate distances to sides
    ellipse = shapely.geometry.LineString(ellipse_geometry)
    # create_line_object(ellipse_geometry, "ellipse_new_anchor", color=(0, 1, 0, 1))
    # create_line_object(sides_data['poly'].coords, "sides_data['poly']", color=(1, 0, 0, 1))
    # poly = shapely.geometry.Polygon(sides_data['poly'].coords)
    poly = shapely.geometry.Polygon(shapely.get_coordinates(sides_data['poly']))
    shapely.prepare(poly)

    distances = []
    for side_line in sides_data['lines']:
        if ellipse.intersects(side_line):
            ## ellipse part as a result of the intersection
            dist = find_farthest_outside_point(ellipse_geometry, poly)
            distances.append(-dist) # negative, because of intersection
        else:
            distances.append(side_line.distance(ellipse))

    return distances, new_anchor, new_rotation, ellipse_center



def find_ellipse_position(inner_ellipse_data, outer_ellipse_data, sides_data, front_fractions, tolerance=1e-4):
    """
    Finds the optimal scale factor where the inner and outer distances are equal
    using the efficient Brent's method.
    """
    def objective_function(position):
        # Calculate the distances for the given position
        position = _normalize_position(position)
        distances, _, _, _ = get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, position)
        return distances[0] - distances[1]

    return scipy.optimize.brentq(objective_function, a=front_fractions[0], b=front_fractions[1]-1, xtol=tolerance)


def find_minor(outer_ellipse_data, sides_data, front_fractions, minimum_minor, maximum_minor,
               outer_ellipse, test_inner_data, maximum_right_dist, tolerance=1e-4) -> Tuple[float, EllipseOptimizationResult]:
    """
    Find the optimal minor axis length for an ellipse using optimization.

    Returns:
        Tuple containing the optimal minor axis length and optimization results
    """
    # Store optimization results to return
    optimization_result = EllipseOptimizationResult(
        minor=0.0, new_anchor=np.array([0, 0]), new_rotation=0.0, ellipse_center=np.array([0, 0])
    )

    def objective_function(minor: float) -> float:
        """Objective function for minor axis optimization."""
        test_inner_data['minor'] = minor

        position = find_ellipse_position(
            test_inner_data, outer_ellipse_data, sides_data, front_fractions, tolerance=1e-3
        )
        position = _normalize_position(position)

        new_distances, new_anchor, new_rotation, ellipse_center = get_ellipse_distances(
            test_inner_data, outer_ellipse_data, sides_data, position
        )

        ellipse_geometry, _ = create_ellipse(
            anchor_point=ellipse_center,
            semi_major_axis=test_inner_data['major'],
            semi_minor_axis=test_inner_data['minor'],
            rotation=new_rotation
        )

        minor_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)

        # Apply distance corrections based on constraints
        if minor_distance < maximum_right_dist:
            minor_distance = -minor_distance
        elif minor_distance == 0.0:
            minor_distance = -1e100

        # Store results for return
        optimization_result.distances = new_distances
        optimization_result.new_anchor = new_anchor
        optimization_result.new_rotation = new_rotation
        optimization_result.ellipse_center = ellipse_center
        optimization_result.minor = minor

        return minor_distance

    optimal_minor = scipy.optimize.brentq(
        objective_function, a=minimum_minor, b=maximum_minor, xtol=tolerance
    )

    return optimal_minor, optimization_result


def intersection_span_along_line3(outer_data, inner_data, sides_data, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.15 # in mm
        poly = sides_data['poly']

        outer_ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_data['center'],
            semi_major_axis=outer_data['major'],
            semi_minor_axis=outer_data['minor'],
            rotation=outer_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)
        inter_outer = outer_linear_ring.intersection(poly)

        if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')           
            return None
        

        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter_outer.geoms]
        distances.append(
            inter_outer.geoms[np.argmin(outer_projected_distances)].distance(inter_outer.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_data['center'],
            semi_major_axis=inner_data['major'] + inner_correction,
            semi_minor_axis=inner_data['minor'] + inner_correction,
            rotation=inner_data['rotation']
            )

        inner_ellipse = shapely.geometry.LinearRing(inner_ellipse_geometry)

        # left half of the ellipse, ccw
        # anchor -----ccw---->end
        left_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[:36])        
        left_point = shapely.ops.nearest_points(left_inner_linear_ring, sides_data['lines'][0])[1]
        
        # reversed right half of the ellipse, cw
        # anchor -----cw---->end
        right_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[35:][::-1])
        right_point = shapely.ops.nearest_points(right_inner_linear_ring, sides_data['lines'][1])[1]

        distances.append(left_point.distance(right_point))

        front_fractions = [
            inner_ellipse.project(left_point, normalized=True),
            inner_ellipse.project(right_point, normalized=True)
            ]
        '''
        if idx==12:            
            create_line_object(sides_data['lines'][0].coords, "side_left", color=(1, 0, 0, 1))
            create_line_object(sides_data['lines'][1].coords, "side_right", color=(1, 0, 0, 1))
            create_line_object(left_inner_linear_ring.coords, "left_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(right_inner_linear_ring.coords, "right_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(poly.coords, "poly", color=(1, 0, 0, 1))

            create_line_object(left_point.coords, "left_point", color=(0, 1, 0, 1))
            create_line_object(right_point.coords, "right_point", color=(0, 1, 0, 1))
            for geom in inter_outer.geoms:
                create_line_object(geom.coords, "outer_inter", color=(0, 1, 0, 1))
        '''  
            

        return distances, front_fractions


def intersection_span_along_line2(outer_data, inner_data, poly, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.1 # in mm

        outer_ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_data['center'],
            semi_major_axis=outer_data['major'],
            semi_minor_axis=outer_data['minor'],
            rotation=outer_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)
        inter = outer_linear_ring.intersection(poly)

        if inter.is_empty or shapely.get_num_geometries(inter) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
            return None

        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter.geoms]
        distances.append(
            inter.geoms[np.argmin(outer_projected_distances)].distance(inter.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_data['center'],
            semi_major_axis=inner_data['major'] - inner_correction,
            semi_minor_axis=inner_data['minor'] - inner_correction,
            rotation=inner_data['rotation']
            )

        inner_points = shapely.points(inner_ellipse_geometry)
        inner_distances = shapely.distance(inner_points, poly)
        max_distance = 0.3
        mask_inner = inner_distances < max_distance
        inner_distances = inner_distances[mask_inner]
        print(f'inner_distances: {inner_distances}')

        return

        inner_projected_distances = [inner_linear_ring.project(d, normalized=True) for d in inter.geoms]
        distances.append(
            inter.geoms[np.argmin(inner_projected_distances)].distance(inter.geoms[np.argmax(inner_projected_distances)])
        )


        for i, ellipse_data in enumerate((outer_data, inner_data)):
            inner_scale_correction  = 1 - (0.01 * i)
            ellipse_geometry, _ = create_ellipse(
                anchor_point=ellipse_data['center'],
                semi_major_axis=ellipse_data['major'] * inner_scale_correction,
                semi_minor_axis=ellipse_data['minor'] * inner_scale_correction,
                rotation=ellipse_data['rotation']
                )
            outer_linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
            inter = outer_linear_ring.intersection(poly)

            if inter.is_empty or shapely.get_num_geometries(inter) < 4:
                print('intersection is empty or has less than 4 components (linear_ring is to small) -> intersection_span_along_line()')
                return None

            create_line_object(outer_linear_ring.coords, f"linear_ring_{i}", color=(1, 0, 0, 1))
            for point in inter.geoms:
                create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))

            outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter.geoms]
            distances.append(
                inter.geoms[np.argmin(outer_projected_distances)].distance(inter.geoms[np.argmax(outer_projected_distances)])
                )

            if i == 0: # outer ellipse - front fractions calculus
                front_fractions = [min(outer_projected_distances), max(outer_projected_distances)]

        # print(f'projected_distances: {projected_distances}')
        # if idx==13:
        #     for point in inter.geoms:
        #         create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))
        #     create_line_object(linear_ring.coords, f"linear_ring_{idx}", color=(1, 0, 0, 1))

        return distances, front_fractions



def intersection_span_along_line(outer_data, inner_data, poly, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None

        for i, ellipse_data in enumerate((outer_data, inner_data)):
            inner_scale_correction  = 0.5 * i ## in mm
            ellipse_geometry, _ = create_ellipse(
                anchor_point=ellipse_data['center'],
                semi_major_axis=ellipse_data['major'] + inner_scale_correction,
                semi_minor_axis=ellipse_data['minor'] + inner_scale_correction,
                rotation=ellipse_data['rotation']
                )
            linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
            inter = linear_ring.intersection(poly)

            if inter.is_empty or shapely.get_num_geometries(inter) < 4:
                print(f'intersection is empty or has less than 4 components ({shapely.get_num_geometries(inter)}) -> intersection_span_along_line()')
                print(f'inter: {inter}')
                return None

            # create_line_object(linear_ring.coords, f"linear_ring_{i}", color=(1, 0, 0, 1))
            # for point in inter.geoms:
            #     create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))

            projected_distances = [linear_ring.project(d, normalized=True) for d in inter.geoms]
            distances.append(
                inter.geoms[np.argmin(projected_distances)].distance(inter.geoms[np.argmax(projected_distances)])
                )

            if i == 0: # outer ellipse - front fractions calculus
                front_fractions = [min(projected_distances), max(projected_distances)]

            # print(f'projected_distances: {projected_distances}')
            if idx==0:
                if i in [0,1] :
                    for point in inter.geoms:
                        create_line_object(point.coords, f"inter_{idx}", color=(0, 1, 0, 1))
                    create_line_object(linear_ring.coords, f"linear_ring_{idx}", color=(1, 0, 0, 1))

        return distances, front_fractions

# def get_front_points(ellipse_data, poly):
#         """
#         Returns the front points of an ellipse that are on the boundary of a polygon.
#         """
#         points = []
#         ellipse_geometry, _ = create_ellipse(
#             anchor_point=ellipse_data['center'],
#             semi_major_axis=ellipse_data['major'],
#             semi_minor_axis=ellipse_data['minor'],
#             rotation=ellipse_data['rotation']
#             )
#         linear_ring = shapely.geometry.LinearRing(ellipse_geometry)
#         inter = linear_ring.intersection(poly)
#         if inter.is_empty:
#             print('intersection is empty (get_front_points())')
#             return None
#         projected_distances = [linear_ring.project(d, normalized=True) for d in inter.geoms]
#         points.append(inter.geoms[np.argmin(projected_distances)])
#         points.append(inter.geoms[np.argmax(projected_distances)])

#         return points


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None


def get_ellipse_end_point(polygon_boundary, medial_axis_edge):
        center = shapely.get_coordinates(medial_axis_edge)[-1]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_semi_axis_length,
            semi_minor_axis=outer_semi_axis_length,
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                anchor = shapely.get_coordinates(
                    inter.geoms[np.argmax(distances)])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]
        else:
            print('ellipse does not intersect medial axis')
            return None

        # rotate 180 degrees (pi radians) around center
        return 2 * center - anchor


def arc_polyline(start, end, center, resolution=71):
    """
    Return a 2-D polyline approximating the circular arc from `start` to `end`
    around `center`.  Always counter-clockwise along the shorter arc.
    """
    start, end, center = map(np.asarray, (start, end, center))

    v_start = start - center
    v_end   = end   - center
    #print lenght of v_start and v_end
    # print(f'v_start: {np.linalg.norm(v_start)}')
    # print(f'v_end: {np.linalg.norm(v_end)}')

    r = np.hypot(*v_start)
    if abs(np.hypot(*v_end) - r) > 1e-1:
        print(f'start end distance: {abs(np.hypot(*v_end) - r)}')
        raise ValueError("start and end must be equidistant from center")

    angle = np.arctan2(v_end[1], v_end[0]) - np.arctan2(v_start[1], v_start[0])
    num_pts = int(abs(angle) / (2 * np.pi) * resolution) + 1
    if num_pts < 3:
        num_pts = 3

    if angle < -np.pi:
        angle += 2 * np.pi
    elif angle > np.pi:
        angle -= 2 * np.pi

    t = np.linspace(0, angle, num_pts)
    cos_t, sin_t = np.cos(t), np.sin(t)

    x = center[0] + v_start[0] * cos_t - v_start[1] * sin_t
    y = center[1] + v_start[0] * sin_t + v_start[1] * cos_t

    points = np.column_stack((x, y))
    # Replace last point with exact end coordinates
    points[-1] = end

    return points


def get_ellipse_data(polygon_boundary, medial_axis_edge, cutter_data, medial_axis_start=True):
        if medial_axis_start:
            idx = 0
        else:
            idx = -1
        center = shapely.get_coordinates(medial_axis_edge)[idx]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)
        inner_axis_length = outer_semi_axis_length - cutter_data['radius']

        outer_ellipse_data = {
            'major': outer_semi_axis_length,
            'minor': outer_semi_axis_length,
            'center': center,
            'perimeter': 2 * np.pi * outer_semi_axis_length
        }

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_ellipse_data['major'],
            semi_minor_axis=outer_ellipse_data['minor'],
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                if medial_axis_start:
                    idx = np.argmin(distances)
                else:
                    idx = np.argmax(distances)
                anchor = shapely.get_coordinates(inter.geoms[idx])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]

            outer_ellipse_data['anchor'] = anchor
        else:
            print('ellipse does not intersect medial axis')
            return None, None

        ### Initial rotation of the ellipse in radians ###
        rotation_vector = anchor - center
        outer_ellipse_data['rotation'] = np.arctan2(rotation_vector[1], rotation_vector[0])
        inner_ellipse_data = {**outer_ellipse_data, 'major': inner_axis_length, 'minor': inner_axis_length, 'perimeter': None}

        return inner_ellipse_data, outer_ellipse_data


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front2(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step

    distance_initial = np.arange(
        0.0, sampler.length, path_step)

    offsets = sampler.sample(distance_initial)
    radii = boundary_distance(polygon=polygon, points=offsets)

    return offsets, radii


def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    offsets = [offset[0]]
    radii = [radius[0]]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
            offsets.append(point)
            radii.append(r)

    return np.array(offsets), np.array(radii)


def create_circle_points(center, radius, start_angle, resolution=71, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring    
    p1 = shapely.get_coordinates(reference_ls.interpolate(0.45, normalized=True)).reshape((-1))
   
    # Get the end point of the reference linestring    
    p2 = shapely.get_coordinates(reference_ls.interpolate(0.55, normalized=True)).reshape((-1))

    # Use the first point of the target linestring as its representative point
    q = shapely.get_coordinates(target_ls.interpolate(0.5, normalized=True)).reshape((-1))

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear
    

def objective_function(major_minor_position, inner_ellipse_data, outer_ellipse_data, sides_data, outer_ellipse, maximum_right_dist):           
    def soft_abs(x, epsilon=1e-6):
        return np.sqrt(x**2 + epsilon)
    major, minor, position = major_minor_position
    inner_ellipse_data.update({'major': major, 'minor': minor})
    distances, _, new_rotation, new_center = get_ellipse_distances(inner_ellipse_data, outer_ellipse_data, sides_data, position)
    
    ellipse_geometry, _ = create_ellipse(
            anchor_point=new_center,
            semi_major_axis=inner_ellipse_data['major'],
            semi_minor_axis=inner_ellipse_data['minor'],
            rotation=new_rotation                    
            )
    
    minor_distance = find_farthest_outside_point(ellipse_geometry, outer_ellipse)            
    new_minor = abs(maximum_right_dist - minor_distance)
    new_major = soft_abs(np.sum(np.abs(distances)))
    
    return new_major + new_minor

def solve_ellipse_params(inner_ellipse_data, outer_ellipse_data, sides_data, minimum_major, maximum_major, outer_ellipse, min_radius_of_curvature, maximum_right_dist, tolerance=1e-4):        
        minimum_minor = np.sqrt(min_radius_of_curvature * minimum_major)        
        # initial_guess = np.array([minimum_major, minimum_minor, 0.98])
        bounds = [
            (minimum_major, maximum_major), # Major axis
            (minimum_minor, minimum_major), # Minor axis
            (0.0, 0.1) # possible correction of the position
        ]
        extra_args = (inner_ellipse_data, outer_ellipse_data, sides_data, outer_ellipse, maximum_right_dist)
        time1 = time.time()
        result = scipy.optimize.differential_evolution(
            objective_function,
            bounds,
            args=extra_args,
            # init=initial_guess,  # Good for bounded spaces; or use your initial as a custom init
            seed=42,  # For reproducibility
            popsize=16*4,  # Higher for better exploration in 3D (try 15-30)
            # tol=1e-2,  # Tight tolerance to aim for res ~0
            # maxiter=10,
            # mutation=(0.5, 1.0), 
            # recombination=0.7,
            # strategy='rand1bin',  # Robust for non-smooth functions
            strategy='best1bin',  # Robust for non-smooth functions
            # workers= -1
        )
        time2 = time.time()        
        print(f"Total objective function calls: {result.nfev}")
        print(f"Termination message: {result.message}")
        print(f'params: {result.x}')
        print(f'time: {time2-time1}')

        test_data = {
            'major': result.x[0],
            'minor': result.x[1]
        }
        test_inner_data = {**inner_ellipse_data, **test_data}
        position = result.x[2]

        _, new_anchor, new_rotation, new_center  = get_ellipse_distances(test_inner_data, outer_ellipse_data, sides_data, position)
        ellipse_geometry, _ = create_ellipse(
                anchor_point=new_anchor,
                semi_major_axis=result.x[0],
                semi_minor_axis=result.x[1],
                rotation=new_rotation,
                anchor_on_perimeter=True
                )
        
        new_inner_ellipse_data = {
            'major': result.x[0],
            'minor': result.x[1],
            'center': new_center,
            'anchor': new_anchor,
            'rotation': new_rotation
        }
        # create_line_object(ellipse_geometry, "ellipse_minor", color=(0, 1, 0, 1))
        return new_inner_ellipse_data


def fit_ellipse(inner_ellipse_data, outer_ellipse_data, sides_data, cutter_data, min_radius_of_curvature=15.0, idx=0):
    """
    Fit an ellipse within given constraints using optimization.

    This function has been refactored for better readability and maintainability
    while preserving the core algorithmic logic.

    Args:
        outer_ellipse_data: Dictionary containing outer ellipse parameters
        sides_data: Dictionary containing side geometry data
        cutter_data: Dictionary containing cutter parameters
        min_radius_of_curvature: Minimum radius of curvature constraint

    Returns:
        Dictionary containing the fitted outer ellipse data
    """
    # Configuration constants
    MAXIMUM_RIGHT_DIST = 0.3
    # BOOLEAN_CORRECTION_SCALE_FACTOR = 1.00  # TODO: Quick fix for boolean operations
    TOLERANCE = 0.01

    # Create maximum boundary ellipse for constraint checking
    outer_maximum_ellipse = _create_maximum_boundary_ellipse(outer_ellipse_data, MAXIMUM_RIGHT_DIST)
    outer_ellipse = _create_maximum_boundary_ellipse(outer_ellipse_data, 0)
    

    # Calculate optimization bounds from intersection analysis
    # distances, front_fractions = intersection_span_along_line(
    #     outer_ellipse_data, inner_ellipse_data, sides_data['poly'], idx
    # )
    distances, front_fractions = intersection_span_along_line3(
        outer_ellipse_data, inner_ellipse_data, sides_data, idx
    )

    ## 0- outer, 1- inner
    print(f'distances o:{ distances[0]/2}, i:{ distances[1]/2} ')
    if distances[0] >= distances[1]:
        print('bigger than previous')
        minimum_major = distances[1]
        maximum_major = distances[0]
    else:
        print('smaller than previous')
        minimum_major = distances[0]
        maximum_major = distances[1]

    min_max_ratio = minimum_major / maximum_major

    if min_max_ratio > 0.8: ## TO-DO: make it dynamic / try optimization
        min_max_correction = 0.15 * min_max_ratio
    else:
        min_max_correction = 0.0

    print(f'min_max_ratio: {min_max_ratio}, min_max_correction: {min_max_correction}')

    minimum_major /= 2 + min_max_correction
    maximum_major /= 2 - min_max_correction

    # Perform major axis optimization
    new_ellipse_data = solve_ellipse_params(
        inner_ellipse_data,
        outer_ellipse_data,
        sides_data,
        minimum_major,
        maximum_major,
        outer_ellipse,
        min_radius_of_curvature,
        MAXIMUM_RIGHT_DIST,
        tolerance=TOLERANCE
    )

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=new_ellipse_data['anchor'],
        semi_major_axis=new_ellipse_data['major'],
        semi_minor_axis=new_ellipse_data['minor'],
        rotation=new_ellipse_data['rotation'],
        anchor_on_perimeter=True
    )
    create_line_object(ellipse_geometry, "ellipse_outer", color=(1, 1, 0, 1))

    # Update inner ellipse data with optimization results
    inner_ellipse_data.update({
        'major': new_ellipse_data['major'],
        'minor': new_ellipse_data['minor'],
        'center': ellipse_center,
        'anchor': new_ellipse_data['anchor'],
        'rotation': new_ellipse_data['rotation']
    })

    # Calculate outer ellipse parameters
    outer_major = inner_ellipse_data['major'] + cutter_data['radius']
    outer_minor = inner_ellipse_data['minor'] + cutter_data['radius']

    # Calculate ellipse perimeter using exact formula
    perimeter = _calculate_ellipse_perimeter(outer_major, outer_minor)

    # Calculate new anchor position for outer ellipse
    anchor_offset = rotate_vector(
        np.array([cutter_data['radius'], 0]),
        inner_ellipse_data['rotation']
    )

    # Construct final outer ellipse data
    outer_ellipse_result = {
        'major': outer_major,
        'minor': outer_minor,
        'center': ellipse_center,
        'anchor': inner_ellipse_data['anchor'] + anchor_offset,
        'rotation': inner_ellipse_data['rotation'],
        'perimeter': perimeter
    }

    return inner_ellipse_data, outer_ellipse_result


def get_split_coords(ellipse_data):
    cx, cy = ellipse_data['center']
    angle_rad = ellipse_data['rotation']+np.pi
    x = cx + ellipse_data['major'] * np.cos(angle_rad)
    y = cy + ellipse_data['major'] * np.sin(angle_rad)
    return np.array([x, y])

def resample_linestring(line: shapely.geometry.LineString, segment_length: float) -> shapely.geometry.LineString:
    """
    Resamples a Shapely LineString to have segments of a constant length.

    The last segment will be shorter than segment_length if the total
    length is not a multiple of segment_length.

    Args:
        line (LineString): The original LineString to resample.
        segment_length (float): The desired length of each segment.

    Returns:
        LineString: The new, resampled LineString.
    """
    if segment_length <= 0:
        raise ValueError("Segment length must be positive.")

    # Get the total length of the original line
    total_length = line.length

    # Generate distances along the line at which to place new vertices
    # np.arange creates a sequence from 0 to total_length with a step of segment_length
    distances = np.arange(0, total_length, segment_length)

    # Interpolate points at these distances
    # The list comprehension is a concise way to do this
    new_points = [line.interpolate(dist) for dist in distances]

    # Always include the very last point of the original line to ensure
    # the resampled line has the same extent.
    # We can access the last coordinate directly.
    last_point = shapely.geometry.Point(line.coords[-1])
    if not new_points[-1].equals(last_point):
         new_points.append(last_point)


    # Create a new LineString from the list of points
    return shapely.geometry.LineString(new_points)


def main():
    cutter_dim = 10 # in mm

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_dim / 2
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes).normalize() ## reorder rings if they are not in the expected orientation (exterior ccw, interiors cw)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])
    medial_middle = medial_axis.interpolate(0.5, normalized=True)
    boundary = polygon.boundary
    buffered_boundary = polygon_buffered.boundary


    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]

    tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]

    # for geom in active_geoms:
    #     create_line_object(geom.coords, 'active_geom', color=(1, 0, 0, 1))


    ## Sides per path
    trochos_offsets, trochos_radii = advancing_front(geometry[1], polygon_buffered, 1) # 5mm between trochoids
    # trochos_radii[0] += 0.5 #quick fix for intersection_span_along_line(), we need bigger first circle for proper intersection check.
    # print(trochos_offsets, trochos_radii)
    # return

    ##metrics of the medial axis ends
    ma_start_coords = shapely.get_coordinates(medial_axis)[0]
    ma_start_point = shapely.geometry.Point(medial_axis.coords[0])
    ma_start_radius = ma_start_point.distance(buffered_boundary)

    ma_end_coords = shapely.get_coordinates(medial_axis)[-1]
    ma_end_point = shapely.geometry.Point(medial_axis.coords[-1])
    ma_end_radius = ma_end_point.distance(buffered_boundary)

    points_array = shapely.points(trochos_offsets)
    buffered_points_array = shapely.buffer(points_array, trochos_radii, quad_segs=18) #about 72 points per circle    
    buffered_points_array[0] = shapely.buffer(points_array[0], trochos_radii[0], quad_segs=36)
    buffered_points_array[-1] = shapely.buffer(points_array[-1], trochos_radii[-1], quad_segs=36)
    
    trochos_poly = shapely.unary_union(buffered_points_array).simplify(0.05)
    trochos_poly_coords = shapely.get_coordinates(trochos_poly.exterior)
    
    ##ellipse data    
    start_inner_ellipse_data, start_outer_ellipse_data = get_ellipse_data(boundary, medial_axis, cutter_data, medial_axis_start=True)
    end_inner_ellipse_data, _ = get_ellipse_data(boundary, medial_axis, cutter_data, medial_axis_start=False)

    start_split_coords = get_split_coords(start_inner_ellipse_data)
    end_split_coords = get_split_coords(end_inner_ellipse_data)
    distances =cdist([start_split_coords, end_split_coords], trochos_poly_coords)  # Shape: (len(P), len(points))
    split0, split1 = np.argmin(distances, axis=1)

    # Ensure proper ordering
    start, end = min(split0, split1), max(split0, split1)

    # Create two continuous segments
    side0 = shapely.geometry.LineString(trochos_poly_coords[start:end+1])
    side1_coords = np.concatenate((trochos_poly_coords[end:], trochos_poly_coords[:start+1]))
    side1 = shapely.geometry.LineString(side1_coords)
    sides = [side0, side1]
    # sides = [resample_linestring(side, 0.1) for side in sides]
   
    ### Determine sides, the first side is the one that is on the "left" of the medial axis
    if get_side_of_medial_axis(sides[0], medial_axis) == -1:
        sides.reverse()
    for side in sides:
        shapely.prepare(side)

    create_line_object(side0.coords, "side_left", color=(0, 1, 0, 1))
    create_line_object(side1.coords, "side_right", color=(1, 0, 0, 1))

    trochos_ring = shapely.geometry.LinearRing(trochos_poly.exterior.coords)
    shapely.prepare(trochos_ring) 

    sides_data = {
        'poly': trochos_ring,    
        'lines': sides
    }

    time1 = time.time()   

    min_radius_of_curvature = 10.0
    for i in range(15):
        print(f'iteration: {i}')
        start_inner_ellipse_data, start_outer_ellipse_data = fit_ellipse(
            start_inner_ellipse_data,
            start_outer_ellipse_data,
            sides_data,
            cutter_data,
            min_radius_of_curvature,            
            i
            )

    time2 = time.time()
    print(f'Time: {time2-time1}')

if __name__ == "__main__":
    main()
