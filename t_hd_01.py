import shapely
from shapely.geometry import LineString, Point
from shapely.ops import substring

def between_segments_by_substring(l1: LineString, l2: LineString):
    inter = l1.intersection(l2)
    # collect the two intersection points
    if inter.geom_type == 'MultiPoint':
        pts = shapely.get_parts(inter)
    else:
        print('No intersection')
        return None, None
    
    # For l1
    pA, pB = sorted(pts, key=lambda p: l1.project(p))
    between1 = substring(l1, l1.project(pA), l1.project(pB))

    # For l2 (order by l2)
    qA, qB = sorted(pts, key=lambda p: l2.project(p))
    between2 = substring(l2, l2.project(qA), l2.project(qB))

    return between1, between2

# ------------------------------------------------------------------
# Demo
# ------------------------------------------------------------------
if __name__ == '__main__':
    a = LineString([(0, 0), (10, 0)])
    b = LineString([(2, -2), (2, 2), (8, 2), (8, -2)])

    seg_a, seg_b = between_segments_by_substring(a, b)
    print('Segment on a:', seg_a)
    print('Segment on b:', seg_b)