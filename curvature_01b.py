import numpy as np
import matplotlib.pyplot as plt

def compute_bezier_curve_and_curvature(P0, P1, P2, P3, num_points=100):
    """
    Compute points and curvature of a cubic Bézier curve defined by four control points.
    
    Parameters:
    - P0, P1, P2, P3: NumPy arrays of shape (2,) representing the control points [x, y]
    - num_points: Number of points to sample along the curve
    
    Returns:
    - t: Array of parameter values
    - B: Array of curve points, shape (num_points, 2)
    - kappa: Array of curvature values
    """
    # Generate parameter t values
    t = np.linspace(0, 1, num_points)
    
    # Compute curve points B(t) = (1-t)^3 P0 + 3t(1-t)^2 P1 + 3t^2(1-t) P2 + t^3 P3
    t = t[:, None]  # Shape (num_points, 1) for broadcasting
    B = (1 - t)**3 * P0 + 3 * t * (1 - t)**2 * P1 + 3 * t**2 * (1 - t) * P2 + t**3 * P3  # Shape (num_points, 2)
    
    # Compute second derivative
    a = 6 * ((1 - t) * (P1 - P0) + t * (P2 - P1))  # a = B''(t), shape (num_points, 2)
    a_x, a_y = a[:, 0], a[:, 1]
    
    # Compute first derivative v(t) = 3[(1-t)^2(P1 - P0) + 2t(1-t)(P2 - P1) + t^2(P3 - P2)]
    v = 3 * ((1 - t)**2 * (P1 - P0) + 2 * t * (1 - t) * (P2 - P1) + t**2 * (P3 - P2))  # Shape (num_points, 2)
    v_x, v_y = v[:, 0], v[:, 1]
    
    # Compute curvature kappa(t) = |x'y'' - y'x''| / (x'^2 + y'^2)^(3/2)
    numerator = np.abs(v_x * a_y - v_y * a_x)
    denominator = (v_x**2 + v_y**2)**1.5
    # Avoid division by zero by setting kappa to NaN where denominator is too small
    kappa = np.where(denominator > 1e-10, numerator / denominator, np.nan)
    
    return t.flatten(), B, kappa

# Example usage
if __name__ == "__main__":
    # Define control points
    P0 = np.array([0.0, 0.0])
    P1 = np.array([1.0, 5.0])
    P2 = np.array([2.0, 0.0])
    P3 = np.array([3.0, 1.0])
    
    # Compute curve and curvature
    t, B, kappa = compute_bezier_curve_and_curvature(P0, P1, P2, P3, num_points=100)
    
    # Plot the Bézier curve
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(B[:, 0], B[:, 1], 'b-', label='Bézier Curve')
    plt.plot([P0[0], P1[0], P2[0], P3[0]], [P0[1], P1[1], P2[1], P3[1]], 'ro--', label='Control Points')
    plt.title('Cubic Bézier Curve')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')
    
    # Plot the curvature
    plt.subplot(1, 2, 2)
    plt.plot(t, kappa, 'g-', label='Curvature')
    plt.title('Curvature vs Parameter t')
    plt.xlabel('t')
    plt.ylabel('κ(t)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    # plt.savefig('bezier_curve_and_curvature.png')
    plt.show() #is replaced with plt.savefig() for compatibility
