import numpy as np
from scipy.spatial.distance import cdist
from typing import List, Tuple
import time

def merge_linestrings(
    lines: List[np.ndarray], 
    tolerance: float
) -> List[np.ndarray]:
    """
    Merges a list of linestrings end-to-end using a proximity tolerance.

    This function provides a robust alternative to shapely's line_merge,
    avoiding issues with floating-point precision by allowing a tolerance.
    The core distance calculations are vectorized for efficiency.

    Args:
        lines (List[np.ndarray]): A list of NumPy arrays, where each array
                                  represents a linestring with shape (N, 2).
        tolerance (float): The maximum distance between two endpoints to be
                           considered for merging.

    Returns:
        List[np.ndarray]: A list of merged linestrings as NumPy arrays.
                          If the input lines form multiple disjoint chains,
                          multiple merged lines will be returned.
    """
    if not lines:
        return []

    num_lines = len(lines)
    if num_lines == 1:
        return lines

    # --- 1. Pre-process inputs for vectorization ---
    
    # Extract all start and end points into numpy arrays
    starts = np.array([line[0] for line in lines])
    ends = np.array([line[-1] for line in lines])

    # For convenience, create a list of flipped versions of each line
    flipped_lines = [np.flip(line, axis=0) for line in lines]
    
    # Keep track of which lines have already been incorporated into a merged line
    visited_mask = np.zeros(num_lines, dtype=bool)
    
    merged_lines = []

    # --- 2. Iterate through each line to start a new chain if it's unvisited ---
    for i in range(num_lines):
        if visited_mask[i]:
            continue

        # Start a new chain with the current line
        # The chain stores tuples of (line_index, is_flipped)
        chain: List[Tuple[int, bool]] = [(i, False)]
        visited_mask[i] = True
        
        # Keep track of indices used in the *current* chain to avoid self-connections
        # during the tracing process for this single chain.
        chain_indices = {i}

        # --- 3. Trace forward from the end of the current chain ---
        while True:
            last_idx, last_flipped = chain[-1]
            
            # Get the coordinates of the endpoint we're trying to connect from
            current_endpoint = ends[last_idx] if not last_flipped else starts[last_idx]

            # VECTORIZED PART: Calculate distances to all other start/end points
            dist_to_starts = cdist([current_endpoint], starts)[0]
            dist_to_ends = cdist([current_endpoint], ends)[0]
            
            # Invalidate connections to already used lines by setting distance to infinity
            dist_to_starts[list(chain_indices)] = np.inf
            dist_to_ends[list(chain_indices)] = np.inf
            
            # Find the closest potential connection
            min_dist_to_start = np.min(dist_to_starts)
            min_dist_to_end = np.min(dist_to_ends)

            best_dist = min(min_dist_to_start, min_dist_to_end)

            if best_dist > tolerance:
                break # No more connections found in the forward direction

            if min_dist_to_start < min_dist_to_end:
                # Connect to the start of another line
                next_idx = np.argmin(dist_to_starts)
                chain.append((next_idx, False)) # Don't flip the next line
            else:
                # Connect to the end of another line
                next_idx = np.argmin(dist_to_ends)
                chain.append((next_idx, True)) # Flip the next line
            
            visited_mask[next_idx] = True
            chain_indices.add(next_idx)

        # --- 4. Trace backward from the start of the current chain ---
        while True:
            first_idx, first_flipped = chain[0]

            # Get the coordinates of the startpoint we're trying to connect from
            current_startpoint = starts[first_idx] if not first_flipped else ends[first_idx]

            # VECTORIZED PART: Calculate distances from all other ends
            dist_from_ends = cdist([current_startpoint], ends)[0]
            dist_from_starts = cdist([current_startpoint], starts)[0]
            
            dist_from_ends[list(chain_indices)] = np.inf
            dist_from_starts[list(chain_indices)] = np.inf
            
            min_dist_from_end = np.min(dist_from_ends)
            min_dist_from_start = np.min(dist_from_starts)

            best_dist = min(min_dist_from_end, min_dist_from_start)

            if best_dist > tolerance:
                break # No more connections found

            if min_dist_from_end < min_dist_from_start:
                # Another line's end connects to our start
                prev_idx = np.argmin(dist_from_ends)
                chain.insert(0, (prev_idx, False)) # Don't flip the previous line
            else:
                # Another line's start connects to our start
                prev_idx = np.argmin(dist_from_starts)
                chain.insert(0, (prev_idx, True)) # Flip the previous line
            
            visited_mask[prev_idx] = True
            chain_indices.add(prev_idx)

        # --- 5. Stitch the identified chain together ---
        coords_to_stitch = []
        for j, (line_idx, is_flipped) in enumerate(chain):
            segment = flipped_lines[line_idx] if is_flipped else lines[line_idx]
            # For all but the first segment, drop the first point to avoid duplication
            if j > 0:
                segment = segment[1:]
            coords_to_stitch.append(segment)
        
        merged_lines.append(np.vstack(coords_to_stitch))

    return merged_lines

def merge_linestrings_optimized(
    lines: List[np.ndarray], 
    tolerance: float
) -> List[np.ndarray]:
    """
    Optimized version of merge_linestrings with improved performance.
    
    Key optimizations:
    1. Avoid precomputing all flipped lines - flip only when needed
    2. Use boolean masks instead of set operations for better performance
    3. Precompute distance matrices for small datasets
    4. Early termination when no valid connections remain
    5. More efficient unvisited tracking
    """
    if not lines:
        return []

    num_lines = len(lines)
    if num_lines == 1:
        return lines

    # Extract all start and end points into numpy arrays
    starts = np.array([line[0] for line in lines])
    ends = np.array([line[-1] for line in lines])

    # Keep track of which lines have already been incorporated
    visited_mask = np.zeros(num_lines, dtype=bool)
    
    merged_lines = []

    # For small datasets, precompute distance matrices (more efficient for repeated lookups)
    if num_lines <= 100:  # Threshold can be adjusted based on memory constraints
        start_to_start = cdist(starts, starts)
        start_to_end = cdist(starts, ends)
        end_to_start = cdist(ends, starts)
        end_to_end = cdist(ends, ends)
        use_precomputed = True
    else:
        use_precomputed = False

    # Iterate through each line to start a new chain if it's unvisited
    for i in range(num_lines):
        if visited_mask[i]:
            continue

        # Start a new chain with the current line
        chain: List[Tuple[int, bool]] = [(i, False)]
        visited_mask[i] = True
        
        # Keep track of indices used in the current chain using boolean mask
        chain_mask = np.zeros(num_lines, dtype=bool)
        chain_mask[i] = True

        # Trace forward from the end of the current chain
        while True:
            last_idx, last_flipped = chain[-1]
            
            if use_precomputed:
                # Use precomputed distance matrices
                if not last_flipped:
                    dist_to_starts = end_to_start[last_idx].copy()
                    dist_to_ends = end_to_end[last_idx].copy()
                else:
                    dist_to_starts = start_to_start[last_idx].copy()
                    dist_to_ends = start_to_end[last_idx].copy()
            else:
                # Calculate distances on-the-fly for larger datasets
                current_endpoint = ends[last_idx] if not last_flipped else starts[last_idx]
                dist_to_starts = cdist([current_endpoint], starts)[0]
                dist_to_ends = cdist([current_endpoint], ends)[0]
            
            # Invalidate connections to already used lines
            dist_to_starts[chain_mask] = np.inf
            dist_to_ends[chain_mask] = np.inf
            
            # Find the closest potential connection
            min_start_idx = np.argmin(dist_to_starts)
            min_end_idx = np.argmin(dist_to_ends)
            min_dist_to_start = dist_to_starts[min_start_idx]
            min_dist_to_end = dist_to_ends[min_end_idx]

            best_dist = min(min_dist_to_start, min_dist_to_end)

            if best_dist > tolerance:
                break  # No more connections found in the forward direction

            if min_dist_to_start < min_dist_to_end:
                # Connect to the start of another line
                next_idx = min_start_idx
                chain.append((next_idx, False))  # Don't flip the next line
            else:
                # Connect to the end of another line
                next_idx = min_end_idx
                chain.append((next_idx, True))  # Flip the next line
            
            visited_mask[next_idx] = True
            chain_mask[next_idx] = True

        # Trace backward from the start of the current chain
        while True:
            first_idx, first_flipped = chain[0]

            if use_precomputed:
                # Use precomputed distance matrices
                if not first_flipped:
                    dist_from_ends = start_to_end[first_idx].copy()
                    dist_from_starts = start_to_start[first_idx].copy()
                else:
                    dist_from_ends = end_to_end[first_idx].copy()
                    dist_from_starts = end_to_start[first_idx].copy()
            else:
                # Calculate distances on-the-fly for larger datasets
                current_startpoint = starts[first_idx] if not first_flipped else ends[first_idx]
                dist_from_ends = cdist([current_startpoint], ends)[0]
                dist_from_starts = cdist([current_startpoint], starts)[0]
            
            dist_from_ends[chain_mask] = np.inf
            dist_from_starts[chain_mask] = np.inf
            
            min_end_idx = np.argmin(dist_from_ends)
            min_start_idx = np.argmin(dist_from_starts)
            min_dist_from_end = dist_from_ends[min_end_idx]
            min_dist_from_start = dist_from_starts[min_start_idx]

            best_dist = min(min_dist_from_end, min_dist_from_start)

            if best_dist > tolerance:
                break  # No more connections found

            if min_dist_from_end < min_dist_from_start:
                # Another line's end connects to our start
                prev_idx = min_end_idx
                chain.insert(0, (prev_idx, False))  # Don't flip the previous line
            else:
                # Another line's start connects to our start
                prev_idx = min_start_idx
                chain.insert(0, (prev_idx, True))  # Flip the previous line
            
            visited_mask[prev_idx] = True
            chain_mask[prev_idx] = True

        # Stitch the identified chain together
        coords_to_stitch = []
        for j, (line_idx, is_flipped) in enumerate(chain):
            # Flip only when needed during stitching (avoid precomputing all flipped lines)
            segment = np.flip(lines[line_idx], axis=0) if is_flipped else lines[line_idx]
            # For all but the first segment, drop the first point to avoid duplication
            if j > 0:
                segment = segment[1:]
            coords_to_stitch.append(segment)
        
        merged_lines.append(np.vstack(coords_to_stitch))

    return merged_lines

def merge_linestrings_numpy_optimized2(
    lines: List[np.ndarray], 
    tolerance: float
) -> List[np.ndarray]:
    """
    Merges a list of linestrings end-to-end using a proximity tolerance,
    optimized for performance.

    This version pre-computes a distance matrix for all endpoints to minimize
    costly calculations during the chain-building phase. It's significantly
    faster for larger numbers of linestrings.

    Args:
        lines (List[np.ndarray]): A list of NumPy arrays, where each array
                                  represents a linestring with shape (N, 2).
        tolerance (float): The maximum distance between two endpoints to be
                           considered for merging.

    Returns:
        List[np.ndarray]: A list of merged linestrings as NumPy arrays.
    """
    if not lines:
        return []

    num_lines = len(lines)
    if num_lines == 1:
        return lines

    # --- 1. Pre-computation and Graph Building (The Core Optimization) ---

    # Create one big array of all endpoints.
    # Shape: (2 * num_lines, 2).
    # Even indices (0, 2, 4, ...) are START points.
    # Odd indices (1, 3, 5, ...) are END points.
    endpoints = np.empty((num_lines * 2, 2), dtype=np.float64)
    for i, line in enumerate(lines):
        endpoints[i * 2] = line[0]
        endpoints[i * 2 + 1] = line[-1]

    # Pre-calculate a full distance matrix. This is the only expensive call.
    dist_matrix = cdist(endpoints, endpoints)

    # Invalidate connections:
    # 1. A point cannot connect to itself (diagonal).
    # 2. The start of a line cannot connect to the end of the SAME line.
    for i in range(num_lines):
        dist_matrix[i * 2, i * 2 + 1] = np.inf
        dist_matrix[i * 2 + 1, i * 2] = np.inf
    np.fill_diagonal(dist_matrix, np.inf)

    # 3. Discard any connections that are larger than the tolerance.
    dist_matrix[dist_matrix > tolerance] = np.inf
    
    # For each endpoint, find the index of its closest neighbor.
    # This creates our static "connection graph".
    # `best_neighbor_idx[i]` gives the endpoint index that `i` should connect to.
    best_neighbor_idx = np.argmin(dist_matrix, axis=1)
    
    # --- 2. Tracing the Chains ---
    
    merged_lines = []
    # Mask to track which *lines* (not endpoints) have been used.
    visited_lines = np.zeros(num_lines, dtype=bool)
    flipped_lines = [np.flip(line, axis=0) for line in lines]

    for i in range(num_lines):
        if visited_lines[i]:
            continue

        visited_lines[i] = True
        
        # A chain is a list of tuples: (line_index, is_flipped)
        chain = [(i, False)]

        # --- Trace forward ---
        current_endpoint_idx = i * 2 + 1  # Start with the END of the first line
        while True:
            neighbor_endpoint_idx = best_neighbor_idx[current_endpoint_idx]
            
            # Check if a valid connection exists
            if dist_matrix[current_endpoint_idx, neighbor_endpoint_idx] == np.inf:
                break

            neighbor_line_idx = neighbor_endpoint_idx // 2

            # If the neighbor is already used, stop (handles closed loops)
            if visited_lines[neighbor_line_idx]:
                break
                
            visited_lines[neighbor_line_idx] = True
            
            # If we connected to the START of the neighbor (even index), it's not flipped.
            # If we connected to the END of the neighbor (odd index), it IS flipped.
            is_flipped = (neighbor_endpoint_idx % 2 != 0)
            chain.append((neighbor_line_idx, is_flipped))
            
            # Update the current endpoint for the next iteration.
            # If we used the neighbor unflipped, our new end is its end.
            # If we flipped the neighbor, our new end is its original start.
            current_endpoint_idx = neighbor_line_idx * 2 + (1 - (neighbor_endpoint_idx % 2))


        # --- Trace backward ---
        current_endpoint_idx = i * 2  # Start with the START of the first line
        while True:
            neighbor_endpoint_idx = best_neighbor_idx[current_endpoint_idx]

            if dist_matrix[current_endpoint_idx, neighbor_endpoint_idx] == np.inf:
                break
            
            neighbor_line_idx = neighbor_endpoint_idx // 2
            
            if visited_lines[neighbor_line_idx]:
                break
            
            visited_lines[neighbor_line_idx] = True
            
            is_flipped = (neighbor_endpoint_idx % 2 == 0) # Flip if we connect to neighbor's START
            chain.insert(0, (neighbor_line_idx, is_flipped))
            
            # Update the current endpoint for the next iteration
            current_endpoint_idx = neighbor_line_idx * 2 + (neighbor_endpoint_idx % 2)

        # --- 3. Stitching the final chain ---
        
        coords_to_stitch = []
        for j, (line_idx, is_flipped) in enumerate(chain):
            segment = flipped_lines[line_idx] if is_flipped else lines[line_idx]
            if j > 0:
                segment = segment[1:]
            coords_to_stitch.append(segment)
        
        # Use np.concatenate for a slight performance edge over vstack
        merged_lines.append(np.concatenate(coords_to_stitch))

    return merged_lines


# --- Example Usage ---

iterations = 1

# Case 1: A simple, ordered chain
print("--- Case 1: Simple Chain ---")
line1 = np.array([[0.0, 0.0], [1.0, 1.0]])
line2 = np.array([[1.0000001, 1.0], [2.0, 0.0]]) # Floating point inaccuracy
line3 = np.array([[2.0, -0.0000002], [3.0, 1.0]])
# big linestring1: 500 points+
big_line1 =  np.array([[i, i] for i in range(500)])
big_line2 =  np.array([[i, i] for i in range(500, 1000)])
big_line3 =  np.array([[i, i] for i in range(1000, 1500)])


lines_to_merge = [line1, line2, line3]
lines_to_merge2 = [big_line1, big_line2, big_line3]
time1 = time.time()
for i in range(iterations):
    result = merge_linestrings(lines_to_merge, tolerance=1e-5)    
time2 = time.time()
print(f"Time: {time2-time1}")
time1 = time.time()
for i in range(iterations):
    result2 = merge_linestrings_optimized(lines_to_merge, tolerance=1e-5)
time2 = time.time()
print(f"Time: {time2-time1}")
time1 = time.time()
for i in range(iterations):
    result2 = merge_linestrings_numpy_optimized2(lines_to_merge, tolerance=1e-5)
time2 = time.time()
print(f"Time: {time2-time1}")

# print(f"Found {len(result)} merged line(s).")
# print(f"Found {len(result2)} merged line(s).")
# print("Merged line coordinates:")
# print(result[0])
# print(result2[0])
# print("-" * 20)

'''

# Case 2: Unordered lines, one needs to be flipped
print("--- Case 2: Unordered and Flipped Chain ---")
lineA = np.array([[10, 10], [11, 11]])
lineB = np.array([[13, 13], [12, 12]])  # This one is backward
lineC = np.array([[11.0001, 11.0001], [11.9999, 11.9999]])
lines_to_merge_2 = [lineA, lineB, lineC]

result_2 = merge_linestrings(lines_to_merge_2, tolerance=1e-3)
print(f"Found {len(result_2)} merged line(s).")
print("Merged line coordinates:")
# Expected order: A -> C -> B(flipped)
# (10,10)->(11,11)->(12,12)->(13,13)
print(result_2[0])
print("-" * 20)


# Case 3: Two separate chains
print("--- Case 3: Two Disjoint Chains ---")
# Chain 1
chain1_a = np.array([[0,0], [1,1]])
chain1_b = np.array([[1,1.001], [2,2]])
# Chain 2
chain2_a = np.array([[5,5], [6,6]])
chain2_b = np.array([[7,7], [6,5.999]]) # Flipped

lines_to_merge_3 = [chain1_a, chain2_a, chain1_b, chain2_b]
result_3 = merge_linestrings(lines_to_merge_3, tolerance=1e-2)
print(f"Found {len(result_3)} merged line(s).")
for i, line in enumerate(result_3):
    print(f"Merged line {i+1}:")
    print(line)
print("-" * 20)
'''