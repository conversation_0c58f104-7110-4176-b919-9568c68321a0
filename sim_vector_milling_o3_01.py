import numpy as np
from shapely import geometry, ops
from shapely.prepared import prep
import shapely

def angle_engagement(center, p_center, r, stock_poly, arc_resolution=12):    
    # Calculate rotation angle based on vector from center to p_center
    vec = [p_center[0] - center[0], p_center[1] - center[1]]
    rotation_angle = np.arctan2(vec[1], vec[0])    
    
    # Create a 180-degree arc as a LineString
    arc_points = [(center[0] + r * np.cos(rotation_angle + np.pi/2 - t), 
                  center[1] + r * np.sin(rotation_angle + np.pi/2 - t)) 
                 for t in np.linspace(0, np.pi, arc_resolution)]
    
    # Create the arc as a LineString
    arc = shapely.LineString(arc_points)
    
    intersection = shapely.intersection(stock_poly, arc, grid_size=None)
    if intersection:    
        if intersection.geom_type == "LineString":
            # Convert line length to angle width (in radians)
            return intersection.length / r
        elif intersection.geom_type == "MultiLineString":
            section_lengths = [line.length for line in intersection.geoms]
            return sum(section_lengths) / r
    else:
        return None

def main():

    R = 10.0                   # tool radius mm
    toolpath_centers = [(20,-10), (20,-5), (20,0), (20,5), (20,10), (20,15)]
    stock = geometry.Polygon([(0,0), (100,0), (100,100), (0,100)])
    angles = []

    for i, p in enumerate(toolpath_centers):              # 10k–100k steps typical
        if i > 0:
            θ = angle_engagement(p, toolpath_centers[i-1], R, stock)
            angles.append(θ)        
        # Create circle using NumPy sin/cos
        theta = np.linspace(0, 2*np.pi, 24)
        circle_points = [(p[0] + R * np.cos(t), p[1] + R * np.sin(t)) for t in theta]
        circle = geometry.Polygon(circle_points)
        stock = stock.difference(circle)

    print(f"engagement angles: {angles}")

if __name__ == '__main__':
    main()
