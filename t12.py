import numpy as np

def find_decreasing_closest_above_target(arr, target=5.0):
    """
    Finds the index of the value closest to, but greater than, a target value,
    within the initial strictly decreasing segment of a NumPy array.

    Args:
        arr (np.ndarray): The input NumPy array.
        target (float): The target value.

    Returns:
        int or None: The index of the found value, or None if no such value exists.
    """
    arr = np.asarray(arr)
    
    if arr.size == 0:
        return None
        
    # Find where array stops decreasing
    decreasing_end = np.where(np.diff(arr) >= 0)[0]
    end_idx = decreasing_end[0] + 1 if decreasing_end.size > 0 else len(arr)
    
    # Find values above target in decreasing segment
    mask = arr[:end_idx] > target
    if not np.any(mask):
        return None
        
    # Return last index (closest to target since sequence is decreasing)
    return np.where(mask)[0][-1]

# Your example array:
a = np.array([14.12508401, 13.87979857, 13.63523678, 13.39153022, 13.14864361, 12.90661115,
              12.66548204, 12.42522793, 12.18594929, 11.94777071, 11.71026175, 11.47358566,
              11.23792323, 11.00199397, 10.76606472, 10.53013546, 10.2942062,  10.05827695,
              9.82302211,  9.5890483,   9.3562618,   9.12469549,  8.89401572,  8.66350463,
              8.43299354,  8.20248246,  7.97197137,  7.74146028,  7.51094919,  7.28043811,
              7.04992702,  6.81941593,  6.58890485,  6.35839376,  6.12788267,  5.89730985,
              5.66669576,  5.43608167,  5.20546759,  4.9748535,   4.74423941,  4.51362533,
              4.28301124,  4.05239715,  3.82178307,  3.59116898,  3.3605549,   3.12994081,
              2.89932672,  2.66871264,  2.43809855,  2.20748446,  1.97687038,  1.74625629,
              1.5156422,   1.28502812,  1.05441403,  0.82379994,  0.59318586,  0.36257177,
              0.13195768,  0.12660396,  0.42659162,  0.72658948,  1.02658858,  1.32658809,
              1.62658779,  1.92658757,  2.22658742,  2.5265873,   2.82658721,  3.12658713,
              3.42658707,  3.72658702,  4.02658697,  4.32658693,  4.6265869,   4.92658687,
              5.22658685,  5.52658682,  5.8265868,   6.12658678,  6.42658677])

target_value = 5.0
result_index = find_decreasing_closest_above_target(a, target_value)

if result_index is not None:
    # print(f"Array: {a}")
    print(f"Target value: {target_value}")
    print("-" * 20)
    print(f"Index found: {result_index}")
    print(f"Value at index {result_index}: {a[result_index]}")
