import numpy as np
import time
import numpy as np

class EllipseFitter:
    def fit(self, points):
        # Ensure points are a numpy array of float64
        points = np.asarray(points, dtype=np.float64)
        
        # Number of points
        n = points.shape[0]
        
        # Build the design matrix D (n x 6)
        D = np.zeros((n, 6))
        D[:, 0] = points[:, 0]**2  # x^2
        D[:, 1] = points[:, 0] * points[:, 1]  # xy
        D[:, 2] = points[:, 1]**2  # y^2
        D[:, 3] = points[:, 0]  # x
        D[:, 4] = points[:, 1]  # y
        D[:, 5] = 1  # constant term
        
        # Compute the scatter matrix S = D^T D
        S = D.T @ D
        
        # Build the constraint matrix C (6x6)
        C = np.zeros((6, 6))
        C[0, 2] = 2
        C[2, 0] = 2
        C[1, 1] = -1
        
        # Partition S into blocks
        S11 = S[:3, :3]
        S12 = S[:3, 3:]
        S21 = S[3:, :3]
        S22 = S[3:, 3:]
        
        # Compute T = S11 - S12 @ inv(S22) @ S21
        try:
            inv_S22 = np.linalg.inv(S22)
        except np.linalg.LinAlgError:
            raise ValueError("Singular matrix encountered during inversion.")
        T = S11 - S12 @ inv_S22 @ S21
        
        # Constraint matrix M (top-left 3x3 of C)
        M = np.array([
            [0, 0, 2],
            [0, -1, 0],
            [2, 0, 0]
        ])
        
        # Solve the generalized eigenvalue problem T q = lambda M q
        # This is equivalent to solving M^{-1} T q = lambda q
        try:
            M_inv = np.linalg.inv(M)
        except np.linalg.LinAlgError:
            raise ValueError("Singular matrix encountered during inversion of M.")
        M_inv_T = M_inv @ T
        
        # Compute eigenvalues and eigenvectors
        eigenvals, eigenvecs = np.linalg.eig(M_inv_T)
        
        # Find the index of the smallest positive eigenvalue
        pos_eigenvals = eigenvals[eigenvals > 0]
        if len(pos_eigenvals) == 0:
            raise ValueError("No positive eigenvalues found, not an ellipse.")
        min_pos_idx = np.argmin(pos_eigenvals)
        q = eigenvecs[:, min_pos_idx]
        
        # Normalize q so that q^T M q = 1
        norm_factor = np.sqrt(np.dot(q, M @ q))
        if norm_factor == 0:
            raise ValueError("Normalization factor is zero.")
        q /= norm_factor
        
        # Compute r = -inv(S22) @ S21 @ q
        r = -inv_S22 @ S21 @ q
        
        # Combine q and r to form the conic coefficients c
        c = np.concatenate([q, r])
        
        # Extract coefficients A, B, C, D, E, F
        A, B, C_coeff, D_coeff, E_coeff, F_coeff = c
        
        # Compute the center (h, k)
        M_center = np.array([
            [2 * A, B],
            [B, 2 * C_coeff]
        ])
        try:
            center = -np.linalg.solve(M_center, [D_coeff, E_coeff])
        except np.linalg.LinAlgError:
            raise ValueError("Singular matrix encountered while computing center.")
        
        # Translate the ellipse to the origin
        F_prime = A * center[0]**2 + B * center[0] * center[1] + C_coeff * center[1]**2 + \
                  D_coeff * center[0] + E_coeff * center[1] + F_coeff
        
        # Compute the rotation angle theta
        if A == C_coeff:
            theta = np.pi / 4
        else:
            theta = 0.5 * np.arctan2(B, A - C_coeff)
        
        # Compute the rotated coefficients A' and C'
        d = np.sqrt((A - C_coeff)**2 + B**2)
        A_prime = (A + C_coeff) / 2 + d / 2
        C_prime = (A + C_coeff) / 2 - d / 2
        
        # Check for valid ellipse parameters
        if F_prime >= 0 or A_prime <= 0 or C_prime <= 0:
            raise ValueError("Invalid ellipse parameters detected.")
        
        # Compute semi-axes lengths
        a = np.sqrt(-F_prime / A_prime)
        b = np.sqrt(-F_prime / C_prime)
        
        # Ensure a is the semi-major axis
        if a < b:
            a, b = b, a
            theta += np.pi / 2
        
        return {
            'center': tuple(center),
            'a': a,
            'b': b,
            'theta': theta
        }
    

# Example usage:
if __name__ == "__main__":
    points = np.array([
        [-18107.85742188,  -9668.421875  ],
        [-18109.07421875,  -9649.95117188],
        [-18133.55859375,  -9622.34765625],
        [-18161.0234375,   -9615.94433594],
        [-18180.34570312,  -9623.63476562]
    ], dtype=np.float64)

    points = np.array([[-18207.74825912,  -9692.71639934],
                       [-18184.12844828,  -9717.36587973],
                       [-18211.82190713,  -9658.83258577],
                       [-18152.71974009,  -9719.75775507],
                       [-18196.4769546,   -9635.31620922]], dtype=np.float64)


    
    time1 = time.time()
    result = EllipseFitter().fit(points)
    print(result)
    time2 = time.time()
    print(f'Time: {time2-time1}')