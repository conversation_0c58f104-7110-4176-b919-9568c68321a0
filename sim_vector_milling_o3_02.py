import numpy as np
import bpy
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point
from shapely import prepare, crosses
import math
import random
from shapely.ops import linemerge
import networkx as nx
from itertools import combinations

import time


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []
    

def get_collection(name: str, rand_int: int) -> bpy.types.Collection:
    """Get a collection by name, creating it if it doesn't exist."""
    name = name + str(rand_int)
    if name not in bpy.data.collections:
        bpy.data.collections.new(name)
        bpy.context.scene.collection.children.link(bpy.data.collections[name])
    return bpy.data.collections[name]


def create_ring_object(coords: list[tuple[float, float]], name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def make_medial_edges(polygon, rounding_precision=4):
    pv = pyvoronoi.Pyvoronoi(100)

    # Process exterior polygon more efficiently
    coords = np.array(polygon.exterior.coords[:-1])  # Exclude last point
    segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
    for segment in segments:
        # Convert segment to the format expected by pyvoronoi
        start = [segment[0], segment[1]]
        end = [segment[2], segment[3]]
        pv.AddSegment([start, end])

    # Process holes more efficiently
    if polygon.interiors:
        for interior in polygon.interiors:
            coords = np.array(interior.coords[:-1])
            segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
            for segment in segments:
                start = [segment[0], segment[1]]
                end = [segment[2], segment[3]]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    # Prepare polygon for faster contains checks
    shapely.prepare(polygon)  # This modifies polygon in-place
    
    ma_edges = []
    twins = set()

    for cell in cells:
        for edge_idx in cell.edges:
            e = edges[edge_idx]
            
            if not e.is_primary or e.start == -1 or e.end == -1 or edge_idx in twins:
                continue

            startVertex = vertices[e.start]
            endVertex = vertices[e.end]
            mid_point = Point(((startVertex.X + endVertex.X) / 2, 
                             (startVertex.Y + endVertex.Y) / 2))

            if not polygon.contains(mid_point):
                continue

            twins.add(e.twin)
            
            if e.is_linear:
                start_point = (round(startVertex.X, rounding_precision), 
                             round(startVertex.Y, rounding_precision))
                end_point = (round(endVertex.X, rounding_precision), 
                           round(endVertex.Y, rounding_precision))
                ma_edges.append(shapely.geometry.LineString([start_point, end_point]))
            else:
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                       (startVertex.Y - endVertex.Y)**2) / 10
                # Convert map object to list
                points = list(pv.DiscretizeCurvedEdge(edge_idx, max_distance*1, 
                                                    parabola_equation_tolerance=0.001))
                # Handle first and last points separately
                points[0] = [round(points[0][0], rounding_precision), 
                           round(points[0][1], rounding_precision)]
                points[-1] = [round(points[-1][0], rounding_precision), 
                            round(points[-1][1], rounding_precision)]
                ma_edges.append(shapely.geometry.LineString(points))

    # Merge and simplify edges
    merged = linemerge(ma_edges)
    if merged.geom_type == 'LineString':
        ma_edges = [merged.simplify(0.05)]
    else:
        ma_edges = [edge.simplify(0.05) for edge in merged.geoms]

    return ma_edges


def make_acyclic_undirected(graph):
    """Removes edges from an undirected graph to make it acyclic."""
    graph_copy = graph.copy()
    removed_edges = []
    
    while True:
        try:
            cycle = nx.find_cycle(graph_copy)
            edge_to_remove = cycle[0]
            u, v = edge_to_remove
            graph_copy.remove_edge(u, v)
            removed_edges.append((u, v, graph.get_edge_data(u, v)))
        except nx.NetworkXNoCycle:
            break
            
    return graph_copy, removed_edges


def get_subgraph_from_edge(acyclic_graph, edge, start_node):
    """
    Extract subgraph information from edge connected to start_node.
    
    Args:
        acyclic_graph: NetworkX graph
        edge: Edge in the graph
        start_node: Starting node in the graph
        
    Returns:
        tuple: Contains 'branches', 'leafs' information
    """

    branches = []
    leafs = []

    edge_nodes = list(edge)
    end_node = edge_nodes[1] if edge_nodes[0] == start_node else edge_nodes[0]
                
    visited = {end_node}
    to_visit = {end_node}
    
    # Simple BFS
    while to_visit:
        current = to_visit.pop()
        
        if acyclic_graph.nodes[current].get('branch', False):
            branches.append(current)
        else:
            leafs.append(current)
        
        for neighbor in acyclic_graph.neighbors(current):
            if neighbor != start_node and neighbor not in visited:
                visited.add(neighbor)
                to_visit.add(neighbor)
        
    return branches, leafs


def solve_cpp(graph: nx.Graph, start_node):
    """
    Solves the Chinese Postman Problem for an unweighted, undirected graph.
    Returns a path that covers all edges starting from start_node.
    """
    if not graph or start_node not in graph:
        return None

    if graph.number_of_edges() == 0:
        return [start_node] if start_node in graph else None

    if not nx.is_connected(graph):
        raise nx.NetworkXError("Graph must be connected")

    # Create MultiGraph for edge duplication
    multi_graph = nx.MultiGraph(graph)
    odd_nodes = [node for node, degree in multi_graph.degree() if degree % 2 != 0]

    # If graph is already Eulerian
    if not odd_nodes:
        eulerian_edges = list(nx.eulerian_circuit(multi_graph, source=start_node))
        return [start_node] + [v for u, v in eulerian_edges]

    # Create complete graph of odd nodes with shortest path distances
    odd_graph = nx.Graph()
    shortest_paths = dict(nx.all_pairs_shortest_path_length(graph))
    for u, v in combinations(odd_nodes, 2):
        odd_graph.add_edge(u, v, weight=shortest_paths[u][v])

    # Find minimum weight matching and augment graph
    min_matching = nx.min_weight_matching(odd_graph, weight='weight')
    for u, v in min_matching:
        path = nx.shortest_path(graph, u, v)
        for i in range(len(path) - 1):
            multi_graph.add_edge(path[i], path[i + 1])

    # Find and return Eulerian circuit
    eulerian_edges = list(nx.eulerian_circuit(multi_graph, source=start_node))
    return [start_node] + [v for u, v in eulerian_edges]


def angle_engagement(center, p_center, r, stock_poly, arc_resolution=12):    
    # Calculate rotation angle based on vector from center to p_center
    vec = [center[0] - p_center[0], center[1] - p_center[1]]  # Flipped vector direction
    rotation_angle = np.arctan2(vec[1], vec[0])    
    # Create a 180-degree arc as a LineString
    arc_points = [(center[0] + r * np.cos(rotation_angle + np.pi/2 - t), 
                  center[1] + r * np.sin(rotation_angle + np.pi/2 - t)) 
                 for t in np.linspace(0, np.pi, arc_resolution)]
    
    # Create the arc as a LineString
    arc = shapely.LineString(arc_points)

    # create_line_object(arc_points, "arc")
    
    intersection = shapely.intersection(stock_poly, arc, grid_size=None)
    if intersection:    
        if intersection.geom_type == "LineString":
            # Convert line length to angle width (in radians)
            return intersection.length / r
        elif intersection.geom_type == "MultiLineString":
            section_lengths = [line.length for line in intersection.geoms]
            return sum(section_lengths) / r
    else:
        return None



def main():
    R = 10.0                   # tool radius mm
    toolpath_centers = [(20,-10), (20,-5), (20,0), (20,5), (20,10), (20,15), (22, 18), (20, 18) ]
    # toolpath_centers = [(20,-10), (20,-5)]
    stock = shapely.geometry.Polygon([(0,0), (100,0), (100,100), (0,100)])
    angles = []
    iterations = 10000

    start_time = time.time()
    for i in range(iterations):    
        stock = shapely.geometry.Polygon([(0,0), (100,0), (100,100), (0,100)])
        for i, p in enumerate(toolpath_centers):              # 10k–100k steps typical
            if i > 0:
                θ = angle_engagement(p, toolpath_centers[i-1], R, stock)
                angles.append(θ)
            stock = stock.difference(shapely.geometry.Point(*p).buffer(R,5))


    end_time = time.time()
    print(f"Simulation completed in {end_time - start_time:.2f} seconds")
    # print(f"engagement angles: {angles}")
    # create_ring_object(stock.exterior.coords, "stock")

if __name__ == "__main__":
    main()
