import numpy as np
import jax
import jax.numpy as jnp
import jaxls

# ------------------------------------------------------------------
# 1. Data & bounds
# ------------------------------------------------------------------
points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

lb, ub = ([-18170, -9679, 30, 20, np.deg2rad(320)],
          [-18130, -9625, 60, 40, np.deg2rad(340)])

initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)

# ------------------------------------------------------------------
# 2. Ellipse model
#    x = cx + a cos(t) cosθ − b sin(t) sinθ
#    y = cy + a cos(t) sinθ + b sin(t) cosθ
# ------------------------------------------------------------------
def ellipse_points(params, ts):
    cx, cy, a, b, theta = params
    cos_t, sin_t = jnp.cos(ts), jnp.sin(ts)
    cos_th, sin_th = jnp.cos(theta), jnp.sin(theta)

    x = cx + a * cos_t * cos_th - b * sin_t * sin_th
    y = cy + a * cos_t * sin_th + b * sin_t * cos_th
    return jnp.stack([x, y], axis=1)            # (N, 2)

# ------------------------------------------------------------------
# 3. Construct the factor-graph
#    * 5 "parameter" factors → each point must lie on the ellipse
#    * 1 "prior" factor → each parameter must stay inside its bounds
# ------------------------------------------------------------------
def build_factors(points, lb, ub):
    factors = []

    # 1) Five residuals that force the points to lie on the ellipse
    ts = jnp.array([0.0, 1.0, 2.0, 3.0, 4.0])   # will be optimised
    for i, pt in enumerate(points):
        factors.append(
            jaxls.Factor(
                residual=lambda p, t, pt=pt: ellipse_points(p, jnp.array([t]))[0] - pt,
                param_ids=[0, i+1],
                residuals_dim=2
            )
        )

    # 2) Box constraints (turned into soft “prior” factors)
    for k, (lo, hi) in enumerate(zip(lb, ub)):
        factors.append(
            jaxls.Factor(
                residual=lambda p, lo=lo, hi=hi: jnp.clip(p[k], lo, hi) - p[k],
                param_ids=[0],
                residuals_dim=1
            )
        )

    return factors

# ------------------------------------------------------------------
# 4. Solve
# ------------------------------------------------------------------
# Flattened parameter block:
# indices 0..4  -> the ellipse parameters
# indices 5..9  -> the five angle parameters t_i
params = jnp.concatenate([initial_guess, jnp.zeros(5)])

factors = build_factors(points, lb, ub)

problem = jaxls.Problem(
    factors=factors,
    param_blocks={
        0: params[0:5],       # ellipse parameters
        1: params[5:6],      # t0
        2: params[6:7],      # t1
        3: params[7:8],      # t2
        4: params[8:9],      # t3
        5: params[9:10],     # t4
    },
    param_lower_bounds={0: jnp.array(lb)},
    param_upper_bounds={0: jnp.array(ub)},
)

solution = jaxls.solve(problem, verbose=True)
ellipse_params = solution.param_blocks[0]

print("\nFitted ellipse parameters:")
print("cx      :", ellipse_params[0])
print("cy      :", ellipse_params[1])
print("a       :", ellipse_params[2])
print("b       :", ellipse_params[3])
print("theta   :", np.rad2deg(ellipse_params[4]), "deg")
