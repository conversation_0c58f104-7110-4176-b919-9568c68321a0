#!/usr/bin/env python3
"""
GPU milling‑simulation demo
===========================

– Computes, at every tool position, the arc‑engagement angle
  (fraction of cutter outline that still touches material)
– Removes the swept material from the stock bitmap in the *same* CUDA kernel

Dependencies
------------
• Python 3.9+
• CuPy built for your CUDA version, e.g.:
      pip install cupy-cuda12x        # CUDA 12.* toolkit present
"""

import math
import numpy as np
import cupy as cp


# --------------------------------------------------------------------------
# 0.  User‑tunable parameters
# --------------------------------------------------------------------------
pixel_size_mm = 0.05                     # simulation resolution
stock_W_mm, stock_H_mm = 60.0, 30.0      # stock size in mm
tool_R_mm = 5.0                          # cutter radius
path_step_mm = 0.20                      # spacing between path points


# --------------------------------------------------------------------------
# 1.  Build stock bitmap (host) --------------------------------------------
# --------------------------------------------------------------------------
stock_W_px = int(round(stock_W_mm / pixel_size_mm))
stock_H_px = int(round(stock_H_mm / pixel_size_mm))
stock_host = np.ones((stock_H_px, stock_W_px), dtype=np.uint8)   # 1 = material


# --------------------------------------------------------------------------
# 2.  Build filled tool disk + outline offsets (host)  ←— FIXED
# --------------------------------------------------------------------------
tool_R_px = int(round(tool_R_mm / pixel_size_mm))
diam_px   = 2 * tool_R_px + 1                # e.g. 201

# full (H, W) coordinate grids
Y, X = np.mgrid[-tool_R_px:tool_R_px + 1,    # shape (H, W)
                -tool_R_px:tool_R_px + 1]

dist2 = X*X + Y*Y

filled = (dist2 <= tool_R_px**2).astype(np.uint8)
rim    = (dist2 <= tool_R_px**2) & (dist2 > (tool_R_px-1)**2)

# (N, 2)   columns:  dy, dx   (int32)
rim_offsets = np.column_stack((Y[rim].ravel(), X[rim].ravel())).astype(np.int32)

print(f"Tool radius: {tool_R_px}px; filled px: {filled.sum()}; "
      f"outline px: {len(rim_offsets)}")

# --------------------------------------------------------------------------
# 3.  Transfer immutable data to GPU ---------------------------------------
# --------------------------------------------------------------------------
stock_gpu       = cp.asarray(stock_host)                 # mutable!
tool_filled_gpu = cp.asarray(filled)
rim_gpu         = cp.asarray(rim_offsets)

tool_W, tool_H  = filled.shape[1], filled.shape[0]
tool_off        = cp.int32(tool_R_px)                    # x/y offset centre→array(0,0)


# --------------------------------------------------------------------------
# 4.  CUDA kernel (contact test + erase) -----------------------------------
# --------------------------------------------------------------------------
cuda_src = r'''
extern "C" __global__
void contact_erase(const int n_idx,
                   const int2 *idx,          /* outline offsets (dy,dx)   */
                   const int cx, const int cy,
                   const int W,  const int H,
                   unsigned char *wp,        /* stock bitmap (row major)  */
                   const int tool_W, const int tool_H,
                   const unsigned char *tool_fill,
                   const int tool_off,       /* = radius in pixels        */
                   unsigned int *d_hits)     /* out: #outline px engaged  */
{
    /* ---------------- contact test ---------------- */
    unsigned int local = 0;
    for (int k = threadIdx.x; k < n_idx; k += blockDim.x){
        int dx = idx[k].x;   // NB: .x = dy, .y = dx (host packs so)
        int dy = idx[k].y;
        int px = cx + dx;
        int py = cy + dy;
        if ((unsigned)px < W && (unsigned)py < H){
            if (wp[py * W + px]) local++;
        }
    }
    /* reduction to one value per block (<=256 threads) */
    __shared__ unsigned int smem[256];
    int t = threadIdx.x;
    smem[t] = local;
    __syncthreads();
    for (int s = blockDim.x >> 1; s; s >>= 1){
        if (t < s) smem[t] += smem[t+s];
        __syncthreads();
    }
    if (t == 0) atomicAdd(d_hits, smem[0]);

    /* ---------------- erase filled disk ------------ */
    int tid = blockDim.x + threadIdx.x;  // reuse same block
    int total = tool_W * tool_H;
    for (int p = tid; p < total; p += blockDim.x){
        if (tool_fill[p]){
            int ty = p / tool_W;
            int tx = p - ty * tool_W;
            int px = cx - tool_off + tx;
            int py = cy - tool_off + ty;
            if ((unsigned)px < W && (unsigned)py < H){
                wp[py * W + px] = 0;
            }
        }
    }
}
'''
contact_erase_kernel = cp.RawKernel(cuda_src, "contact_erase")


# --------------------------------------------------------------------------
# 5.  Tool path (simple straight line demo) --------------------------------
# --------------------------------------------------------------------------
path_points = []
x0_mm = tool_R_mm + 1.0
x1_mm = stock_W_mm - tool_R_mm - 1.0
y_mm  = stock_H_mm * 0.5
n_pts = int((x1_mm - x0_mm) / path_step_mm) + 1
for i in range(n_pts):
    path_points.append((x0_mm + i * path_step_mm, y_mm))

print(f"Path: {len(path_points)} points, from {x0_mm:.1f} mm to {x1_mm:.1f} mm")


# helpers ------------------------------------------------
def world_to_px(x_mm, y_mm):
    """World (mm) → pixel (int).  (0,0) mm is bottom‑left corner."""
    px = int(round(x_mm / pixel_size_mm))
    py = int(round(y_mm / pixel_size_mm))
    # Flip Y so that pixel (0,0) = top‑left as in images
    py = stock_H_px - 1 - py
    return px, py


# --------------------------------------------------------------------------
# 6.  Main simulation loop -------------------------------------------------
# --------------------------------------------------------------------------
hits_dev = cp.zeros(1, dtype=cp.uint32)
outline_N = rim_gpu.shape[0]
angles = []

for step, (x_mm, y_mm) in enumerate(path_points, 1):

    cx, cy = world_to_px(x_mm, y_mm)

    # reset device counter
    hits_dev.fill(0)

    # launch kernel (one block, 256 threads)
    contact_erase_kernel(
        (1,), (256,),
        args=(np.int32(outline_N),
              rim_gpu.ptr,
              np.int32(cx), np.int32(cy),
              np.int32(stock_W_px), np.int32(stock_H_px),
              stock_gpu.ptr,
              np.int32(tool_W), np.int32(tool_H),
              tool_filled_gpu.ptr,
              tool_off,
              hits_dev.ptr))

    # Bring back single scalar
    hits = int(hits_dev.get()[0])
    angle = 360.0 * hits / outline_N
    angles.append(angle)

    if step % 200 == 0 or step == len(path_points):
        print(f"step {step:5d}/{len(path_points)}   engagement {angle:6.2f}°")

cp.cuda.Stream.null.synchronize()         # wait for GPU

print("\nSimulation finished.")
print(f"Average engagement angle: {np.mean(angles):.2f}°")


# --------------------------------------------------------------------------
# 7.  Retrieve final stock bitmap (optional) -------------------------------
# --------------------------------------------------------------------------
stock_final = cp.asnumpy(stock_gpu)

# Quick text output: % of material left
remain = stock_final.sum() / stock_host.size * 100
print(f"Material remaining: {remain:.2f} %")

# --------------------------------------------------------------------------
# 8.  Minimal visualisation (requires matplotlib) --------------------------
# --------------------------------------------------------------------------
try:
    import matplotlib.pyplot as plt
    plt.figure(figsize=(8,4))
    plt.subplot(1,2,1)
    plt.title("Final stock")
    plt.imshow(stock_final, cmap='gray', origin='upper')
    plt.axis('off')

    plt.subplot(1,2,2)
    plt.title("Engagement angle along path")
    plt.plot(angles)
    plt.xlabel("Step")
    plt.ylabel("Angle (deg)")
    plt.tight_layout()
    plt.show()
except ModuleNotFoundError:
    print("matplotlib not installed – skipping plot.")