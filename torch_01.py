import torch

# Example vertices (replace with actual data)
A_t = torch.tensor([[1.0, 1.0], [2.0, 2.0]])  # n=2
B_t = torch.tensor([[0.0, 0.0], [3.0, 0.0], [3.0, 3.0], [0.0, 3.0]])  # m=4

n, m = A_t.shape[0], B_t.shape[0]

# Candidate segments: differences B[j] - A[i]
delta_bj_ai = (B_t[None, :, :] - A_t[:, None, :]).unsqueeze(2)  # (n, m, 1, 2)

# Differences B[l] - A[i] for all l
delta_bl_ai = B_t[None, None, :, :] - A_t[:, None, None, :]  # (n, 1, m, 2)

# Orientation of (A[i], B[j], B[l]) for all i, j, l
cross_abc = (delta_bj_ai[..., 0] * delta_bl_ai[..., 1]) - (delta_bj_ai[..., 1] * delta_bl_ai[..., 0])  # (n, m, m)
orientation_abc = torch.sign(cross_abc)  # (n, m, m)

# Orientation for edges B[k] to B[k+1]
B_t_roll = torch.roll(B_t, -1, dim=0)  # B[(k+1) % m]
delta_bkp1_bk = B_t_roll - B_t  # (m, 2)

# Orientation of (B[k], B[k+1], A[i])
delta_ai_bk = A_t[:, None, :] - B_t[None, :, :]  # (n, m, 2)
cross_bk_bkp1_ai = (delta_bkp1_bk[None, :, 0] * delta_ai_bk[..., 1]) - (delta_bkp1_bk[None, :, 1] * delta_ai_bk[..., 0])  # (n, m)
orientation_bk_bkp1_ai = torch.sign(cross_bk_bkp1_ai)  # (n, m)

# Orientation of (B[k], B[k+1], B[j])
delta_bj_bk = B_t[None, :, :] - B_t[:, None, :]  # (m, m, 2)
cross_bk_bkp1_bj = (delta_bkp1_bk[:, 0].unsqueeze(1) * delta_bj_bk[..., 1]) - (delta_bkp1_bk[:, 1].unsqueeze(1) * delta_bj_bk[..., 0])  # (m, m)
orientation_bk_bkp1_bj = torch.sign(cross_bk_bkp1_bj)  # (m, m)

# Roll orientations for B[k+1]
orientation_abc_rolled = torch.roll(orientation_abc, -1, dim=2)  # (n, m, m)

# Condition 1: A[i], B[j] on opposite sides of B[k]B[k+1]
condition1 = (orientation_abc != orientation_abc_rolled)  # (n, m, m)

# Condition 2: B[k], B[k+1] on opposite sides of A[i]B[j]
condition2 = (orientation_bk_bkp1_ai[:, None, :] != orientation_bk_bkp1_bj[None, :, :])  # (n, m, m)

# Intersects for each (i, j, k)
intersects = condition1 & condition2  # (n, m, m)

# Any intersection with an edge of B for each (i, j)
mask = torch.any(intersects, dim=2)  # (n, m)

# Indices of intersecting segments
indices = torch.nonzero(mask, as_tuple=False)  # Tensor of shape (num_intersections, 2)

# List of line segment coordinates
edges = [[A_t[i].tolist(), B_t[j].tolist()] for i, j in indices]

print("Intersecting segments:", edges)