from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp

def solve_constrained_tsp_open_path(points, distance_matrix, forced_arcs, start_point_index):
    """Solves the constrained TSP problem with an open path and start point using OR-Tools.

    Args:
        points: A list of points (e.g., indices 0 to n-1).
        distance_matrix: A 2D list/array representing distances between points.
        forced_arcs: A list of tuples, e.g., [(4, 1), (12, 5)] (0-indexed).
        start_point_index: The index of the starting point (0-indexed).

    Returns:
        A tuple:
            - status: OR-Tools routing status.
            - path: A list representing the ordered path of point indices (or None if no solution).
            - objective: The total distance of the path (or None if no solution).
    """

    num_locations = len(points)
    num_vehicles = 1  # Single path (single "vehicle")
    depot = start_point_index  # For open path, start and depot are the same

    # Create routing model
    routing_parameters = pywrapcp.DefaultRoutingSearchParameters()
    routing = pywrapcp.RoutingModel(num_locations, num_vehicles, [depot], [depot], routing_parameters) # Start and end depots are the same for open path, but we only use start depot

    # Distance callback (function to calculate distance between locations)
    def distance_callback(from_index, to_index):
        """Returns the distance between the two nodes."""
        # Convert from routing variable Index to distance matrix NodeIndex.
        from_node = routing.IndexToNode(from_index)
        to_node = routing.IndexToNode(to_index)
        return distance_matrix[from_node][to_node]

    transit_callback_index = routing.RegisterTransitCallback(distance_callback)
    routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)

    # Add precedence constraints (forced arcs)
    for from_point, to_point in forced_arcs:
        routing.AddPrecedenceConstraint(routing.NodeToIndex(from_point), routing.NodeToIndex(to_point))

    # Setting first solution heuristic (optional, but often helps)
    search_parameters = pywrapcp.DefaultRoutingSearchParameters()
    search_parameters.first_solution_strategy = (
        routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC
    )
    search_parameters.local_search_metaheuristic = (
        routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
    )
    search_parameters.time_limit.seconds = 10  # Time limit for search (optional)

    # Solve the problem.
    assignment = routing.SolveWithParameters(search_parameters)

    if assignment:
        status = assignment.StatusName()
        objective = assignment.ObjectiveValue()
        path = []
        index = routing.Start(0)  # Start from the depot (which is our start point)
        while not routing.IsEnd(index): # For open path, we stop when there's no next node
            node_index = routing.IndexToNode(index)
            path.append(node_index)
            index = assignment.Value(routing.NextVar(index))
        return status, path, objective
    else:
        return "No solution found", None, None


if __name__ == '__main__':
    # --- Example Usage ---
    num_points = 20
    points_list = list(range(num_points)) # Points are 0 to 19

    # Example Distance Matrix (replace with your actual distances)
    # For demonstration, we'll use a simple distance based on index difference
    distance_matrix_example = [[0] * num_points for _ in range(num_points)]
    for i in range(num_points):
        for j in range(num_points):
            if i != j:
                distance_matrix_example[i][j] = abs(i - j) # Simple distance example

    forced_arcs_example = [(4, 1), (12, 5)] # Example forced arcs (0-indexed)
    start_point_index_example = 0 # Start at point 0 (index 0)

    status, path, objective = solve_constrained_tsp_open_path(
        points_list, distance_matrix_example, forced_arcs_example, start_point_index_example
    )

    print(f"Status: {status}")
    if path:
        print("Optimal Path:", path)
        print("Total Distance:", objective)
    else:
        print("No path found.")