import numpy as np

arr = np.array([[3, 4, 5, 5, 7, 7, 7, 7, 7, 7],
                [0, 2, 0, 3, 5, 0, 3, 1, 2, 4]])

# Dynamically get all unique keys sorted from the second row
polygons = 8
keys = np.unique(arr[1])
arr_size = ((polygons-1)*2 + (polygons-1))
next_keys = np.full((arr_size), -1)
idx = 0
for key in keys:    
    if not key in next_keys:
        # Create a mask for the current key
        mask = arr[1] == key
        # Extract values from the first row using the mask
        values = arr[0][mask]        
        next_keys[idx] = key
        idx += 1
        values_len = len(values)
        next_keys[idx:idx+values_len] = values
        idx += values_len
        next_keys[idx] = -2
        idx += 1        

print(next_keys)