import bpy
import numpy as np
import shapely
from mathutils import Vector

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None

def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None

        
def geometry_to_multipolygon(geometry, simplify=False, simplify_exterior=0.0, simplify_interior=0.0):
    polygons = [shapely.geometry.Polygon(shell=geom) for geom in geometry]
    if simplify:
        if simplify_exterior != simplify_interior and len(polygons) > 1:
            interiors = [polygon.simplify(simplify_interior, preserve_topology=True) for polygon in polygons[1:]]
            interiors.insert(0, polygons[0].simplify(simplify_exterior, preserve_topology=True))
            polygons = interiors
        else:
            polygons = [polygon.simplify(simplify_exterior, preserve_topology=False) for polygon in polygons]

    return shapely.geometry.MultiPolygon(polygons)


def buffered_geometry_to_multipolygon(geometry, decompose=False):
    if decompose:
        if not geometry.is_empty:
            polys = []
            if geometry.geom_type == 'MultiPolygon':
                for polygon in geometry.geoms:
                    polys.append(shapely.geometry.Polygon(shell=polygon.exterior))
                    for interior in polygon.interiors:
                        polys.append(shapely.geometry.Polygon(shell=interior))

            elif geometry.geom_type == 'Polygon':
                polys.append(shapely.geometry.Polygon(shell=geometry.exterior))
                for interior in geometry.interiors:
                    polys.append(shapely.geometry.Polygon(shell=interior))

            else:
                print(geometry.geom_type, 'shapely conversion aborted')
                return geometry.MultiPolygon()

            return shapely.geometry.MultiPolygon(polys)

    else:
        if geometry.geom_type == 'MultiPolygon':
            return geometry
        elif geometry.geom_type == 'Polygon':
            if not geometry.is_empty:
                return shapely.geometry.MultiPolygon([geometry])
            else:
                return geometry.MultiPolygon()
        else:
            print(geometry.geom_type, 'shapely conversion aborted')
            return geometry.MultiPolygon()


def shapely_to_blender(shapely_geom, name="OffsetObject"):
    """
    Transfers a Shapely geometry (Polygon or MultiPolygon) to Blender edge loops.
    """
    
    # Create new mesh and object
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = []
    edges = []
    
    def process_ring(ring, offset):
        coords = list(ring.coords)
        n_coords = len(coords)
        vertices.extend(Vector((x, y, 0)) for x, y in coords)
        edges.extend((offset + i, offset + (i+1)%n_coords) for i in range(n_coords))
        return n_coords

    def process_polygon(poly, offset):
        n = process_ring(poly.exterior, offset)
        offset += n
        for interior in poly.interiors:
            n = process_ring(interior, offset)
            offset += n
        return offset
        

    if shapely_geom.geom_type == "Polygon":
        # Process exterior and interiors
        offset = 0
        offset += process_ring(shapely_geom.exterior, 0)
        for interior in shapely_geom.interiors:
            offset += process_ring(interior, offset)

    elif shapely_geom.geom_type == "MultiPolygon":
        # Process all polygons
        offset = 0
        for poly in shapely_geom.geoms:
            offset = process_polygon(poly, offset)
    else:
        raise ValueError("Unsupported geometry type: {}".format(shapely_geom.geom_type))
    
    # Create the mesh from the vertices and edges
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


# def buffer_multipolygon(multipolygon, width):
#     # Buffer the exterior polygon
#     exterior = multipolygon.geoms[0].buffer(width, join_style='mitre', mitre_limit=1.1)
#     buffered_shape = bufferedGeometryToMultipolygon(       
#         exterior
#     )

#     # Buffer each interior negatively and combine them
#     if len(multipolygon.geoms) > 1:
#         buffered_islands = [island.buffer(-width, join_style='mitre', mitre_limit=1.1) for island in multipolygon.geoms[1:].geoms]
#         combined_buffered_islands = bufferedGeometryToMultipolygon(
#             unary_union(buffered_islands)
#         )
#         # Subtract buffered interiors from buffered exterior
#         buffered_shape = bufferedGeometryToMultipolygon(
#             remove_repeated_points(buffered_shape.difference(combined_buffered_islands), tolerance=0.00004), True
#         )

#     if buffered_shape == None:
#         print('buffered_shape is empty')
#         break




'''
def tessellate_polygon(polygon, width):
    def process_edge(p0, p1, w):
        vector = p1 - p0
        L = np.linalg.norm(vector)
        if L < 3 * w:
            return [p0.copy(), p1.copy()]
        dir_unit = vector / L
        s1 = p0 + w * dir_unit
        e1 = p1 - w * dir_unit
        middle_vector = e1 - s1
        middle_length = np.linalg.norm(middle_vector)
        k = int(np.floor(middle_length / w))
        if k == 0:
            k = 1
        step = middle_vector / k
        split_points = [p0.copy(), s1.copy()]
        for i in range(1, k):
            new_point = s1 + i * step
            split_points.append(new_point)
        split_points.append(e1.copy())
        split_points.append(p1.copy())
        return split_points

def tessellate_polygon2(polygon, width):
    def process_edge(p0, p1, w):
        vector = p1 - p0
        L = np.linalg.norm(vector)
        if L < 3 * w:
            return np.array([p0, p1])
        dir_unit = vector / L
        s1 = p0 + w * dir_unit
        e1 = p1 - w * dir_unit
        middle_vector = e1 - s1
        middle_length = np.linalg.norm(middle_vector)
        k = int(np.floor(middle_length / w))
        step = middle_vector / k
        i_values = np.arange(1, k)
        intermediate_points = s1 + i_values[:, None] * step
        points = np.vstack([p0[None, :], s1[None, :], intermediate_points, e1[None, :], p1[None, :]])
        return points

    n = polygon.shape[0]
    if n == 0:
        return polygon.copy()
    new_edges = []
    for i in range(n):
        p0 = polygon[i]
        p1 = polygon[(i + 1) % n]
        edge_points = process_edge(p0, p1, width)
        new_edges.append(edge_points[:-1])
    if not new_edges:
        return np.empty((0, 2), dtype=polygon.dtype)
    final_array = np.vstack(new_edges)
    final_array = np.vstack([final_array, final_array[0:1]])
    return final_array

def tessellate_polygon3(polygon, width):
    if len(polygon) == 0:
        return np.empty((0, 2), dtype=polygon.dtype), np.array([], dtype=int)
    
    p_next = np.roll(polygon, -1, axis=0)
    vectors = p_next - polygon
    L = np.linalg.norm(vectors, axis=1)
    n = len(polygon)
    
    mask = L >= 3 * width
    k = np.where(mask, ((L - 2 * width) // width).astype(int), 0)
    counts = np.where(mask, k + 3, 2)
    counts_minus_1 = counts - 1
    
    total_points = np.sum(counts_minus_1) + 1  # +1 for closing
    final_array = np.empty((total_points, 2), dtype=polygon.dtype)
    
    # Compute start indices for each edge
    start_indices = np.cumsum(counts_minus_1) - counts_minus_1
    
    # Process all edges
    dir_unit = vectors / L[:, None]
    s1 = polygon + width * dir_unit
    e1 = p_next - width * dir_unit
    
    # Handle non-masked edges (counts == 2)
    non_masked = ~mask
    final_array[start_indices[non_masked]] = polygon[non_masked]
    
    # Handle masked edges (counts > 2)
    if np.any(mask):
        masked_indices = np.where(mask)[0]
        k_masked = k[mask]
        max_k = np.max(k_masked) if len(k_masked) > 0 else 0
        
        # Generate intermediate indices using broadcasting
        t = np.arange(1, max_k)[None, :]
        valid = t <= (k_masked - 1)[:, None]
        
        # Compute intermediate points
        steps = (e1[mask] - s1[mask]) / k_masked[:, None]
        intermediates = s1[mask][:, None, :] + t[:, :, None] * steps[:, None, :]
        intermediates = intermediates[valid]
        
        # Prepare segments for masked edges
        segments = np.concatenate([
            polygon[mask][:, None, :],
            s1[mask][:, None, :],
            intermediates.reshape(len(masked_indices), -1, 2),
            e1[mask][:, None, :]
        ], axis=1)
        
        # Flatten and fill final_array
        lengths = 2 + k_masked  # p0, s1, e1 + intermediates
        flat_segments = np.concatenate(segments)
        indices = np.concatenate([np.arange(s, s + l) for s, l in zip(start_indices[mask], lengths)])
        final_array[indices] = flat_segments
    
    # Close the polygon
    final_array[-1] = final_array[0]
    
    return final_array
'''

if __name__ == "__main__":
    coords = get_geometry()
    print(len(coords))
    polygon = geometry_to_polygon(coords)
    buffer = polygon.buffer(-0.008)
    print(buffer.geom_type)
    print(f'buffer {len(buffer.interiors)}, polygon {len(polygon.interiors)}')
    
    transfer_shapely_to_blender(buffer, "Polygon")