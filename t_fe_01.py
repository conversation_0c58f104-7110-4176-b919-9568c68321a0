import numpy as np
from scipy.optimize import least_squares
import matplotlib.pyplot as plt
import time

# Step 1: Your points here! Replace with your data (Nx2 array, N >= 5 recommended)
# Example: points = np.array([[x1,y1], [x2,y2], [x3,y3], [x4,y4], [x5,y5], ...])
# For demo, generating sample noisy points on an ellipse
def generate_ellipse_points(h, k, a, b, theta, num_points=50, noise=0.1):
    t = np.linspace(0, 2 * np.pi, num_points)
    x = h + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
    y = k + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)
    x += noise * np.random.randn(num_points)
    y += noise * np.random.randn(num_points)
    return np.column_stack((x, y))

true_h, true_k, true_a, true_b, true_theta = 0.0, 0.0, 5.0, 3.0, np.pi / 4
points = generate_ellipse_points(true_h, true_k, true_a, true_b, true_theta, num_points=8)  # Using 10 for demo, but swap with yours

# Specify indices of the 3 must-hit points (0-based, list of 3 integers)
must_hit_indices = [0, 3]  # Example: first three points must be exactly on the ellipse

# Step 2: Define the residual function with weighting for must-hit points
def ellipse_residuals(params, points, must_hit_indices, weight=1000):
    h, k, a, b, theta = params
    x, y = points[:, 0], points[:, 1]
    term1 = ((x - h) * np.cos(theta) + (y - k) * np.sin(theta)) / a
    term2 = (-(x - h) * np.sin(theta) + (y - k) * np.cos(theta)) / b
    residuals = term1**2 + term2**2 - 1
    
    # Apply high weight to must-hit points to force residuals ~0
    weights = np.ones_like(residuals)
    weights[must_hit_indices] = weight  # Big number to prioritize
    return residuals * weights

# Step 3: Initial guess (rough estimate: center at mean, axes around data spread, theta=0)
initial_guess = [np.mean(points[:, 0]), np.mean(points[:, 1]),
                 (np.max(points[:, 0]) - np.min(points[:, 0])) / 2,
                 (np.max(points[:, 1]) - np.min(points[:, 1])) / 2,
                 0.0]

# Add bounds to ensure a > b > 0 and theta in [-pi/2, pi/2] for uniqueness
bounds = ([-np.inf, -np.inf, 0.1, 0.1, -np.pi/2], [np.inf, np.inf, np.inf, np.inf, np.pi/2])

# Step 4: Run the optimization (pass must_hit_indices and points)
time1 = time.time()
result = least_squares(ellipse_residuals, initial_guess, args=(points, must_hit_indices), bounds=bounds)
time2 = time.time()
print(f'Time: {time2-time1}')

# Ensure a > b (swap if necessary)
h, k, a, b, theta = result.x
if a < b:
    a, b = b, a
    theta += np.pi / 2  # Adjust rotation accordingly
    theta = theta % np.pi  # Normalize

# Step 5: Print results, including check for must-hit residuals
print("Optimized parameters:")
print(f"Center: (h={h:.2f}, k={k:.2f})")
print(f"Semi-major axis (a): {a:.2f}")
print(f"Semi-minor axis (b): {b:.2f}")
print(f"Rotation angle (theta, in degrees): {np.degrees(theta):.2f}°")
print("Success:", result.success)
print("Cost (sum of squares):", result.cost)

# Check residuals for must-hit points (should be very close to 0)
must_hit_residuals = ellipse_residuals(result.x, points, must_hit_indices, weight=1)[must_hit_indices]  # weight=1 for check
print("Residuals for must-hit points (should be ~0):", must_hit_residuals)

# Step 6: Plot the results
def plot_ellipse(h, k, a, b, theta, ax, color='r', label='Fitted'):
    t = np.linspace(0, 2 * np.pi, 100)
    x = h + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
    y = k + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)
    ax.plot(x, y, color, label=label)

fig, ax = plt.subplots()
ax.scatter(points[:, 0], points[:, 1], label='Data points')
ax.scatter(points[must_hit_indices, 0], points[must_hit_indices, 1], color='g', label='Must-hit points', s=100)  # Highlight them
plot_ellipse(true_h, true_k, true_a, true_b, true_theta, ax, 'k--', 'True ellipse')  # Demo only, remove if no true ellipse
plot_ellipse(h, k, a, b, theta, ax, 'r-', 'Fitted ellipse')
ax.legend()
ax.set_xlabel('x')
ax.set_ylabel('y')
ax.set_title('Weighted Ellipse Fit (Must-Hit Points in Green)')
ax.axis('equal')  # Equal scaling
plt.show()