import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import LineString

def analyze_normalization():
    """
    Analyze how the normalization works across different ellipse dimensions.
    """
    
    # The normalization explanation
    print("=== ELLIPSE DISTANCE NORMALIZATION ===\n")
    
    print("The ellipse equation is: (x/a)² + (y/b)² = 1")
    print("For any point (x,y), we compute: ellipse_value = (x/a)² + (y/b)²\n")
    
    print("Interpretation:")
    print("- ellipse_value = 1.0  → point is ON the ellipse boundary")
    print("- ellipse_value < 1.0  → point is INSIDE the ellipse")  
    print("- ellipse_value > 1.0  → point is OUTSIDE the ellipse\n")
    
    print("Distance calculation:")
    print("- Outside: distance = sqrt(ellipse_value) - 1")
    print("- Inside:  distance = -(1 - sqrt(ellipse_value))")
    print("- This gives distance in 'ellipse radii units'\n")
    
    # Demonstrate with examples
    print("=== EXAMPLES ===\n")
    
    # Test point
    test_point = np.array([6, 4])
    
    ellipses = [
        ("Small ellipse", [0, 0, 3, 2]),      # a=3, b=2
        ("Medium ellipse", [0, 0, 6, 4]),     # a=6, b=4 (2x scale)
        ("Large ellipse", [0, 0, 12, 8]),     # a=12, b=8 (4x scale)
        ("Thin ellipse", [0, 0, 10, 1]),      # a=10, b=1 (very elongated)
        ("Wide ellipse", [0, 0, 2, 8]),       # a=2, b=8 (very wide)
    ]
    
    print(f"Test point: {test_point}")
    print("Ellipse dimensions (cx, cy, a, b) → ellipse_value → normalized_distance")
    print("-" * 70)
    
    for name, (cx, cy, a, b) in ellipses:
        # Compute ellipse value
        x, y = test_point[0] - cx, test_point[1] - cy
        ellipse_value = (x/a)**2 + (y/b)**2
        
        # Compute normalized distance
        if ellipse_value > 1:
            norm_distance = np.sqrt(ellipse_value) - 1
            status = "OUTSIDE"
        else:
            norm_distance = -(1 - np.sqrt(ellipse_value))
            status = "INSIDE"
        
        print(f"{name:15s} ({cx:2}, {cy:2}, {a:2}, {b:2}) → {ellipse_value:6.3f} → {norm_distance:6.3f} ({status})")
    
    print(f"\n=== CONSISTENCY CHECK ===\n")
    
    # Show how the same relative position gives same normalized distance
    base_ellipse = [0, 0, 4, 2]  # a=4, b=2
    scaled_ellipse = [0, 0, 8, 4]  # a=8, b=4 (2x scale)
    
    # Points at same relative positions
    point1 = [6, 3]      # 1.5 * a, 1.5 * b for base ellipse
    point2 = [12, 6]     # 1.5 * a, 1.5 * b for scaled ellipse
    
    def compute_distance(point, ellipse_params):
        cx, cy, a, b = ellipse_params
        x, y = point[0] - cx, point[1] - cy
        ellipse_value = (x/a)**2 + (y/b)**2
        if ellipse_value > 1:
            return np.sqrt(ellipse_value) - 1
        else:
            return -(1 - np.sqrt(ellipse_value))
    
    dist1 = compute_distance(point1, base_ellipse)
    dist2 = compute_distance(point2, scaled_ellipse)
    
    print(f"Base ellipse (a=4, b=2) with point (6,3):   distance = {dist1:.6f}")
    print(f"2x ellipse   (a=8, b=4) with point (12,6):  distance = {dist2:.6f}")
    print(f"Difference: {abs(dist1 - dist2):.10f}")
    print("→ Same relative position = same normalized distance!\n")
    
    # Show what happens with actual Euclidean distances
    print("=== EUCLIDEAN vs NORMALIZED DISTANCE ===\n")
    
    point = [5, 0]  # Point on x-axis
    
    print("Point (5,0) relative to different ellipses:")
    print("Ellipse (a,b)     | Normalized | Euclidean to boundary")
    print("-" * 50)
    
    for name, (cx, cy, a, b) in ellipses[:3]:  # Just first 3 for clarity
        norm_dist = compute_distance(point, [cx, cy, a, b])
        
        # Approximate Euclidean distance to boundary (simplified for x-axis point)
        if point[0] > a:
            euclidean_dist = point[0] - a  # Distance to closest point on ellipse
        else:
            euclidean_dist = a - point[0]  # Distance to ellipse boundary
            
        print(f"({a:2},{b:2})           | {norm_dist:8.4f}   | {euclidean_dist:8.1f}")
    
    print(f"\n=== WHY THIS NORMALIZATION IS USEFUL ===\n")
    
    print("1. SCALE INVARIANCE:")
    print("   - A linestring that 'fits the same way' in differently-sized ellipses")
    print("     will have the same normalized distance")
    print("   - Your optimization won't be biased by coordinate system scale\n")
    
    print("2. CONSISTENT MEANING:")
    print("   - distance = 0.5 always means 'halfway to double the ellipse size'")
    print("   - distance = -0.2 always means '20% of the way from center to boundary'\n")
    
    print("3. OPTIMIZATION FRIENDLY:")
    print("   - CMA-ES can use the same parameter ranges regardless of ellipse size")
    print("   - Convergence behavior is more predictable\n")
    
    print("4. SHAPE AWARENESS:")
    print("   - The normalization accounts for ellipse aspect ratio")
    print("   - A point far from a thin ellipse gets higher penalty than")
    print("     the same point far from a wide ellipse covering the same area")


def demonstrate_with_linestring():
    """
    Show how normalization works with actual linestrings.
    """
    print("\n" + "="*60)
    print("LINESTRING NORMALIZATION DEMONSTRATION")
    print("="*60 + "\n")
    
    # Create a test linestring
    coords = [(2, 1), (4, 2), (3, 4), (1, 3)]
    linestring = LineString(coords)
    
    print(f"Test linestring coordinates: {coords}\n")
    
    # Test with proportionally scaled ellipses
    ellipses = [
        ("Reference", [2, 2, 2, 1.5]),       # Base ellipse  
        ("2x scale", [2, 2, 4, 3]),          # Everything doubled
        ("3x scale", [2, 2, 6, 4.5]),        # Everything tripled
    ]
    
    from ellipse_linestring_distance import linestring_ellipse_distance
    
    print("Ellipse scaling test:")
    print("Scale | Ellipse (cx,cy,a,b) | Normalized Distance")
    print("-" * 45)
    
    for name, (cx, cy, a, b) in ellipses:
        distance = linestring_ellipse_distance(linestring, (cx, cy), a, b, 0)
        print(f"{name:8s} | ({cx},{cy},{a:3.1f},{b:3.1f})    | {distance:12.6f}")
    
    print(f"\n→ Notice how the normalized distances are identical!")
    print(f"→ This is because the linestring has the same relative position")
    print(f"→ to each proportionally-scaled ellipse.\n")
    
    # Show what happens with different aspect ratios
    print("Aspect ratio test (same area ≈ π*3*2.25 ≈ 21.2):")
    aspect_ellipses = [
        ("Circular", [2, 2, 2.6, 2.6]),      # Nearly circular
        ("Wide", [2, 2, 4, 1.7]),            # Wide ellipse  
        ("Tall", [2, 2, 1.7, 4]),            # Tall ellipse
    ]
    
    print("Shape  | Ellipse (cx,cy,a,b) | Normalized Distance")
    print("-" * 45)
    
    for name, (cx, cy, a, b) in aspect_ellipses:
        distance = linestring_ellipse_distance(linestring, (cx, cy), a, b, 0)
        print(f"{name:8s} | ({cx},{cy},{a:3.1f},{b:3.1f})    | {distance:12.6f}")
    
    print(f"\n→ Different aspect ratios give different distances")
    print(f"→ This is correct: the linestring fits differently in each shape")


if __name__ == "__main__":
    analyze_normalization()
    # demonstrate_with_linestring()  # Commented out since we need the main function