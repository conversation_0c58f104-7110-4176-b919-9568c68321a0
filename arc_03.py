import numpy as np
import matplotlib.pyplot as plt

# 1) Quintic Hermite basis functions
def hermite_basis(t):
    # t: array of shape (N,)
    h0 = 1 - 10*t**3 + 15*t**4 - 6*t**5
    h1 = t - 6*t**3 + 8*t**4 - 3*t**5
    h2 = 0.5*(t**2 - 3*t**3 + 3*t**4 -   t**5)
    h3 =   10*t**3 - 15*t**4 + 6*t**5
    h4 = - 4*t**3 + 7*t**4 - 3*t**5
    h5 = 0.5*(t**3 - 2*t**4 +   t**5)
    return h0, h1, h2, h3, h4, h5

def hermite_quintic(P0, V0, A0, P1, V1, A1, num=200):
    """Build a quintic Hermite curve from P0→P1 with
       specified first‐ and second‐derivatives."""
    t = np.linspace(0,1,num)
    h0,h1,h2,h3,h4,h5 = hermite_basis(t)
    # Broadcast & combine:
    C = (h0[:,None]*P0
       +h1[:,None]*V0
       +h2[:,None]*A0
       +h3[:,None]*P1
       +h4[:,None]*V1
       +h5[:,None]*A1)
    return C

# 2) Helpers to extract endpoint geometry from a circular arc
def arc_endpoint_geometry(center, radius, theta):
    """
    Given arc: P(θ) = center + radius*[cos θ, sin θ],
    returns (position, unit_tangent, 2nd‐derivative).
    We parametrize by arc‐length so that |P'(θ)|=1.
    """
    x = center + radius * np.array([np.cos(theta), np.sin(theta)])
    # unit‐tangent is derivative w.r.t. arc‐length:
    ut = np.array([-np.sin(theta),  np.cos(theta)])
    # curvature = 1/radius; normal = -cos,sin for CCW arcs
    curvature = 1.0/radius
    # second derivative vector = curvature * normal
    un = np.array([-np.cos(theta), -np.sin(theta)])  # points toward center
    acc = curvature * un
    return x, ut, acc

# 3) Sample data: two arcs and point X
c1, r1 = np.array([0.0,0.0]), 1.2
start1, end1 = np.deg2rad(30), np.deg2rad(120)

c2, r2 = np.array([3.0,1.5]), 0.8
start2, end2 = np.deg2rad(210), np.deg2rad(300)

# Interpolation point X
X = np.array([2.0, 0.5])

# Extract end‐of‐a₁ geometry
P0, T0, A0 = arc_endpoint_geometry(c1, r1, end1)

# Extract start‐of‐a₂ geometry
P3, T3, A3 = arc_endpoint_geometry(c2, r2, start2)

# 4) Pick an intermediate tangent & curvature at X
# Here we simply bisect the two tangents and average curvatures
Tv = T0 + T3
Tv /= np.linalg.norm(Tv)
Kv = 0.5*(1/r1 + 1/r2)
# normal to Tv (rotate CCW by 90°)
Nv = np.array([-Tv[1], Tv[0]])
Av = Kv * Nv

# 5) Build two Hermite segments
C1 = hermite_quintic(P0,  T0,  A0,
                     X,   Tv,  Av,  num=200)
C2 = hermite_quintic(X,   Tv,  Av,
                     P3,  T3,  A3,  num=200)

# 6) Plot everything
fig, ax = plt.subplots(figsize=(6,6))
# plot original arcs
theta1 = np.linspace(start1, end1, 100)
arc1 = c1 + r1*np.vstack((np.cos(theta1), np.sin(theta1))).T
theta2 = np.linspace(start2, end2, 100)
arc2 = c2 + r2*np.vstack((np.cos(theta2), np.sin(theta2))).T

ax.plot(arc1[:,0], arc1[:,1], 'b-', label='Arc 1')
ax.plot(arc2[:,0], arc2[:,1], 'g-', label='Arc 2')
ax.plot(C1[:,0], C1[:,1], 'r--', linewidth=2, label='Hermite 1')
ax.plot(C2[:,0], C2[:,1], 'r--', linewidth=2, label='Hermite 2')
ax.plot(*X, 'ko', label='X')
ax.set_aspect('equal', 'box')
ax.legend()
ax.set_title('G²‐Continuous Connector via Two Quintic Hermite Curves')
plt.show()