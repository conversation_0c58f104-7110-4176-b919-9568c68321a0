import numpy as np
from scipy.optimize import dual_annealing
from math import pi, sin, cos
import time

def fit_ellipse_segment(x_data, y_data, h_bounds=(0.1, 0.5), maxiter=1000):
    """
    Fits a parametric ellipse segment to 2D points using least squares and simulated annealing.
    
    Parameters:
    - x_data, y_data: numpy arrays of x and y coordinates (same length n).
    - h_bounds: tuple (v1, v2) for h range (default from paper: 0.1 to 0.5).
    - maxiter: max iterations for dual_annealing.
    
    Returns:
    - dict with 'a', 'b', 'p', 'q', 'theta1', 'h', 'min_S', 'error_d', 'z_values', 'predicted_points'.
    """
    n = len(x_data)
    if n != len(y_data) or n < 2:
        raise ValueError("Input data must have at least 2 points and matching lengths.")
    
    def objective(params):
        theta1, h = params
        # Compute z_i = theta1 + (i-1)*h
        indices = np.arange(n)
        z = theta1 + indices * h
        
        # Compute cos(z) and sin(z)
        c = np.cos(z)
        s = np.sin(z)
        
        # Linear least squares for x: fit to [1, cos(z)]
        A = np.c_[np.ones(n), c]
        a_p = np.linalg.lstsq(A, x_data, rcond=None)[0]  # [a, p]
        
        # For y: fit to [1, sin(z)]
        B = np.c_[np.ones(n), s]
        b_q = np.linalg.lstsq(B, y_data, rcond=None)[0]  # [b, q]
        
        # Compute predicted points
        x_pred = a_p[0] + a_p[1] * c
        y_pred = b_q[0] + b_q[1] * s
        
        # Sum of squared errors S
        S = np.sum((x_pred - x_data)**2 + (y_pred - y_data)**2)
        return S
    
    # Bounds: [theta1_bounds, h_bounds]
    bounds = [(0, 2 * pi), h_bounds]
    
    # Run simulated annealing (dual_annealing is SciPy's implementation)
    result = dual_annealing(objective, bounds, maxiter=maxiter)
    
    # Extract optimal theta1 and h
    theta1_opt, h_opt = result.x
    
    # Recompute optimal a, b, p, q, z, predictions, and errors
    z_opt = theta1_opt + np.arange(n) * h_opt
    c_opt = np.cos(z_opt)
    s_opt = np.sin(z_opt)
    
    A_opt = np.c_[np.ones(n), c_opt]
    a_p_opt = np.linalg.lstsq(A_opt, x_data, rcond=None)[0]
    a, p = a_p_opt
    
    B_opt = np.c_[np.ones(n), s_opt]
    b_q_opt = np.linalg.lstsq(B_opt, y_data, rcond=None)[0]
    b, q = b_q_opt
    
    x_pred_opt = a + p * c_opt
    y_pred_opt = b + q * s_opt
    
    # Min S (sum of squared errors)
    min_S = result.fun
    
    # Error distance d = sum of Euclidean distances (as in paper Equation 5)
    distances = np.sqrt((x_pred_opt - x_data)**2 + (y_pred_opt - y_data)**2)
    error_d = np.sum(distances)
    
    return {
        'a': a,
        'b': b,
        'p': p,
        'q': q,
        'theta1': theta1_opt,
        'h': h_opt,
        'min_S': min_S,
        'error_d': error_d,
        'z_values': z_opt,
        'predicted_points': np.c_[x_pred_opt, y_pred_opt]
    }

def compute_curvature_radius(a, b, p, q, z):
    """
    Computes radius of curvature R at a given z using paper's Equation (6).
    
    Parameters:
    - a, b, p, q: ellipse parameters.
    - z: scalar or array of z values.
    
    Returns:
    - R: radius of curvature (scalar or array).
    """
    z = np.asarray(z)  # Handle scalar or array
    sin_z = np.sin(z)
    cos_z = np.cos(z)
    
    # Derivatives
    xp = -p * sin_z
    yp = q * cos_z
    xpp = -p * cos_z
    ypp = -q * sin_z
    
    numerator = (xp**2 + yp**2)**1.5
    denominator = np.abs(xp * ypp - yp * xpp)
    R = numerator / denominator
    return R if R.size > 1 else R.item()  # Return scalar if input was scalar

# Example usage with sample data from paper's Table 1
if __name__ == "__main__":
    # Sample data from paper
    x_data = np.array([5.52445, 4.36629, 3.10566, 1.85366, 0.70330, -0.32466, -1.10022, -1.74638, -2.07959])
    y_data = np.array([3.83790, 4.12056, 4.11217, 3.99601, 3.67780, 3.42801, 2.99983, 2.67906, 2.18775])
    
    # Fit the model
    time1 = time.time()
    fit_results = fit_ellipse_segment(x_data, y_data)
    time2 = time.time()
    print(f'Time: {time2-time1}')
    print("Fitted Parameters:")
    for key, value in fit_results.items():
        if key not in ['z_values', 'predicted_points']:
            print(f"{key}: {value}")
    
    # Compute curvature radius at each fitted z (example)
    z_values = fit_results['z_values']
    R_values = compute_curvature_radius(fit_results['a'], fit_results['b'], fit_results['p'], fit_results['q'], z_values)
    print("\nRadius of Curvature at each z:")
    print(R_values)