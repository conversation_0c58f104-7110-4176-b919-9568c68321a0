import time
import numpy as np
from scipy.optimize import least_squares


    
# Residual function - algebraic distance
def residuals(params, points):
    cx, cy, a, b, theta = params
    
    # Transform points to ellipse coordinate system
    cos_t = np.cos(theta)
    sin_t = np.sin(theta)
    
    # Translate to origin
    xc = points[:, 0] - cx
    yc = points[:, 1] - cy
    
    # Rotate to align with ellipse axes
    xr = xc * cos_t + yc * sin_t
    yr = -xc * sin_t + yc * cos_t
    
    # Algebraic distance from ellipse equation: (x/a)² + (y/b)² - 1 = 0
    # Scale by axis lengths to normalize residuals
    res = (xr/a)**2 + (yr/b)**2 - 1
    
    # Weight by axis size to handle large coordinate values
    return res * np.sqrt(a * b)    

# Jacobian function
def jacobian(params, points):
    cx, cy, a, b, theta = params
    cos_t = np.cos(theta)
    sin_t = np.sin(theta)
    
    xc = points[:, 0] - cx
    yc = points[:, 1] - cy
    
    xr = xc * cos_t + yc * sin_t
    yr = -xc * sin_t + yc * cos_t
    
    f = (xr**2 / a**2) + (yr**2 / b**2) - 1
    w = np.sqrt(a * b)
    
    # Partial derivatives for df/dxr and df/dyr
    df_dxr = 2 * xr / a**2
    df_dyr = 2 * yr / b**2
    
    # df for each parameter (through xr/yr where applicable)
    df_cx = df_dxr * (-cos_t) + df_dyr * sin_t
    df_cy = df_dxr * (-sin_t) + df_dyr * (-cos_t)
    
    d_xr_dtheta = -xc * sin_t + yc * cos_t
    d_yr_dtheta = -xc * cos_t - yc * sin_t
    df_theta = df_dxr * d_xr_dtheta + df_dyr * d_yr_dtheta
    
    df_a = -2 * (xr**2 / a**3)
    df_b = -2 * (yr**2 / b**3)
    
    # Full partials for res = f * w
    jac = np.empty((len(points), 5))
    jac[:, 0] = df_cx * w  # cx
    jac[:, 1] = df_cy * w  # cy
    jac[:, 2] = df_a * w + f * (0.5 * w / a)  # a
    jac[:, 3] = df_b * w + f * (0.5 * w / b)  # b
    jac[:, 4] = df_theta * w  # theta
    
    return jac
    


# Test with your exact data
points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

# Your bounds format: (lower_bounds, upper_bounds)
bounds = ([-18170, -9679, 30, 20, np.deg2rad(320)],  # min: x,y, a,b, theta
          [-18130, -9625, 60, 40, np.deg2rad(340)])   # max

# Your initial guess
initial_guess = np.array([-18148, -9653, 45, 32, np.deg2rad(330)], dtype=np.float64)

# Fit
import time
time1 = time.time()

# Optimize
result = least_squares(
    residuals,
    initial_guess,
    jac=jacobian,
    bounds=bounds,
    args=(points,),
    ftol=1e-6,
    xtol=1e-6,
    gtol=1e-6,
    max_nfev=1000,
    diff_step=1e-8  # Smaller step for numerical derivatives
    )
    
# Extract results
cx, cy, a, b, theta = result.x

# Ensure a >= b
if a < b:
    a, b = b, a
    theta = theta + np.pi/2

# Normalize theta to [-pi, pi]
theta = np.arctan2(np.sin(theta), np.cos(theta))

time2 = time.time()
print(f'Time: {time2-time1}')

res = {    
    'center': (cx, cy),
    'axes': (a, b),
    'theta': theta,
    'params': np.array([cx, cy, a, b, theta]),
    'success': result.success,
    'cost': result.cost,
    'nfev': result.nfev    
}

print(res)
