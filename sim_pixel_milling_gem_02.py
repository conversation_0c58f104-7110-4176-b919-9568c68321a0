import numpy as np
import math
import time

# --- Simulation Parameters (Keep as before, or adjust as needed) ---
# Tool
tool_diameter = 10.0
tool_radius = tool_diameter / 2.0
num_teeth = 4

# Process
axial_depth_of_cut = 5.0
spindle_speed_rpm = 4000
feed_per_tooth = 0.05
feed_rate_vf = spindle_speed_rpm * num_teeth * feed_per_tooth

# Simulation Grid
pixel_size = 0.05 # Smaller pixel size helps outline accuracy
workpiece_width_mm = 50.0
workpiece_height_mm = 30.0
padding_mm = tool_diameter
sim_width_mm = workpiece_width_mm + 2 * padding_mm
sim_height_mm = workpiece_height_mm + 2 * padding_mm

# Simulation Steps
sim_step_distance = 0.1 # mm (ds) - Adjust for path resolution

print(f"Feed Rate (vf): {feed_rate_vf:.2f} mm/min")
print(f"Pixel Size (Delta): {pixel_size} mm")
print(f"Sim Step Distance (ds): {sim_step_distance} mm")


# --- Helper Functions ---
def world_to_pixel(xy_mm, origin_xy_mm, p_size):
    px = int(round((xy_mm[0] - origin_xy_mm[0]) / p_size))
    py = int(round((xy_mm[1] - origin_xy_mm[1]) / p_size))
    return px, py

def create_tool_matrices(diameter, p_size):
    """
    Creates:
    1. A binary matrix representing the circular tool's FILLED area.
    2. A list of relative pixel coordinates (dr, dc) for the tool's FULL OUTLINE.
    3. The offset from the matrix corner to the tool center pixel.
    """
    radius = diameter / 2.0
    radius_pixels = radius / p_size
    tool_matrix_size_pixels = int(math.ceil(diameter / p_size))
    if tool_matrix_size_pixels % 2 == 0:
        tool_matrix_size_pixels += 1
    center_pixel = tool_matrix_size_pixels // 2

    tool_matrix_filled = np.zeros((tool_matrix_size_pixels, tool_matrix_size_pixels), dtype=np.uint8)
    # Store ALL outline pixels relative to the center
    tool_full_outline_indices_relative = []
    outline_tolerance = 0.6 # Pixels within this distance (in pixels) of the ideal radius

    for r in range(tool_matrix_size_pixels):
        for c in range(tool_matrix_size_pixels):
            dr, dc = r - center_pixel, c - center_pixel
            dist = math.sqrt(dr**2 + dc**2)

            # Fill the tool area matrix
            if dist <= radius_pixels + 1e-6:
                 tool_matrix_filled[r, c] = 1

            # Identify ALL outline pixels
            if abs(dist - radius_pixels) < outline_tolerance:
                 # Store as (row_offset_from_center, col_offset_from_center)
                 tool_full_outline_indices_relative.append((dr, dc))

    # print(f"Debug: Total FULL outline pixels found: {len(tool_full_outline_indices_relative)}")
    if not tool_full_outline_indices_relative:
         print("Warning: No outline pixels found. Check parameters/tolerance.")

    # Return the FILLED matrix and the FULL outline coordinates
    return tool_matrix_filled, tool_full_outline_indices_relative, center_pixel

# --- create_workpiece_matrix, generate_tool_path_straight, generate_tool_path_slot remain the same ---
def create_workpiece_matrix(sim_w_mm, sim_h_mm, wp_w_mm, wp_h_mm, pad_mm, p_size):
    sim_width_pixels = int(round(sim_w_mm / p_size))
    sim_height_pixels = int(round(sim_h_mm / p_size))
    workpiece_matrix = np.zeros((sim_height_pixels, sim_width_pixels), dtype=np.uint8)
    wp_x_min_mm = pad_mm
    wp_x_max_mm = pad_mm + wp_w_mm
    wp_y_min_mm = pad_mm
    wp_y_max_mm = pad_mm + wp_h_mm
    wp_start_px, wp_start_py = world_to_pixel((wp_x_min_mm, wp_y_min_mm), (0,0), p_size)
    wp_end_px, wp_end_py = world_to_pixel((wp_x_max_mm, wp_y_max_mm), (0,0), p_size)
    wp_start_py = max(0, wp_start_py)
    wp_start_px = max(0, wp_start_px)
    wp_end_py = min(sim_height_pixels, wp_end_py)
    wp_end_px = min(sim_width_pixels, wp_end_px)
    if wp_end_py > wp_start_py and wp_end_px > wp_start_px:
        workpiece_matrix[wp_start_py:wp_end_py, wp_start_px:wp_end_px] = 1
    else:
        print("Warning: Workpiece dimensions resulted in zero area in pixel grid.")
    sim_origin_mm = (0.0, 0.0)
    return workpiece_matrix, sim_origin_mm

def generate_tool_path_straight(start_xy, end_xy, step_ds):
    start_vec = np.array(start_xy)
    end_vec = np.array(end_xy)
    path_vector = end_vec - start_vec
    total_dist = np.linalg.norm(path_vector)
    path = [tuple(start_vec)]
    if total_dist <= 1e-9: return path
    direction = path_vector / total_dist
    num_steps = int(math.floor(total_dist / step_ds))
    for i in range(1, num_steps + 1):
        path.append(tuple(start_vec + direction * i * step_ds))
    if not np.allclose(path[-1], end_xy, atol=step_ds*0.1):
         path.append(tuple(end_xy))
    elif total_dist > 0 and len(path) == 1:
         path.append(tuple(end_xy))
    return path

def generate_tool_path_slot(wp_x_start_mm, wp_y_center_mm, wp_width_mm, tool_rad_mm, pad_mm, step_ds):
    start_x = wp_x_start_mm - pad_mm / 2.0
    end_x = wp_x_start_mm + wp_width_mm + pad_mm / 2.0
    y_pos = wp_y_center_mm
    start_point = (start_x, y_pos)
    end_point = (end_x, y_pos)
    return generate_tool_path_straight(start_point, end_point, step_ds)

# --- Setup ---
print("Creating Tool Matrices...")
tool_matrix_filled, full_outline_indices, tool_matrix_center_offset = create_tool_matrices(
    tool_diameter, pixel_size
)
print(f"Tool filled matrix shape: {tool_matrix_filled.shape}")
print(f"Total FULL Outline Pixels: {len(full_outline_indices)}")
if not full_outline_indices:
    raise ValueError("Cannot simulate with zero outline pixels. Check parameters.")

print("Creating Workpiece Matrix...")
workpiece, sim_origin = create_workpiece_matrix(
    sim_width_mm, sim_height_mm,
    workpiece_width_mm, workpiece_height_mm,
    padding_mm, pixel_size
)
initial_workpiece = workpiece.copy()
sim_height_pixels, sim_width_pixels = workpiece.shape
print(f"Workpiece matrix shape: {workpiece.shape}")

# --- Define Tool Path (Example: Simple Slot) ---
wp_center_y_mm = padding_mm + workpiece_height_mm / 2.0
tool_path_mm = generate_tool_path_slot(
    padding_mm, wp_center_y_mm, workpiece_width_mm,
    tool_radius, padding_mm, sim_step_distance
)
print(f"Generated tool path with {len(tool_path_mm)} steps.")

# --- Run Simulation ---
results_engagement_angle = []
path_lengths = []
current_path_length = 0.0
# Store the last valid direction vector for cases where the tool doesn't move
last_valid_direction_vec = np.array([1.0, 0.0]) # Default to +X initially

start_time = time.time()

n_steps = len(tool_path_mm)
for i, tool_center_mm in enumerate(tool_path_mm):

    # --- 1. Calculate Path Length ---
    if i > 0:
        dist_moved = np.linalg.norm(np.array(tool_center_mm) - np.array(tool_path_mm[i-1]))
        current_path_length += dist_moved
    path_lengths.append(current_path_length)

    # --- 2. Determine Current Movement Direction ---
    current_pos = np.array(tool_center_mm)
    direction_vec = np.array([0.0, 0.0]) # Initialize

    if i == 0:
        # First step: Look ahead if possible
        if n_steps > 1:
            next_pos = np.array(tool_path_mm[1])
            move_vec = next_pos - current_pos
        else:
            move_vec = np.array([0.0, 0.0]) # No movement if only one point
    else:
        # Subsequent steps: Look behind
        prev_pos = np.array(tool_path_mm[i-1])
        move_vec = current_pos - prev_pos

    # Normalize the movement vector to get direction
    move_norm = np.linalg.norm(move_vec)
    if move_norm > 1e-9: # Check for actual movement
        direction_vec = move_vec / move_norm
        last_valid_direction_vec = direction_vec # Update last valid direction
    else:
        # No movement or first step with no lookahead: use last valid direction
        direction_vec = last_valid_direction_vec

    # --- 3. Calculate Engagement Angle using Dynamic Outline Method ---
    tool_center_px, tool_center_py = world_to_pixel(tool_center_mm, sim_origin, pixel_size)
    contacted_outline_pixels = 0
    total_engageable_outline_pixels_dynamic = 0
    dot_product_threshold = 1e-6 # Consider > 0 for engageable

    for dr, dc in full_outline_indices:
        # Relative vector from tool center to this outline pixel
        # Treating dc as x, dr as y for vector math consistency
        outline_vec = np.array([dc, dr])

        # Check if this pixel is facing the direction of travel
        dot_product = np.dot(direction_vec, outline_vec)

        if dot_product > dot_product_threshold:
            # This pixel is potentially engageable
            total_engageable_outline_pixels_dynamic += 1

            # Calculate absolute pixel coordinates in the workpiece grid
            px = tool_center_px + dc
            py = tool_center_py + dr

            # Check bounds
            if 0 <= py < sim_height_pixels and 0 <= px < sim_width_pixels:
                # Check if material exists at this outline pixel location
                if workpiece[py, px] == 1:
                    contacted_outline_pixels += 1

    # Calculate the angle (degrees)
    engagement_angle_deg = 0.0
    if total_engageable_outline_pixels_dynamic > 0:
         # Max angle represented by the engageable half is 180 deg
        engagement_angle_deg = (contacted_outline_pixels / total_engageable_outline_pixels_dynamic) * 180.0
    elif len(full_outline_indices)>0: # Handle case where no pixels face forward (e.g., tool inside material fully?)
        # Check if tool center is inside material - if so, maybe 180? Or check full outline contact?
        # Let's check full outline contact as a fallback measure
        full_contact = 0
        for dr, dc in full_outline_indices:
            px = tool_center_px + dc
            py = tool_center_py + dr
            if 0 <= py < sim_height_pixels and 0 <= px < sim_width_pixels:
                if workpiece[py, px] == 1:
                    full_contact += 1
        if full_contact > 0: # If any part touches, but none face forward (unlikely)
             engagement_angle_deg = 180.0 # Assign max angle? Or calculate based on full_contact/len(full_outline)? Let's use 180 for now.


    results_engagement_angle.append(engagement_angle_deg)

    # --- 4. Update Workpiece State (Using Full Tool Area - Same as before) ---
    y_start = tool_center_py - tool_matrix_center_offset
    y_end = y_start + tool_matrix_filled.shape[0]
    x_start = tool_center_px - tool_matrix_center_offset
    x_end = x_start + tool_matrix_filled.shape[1]
    y_start_clip = max(0, y_start)
    y_end_clip = min(sim_height_pixels, y_end)
    x_start_clip = max(0, x_start)
    x_end_clip = min(sim_width_pixels, x_end)
    tool_y_start_offset = y_start_clip - y_start
    tool_y_end_offset = tool_matrix_filled.shape[0] - (y_end - y_end_clip)
    tool_x_start_offset = x_start_clip - x_start
    tool_x_end_offset = tool_matrix_filled.shape[1] - (x_end - x_end_clip)
    workpiece_slice = workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip]
    tool_slice_filled = tool_matrix_filled[tool_y_start_offset:tool_y_end_offset,
                                           tool_x_start_offset:tool_x_end_offset]
    if workpiece_slice.shape == tool_slice_filled.shape and workpiece_slice.size > 0:
        overlap = workpiece_slice & tool_slice_filled
        workpiece[y_start_clip:y_end_clip, x_start_clip:x_end_clip][overlap == 1] = 0

    # Optional: Print progress
    if (i + 1) % 100 == 0 or i == n_steps - 1:
        print(f"Step {i+1}/{n_steps}, "
              f"Dir: [{direction_vec[0]:.2f},{direction_vec[1]:.2f}], "
              f"Engageable Pix: {total_engageable_outline_pixels_dynamic}, "
              f"Contact Pix: {contacted_outline_pixels}, "
              f"Angle: {engagement_angle_deg:.2f} deg")


end_time = time.time()
print(f"\nSimulation finished in {end_time - start_time:.4f} seconds.")

# --- Output / Visualization (Same as before) ---
print("\nSample Engagement Angles:")
sample_indices = np.linspace(0, len(results_engagement_angle)-1, 10, dtype=int)
for idx in sample_indices:
    print(f"Path Length {path_lengths[idx]:.2f} mm: Angle = {results_engagement_angle[idx]:.2f} deg")