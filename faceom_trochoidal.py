"""
faceom_trochoidal.py
Quick demonstration of the FACEOM constant‑engagement algorithm
applied to trochoidal milling of a straight slot.
<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> – IJAMT 103 (2019) 4293‑4305
"""

import numpy as np
import matplotlib.pyplot as plt

# ------------------------------------------------------------
# basic helpers
# ------------------------------------------------------------
def rot(vec, ang_deg):
    """Rotate 2‑D vector by ang_deg CCW."""
    ang = np.deg2rad(ang_deg)
    c, s = np.cos(ang), np.sin(ang)
    return np.array([c*vec[0] - s*vec[1],
                     s*vec[0] + c*vec[1]])

def intersect_line_circle(P, v, C, r):
    """
    Intersection of a half‑line P + s·v (s ≥ 0) with a circle (C,r).
    Returns the *first* positive intersection point.
    """
    # Quadratic in s: |(P + s v) - C|² = r²
    d  = P - C
    b  = 2.0*np.dot(v, d)
    c  = np.dot(d, d) - r*r
    disc = b*b - 4*c
    if disc < 0:                      # should not happen in normal runs
        return None
    s = (-b - np.sqrt(disc)) / 2.0    # smaller positive root
    if s <= 0:
        s = (-b + np.sqrt(disc)) / 2.0
    return P + s*v


# ------------------------------------------------------------
# FACEOM generator for a *straight* left‑hand wall  (slot milling)
# ------------------------------------------------------------
def faceom_trochoidal(slot_length      = 40.0,   # mm
                      tool_diam        = 10.0,   # mm
                      theta_deg        = 60.0,   # constant engagement
                      dt_wall          = 0.50,   # wall‑parameter step  (mm)
                      slot_width       = 15.0):  # mm  (≥ tool_D + 2·ae)

    r = tool_diam/2.0
    theta = np.deg2rad(theta_deg)

    # --- radial immersion that corresponds to theta
    #     θ = arccos((r - ae)/r)  →  ae = r·(1 - cos θ)
    ae = r * (1.0 - np.cos(theta))

    # --------------------------------------------------------
    # 0.  initialise (Fig. 5 of the paper)
    # --------------------------------------------------------
    C  = np.array([0.0, 0.0])               # first point on wall (y = 0)
    t_hat = np.array([1.0, 0.0])            # tangent of wall (+X)
    ω = theta_deg + 90.0                    # β = 0  →  ω = θ + 90°
    P  = C + r * rot(t_hat,  ω)             # tool centre
    d  = C - P
    α = 90.0 - theta_deg
    v  = rot(d/np.linalg.norm(d), -α)       # initial feed dir (unit)

    path = [P.copy()]                       # store tool‑centre points

    # --------------------------------------------------------
    # 1.  step through wall points, build polyline
    # --------------------------------------------------------
    x_w = 0.0
    while x_w < slot_length:
        # 1) next wall (contour) point
        x_w += dt_wall
        Cn  = np.array([x_w, 0.0])

        # 2) intersect half‑line P + s·v with circle (Cn,r)
        Pn = intersect_line_circle(P, v, Cn, r)
        if Pn is None:
            break

        # 3) new feed direction
        dn = Cn - Pn
        vn = rot(dn/np.linalg.norm(dn), -α)

        # store & iterate
        path.append(Pn.copy())
        P, v = Pn, vn

    return np.array(path), r, ae, theta_deg


# ------------------------------------------------------------
# small verification helper
# ------------------------------------------------------------
def engagement_angle(P, C, r):
    """Return θ (deg) for one tool position wrt the wall point C."""
    d = np.linalg.norm(P - C)
    ae = r - d
    theta = np.arccos((r - ae)/r)
    return np.rad2deg(theta)

# ------------------------------------------------------------
# run demo
# ------------------------------------------------------------
if __name__ == '__main__':
    pts, r, ae, θ = faceom_trochoidal()
    xs, ys = pts[:,0], pts[:,1]

    # ------------------------------------------
    # quick visual check
    # ------------------------------------------
    fig, ax = plt.subplots(figsize=(7,2.2))
    ax.plot(xs, ys, 'k-', lw=1.3, label='FACEOM trochoidal path')
    ax.plot([0, xs.max()+5], [0,0], 'b', lw=2, label='slot wall')
    ax.set_aspect('equal')
    ax.set_title(f'Constant‑engagement trochoidal path (θ ≈ {θ}°)')
    ax.set_xlabel('X  [mm]')
    ax.set_ylabel('Y  [mm]')
    ax.legend()
    ax.grid(True, ls=':')
    plt.tight_layout()
    plt.show()

    # ------------------------------------------
    # sanity: engagement really constant?
    # ------------------------------------------
    wall_pts = np.column_stack([xs, np.zeros_like(xs)])
    θs = [engagement_angle(P, C, r) for P, C in zip(pts, wall_pts)]
    print(f'θ mean = {np.mean(θs):.2f}°, σ = {np.std(θs):.3f}°')