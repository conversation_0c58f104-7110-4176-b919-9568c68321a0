import numpy as np
from shapely.geometry import LineString, Point
import shapely

import numpy as np
import bpy
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point
from shapely import prepare, crosses
import math
import random
from shapely.ops import linemerge
import networkx as nx
from itertools import combinations

import time



def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []
    

def get_collection(name: str, rand_int: int) -> bpy.types.Collection:
    """Get a collection by name, creating it if it doesn't exist."""
    name = name + str(rand_int)
    if name not in bpy.data.collections:
        bpy.data.collections.new(name)
        bpy.context.scene.collection.children.link(bpy.data.collections[name])
    return bpy.data.collections[name]


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


# --- Core Functions based on the Article ---

# Blending functions from Eq. (2) [cite: 84]
def f0(s):
  """Blending function f0(s)."""
  return (1 + 2 * s) * (s - 1)**2

def f1(s):
  """Blending function f1(s)."""
  return (3 - 2 * s) * s**2

def f2(s):
  """Blending function f2(s)."""
  return s * (s - 1)**2

def f3(s):
  """Blending function f3(s)."""
  return (s - 1) * s**2

def calculate_angle(v1, v2):
  """Calculates the angle between two vectors in radians."""
  v1_u = v1 / np.linalg.norm(v1)
  v2_u = v2 / np.linalg.norm(v2)
  dot_product = np.dot(v1_u, v2_u)
  # Clip the dot product to avoid numerical errors outside [-1, 1]
  dot_product = np.clip(dot_product, -1.0, 1.0)
  angle = np.arccos(dot_product)
  return angle

def calculate_coefficients(P0, P1, t0, t1):
  """
    Calculates the coefficients k0 and k1 based on Eq. (9)[cite: 185].

    Args:
        P0 (np.array): Start point [x, y].
        P1 (np.array): End point [x, y].
        t0 (np.array): Normalized tangent vector at P0 [x, y].
        t1 (np.array): Normalized tangent vector at P1 [x, y].

    Returns:
        tuple: (k0, k1) coefficients.
  """
  # Ensure inputs are numpy arrays
  P0 = np.array(P0)
  P1 = np.array(P1)
  t0 = np.array(t0)
  t1 = np.array(t1)

  # Calculate IISW d (distance between P0 and P1) [cite: 96]
  d = np.linalg.norm(P1 - P0)
  if d < 1e-9: # Avoid division by zero or very small distance issues
      print("Warning: Points P0 and P1 are very close or identical.")
      return 0.0, 0.0

  # Position vectors for angle calculation
  P1P0 = P0 - P1 # Vector from P1 to P0
  P0P1 = P1 - P0 # Vector from P0 to P1

  # Calculate angles alpha0 and alpha1 [cite: 135]
  # alpha0: angle between P1P0 and t0
  alpha0 = calculate_angle(P1P0, t0)
  # alpha1: angle between P0P1 and t1 (Note: paper figure 9 uses P1P0 for alpha1,
  # but Eq. 7 and 9's derivation implies relative directionality. Using P0P1 for alpha1.)
  alpha1 = calculate_angle(P0P1, t1)

  # Calculate width factor xi_d based on Eq. (3) [cite: 111] (used in Eq. 9)
  xi_d = 1.2 * np.exp(-0.01 * d)

  # Calculate direction factors xi_alpha0 and xi_alpha1 based on Eq. (7) [cite: 156] (used in Eq. 9)
  xi_alpha0 = 1 + np.cos(alpha0)
  xi_alpha1 = 1 + np.cos(alpha1)

  # Calculate final coefficients k0 and k1 using Eq. (9) [cite: 185]
  k0 = xi_d * d * xi_alpha0 
  k1 = xi_d * d * xi_alpha1 #*2
#   k0 = 200
#   k1 = 400

  return k0, k1

def cubic_hermite_spline(s, P0, P1, t0, t1, k0, k1):
  """
    Calculates a point on the cubic Hermite spline for a given parameter s.
    Based on Eq. (1)[cite: 79].

    Args:
        s (float): Curve parameter (0 <= s <= 1).
        P0 (np.array): Start point [x, y].
        P1 (np.array): End point [x, y].
        t0 (np.array): Normalized tangent vector at P0 [x, y].
        t1 (np.array): Normalized tangent vector at P1 [x, y].
        k0 (float): Coefficient for t0.
        k1 (float): Coefficient for t1.

    Returns:
        np.array: Point [x, y] on the spline.
  """
  P0 = np.array(P0)
  P1 = np.array(P1)
  t0 = np.array(t0)
  t1 = np.array(t1)

  term0 = P0 * f0(s)
  term1 = P1 * f1(s)
  term2 = k0 * f2(s) * t0
  term3 = k1 * f3(s) * t1

  C1_s = term0 + term1 + term2 + term3
  return C1_s

def generate_hermite_spline_segment(P0, P1, t0, t1, num_points=100):
  """
    Generates a sequence of points representing the cubic Hermite spline segment.

    Args:
        P0 (np.array): Start point [x, y].
        P1 (np.array): End point [x, y].
        t0 (np.array): Normalized tangent vector at P0 [x, y].
        t1 (np.array): Normalized tangent vector at P1 [x, y].
        num_points (int): Number of points to generate along the spline.

    Returns:
        np.array: An array of shape (num_points, 2) representing the spline points.
  """
  k0, k1 = calculate_coefficients(P0, P1, t0, t1)
  s_values = np.linspace(0, 1, num_points)
  spline_points = np.array([cubic_hermite_spline(s, P0, P1, t0, t1, k0, k1) for s in s_values])
  return spline_points

def get_tangent(line, point_on_line, segment_length=1e-6):
    """
    Estimates the tangent vector at a point on a Shapely LineString.

    Args:
        line (LineString): The boundary line.
        point_on_line (Point): The point on the line where the tangent is needed.
        segment_length (float): Small distance along the line to find the next point
                                 for tangent calculation.

    Returns:
        np.array: Normalized tangent vector [x, y], or None if calculation fails.
    """
    try:
        start_dist = line.project(point_on_line)
        # Find points slightly before and after the target point
        dist_before = max(0, start_dist - segment_length / 2)
        dist_after = min(line.length, start_dist + segment_length / 2)

        # Handle cases where the point is exactly at the start or end
        if abs(dist_after - dist_before) < 1e-9:
             if start_dist < segment_length: # Near start
                 dist_after = min(line.length, segment_length)
                 point_before = np.array(line.interpolate(0).coords[0])
                 point_after = np.array(line.interpolate(dist_after).coords[0])
             elif line.length - start_dist < segment_length: # Near end
                 dist_before = max(0, line.length - segment_length)
                 point_before = np.array(line.interpolate(dist_before).coords[0])
                 point_after = np.array(line.interpolate(line.length).coords[0])
             else: # Should not happen if segment_length is small enough
                 print("Warning: Could not determine points for tangent calculation.")
                 return None
        else:
            point_before = np.array(line.interpolate(dist_before).coords[0])
            point_after = np.array(line.interpolate(dist_after).coords[0])


        tangent_vec = point_after - point_before
        norm = np.linalg.norm(tangent_vec)
        if norm < 1e-9:
            print("Warning: Tangent vector norm is close to zero.")
            # Fallback for straight segments or very close points
            coords = np.array(line.coords)
            if len(coords) > 1:
                 # Try using the segment the point lies on
                 for i in range(len(coords) - 1):
                     p_a = Point(coords[i])
                     p_b = Point(coords[i+1])
                     segment = LineString([p_a, p_b])
                     if segment.distance(point_on_line) < 1e-6:
                         tangent_vec = coords[i+1] - coords[i]
                         norm = np.linalg.norm(tangent_vec)
                         if norm > 1e-9:
                             return tangent_vec / norm
                         break # Found segment
            return None # Cannot determine tangent
        return tangent_vec / norm
    except Exception as e:
        print(f"Error calculating tangent: {e}")
        return None

def resample_geometry_with_smoothing(coords, factor, smoothing=0.01):
    """
    Resamples geometry with optional smoothing by a specified factor.
    
    Args:
        coords (np.ndarray): Array of shape (N, 2) representing the geometry.
        factor (float): Resampling factor (>1 increases points, <1 decreases).
        smoothing (float): Smoothing parameter for spline (0 = no smoothing).
        
    Returns:
        np.ndarray: Resampled geometry with shape (M, 2).
    """
    from scipy.interpolate import make_splrep, splev
    import numpy as np
    
    # Calculate target number of points
    n_points = int(len(coords) * factor)
    if n_points < 2:
        return coords  # Can't resample with fewer than 2 points
    
    # Create parameter space
    t = np.arange(len(coords))
    t_new = np.linspace(0, len(coords) - 1, n_points)
    
    # Create spline for x and y coordinates
    k = min(3, len(coords) - 1)  # Cubic spline if possible
    spline_x = make_splrep(t, coords[:, 0], k=k, s=smoothing)
    spline_y = make_splrep(t, coords[:, 1], k=k, s=smoothing)
    
    # Evaluate spline at new points
    x_new = splev(t_new, spline_x)
    y_new = splev(t_new, spline_y)
    
    return np.column_stack((x_new, y_new))

def resample_geometry_equal_distance(coords, num_points=None, distance=None):
    """
    Resamples geometry with equal distance between vertices.
    
    Args:
        coords (np.ndarray): Array of shape (N, 2) representing the geometry.
        num_points (int, optional): Number of points in output. If None, distance must be provided.
        distance (float, optional): Target distance between points. If None, num_points must be provided.
        
    Returns:
        np.ndarray: Resampled geometry with equal spacing between points.
    """
    from scipy.interpolate import CubicSpline
    import numpy as np
    
    if len(coords) < 2:
        return coords
        
    # Create a line from the points
    line = LineString(coords)
    total_length = line.length
    
    # Determine sampling strategy
    if num_points is not None:
        # Sample by number of points
        distances = np.linspace(0, total_length, num_points)
    elif distance is not None:
        # Sample by distance between points
        num_segments = max(1, int(total_length / distance))
        distances = np.linspace(0, total_length, num_segments + 1)
    else:
        raise ValueError("Either num_points or distance must be provided")
    
    # Sample points at equal distances along the line
    points = np.array([line.interpolate(distance).coords[0] for distance in distances])
    
    return points

def resample_geometry_equal_distance_with_smoothing(coords, distance=None, num_points=None, smoothing=0.01):
    """
    Resamples geometry with equal distance between vertices and optional smoothing.
    
    Args:
        coords (np.ndarray): Array of shape (N, 2) representing the geometry.
        distance (float, optional): Target distance between points. If None, num_points must be provided.
        num_points (int, optional): Number of points in output. If None, distance must be provided.
        smoothing (float): Smoothing parameter (0 = no smoothing, higher values = more smoothing).
        
    Returns:
        np.ndarray: Resampled geometry with equal spacing between points and smoothing applied.
    """
    from scipy.interpolate import make_splrep, splev
    import numpy as np
    
    if len(coords) < 3:  # Need at least 3 points for cubic spline
        return coords
    
    # First, create a smoothed version of the original geometry
    t_orig = np.arange(len(coords))
    k = min(3, len(coords) - 1)  # Cubic spline if possible
    
    # Create splines with smoothing
    spline_x = make_splrep(t_orig, coords[:, 0], k=k, s=smoothing)
    spline_y = make_splrep(t_orig, coords[:, 1], k=k, s=smoothing)
    
    # Generate a densely sampled smooth curve
    t_dense = np.linspace(0, len(coords) - 1, len(coords) * 10)
    x_dense = splev(t_dense, spline_x)
    y_dense = splev(t_dense, spline_y)
    smooth_coords = np.column_stack((x_dense, y_dense))
    
    # Create a line from the smoothed points
    line = LineString(smooth_coords)
    total_length = line.length
    
    # Determine sampling strategy
    if num_points is not None:
        # Sample by number of points
        distances = np.linspace(0, total_length, num_points)
    elif distance is not None:
        # Sample by distance between points
        num_segments = max(1, int(total_length / distance))
        distances = np.linspace(0, total_length, num_segments + 1)
    else:
        raise ValueError("Either num_points or distance must be provided")
    
    # Sample points at equal distances along the smoothed line
    points = np.array([line.interpolate(distance).coords[0] for distance in distances])
    
    return points


import numpy as np
from scipy.interpolate import splprep, splev, interp1d

def resample_and_smooth(points, smoothing_factor, segment_length):
    """
    Resample a 2D polyline with equal arc length segments and apply smoothing.
    
    Parameters:
    - points: np.ndarray of shape (n, 2), input 2D points
    - smoothing_factor: float, spline smoothing parameter (e.g., 1.0)
    - segment_length: float, desired arc length between points
    
    Returns:
    - new_points: np.ndarray of shape (m, 2), resampled and smoothed points
    """
    # Fit a smoothing spline
    tck, u = splprep([points[:, 0], points[:, 1]], s=smoothing_factor)
    
    # Evaluate on a fine grid
    t_fine = np.linspace(0, 1, 1000)
    x_fine, y_fine = splev(t_fine, tck)
    
    # Compute cumulative arc length
    dx = np.diff(x_fine)
    dy = np.diff(y_fine)
    seg_lengths = np.sqrt(dx**2 + dy**2)
    s_fine = np.cumsum(seg_lengths)
    s_fine = np.insert(s_fine, 0, 0)
    total_length = s_fine[-1]
    
    # Target arc lengths with equal spacing
    s_target = np.arange(0, total_length, segment_length)
    if s_target[-1] < total_length:
        s_target = np.append(s_target, total_length)
    
    # Interpolate to find corresponding t values
    t_target = interp1d(s_fine, t_fine, kind='linear')(s_target)
    
    # Evaluate spline at target parameters
    x_smooth, y_smooth = splev(t_target, tck)
    new_points = np.vstack([x_smooth, y_smooth]).T
    
    return new_points


def main():
    rand_int = random.randint(0, 1000000)
    geometry = get_geometry()

    resampled_geoms = []

    # for geom in geometry:
    #     rg = resample_and_smooth(geom, 0.02, 0.5)
    #     resampled_geoms.append(rg)

    resampled_geoms = geometry
    
    if len(geometry) == 2:
        bs0 = LineString(resampled_geoms[0])
        bs1 = LineString(resampled_geoms[1])
    else:
        print("Please select exactly two objects: a boundary polygon (active) and a path.")
        return
    
    
    frac = 1

    for frac in np.linspace(0, 1, 50):
        P0_geom = bs0.interpolate(frac, normalized=True)
        P1_geom = bs1.interpolate(1-frac, normalized=True)

        P0 = np.array(P0_geom.coords[0])
        P1 = np.array(P1_geom.coords[0])

        # Calculate tangent vectors at P0 and P1
        t0 = get_tangent(bs0, P0_geom)
        t1 = get_tangent(bs1, P1_geom)
        
        if t0 is not None and t1 is not None:       
            curve = generate_hermite_spline_segment(P0, P1, t0, t1, num_points=50)
            create_line_object(curve, f"hermite_spline_segment", color=(1, 0, 0, 1))

if __name__ == "__main__":
    main()
