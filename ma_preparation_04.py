import numpy as np
import bpy
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point
from shapely.ops import nearest_points
import math


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []


def geometry_to_polygon(geometry: list[np.ndarray]) -> shapely.geometry.Polygon:
    """Convert geometry to a Shapely Polygon."""
    if not geometry:
        return None
    exterior = geometry[0]
    interiors = geometry[1:]
    return shapely.geometry.Polygon(shell=exterior, holes=interiors)


def geometry_to_shapely(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.MultiPolygon]:
    """Convert geometry to Shapely Polygon and MultiPolygon."""
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = shapely.geometry.MultiPolygon()
    return contour, islands


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) == 2:
        contour = shapely.geometry.Polygon(shell=geometry[0])
        path = shapely.geometry.LineString(geometry[1])
    else:
        print("Please select a valid mesh object.")
        return []
    return contour, path


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """

    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def decompose_to_polygons(geom):
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def find_closest_points(points, x, k=5):
    squared_distances = np.sum((points - x) ** 2, axis=1)
    min_dist = np.min(squared_distances)
    closest_indices = np.argsort(squared_distances)[:k] #k closest points instead of just one
    closest_points = points[closest_indices]
    indices = np.where(squared_distances == min_dist)[0]

    return points[indices]


def classify_vertices_by_angles_and_short_edges(vertices, min_edge_length=0.1):
    """
    Classify vertices of a polygon based on their angles and identify vertices
    connected to short edges.

    Parameters:
    vertices: np.ndarray of shape (N, 2) containing vertex coordinates
    min_edge_length: float, minimum edge length threshold

    Returns:
    tuple of three arrays containing:
    - indices of convex angles
    - indices of concave angles
    - indices of vertices connected to edges shorter than min_edge_length
    """
    # Roll vertices to get previous and next points
    prev_vertices = np.roll(vertices, 1, axis=0)
    next_vertices = np.roll(vertices, -1, axis=0)

    # Calculate vectors
    v1 = prev_vertices - vertices
    v2 = next_vertices - vertices

    # Calculate edge lengths
    prev_edge_lengths = np.linalg.norm(v1, axis=1)
    next_edge_lengths = np.linalg.norm(v2, axis=1)

    # Find vertices connected to short edges
    short_edges_mask = (prev_edge_lengths < min_edge_length) | (next_edge_lengths < min_edge_length)
    short_edges_indices = np.where(short_edges_mask)[0]

    # Normalize vectors
    v1_norm = v1 / prev_edge_lengths[:, np.newaxis]
    v2_norm = v2 / next_edge_lengths[:, np.newaxis]

    # Calculate dot product
    dot_products = np.sum(v1_norm * v2_norm, axis=1)
    dot_products = np.clip(dot_products, -1, 1)

    # Calculate angles in radians
    angles = np.arccos(dot_products)

    # Calculate 2D cross product
    cross_products = v1[:, 0] * v2[:, 1] - v1[:, 1] * v2[:, 0]

    # Flip angles where cross product is negative
    angles = np.where(cross_products < 0, 2 * np.pi - angles, angles)

    # Classify angles (from inside perspective)
    concave_mask = angles < np.pi
    convex_mask = angles > np.pi

    return (np.where(convex_mask)[0],
            np.where(concave_mask)[0],
            short_edges_indices)


def minimum_distance_to_boundary(point, contour, islands=None):
    """
    Calculate the minimum distance from a point to the boundary of the shape,
    considering both contour and islands.

    Parameters:
    -----------
    point : array-like
        Coordinates of the point [x, y]
    contour : shapely.geometry.Polygon7
        Exterior boundary of the shape
    islands : shapely.geometry.MultiPolygon, optional
        Interior islands/holes

    Returns:
    --------
    float : Minimum distance to any part of the boundary
    tuple : Coordinates of the closest boundary point
    """
    # Convert point to shapely Point
    point_geom = Point(point)

    # Get distance to exterior boundary
    min_dist = point_geom.distance(contour.exterior)
    closest_point = nearest_points(point_geom, contour.exterior)[1].coords[0]

    # Check islands if they exist
    if islands and not islands.is_empty:
        # For each island, check if distance is smaller
        for island in islands.geoms:
            island_dist = point_geom.distance(island.exterior)
            if island_dist < min_dist:
                min_dist = island_dist
                closest_point = nearest_points(point_geom, island.exterior)[1].coords[0]

    return min_dist, closest_point


def hash_line(line, tolerance=1e-6):
    """Hashes a LineString for duplicate detection, considering direction."""
    coords = np.array(line.coords)
    rounded_coords = np.round(coords, int(-np.log10(tolerance)))
    # Sort coordinates to make hash direction-independent
    return hash(tuple(sorted(map(tuple, rounded_coords))))


def hash_edge(edge, tolerance=1e-6):
    """Hashes an edge (start, end) for duplicate detection."""
    start, end = edge
    rounded_start = tuple(np.round(start, int(-np.log10(tolerance))))
    rounded_end = tuple(np.round(end, int(-np.log10(tolerance))))
    return hash((rounded_start, rounded_end))


def remove_duplicate_edges(edges, tolerance=1e-6):
    unique_edges = []
    seen_hashes = set()

    for edge in edges:
        edge_hash = hash_edge(edge, tolerance)
        if edge_hash not in seen_hashes:
            unique_edges.append(edge)
            seen_hashes.add(edge_hash)

    return unique_edges


def make_medial_edges(contour, islands=None):
    pv = pyvoronoi.Pyvoronoi(100)

    # Process the contour (exterior polygon)
    coords = list(contour.exterior.coords)
    print(f"Adding contour with {len(coords)} points")

    # Add segments for each edge of the contour (excluding the last point which is same as first)
    for i in range(len(coords) - 1):
        start = coords[i]
        end = coords[i + 1]
        pv.AddSegment([start, end])

    # Process islands (holes) if provided
    if islands and not islands.is_empty:
        # If islands is a MultiPolygon, iterate through its polygons
        for island in islands.geoms:
            hole_coords = list(island.exterior.coords)
            print(f"Adding island with {len(hole_coords)} points")

            # Add segments for each edge of the island
            for i in range(len(hole_coords) - 1):
                start = hole_coords[i]
                end = hole_coords[i + 1]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    # Collect all edges data first
    linear_edges = []
    curved_edges = []

    # Use sets for efficient duplicate detection
    seen_linear_hashes = set()
    seen_curved_hashes = set()

    for cIndex, cell in enumerate(cells):
        for i, edge_idx in enumerate(cell.edges):
            e = edges[edge_idx]
            # Skip edges with invalid vertices
            if e.start == -1 or e.end == -1:
                continue

            startVertex = vertices[e.start]
            endVertex = vertices[e.end]

            # Create hash from the edge points (sorted for direction independence)            
            print(f'twin: {e.twin}')
            if e.twin == 600:
                print(e.is_primary)
            # if True:
                # Store edge data with identifier for naming
                if e.is_linear:
                    # Create tuple form of start and end points for hashing
                    start_point = (startVertex.X, startVertex.Y)
                    end_point = (endVertex.X, endVertex.Y)
                    create_line_object([start_point, end_point], f"{cIndex}_{i}")
                    
                else:
                    # Calculate distance only for curved edges
                    max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                            (startVertex.Y - endVertex.Y)**2) / 10                
                    points = pv.DiscretizeCurvedEdge(edge_idx, max_distance, parabola_equation_tolerance=0.001)
                    points_array = np.array([[p[0], p[1]] for p in points])                
                    create_line_object(points_array, f"{cIndex}_{i}")
               
    return


def boundary_distance(polygon, points):
    """
    Find the distance between a polygon's boundary and an
    array of points.

    Parameters
    -------------
    polygon : shapely.geometry.Polygon
      Polygon to query
    points : (n, 2) float
      2D points

    Returns
    ------------
    distance : (n,) float
      Minimum distance from each point to polygon boundary
    """     
    boundary = polygon.boundary
    return np.array([boundary.distance(Point(p)) for p in points])


def find_accurate_cut_point(p0, p1, contour, islands=None, min_radius=0.01, sample_step=0.1):
    """
    Find cut point by sampling the segment.
    
    Args:
        p1, p2: Start and end points of the segment
        r1, r2: Radii at start and end points
        min_radius: Minimum allowed radius
        sample_step: Distance between samples along the segment
    """
    # Calculate segment length and required number of samples
    segment_length = np.linalg.norm(p1 - p0)
    num_samples = max(int(segment_length / sample_step), 2)  # At least 2 samples
    
    # Generate sample points
    t_range = np.linspace(0, 1, num_samples)
    sample_points = p0[None, :] + t_range[:, None] * (p1 - p0)[None, :]
    
    # Get distances to contour
    contour_distances = boundary_distance(contour, sample_points)    
    
    # If islands exist, check distances to them
    if islands is not None and not islands.is_empty:
        # Calculate distances to all islands
        island_distances = np.array([boundary_distance(island, sample_points) for island in islands.geoms])
        # Take minimum distance to any island at each point
        island_min_distances = np.min(island_distances, axis=0)
        # Take minimum between contour and island distances
        sample_radii = np.minimum(contour_distances, island_min_distances)
    else:
        sample_radii = contour_distances
    
    # Find where radius crosses min_radius
    crossings = np.where(np.diff(sample_radii >= min_radius))[0]
    if len(crossings) > 0:        
        # Use the first crossing
        idx = crossings[0]
        t = t_range[idx] + (min_radius - sample_radii[idx]) / (sample_radii[idx + 1] - sample_radii[idx]) * (t_range[idx + 1] - t_range[idx])
        return p0 + t * (p1 - p0)
    
    return None  # No crossing found


def separate_edges_by_radius(edges: list, contour, islands, minimum_radius: float) -> tuple[list, list]:
    """
    Separates edges into two groups based on radius criteria and processes edges that need splitting.
    Uses accurate cut point finding by sampling the segment.
    
    Args:
        edges: List of edge dictionaries containing 'points' and 'radii'
        minimum_radius: Threshold value for separating edges
        
    Returns:
        tuple containing:
        - list of edges with radius >= minimum_radius (normal_edges + shortened_edges_bigger)
        - list of edges with radius < minimum_radius (all_small_radius + shortened_edges_smaller)
    """
    # Stage 1: Initial separation
    stage1_filtered = []  # edges where at least one point has radius >= minimum_radius
    all_small_radius = []  # edges where ALL points have radius < minimum_radius
    
    for edge in edges:
        if any(radius >= minimum_radius for radius in edge['radii']):
            stage1_filtered.append(edge)
        else:
            all_small_radius.append(edge)
    
    # Stage 2: Separate edges that need no splitting from those that do
    small_radius_edges = [
        edge for edge in stage1_filtered
        if any(radius < minimum_radius for radius in edge['radii'])
    ]
    
    normal_edges = [
        edge for edge in stage1_filtered
        if all(radius >= minimum_radius for radius in edge['radii'])
    ]
    
    # Process edges that need splitting
    shortened_edges_bigger = []
    shortened_edges_smaller = []
    
    for edge in small_radius_edges:
        points = edge['points']
        radii = edge['radii']
        
        # Handle edges point by point
        current_segment_points = []
        current_segment_radii = []
        
        for i in range(len(points) - 1):
            p1, p2 = points[i], points[i + 1]
            r1, r2 = radii[i], radii[i + 1]
            
            # Start new segment with first point
            if not current_segment_points:
                current_segment_points.append(p1)
                current_segment_radii.append(r1)
            
            cut_point = find_accurate_cut_point(p1, p2, contour, islands, minimum_radius, sample_step=0.05)
            
            if cut_point is not None:
                # Add cut point to current segment and create new edge
                if r1 >= minimum_radius:
                    # Finish bigger segment
                    current_segment_points.append(cut_point)
                    current_segment_radii.append(minimum_radius)
                    
                    new_edge = edge.copy()
                    new_edge['points'] = np.array(current_segment_points)
                    new_edge['radii'] = np.array(current_segment_radii)
                    shortened_edges_bigger.append(new_edge)
                    
                    # Start smaller segment
                    current_segment_points = [cut_point, p2]
                    current_segment_radii = [minimum_radius, r2]
                    
                else:
                    # Finish smaller segment
                    current_segment_points.append(cut_point)
                    current_segment_radii.append(minimum_radius)
                    
                    new_edge = edge.copy()
                    new_edge['points'] = np.array(current_segment_points)
                    new_edge['radii'] = np.array(current_segment_radii)
                    shortened_edges_smaller.append(new_edge)
                    
                    # Start bigger segment
                    current_segment_points = [cut_point, p2]
                    current_segment_radii = [minimum_radius, r2]
            else:
                # No cut point, continue current segment
                current_segment_points.append(p2)
                current_segment_radii.append(r2)
            
        # Add final segment if points exist
        if current_segment_points:
            new_edge = edge.copy()
            new_edge['points'] = np.array(current_segment_points)
            new_edge['radii'] = np.array(current_segment_radii)
            
            # Determine which list to append to based on segment radii
            if all(r >= minimum_radius for r in current_segment_radii):
                shortened_edges_bigger.append(new_edge)
            else:
                shortened_edges_smaller.append(new_edge)
    
    # Combine results
    bigger_radius_edges = normal_edges + shortened_edges_bigger
    smaller_radius_edges = all_small_radius + shortened_edges_smaller
    
    return bigger_radius_edges, smaller_radius_edges
    

def find_max_radius_point(edges: list) -> tuple[np.ndarray, float]:
    """
    Find the point with the maximum radius from a list of edges.
    
    Args:
        edges: List of edge dictionaries containing 'points' and 'radii'
        
    Returns:
        tuple containing:
        - np.ndarray: Coordinates of the point with the maximum radius
        - float: Maximum radius value
    """
    max_radius = float('-inf')
    max_point = None
    
    for edge in edges:
        max_radius_edge = max(edge['radii'])
        if max_radius_edge > max_radius:
            max_radius = max_radius_edge
            max_point = edge['points'][np.argmax(edge['radii'])]
    
    return max_point, max_radius


def extract_medial_axis(polygon_points, epsilon=1.0):
    """Extract the Medial Axis from a polygon using Pyvoronoi."""
    # Create polygon for inside/outside testing
    polygon = shapely.geometry.Polygon(polygon_points)

    # Initialize Pyvoronoi
    pv = pyvoronoi.Pyvoronoi(epsilon)

    # Keep track of which sites are segments
    segment_sites = []

    # Add polygon segments
    for i in range(len(polygon_points)):
        p1 = polygon_points[i]
        p2 = polygon_points[(i+1) % len(polygon_points)]
        site_index = pv.AddSegment((p1, p2))
        segment_sites.append(site_index)

    # Construct Voronoi diagram
    pv.Construct()

    # Get components
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    medial_axis = []

    # Process edges efficiently
    for edge_idx, edge in enumerate(edges):
        # Only process primary edges to avoid duplicates
        if not edge.is_primary:
            continue

        # Skip infinite edges
        if edge.start < 0 or edge.end < 0:
            continue

        e = edges[edge_idx]

        # Get vertices
        start_vertex = vertices[edge.start]
        end_vertex = vertices[edge.end]
        start_point = (start_vertex.X, start_vertex.Y)
        end_point = (end_vertex.X, end_vertex.Y)
        startVertex = vertices[e.start]
        endVertex = vertices[e.end]

        # Check if points are inside the polygon
        # if not (polygon.contains(Point(start_point)) and
        #         polygon.contains(Point(end_point))):
        #     continue

        # Get cells connected by this edge
        cell1 = cells[edge.cell]
        cell2 = cells[edges[edge.twin].cell]

        # Check if both cells are from segment sites
        # if cell1.site not in segment_sites or cell2.site not in segment_sites:
        #     continue

        # Process based on edge type
        if edge.is_linear:
            medial_axis.append((start_point, end_point))
        else:
            # Calculate distance only for curved edges
            max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                         (startVertex.Y - endVertex.Y)**2) / 10                
            points = pv.DiscretizeCurvedEdge(edge_idx, max_distance, parabola_equation_tolerance=0.01)
            points_array = np.array([[p[0], p[1]] for p in points])
            medial_axis.append(points_array)

    return medial_axis


def main():
    minimum_radius = 0
    geometry = get_geometry()
    islands = None

    if len(geometry) == 1:
        polygon = geometry_to_polygon(geometry)
    elif len(geometry) >= 2:
        polygon, islands = geometry_to_shapely(geometry)
        # polygon, path = geometry_to_trocho_path(geometry)   

    else:
        print("Please select a valid mesh object.")
        exit()
        
    ma = extract_medial_axis(polygon.exterior.coords)
    print(ma)
    

if __name__ == "__main__":
    main()
