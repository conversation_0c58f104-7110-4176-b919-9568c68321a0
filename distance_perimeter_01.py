import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from dataclasses import dataclass

# Use a dataclass for clean and readable ellipse definition
@dataclass
class Ellipse:
    cx: float  # center x
    cy: float  # center y
    a: float   # semi-major axis (half-width)
    b: float   # semi-minor axis (half-height)
    theta: float # rotation angle in radians

# --- Helper Functions ---

def get_ellipse_point(t: float, ellipse: Ellipse) -> np.ndarray:
    """Calculates the coordinates of a point on the ellipse at parameter t."""
    x = ellipse.cx + ellipse.a * np.cos(t) * np.cos(ellipse.theta) - \
                  ellipse.b * np.sin(t) * np.sin(ellipse.theta)
    y = ellipse.cy + ellipse.a * np.cos(t) * np.sin(ellipse.theta) + \
                  ellipse.b * np.sin(t) * np.cos(ellipse.theta)
    return np.array([x, y])

def generate_ellipse_perimeter(ellipse: Ellipse, num_points: int = 1000) -> np.ndarray:
    """Generates an array of points on the ellipse's perimeter."""
    t = np.linspace(0, 2 * np.pi, num_points)
    points = np.array([get_ellipse_point(ti, ellipse) for ti in t])
    return points

def is_point_inside_ellipse(point: np.ndarray, ellipse: Ellipse) -> bool:
    """Checks if a point is inside or on the boundary of an ellipse."""
    # Translate and un-rotate the point to the ellipse's local frame
    x_local = (point[0] - ellipse.cx) * np.cos(ellipse.theta) + \
              (point[1] - ellipse.cy) * np.sin(ellipse.theta)
    y_local = -(point[0] - ellipse.cx) * np.sin(ellipse.theta) + \
               (point[1] - ellipse.cy) * np.cos(ellipse.theta)
    
    # Check the implicit ellipse equation
    return (x_local / ellipse.a)**2 + (y_local / ellipse.b)**2 <= 1.0

def find_closest_point_on_ellipse(point: np.ndarray, ellipse: Ellipse):
    """
    Finds the point on the ellipse perimeter closest to a given external point.
    This is an optimization problem.
    """
    def distance_objective(t: float) -> float:
        """The function to minimize: squared Euclidean distance."""
        ellipse_point = get_ellipse_point(t, ellipse)
        return np.sum((point - ellipse_point)**2)

    # Provide a good initial guess for the angle t
    # The angle of the vector from ellipse center to the point is a good start
    vec_to_point = point - np.array([ellipse.cx, ellipse.cy])
    initial_guess = np.arctan2(vec_to_point[1], vec_to_point[0]) - ellipse.theta

    # Use scipy.optimize.minimize to find the t that minimizes the distance
    result = minimize(distance_objective, x0=initial_guess, method='Nelder-Mead')
    
    optimal_t = result.x[0]
    closest_point = get_ellipse_point(optimal_t, ellipse)
    min_distance = np.linalg.norm(point - closest_point)
    
    return closest_point, min_distance

# --- Main Logic ---

# 1. Define the two ellipses
# Ellipse A: The reference ellipse
ellipse_A = Ellipse(cx=2, cy=3, a=5, b=2, theta=np.deg2rad(30))
# Ellipse B: The ellipse from which we measure
ellipse_B = Ellipse(cx=6, cy=5, a=4, b=2.5, theta=np.deg2rad(-45))

# 2. Generate a dense set of points on Ellipse B's perimeter
N_SAMPLES = 2000 # Higher number for better accuracy
points_on_B = generate_ellipse_perimeter(ellipse_B, N_SAMPLES)

# 3. Filter points of B that are outside of A
points_B_outside_A = np.array([p for p in points_on_B if not is_point_inside_ellipse(p, ellipse_A)])

if len(points_B_outside_A) == 0:
    print("Ellipse B is completely contained within Ellipse A.")
    print("The distance is 0.")
else:
    # 4. For each outside point, find its closest point on A and the distance
    distances = []
    closest_points_on_A = []
    for point_b in points_B_outside_A:
        closest_point_a, dist = find_closest_point_on_ellipse(point_b, ellipse_A)
        distances.append(dist)
        closest_points_on_A.append(closest_point_a)

    # 5. Find the maximum of these minimum distances
    max_distance_index = np.argmax(distances)
    max_dist = distances[max_distance_index]
    
    # These are the two key points
    farthest_point_on_B = points_B_outside_A[max_distance_index]
    corresponding_closest_point_on_A = closest_points_on_A[max_distance_index]

    # --- Output and Visualization ---
    print(f"Maximum distance from the part of B outside A to the perimeter of A: {max_dist:.4f}")
    print(f"Farthest point on B (outside A): {farthest_point_on_B}")
    print(f"Its corresponding closest point on A: {corresponding_closest_point_on_A}")

    # Plotting
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Plot full ellipses
    ax.plot(*generate_ellipse_perimeter(ellipse_A, 200).T, 'b-', label='Ellipse A')
    ax.plot(*generate_ellipse_perimeter(ellipse_B, 200).T, 'g-', label='Ellipse B')

    # Highlight the part of B outside A
    ax.plot(*points_B_outside_A.T, 'r.', markersize=3, label='Part of B outside A')

    # Highlight the two key points
    ax.plot(*farthest_point_on_B, 'm*', markersize=15, label=f'Farthest Point on B (outside A)')
    ax.plot(*corresponding_closest_point_on_A, 'c*', markersize=15, label=f'Closest Point on A')
    
    # Draw the line representing the max distance
    ax.plot([farthest_point_on_B[0], corresponding_closest_point_on_A[0]],
            [farthest_point_on_B[1], corresponding_closest_point_on_A[1]],
            'k--', linewidth=2, label=f'Max Distance = {max_dist:.2f}')

    ax.set_aspect('equal', 'box')
    ax.legend()
    ax.set_title('Farthest Distance from Ellipse B (outside A) to Ellipse A')
    ax.grid(True, linestyle='--', alpha=0.6)
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.show()