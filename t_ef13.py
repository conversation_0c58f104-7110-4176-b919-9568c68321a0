import numpy as np

def fit_ellipse_to_points_optimized(points, bounds, n_grid_points=20):
    """
    Optimized ellipse fitting using vectorized grid search with corrected einsum notation.
    """
    # Unpack bounds
    (minX, maxX), (minY, maxY), (minTheta, maxTheta) = bounds
    
    # Create search grid
    x_centers = np.linspace(minX, maxX, n_grid_points)
    y_centers = np.linspace(minY, maxY, n_grid_points)
    thetas = np.linspace(minTheta, maxTheta, n_grid_points)
    
    # Create meshgrid for all parameter combinations
    x_grid, y_grid, theta_grid = np.meshgrid(x_centers, y_centers, thetas)
    x_flat = x_grid.ravel()
    y_flat = y_grid.ravel()
    theta_flat = theta_grid.ravel()
    
    # Precompute cos/sin for all thetas
    cos_theta = np.cos(theta_flat)
    sin_theta = np.sin(theta_flat)
    
    # Prepare points for broadcasting
    points = points.reshape(1, -1, 2)  # Shape: (1, n_points, 2)
    
    # Vectorized transformation of points
    centers = np.column_stack((x_flat, y_flat)).reshape(-1, 1, 2)  # Shape: (n_grid, 1, 2)
    
    # Translate points
    translated_points = points - centers
    
    # Rotate points
    rotated_points = np.zeros_like(translated_points)
    rotated_points[:, :, 0] = translated_points[:, :, 0] * cos_theta[:, np.newaxis] + \
                              translated_points[:, :, 1] * sin_theta[:, np.newaxis]
    rotated_points[:, :, 1] = -translated_points[:, :, 0] * sin_theta[:, np.newaxis] + \
                              translated_points[:, :, 1] * cos_theta[:, np.newaxis]
    
    # Prepare for least squares
    x_squared = rotated_points[:, :, 0]**2  # Shape: (n_grid, n_points)
    y_squared = rotated_points[:, :, 1]**2  # Shape: (n_grid, n_points)
    
    # Build design matrix X: [x², y²] for each point and grid combination
    # Shape: (n_grid, n_points, 2)
    X = np.stack([x_squared, y_squared], axis=2)
    
    # Target vector is all ones
    y = np.ones(points.shape[1])  # Shape: (n_points,)
    
    # Solve least squares for all grid points
    try:
        # Calculate X^T X and X^T y for each grid point
        # XTX[m, i, j] = sum_n X[m, n, i] * X[m, n, j]
        XTX = np.einsum('mni,mnj->mij', X, X)  # Shape: (n_grid, 2, 2)
        
        # XTy[m, i] = sum_n X[m, n, i] * y[n]
        XTy = np.einsum('mni,n->mi', X, y)     # Shape: (n_grid, 2)
        
        # Calculate determinant and check invertibility
        det = XTX[:, 0, 0] * XTX[:, 1, 1] - XTX[:, 0, 1] * XTX[:, 1, 0]
        valid_mask = np.abs(det) > 1e-10
        
        if not np.any(valid_mask):
            return None
            
        # Calculate inverse of XTX for valid grid points
        inv_XTX = np.zeros_like(XTX)
        inv_XTX[valid_mask, 0, 0] = XTX[valid_mask, 1, 1] / det[valid_mask]
        inv_XTX[valid_mask, 0, 1] = -XTX[valid_mask, 0, 1] / det[valid_mask]
        inv_XTX[valid_mask, 1, 0] = -XTX[valid_mask, 1, 0] / det[valid_mask]
        inv_XTX[valid_mask, 1, 1] = XTX[valid_mask, 0, 0] / det[valid_mask]
        
        # Calculate solution: beta = (X^T X)^-1 X^T y
        beta = np.einsum('mij,mj->mi', inv_XTX, XTy)  # Shape: (n_grid, 2)
        A_rot = beta[:, 0]
        B_rot = beta[:, 1]
        
        # Check for valid ellipse parameters (both must be positive)
        valid_ellipse = valid_mask & (A_rot > 0) & (B_rot > 0)
        
        if not np.any(valid_ellipse):
            return None
            
        # Calculate error metric for valid ellipses
        # Error = |sqrt(A_rot*x² + B_rot*y²) - 1|
        ellipse_vals = np.sqrt(A_rot[valid_ellipse, np.newaxis] * x_squared[valid_ellipse] + 
                              B_rot[valid_ellipse, np.newaxis] * y_squared[valid_ellipse])
        errors = np.abs(ellipse_vals - 1)
        total_errors = np.sum(errors**2, axis=1)
        
        # Find best fit
        best_idx = np.where(valid_ellipse)[0][np.argmin(total_errors)]
        best_params = {
            'center': (x_flat[best_idx], y_flat[best_idx]),
            'theta': theta_flat[best_idx],
            'A_rot': A_rot[best_idx],
            'B_rot': B_rot[best_idx],
            'a': 1/np.sqrt(A_rot[best_idx]),
            'b': 1/np.sqrt(B_rot[best_idx]),
            'error': total_errors[np.where(valid_ellipse)[0] == best_idx][0]
        }
        
        # Calculate coefficients of general ellipse equation
        cos_theta = np.cos(best_params['theta'])
        sin_theta = np.sin(best_params['theta'])
        
        A_rot = best_params['A_rot']
        C_rot = best_params['B_rot']
        
        A = A_rot * cos_theta**2 + C_rot * sin_theta**2
        B = 2 * (A_rot - C_rot) * cos_theta * sin_theta
        C = A_rot * sin_theta**2 + C_rot * cos_theta**2
        D = -2 * A * best_params['center'][0] - B * best_params['center'][1]
        E = -B * best_params['center'][0] - 2 * C * best_params['center'][1]
        F = (A * best_params['center'][0]**2 + 
             B * best_params['center'][0] * best_params['center'][1] + 
             C * best_params['center'][1]**2 - 1)
        
        best_params['coefficients'] = (A, B, C, D, E, F)
        
        # Verify it's an ellipse (B² - 4AC < 0)
        discriminant = B**2 - 4*A*C
        best_params['discriminant'] = discriminant
        best_params['is_ellipse'] = discriminant < 0
        
        return best_params
        
    except np.linalg.LinAlgError:
        return None

# Given points
points = np.array([[-18107.85742188,  -9668.421875  ],
                   [-18109.07421875,  -9649.95117188],
                   [-18133.55859375,  -9622.34765625],
                   [-18161.0234375,   -9615.94433594],
                   [-18180.34570312,  -9623.63476562]], dtype=np.float64)

# Bounds for center and rotation
bounds = ((-18170, -18130), (-9679, -9625), (np.deg2rad(320), np.deg2rad(340)))

# Fit the ellipse with optimized method
import time
time1 = time.time()
best_fit = fit_ellipse_to_points_optimized(points, bounds, n_grid_points=30)
time2 = time.time()
print(f'Time: {time2-time1}')


if best_fit is not None:
    print("Best fit found:")
    print(f"Center: ({best_fit['center'][0]:.4f}, {best_fit['center'][1]:.4f})")
    print(f"Rotation: {np.rad2deg(best_fit['theta']):.2f} degrees")
    print(f"Semi-major axis: {best_fit['a']:.4f}")
    print(f"Semi-minor axis: {best_fit['b']:.4f}")
    print(f"Error: {best_fit['error']:.8f}")
    
    A, B, C, D, E, F = best_fit['coefficients']
    print(f"\nCoefficients of the general ellipse equation:")
    print(f"A = {A:.8e}")
    print(f"B = {B:.8e}")
    print(f"C = {C:.8e}")
    print(f"D = {D:.8e}")
    print(f"E = {E:.8e}")
    print(f"F = {F:.8e}")
    
    print(f"\nDiscriminant (B² - 4AC): {best_fit['discriminant']:.8e}")
    if best_fit['is_ellipse']:
        print("The equation represents an ellipse.")
    else:
        print("The equation does not represent an ellipse.")
else:
    print("No valid fit found within the given bounds.")
