import numpy as np
'''
arr = np.array([[3, 4, 5, 5, 7, 7, 7, 7, 7, 7],
                [0, 2, 0, 3, 5, 0, 3, 1, 2, 4]])
arr = np.array([[3, 3, 3, 4, 4, 4, 4, 6, 6, 6, 6, 6],  # contain
                [0, 1, 2, 0, 1, 2, 3, 0, 4, 1, 2, 3]]) # within

'''
arr = np.array([[0,  1,  1,  2,  3,  3,  4,  4,  4,  4,  4,  4,  5,  5,  5,  5,  5,  6,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  7,  20, 20, 20, 20, 20, 20, 20, 20, 20],
                [14, 12, 13, 16, 14, 0,  9,  18, 8,  16, 2,  17, 14, 0,  3,  10, 11, 19, 5,  14, 0,  3,  10, 20, 12, 11, 13, 1,  9,  18, 8,  16, 2,  4,  17, 15, 5,  14, 0,  3,  10, 12, 11, 13, 1]])

def sort_containment_ng(arr):
    contain_polygons = np.unique(arr[0])
    leaves = []
    used_polygons = np.array([])

    for poly in contain_polygons:    
        mask_actual = arr[0] == poly # mask actual polygon in first row
        mask_before = np.isin(arr[1], used_polygons)
        mask_combined = mask_actual & ~mask_before
        covered = arr[1, mask_combined]        
        used_polygons = np.append(used_polygons, covered)
        leaves.append(np.append(covered, poly))
        if poly == 20:       
            print(used_polygons)
    
    return leaves

sort_containment_ng(arr)
