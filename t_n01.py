import numpy as np
import time

def points_right_of_polyline(points, polyline):
    if points.ndim == 2:
        points = points[None, ...]  # add group dim

    G, N, _ = points.shape
    M = len(polyline) - 1  # number of segments

    segs = polyline[1:] - polyline[:-1]      # (M, 2)
    segs_start = polyline[:-1]               # (M, 2)

    vecs = points[:, :, None, :] - segs_start[None, None, :, :]

    cross = segs[None, None, :, 0] * vecs[..., 1] - segs[None, None, :, 1] * vecs[..., 0]

    mask = np.all(cross < 0, axis=-1)  # (G, N)

    return mask

# Benchmark setup
np.random.seed(0)
polyline = np.random.rand(150, 2)
points = np.random.rand(20, 150, 2)

# Warmup
points_right_of_polyline(points, polyline)

# Timing
t0 = time.time()
for _ in range(50):
    mask = points_right_of_polyline(points, polyline)
t1 = time.time()

elapsed = (t1 - t0) / 50

print(mask.shape, elapsed)
# mask.shape, elapsed
