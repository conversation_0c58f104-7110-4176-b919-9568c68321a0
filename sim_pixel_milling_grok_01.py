import numpy as np
import matplotlib.pyplot as plt

# Simulation parameters
D = 10.0          # Tool diameter (mm)
R = D / 2         # Tool radius (mm)
z = 2             # Number of teeth
n = 1000          # Spindle speed (rpm)
f_z = 0.1         # Feed per tooth (mm/tooth)
a_p = 5.0         # Axial depth of cut (mm)
dx = 0.05          # Grid resolution (mm/pixel)
workpiece_size = (100.0, 100.0)  # Workpiece dimensions (mm)

# Derived parameters
v_f = f_z * z * n  # Feed rate (mm/min)
ny, nx = int(workpiece_size[1] / dx), int(workpiece_size[0] / dx)  # Grid shape

# Generate a simple straight tool path (x from 10 to 90, y constant at 50)
path_length = 80.0  # mm
num_steps = int(path_length / f_z) + 1
tool_path = [(10.0 + i * f_z, 50.0) for i in range(num_steps)]

# Initialize workpiece grid (1 = material, 0 = no material)
workpiece = np.ones((ny, nx), dtype=np.uint8)

# Coordinate arrays for vectorized operations
X, Y = np.meshgrid(np.arange(nx) * dx, np.arange(ny) * dx)

# Lists to store results
A_list = []
MRR_list = []
theta_list = []
h_ex_list = []
h_m_list = []

# Simulate material removal along the tool path
for xt, yt in tool_path:
    # Compute distance from tool center to all grid points
    dist_sq = (X - xt)**2 + (Y - yt)**2
    
    # Mask for pixels within tool radius
    tool_mask = dist_sq <= R**2
    
    # Pixels to remove: where tool overlaps with material
    remove_mask = tool_mask & (workpiece == 1)
    
    # Calculate area removed (mm^2)
    A_i = np.sum(remove_mask) * dx**2
    
    # Update workpiece
    workpiece[remove_mask] = 0
    
    # Store area removed
    A_list.append(A_i)
    
    # Calculate process parameters
    MRR_i = A_i * a_p * z * n  # mm^3/min
    a_e = A_i / f_z            # Radial immersion (mm)
    theta = np.arccos(1 - a_e / R) if a_e <= D else np.pi  # Engagement angle (rad)
    h_ex = f_z * np.sin(theta)    # Maximum chip thickness (mm)
    h_m = (a_e * f_z) / (R * theta) if theta > 0 else 0  # Average chip thickness (mm)
    
    MRR_list.append(MRR_i)
    theta_list.append(np.degrees(theta))  # Convert to degrees for plotting
    h_ex_list.append(h_ex)
    h_m_list.append(h_m)

# Convert lists to arrays
s = np.array([i * f_z for i in range(len(tool_path))])  # Path parameter (mm)
A = np.array(A_list)
MRR = np.array(MRR_list)
theta = np.array(theta_list)
h_ex = np.array(h_ex_list)
h_m = np.array(h_m_list)

# Plot results
plt.figure(figsize=(12, 10))

plt.subplot(2, 2, 1)
plt.plot(s, MRR, 'b-', label='MRR')
plt.title('Material Removal Rate')
plt.xlabel('Path Parameter (mm)')
plt.ylabel('MRR (mm³/min)')
plt.grid(True)
plt.legend()

plt.subplot(2, 2, 2)
plt.plot(s, theta, 'r-', label='Engagement Angle')
plt.title('Cutter Engagement Angle')
plt.xlabel('Path Parameter (mm)')
plt.ylabel('Angle (degrees)')
plt.grid(True)
plt.legend()

plt.subplot(2, 2, 3)
plt.plot(s, h_ex, 'g-', label='Max Chip Thickness')
plt.title('Maximum Chip Thickness')
plt.xlabel('Path Parameter (mm)')
plt.ylabel('h_ex (mm)')
plt.grid(True)
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(s, h_m, 'm-', label='Avg Chip Thickness')
plt.title('Average Chip Thickness')
plt.xlabel('Path Parameter (mm)')
plt.ylabel('h_m (mm)')
plt.grid(True)
plt.legend()

plt.tight_layout()
plt.savefig('milling_parameters.png')

# Visualize final workpiece
plt.figure(figsize=(8, 8))
plt.imshow(workpiece, cmap='gray', extent=[0, workpiece_size[0], 0, workpiece_size[1]])
plt.title('Final Workpiece')
plt.xlabel('X (mm)')
plt.ylabel('Y (mm)')
plt.savefig('workpiece.png')