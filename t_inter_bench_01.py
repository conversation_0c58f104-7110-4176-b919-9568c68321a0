import numpy as np
import shapely
from shapely.geometry import LineString, Polygon
import shapely.vectorized
import time

# New vectorized version of find_farthest_outside_point_ng
def find_farthest_outside_points_vectorized(
    inner_ellipses: np.ndarray, 
    outer_polygon: shapely.geometry.Polygon
) -> np.ndarray:
    """
    Finds the farthest outside point for each inner ellipse relative to an outer polygon.
    This is a vectorized version that processes an array of ellipses at once.

    Args:
        inner_ellipses (np.ndar<PERSON>): A NumPy array of shapely.geometry.LineString objects.
        outer_polygon (Shapely Polygon): The single outer polygon to check against.

    Returns:
        np.ndarray: A 1D array where each element is the maximum penetration distance 
                    for the corresponding ellipse. Returns 0.0 for an ellipse if no 
                    part of it is outside the polygon.
    """
    if inner_ellipses.size == 0:
        return np.array([], dtype=float)

    # 1. Get all coordinates and an index mapping them back to their original ellipse
    # This is a highly efficient way to handle coordinates from multiple geometries.
    all_coords, geom_indices = shapely.get_coordinates(inner_ellipses, return_index=True)

    # 2. Vectorized check for which points are outside the polygon
    x, y = all_coords[:, 0], all_coords[:, 1]
    is_inside = shapely.vectorized.contains(outer_polygon, x, y)
    outside_mask = ~is_inside
    
    # 3. Filter to get only the points, their original ellipse index, and create geometries
    outside_coords = all_coords[outside_mask]
    if outside_coords.shape[0] == 0:
        # No points are outside for any of the ellipses
        return np.zeros(len(inner_ellipses))
        
    outside_indices = geom_indices[outside_mask]
    outside_points_geom = shapely.points(outside_coords)
    
    # 4. Calculate the distance from the polygon boundary to each outside point
    distances_to_boundary = shapely.distance(outer_polygon, outside_points_geom)

    # 5. Find the maximum distance for each original ellipse
    # Initialize max distances for each ellipse to zero
    num_ellipses = len(inner_ellipses)
    max_distances = np.zeros(num_ellipses)

    # np.maximum.at performs a vectorized "group-by and find max" operation.
    # For each point's distance, it updates the maximum value found so far for its parent ellipse.
    np.maximum.at(max_distances, outside_indices, distances_to_boundary)

    return max_distances


# New vectorized version of get_ellipse_distances_ng
def get_ellipse_distances_vectorized(
    inner_ellipses: np.ndarray, 
    sides_data: dict
) -> np.ndarray:
    """
    Calculates distances from an array of ellipses to a set of side geometries.

    This is a vectorized version that performs the following steps:
    1. Calculates distances between all ellipses and all sides.
    2. Checks for intersections between all ellipses and all sides.
    3. For intersecting pairs, it calculates the penetration distance relative to the outer polygon.
    4. Uses np.where to combine these results into a final distance matrix.

    Args:
        inner_ellipses (np.ndarray): A NumPy array of shapely.geometry.LineString objects.
        sides_data (dict): A dictionary containing:
            - 'lines' (list or np.ndarray): Side geometries to measure distances to.
            - 'polygon' (shapely.geometry.Polygon): The outer polygon used for penetration depth.

    Returns:
        np.ndarray: A 2D array of shape (num_ellipses, num_sides) containing the distances.
                    Distances are negative if the ellipse intersects the corresponding side line.
    """
    # Ensure inputs are NumPy arrays for broadcasting
    side_lines = np.array(sides_data['lines'])
    outer_polygon = sides_data['polygon']

    if inner_ellipses.size == 0 or side_lines.size == 0:
        return np.empty((len(inner_ellipses), len(side_lines)))

    # To compare every ellipse with every side line, we need to use broadcasting.
    # Reshape inner_ellipses to a column vector (N, 1) to operate against the
    # row vector of side_lines (M,). NumPy/Shapely will broadcast this to (N, M).
    ellipses_col = inner_ellipses[:, np.newaxis]

    # 1. Calculate the intersection mask for all (ellipse, side) pairs
    # The result is a boolean matrix of shape (num_ellipses, num_sides)
    intersects_mask = shapely.intersects(ellipses_col, side_lines)
    
    # 2. Calculate the "no intersection" distances for all pairs
    # This is the default distance if there's no intersection.
    # The result is a float matrix of shape (num_ellipses, num_sides)
    positive_distances = shapely.distance(ellipses_col, side_lines)

    # 3. Calculate the "intersection" distances (penetration depth)
    # This is done for each ellipse, independent of which side it hits.
    # The result is a 1D array of shape (num_ellipses,).
    penetration_distances = find_farthest_outside_points_vectorized(inner_ellipses, outer_polygon)

    # Make the penetration distances negative and reshape to a column vector (N, 1)
    # so it can be broadcast across all sides for the np.where condition.
    negative_distances = -penetration_distances[:, np.newaxis]

    # 4. Combine the results using the intersection mask
    # np.where(condition, value_if_true, value_if_false)
    # If intersects_mask[i, j] is True, use the negative_distances[i].
    # Otherwise, use the positive_distances[i, j].
    final_distances = np.where(intersects_mask, negative_distances, positive_distances)

    return final_distances


# --- Original Functions (for comparison) ---
def get_ellipse_distances_ng(inner_ellipse: shapely.geometry.LineString, sides_data):
    distances = []
    for side_line in sides_data['lines']:
        if inner_ellipse.intersects(side_line):
            dist = find_farthest_outside_point_ng(inner_ellipse, sides_data['polygon'])
            distances.append(-dist)
        else:
            distances.append(side_line.distance(inner_ellipse))
    return distances

def find_farthest_outside_point_ng(inner_ellipse: shapely.geometry.LineString, outer_ellipse: shapely.geometry.Polygon):
    inner_ellipse_coords = shapely.get_coordinates(inner_ellipse)
    x = inner_ellipse_coords[:, 0]
    y = inner_ellipse_coords[:, 1]
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b
    outside_points_geom = shapely.points(inner_ellipse_coords[outside_mask])
    if shapely.is_empty(outside_points_geom):
        return 0.0
    distances = shapely.distance(outer_ellipse, outside_points_geom)
    return np.max(distances)

# --- Test Setup ---
if __name__ == '__main__':
    # 1. Define side geometries
    outer_polygon = Polygon([(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)])
    side1 = LineString([(0, 0), (10, 0)])  # Bottom side
    side2 = LineString([(10, 0), (10, 10)]) # Right side
    
    sides_data = {
        'lines': [side1, side2],
        'polygon': outer_polygon
    }

    # 2. Define some ellipses
    # Ellipse 1: Intersects side1 and is partially outside the polygon
    ellipse1 = LineString([(1, 1), (5, -1), (9, 1)]) 
    # Ellipse 2: Does not intersect any side
    ellipse2 = LineString([(3, 3), (5, 5), (7, 3)])
    # Ellipse 3: Intersects side2, but is fully inside the polygon
    ellipse3 = LineString([(9, 4), (11, 6), (9, 8)])
    
    ellipses_array = np.array([ellipse1, ellipse2, ellipse3])

    # 3. Run the original loop-based method
    time1 = time.time()
    print("--- Running Original Looped Method ---")
    results_original = []
    for ellipse in ellipses_array:
        dist = get_ellipse_distances_ng(ellipse, sides_data)
        results_original.append(dist)
        print(f"Ellipse distances: {np.round(dist, 4)}")
    results_original = np.array(results_original)
    time2 = time.time()
    print(f"Time: {time2-time1}")
    
    # 4. Run the new vectorized method
    time1 = time.time()
    print("\n--- Running New Vectorized Method ---")
    results_vectorized = get_ellipse_distances_vectorized(ellipses_array, sides_data)
    print("Resulting distance matrix:")
    print(np.round(results_vectorized, 4))
    time2 = time.time()
    print(f"Time: {time2-time1}")
    
    # 5. Verify the results are identical
    np.testing.assert_allclose(results_original, results_vectorized)
    print("\n✅ Verification successful: Results from both methods are identical.")