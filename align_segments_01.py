import numpy as np
from shapely.geometry import LineString, Point
from shapely import affinity
import matplotlib.pyplot as plt

def resample_linestring(line, num_points):
    """Resamples a LineString to a specific number of points."""
    points = []
    distances = np.linspace(0, line.length, num_points)
    for d in distances:
        points.append(line.interpolate(d))
    return np.array([(p.x, p.y) for p in points])

def kabsch_umeyama(P, Q):
    """
    Aligns point set Q to point set P using the Ka<PERSON>ch-<PERSON><PERSON><PERSON> algorithm.
    Finds optimal rotation R and translation t such that P ~ R @ Q + t.
    Args:
        P (np.ndarray): Target point set (N x D), D is dimension (e.g., 2 for 2D).
        Q (np.ndarray): Source point set (N x D), to be aligned to P.
    Returns:
        R (np.ndarray): Optimal rotation matrix (D x D).
        t (np.ndarray): Optimal translation vector (D,).
        Q_transformed (np.ndarray): Q transformed to align with P.
    """
    assert P.shape == Q.shape
    n, d = P.shape

    # 1. Calculate centroids
    centroid_P = np.mean(P, axis=0)
    centroid_Q = np.mean(Q, axis=0)

    # 2. Center the point sets
    P_centered = P - centroid_P
    Q_centered = Q - centroid_Q

    # 3. Calculate covariance matrix H = Q_centered.T @ P_centered
    # Note: some formulations use H = P_centered.T @ Q_centered. The SVD components
    # will adjust accordingly. Here, we want to transform Q.
    H = Q_centered.T @ P_centered

    # 4. Perform SVD
    U, S, Vt = np.linalg.svd(H) # Vt is V transpose

    # 5. Calculate rotation matrix R
    R = Vt.T @ U.T

    # 6. Handle reflection (important for ensuring a proper rotation)
    if np.linalg.det(R) < 0:
        # print("Reflection detected, correcting...")
        Vt_corrected = Vt.copy()
        Vt_corrected[-1, :] *= -1  # Flip the sign of the last row of Vt
                                  # (corresponds to the smallest singular value)
        R = Vt_corrected.T @ U.T

    # 7. Calculate translation vector
    t = centroid_P - R @ centroid_Q
    
    # 8. Transform Q
    Q_transformed = (R @ Q.T).T + t

    return R, t, Q_transformed


def align_linestrings(line1_shapely, line2_shapely, num_sample_points=100):
    """
    Aligns line2_shapely to line1_shapely using Kabsch algorithm.
    Args:
        line1_shapely (LineString): The reference linestring.
        line2_shapely (LineString): The linestring to be aligned.
        num_sample_points (int): Number of points to use for resampling and alignment.
    Returns:
        line2_aligned_shapely (LineString): The aligned version of line2_shapely.
        R (np.ndarray): Rotation matrix applied.
        t (np.ndarray): Translation vector applied.
    """
    # 1. Resample linestrings to get corresponding point sets
    P = resample_linestring(line1_shapely, num_sample_points) # Target
    Q_orig_samples = resample_linestring(line2_shapely, num_sample_points) # To be moved

    # 2. Apply Kabsch algorithm
    R, t, Q_transformed_samples = kabsch_umeyama(P, Q_orig_samples)

    # 3. Apply the transformation to the original line2_shapely
    # Shapely's affine_transform expects a 6-tuple: (a, b, d, e, xoff, yoff)
    # for x' = a*x + b*y + xoff
    #     y' = d*x + e*y + yoff
    # This corresponds to:
    # R = [[a, b], [d, e]]
    # t = [xoff, yoff]
    a, b = R[0, 0], R[0, 1]
    d, e = R[1, 0], R[1, 1]
    xoff, yoff = t[0], t[1]

    line2_aligned_shapely = affinity.affine_transform(line2_shapely, [a, b, d, e, xoff, yoff])

    return line2_aligned_shapely, R, t

def calculate_segment_angles(line_points):
    """Calculates angles of segments of a line (represented by points)."""
    angles = []
    for i in range(len(line_points) - 1):
        p1 = line_points[i]
        p2 = line_points[i+1]
        angle = np.arctan2(p2[1] - p1[1], p2[0] - p1[0])
        angles.append(angle)
    return np.array(angles)

def quantify_parallelness(line1_shapely, line2_shapely, num_sample_points=100):
    """
    Quantifies how 'parallel' two linestrings are.
    Returns average distance and average absolute angular difference of segments.
    """
    P1_pts = resample_linestring(line1_shapely, num_sample_points)
    P2_pts = resample_linestring(line2_shapely, num_sample_points)

    # Average distance between corresponding points
    distances = np.linalg.norm(P1_pts - P2_pts, axis=1)
    avg_dist = np.mean(distances)

    # Average angular difference between corresponding segments
    angles1 = calculate_segment_angles(P1_pts)
    angles2 = calculate_segment_angles(P2_pts)
    
    # Normalize angle differences to be between -pi and pi
    angle_diffs = angles1 - angles2
    angle_diffs = (angle_diffs + np.pi) % (2 * np.pi) - np.pi 
    avg_angle_diff = np.mean(np.abs(angle_diffs)) # Use absolute difference

    return avg_dist, avg_angle_diff

# --- Example Usage ---
if __name__ == "__main__":
    # Create two example linestrings
    # Line 1: A simple sine wave
    x1 = np.linspace(0, 2 * np.pi, 50)
    y1 = np.sin(x1)
    line1 = LineString(zip(x1, y1))

    # Line 2: A similar sine wave, but shifted, rotated, and slightly different
    x2_orig = np.linspace(0.5, 2.5 * np.pi, 60) # Different start/end and num points
    y2_orig = 0.8 * np.sin(x2_orig - 0.5) + 0.5
    line2_orig_coords = np.array(list(zip(x2_orig, y2_orig)))

    # Apply an arbitrary transformation to line2_orig_coords to make it unaligned
    theta = np.pi / 6 # 30 degrees rotation
    rotation_matrix = np.array([
        [np.cos(theta), -np.sin(theta)],
        [np.sin(theta), np.cos(theta)]
    ])
    translation_vector = np.array([1.0, 1.5])
    
    # Transform line2_orig_coords
    line2_transformed_coords = (rotation_matrix @ line2_orig_coords.T).T + translation_vector
    line2_initial = LineString(line2_transformed_coords)


    # --- Align line2_initial to line1 ---
    N_SAMPLES = 50 # Number of points for resampling in Kabsch
    line2_aligned, R_applied, t_applied = align_linestrings(line1, line2_initial, num_sample_points=N_SAMPLES)

    print(f"Applied Rotation R:\n{R_applied}")
    print(f"Applied Translation t:\n{t_applied}")

    # --- Quantify parallelness ---
    avg_dist_before, avg_angle_diff_before = quantify_parallelness(line1, line2_initial, N_SAMPLES)
    print(f"\nBefore alignment:")
    print(f"  Average distance between points: {avg_dist_before:.4f}")
    print(f"  Average absolute segment angle difference (radians): {avg_angle_diff_before:.4f} (approx {np.degrees(avg_angle_diff_before):.2f} degrees)")

    avg_dist_after, avg_angle_diff_after = quantify_parallelness(line1, line2_aligned, N_SAMPLES)
    print(f"\nAfter alignment:")
    print(f"  Average distance between points: {avg_dist_after:.4f}")
    print(f"  Average absolute segment angle difference (radians): {avg_angle_diff_after:.4f} (approx {np.degrees(avg_angle_diff_after):.2f} degrees)")


    # --- Plotting ---
    fig, ax = plt.subplots(figsize=(10, 6))

    # Original lines
    x1_plot, y1_plot = line1.xy
    x2_initial_plot, y2_initial_plot = line2_initial.xy
    ax.plot(x1_plot, y1_plot, 'b-', label='Line 1 (Reference)', linewidth=2)
    ax.plot(x2_initial_plot, y2_initial_plot, 'r-', label='Line 2 (Initial)', linewidth=2, alpha=0.7)

    # Aligned line
    x2_aligned_plot, y2_aligned_plot = line2_aligned.xy
    ax.plot(x2_aligned_plot, y2_aligned_plot, 'g--', label='Line 2 (Aligned)', linewidth=2)

    # Plot resampled points used for Kabsch (optional, for understanding)
    # P_samples = resample_linestring(line1, N_SAMPLES)
    # Q_initial_samples = resample_linestring(line2_initial, N_SAMPLES)
    # Q_aligned_samples = resample_linestring(line2_aligned, N_SAMPLES) # or use Q_transformed_samples from kabsch
    # ax.plot(P_samples[:,0], P_samples[:,1], 'bo', markersize=3, alpha=0.5, label='Line 1 Samples')
    # ax.plot(Q_initial_samples[:,0], Q_initial_samples[:,1], 'ro', markersize=3, alpha=0.5, label='Line 2 Initial Samples')
    # ax.plot(Q_aligned_samples[:,0], Q_aligned_samples[:,1], 'go', markersize=3, alpha=0.5, label='Line 2 Aligned Samples')


    ax.set_title("Aligning Two Linestrings")
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.legend()
    ax.axis('equal') # Important for visualizing rotations correctly
    plt.grid(True)
    plt.show()