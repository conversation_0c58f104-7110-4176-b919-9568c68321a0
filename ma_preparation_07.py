import numpy as np
import bpy
import shapely
import pyvoronoi
from mathutils import Vector
from shapely.geometry import Point
from shapely import prepare, crosses
import math
import random
from shapely.ops import linemerge
import networkx as nx

import time


def has_selected_objects() -> bool:
    """Check if there are any selected objects in the Blender context."""
    selected_objects = bpy.context.selected_objects
    if selected_objects:
        return True
    print("No objects are currently selected.")
    return False


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    else:
        return []
    return selected_objects


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float64)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return []
    

def get_collection(name: str, rand_int: int) -> bpy.types.Collection:
    """Get a collection by name, creating it if it doesn't exist."""
    name = name + str(rand_int)
    if name not in bpy.data.collections:
        bpy.data.collections.new(name)
        bpy.context.scene.collection.children.link(bpy.data.collections[name])
    return bpy.data.collections[name]


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """    
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def make_medial_edges(polygon, rounding_precision=4):
    pv = pyvoronoi.Pyvoronoi(100)

    # Process exterior polygon more efficiently
    coords = np.array(polygon.exterior.coords[:-1])  # Exclude last point
    segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
    for segment in segments:
        # Convert segment to the format expected by pyvoronoi
        start = [segment[0], segment[1]]
        end = [segment[2], segment[3]]
        pv.AddSegment([start, end])

    # Process holes more efficiently
    if polygon.interiors:
        for interior in polygon.interiors:
            coords = np.array(interior.coords[:-1])
            segments = np.column_stack((coords, np.roll(coords, -1, axis=0)))
            for segment in segments:
                start = [segment[0], segment[1]]
                end = [segment[2], segment[3]]
                pv.AddSegment([start, end])

    pv.Construct()
    edges = pv.GetEdges()
    vertices = pv.GetVertices()
    cells = pv.GetCells()

    # Prepare polygon for faster contains checks
    shapely.prepare(polygon)  # This modifies polygon in-place
    
    ma_edges = []
    twins = set()

    for cell in cells:
        for edge_idx in cell.edges:
            e = edges[edge_idx]
            
            if not e.is_primary or e.start == -1 or e.end == -1 or edge_idx in twins:
                continue

            startVertex = vertices[e.start]
            endVertex = vertices[e.end]
            mid_point = Point(((startVertex.X + endVertex.X) / 2, 
                             (startVertex.Y + endVertex.Y) / 2))

            if not polygon.contains(mid_point):
                continue

            twins.add(e.twin)
            
            if e.is_linear:
                start_point = (round(startVertex.X, rounding_precision), 
                             round(startVertex.Y, rounding_precision))
                end_point = (round(endVertex.X, rounding_precision), 
                           round(endVertex.Y, rounding_precision))
                ma_edges.append(shapely.geometry.LineString([start_point, end_point]))
            else:
                max_distance = math.sqrt((startVertex.X - endVertex.X)**2 +
                                       (startVertex.Y - endVertex.Y)**2) / 10
                # Convert map object to list
                points = list(pv.DiscretizeCurvedEdge(edge_idx, max_distance*1, 
                                                    parabola_equation_tolerance=0.001))
                # Handle first and last points separately
                points[0] = [round(points[0][0], rounding_precision), 
                           round(points[0][1], rounding_precision)]
                points[-1] = [round(points[-1][0], rounding_precision), 
                            round(points[-1][1], rounding_precision)]
                ma_edges.append(shapely.geometry.LineString(points))

    # Merge and simplify edges
    merged = linemerge(ma_edges)
    if merged.geom_type == 'LineString':
        ma_edges = [merged.simplify(0.05)]
    else:
        ma_edges = [edge.simplify(0.05) for edge in merged.geoms]

    return ma_edges


def make_acyclic_undirected(graph):
    """Removes edges from an undirected graph to make it acyclic."""
    graph_copy = graph.copy()
    
    while True:
        try:
            cycle = nx.find_cycle(graph_copy)
            # Find the shortest edge in the cycle
            shortest_edge = min(cycle, key=lambda x: graph_copy[x[0]][x[1]].get('weight', 1))
            print(f"Removing edge: {shortest_edge}")
            u, v = shortest_edge
            graph_copy.remove_edge(u, v)
        except nx.NetworkXNoCycle:
            break
            
    return graph_copy


def get_subgraph_from_edge(acyclic_graph, edge, start_node):
    """
    Extract subgraph information from edge connected to start_node.
    
    Args:
        acyclic_graph: NetworkX graph
        edge: Edge in the graph
        start_node: Starting node in the graph
        
    Returns:
        tuple: Contains 'branches', 'leafs' information
    """

    branches = []
    leafs = []

    edge_nodes = list(edge)
    end_node = edge_nodes[1] if edge_nodes[0] == start_node else edge_nodes[0]
                
    visited = {end_node}
    to_visit = {end_node}
    
    # Simple BFS
    while to_visit:
        current = to_visit.pop()
        
        if acyclic_graph.nodes[current].get('branch', False):
            branches.append(current)
        else:
            leafs.append(current)
        
        for neighbor in acyclic_graph.neighbors(current):
            if neighbor != start_node and neighbor not in visited:
                visited.add(neighbor)
                to_visit.add(neighbor)
        
    return branches, leafs


def main():
    uncut_material_margin = 10 # in mm's
    rand_int = random.randint(0, 1000000)
    geometry = get_geometry()

    if len(geometry) == 1:
        polygon = shapely.geometry.Polygon(shell=geometry[0])
    else:
        holes = [geom for geom in geometry[1:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)    
        
    polygon_buffered = polygon.buffer(-1)
    boundary = polygon.boundary

    shapely.prepare(polygon_buffered)
    shapely.prepare(boundary)
                    
    edges = make_medial_edges(polygon)    
    edges_branches = []
    edges_leafs = []

    for edge in edges:
        if crosses(polygon_buffered, edge):
            edges_leafs.append(edge)
        else:
            edges_branches.append(edge)

    G = nx.Graph()
    rounding_precision = 8
    for edge in edges_branches:
        nodes = []
        for coords in (edge.coords[0], edge.coords[-1]):            
            node = (round(coords[0], rounding_precision), round(coords[1], rounding_precision))
            radius = boundary.distance(shapely.geometry.Point(node))
            if node not in G:
                G.add_node(node, branch=True, radius=radius)
            nodes.append(node)
        G.add_edge(*nodes, weight=edge.length, edge=edge)

    for edge in edges_leafs:
        nodes = []
        for coords in (edge.coords[0], edge.coords[-1]):            
            node = (round(coords[0], rounding_precision), round(coords[1], rounding_precision))
            if node not in G:
                G.add_node(node, branch=False)
            nodes.append(node)
        G.add_edge(*nodes, weight=edge.length, edge=edge)

    try:
        nx.find_cycle(G)
        print("Graph contains a cycle, length:", len(G.edges))
        acyclic_graph = make_acyclic_undirected(G)
        print("Graph made acyclic, length:", len(acyclic_graph.edges))
        
    except nx.NetworkXNoCycle:
        print("Graph does not contain a cycle")
    
    # print(f"nodes: {len(G.nodes)}")
    # print(f"edges: {len(G.edges)}") 

    # Filter nodes that have radius data (branch nodes) and find max
    branch_nodes = [(node, data) for node, data in acyclic_graph.nodes(data=True) 
                   if data.get('branch', False) and 'radius' in data]
    
    max_node = max(branch_nodes, key=lambda x: x[1]['radius'])

    print(f'node: {max_node[0]}, radius: {max_node[1]["radius"]}')
    start_node = max_node[0]
    print(f'edges of node: {len(list(acyclic_graph.edges(start_node)))}')

    traversed_edges = []
    edge = list(acyclic_graph.edges(start_node))[0]    
    # Perform DFS traversal through the edge
    edge_nodes = list(edge)
    other_node = edge_nodes[1] if edge_nodes[0] == start_node else edge_nodes[0]
    
    visited = set()
    stack = [other_node]

    # Simple DFS
    while stack:
        current = stack.pop()
        if current in visited:
            continue
            
        visited.add(current)
        traversed_edges.append(current)
        current_radius = acyclic_graph.nodes[current]['radius']
        
        # Process all edges at once
        edges_to_process = [
            edge for edge in acyclic_graph.edges(current)
            if edge[0] != start_node and edge[1] != start_node
        ]
        
        for edge in edges_to_process:
            subgraph = get_subgraph_from_edge(acyclic_graph, edge, current)
            
            if not subgraph['branches']:
                # Handle leaf-only case
                if subgraph['leafs']:
                    leafs_array = np.array(subgraph['leafs'])
                    leaf_distances = np.linalg.norm(leafs_array - np.array(current), axis=1)
                    
                    if np.all(leaf_distances <= current_radius + uncut_material_margin):
                        nodes_to_remove = [n for n in subgraph['leafs'] if n != current]
                        acyclic_graph.remove_nodes_from(nodes_to_remove)
                continue
            
            # Process branches
            branch_array = np.array(subgraph['branches'])
            branch_distances = np.linalg.norm(branch_array - np.array(current), axis=1)
            
            if not np.all(branch_distances <= current_radius):
                continue
                
            # All branches are within radius, check leafs if any
            if not subgraph['leafs']:
                nodes_to_remove = [n for n in subgraph['branches'] if n != current]
                acyclic_graph.remove_nodes_from(nodes_to_remove)
                continue
                
            # Process leafs
            leafs_array = np.array(subgraph['leafs'])
            leaf_distances = np.linalg.norm(leafs_array - np.array(current), axis=1)
            
            if np.all(leaf_distances <= current_radius + uncut_material_margin):
                nodes_to_remove = [
                    n for n in (subgraph['leafs'] + subgraph['branches'])
                    if n != current
                ]
                acyclic_graph.remove_nodes_from(nodes_to_remove)

        # Get remaining neighbors after all removals
        stack.extend(
            n for n in acyclic_graph.neighbors(current)
            if n != start_node 
            and n not in visited 
            and acyclic_graph.nodes[n].get('branch', False)
        )
        
        start_node = current

    for i, node in enumerate(traversed_edges):
        create_line_object([node], f"traversed_edge_{i}")
                
                
    if False:
        leafs_collection = get_collection("leafs", rand_int)
        branches_collection = get_collection("branches", rand_int)

        for i, edge in enumerate(edges_branches):
            obj = create_line_object(edge.coords, f"branches_{i}", color=(0.6, 0, 0.7, 1))
            try:
                branches_collection.objects.link(obj)
                bpy.context.collection.objects.unlink(obj)
            except:
                pass
        
        for i, edge in enumerate(edges_leafs):
            obj = create_line_object(edge.coords, f"leafs_{i}", color=(1, 0, 0, 1))
            try:
                leafs_collection.objects.link(obj)
                bpy.context.collection.objects.unlink(obj)
            except:
                pass
    

if __name__ == "__main__":
    main()
