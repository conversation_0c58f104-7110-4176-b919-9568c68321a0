import numpy as np
import timeit

# --- Function to be benchmarked (copied from previous answer) ---
# (I've made a small correction to the collinear case: use 1e-9 for b instead of 1e-6 for consistency)
# (Also changed det(M) == 0 to np.isclose(np.linalg.det(M), 0.0) for robustness)
def get_steiner_inellipse_params(p0, p1, p2):
    """
    Calculates the parameters of the Steiner inellipse for a triangle defined by p0, p1, p2.
    Args:
        p0, p1, p2: NumPy arrays or tuples representing the 2D coordinates of triangle vertices.
    Returns:
        A tuple (center, a, b, angle_rad)
    """
    p0 = np.asarray(p0)
    p1 = np.asarray(p1)
    p2 = np.asarray(p2)

    r_r = (2 - np.sqrt(2)) / 2.0
    # I_r = np.array([r_r, r_r]) # Not strictly needed if center is (P0+P1+P2)/3

    t = p0
    v1 = p1 - p0
    v2 = p2 - p0
    M = np.column_stack((v1, v2))

    if np.isclose(np.linalg.det(M), 0.0): # Check for degenerate triangle
        # print("Warning: Points are collinear or form a degenerate triangle.") # Suppress for benchmark
        center = (p0 + p1 + p2) / 3.0
        
        # Simplified handling for benchmark focus if collinear, use outer points
        # This part might add slight overhead if hit often, but random points rarely are perfectly collinear
        points = sorted([p0, p1, p2], key=lambda p_item: (p_item[0], p_item[1]))
        p_start, _, p_end = points[0], points[1], points[2]
        
        vec = p_end - p_start
        length = np.linalg.norm(vec)
        if length == 0: # All points are the same
             return center, 0.0, 0.0, 0.0

        angle_rad = np.arctan2(vec[1], vec[0])
        return center, length / 2.0, 1e-9, angle_rad # Very thin ellipse

    U, S_diag, Vt = np.linalg.svd(M)

    # S_diag from np.linalg.svd is sorted in descending order
    a = S_diag[0] * r_r
    b = S_diag[1] * r_r
    angle_rad = np.arctan2(U[1, 0], U[0, 0]) # Angle from the first column of U

    C_ellipse = (p0 + p1 + p2) / 3.0
    return C_ellipse, a, b, angle_rad

# --- Benchmarking Setup ---

# Setup string for timeit
# This string is executed once before the timing loop.
# It needs to import numpy and define the function.
setup_code = """
import numpy as np

# --- Function definition pasted here ---
def get_steiner_inellipse_params(p0, p1, p2):
    \"\"\"
    Calculates the parameters of the Steiner inellipse for a triangle defined by p0, p1, p2.
    Args:
        p0, p1, p2: NumPy arrays or tuples representing the 2D coordinates of triangle vertices.
    Returns:
        A tuple (center, a, b, angle_rad)
    \"\"\"
    p0 = np.asarray(p0)
    p1 = np.asarray(p1)
    p2 = np.asarray(p2)
    r_r = (2 - np.sqrt(2)) / 2.0
    t = p0
    v1 = p1 - p0
    v2 = p2 - p0
    M = np.column_stack((v1, v2))
    if np.isclose(np.linalg.det(M), 0.0):
        center = (p0 + p1 + p2) / 3.0
        points = sorted([p0, p1, p2], key=lambda p_item: (p_item[0], p_item[1]))
        p_start, _, p_end = points[0], points[1], points[2]
        vec = p_end - p_start
        length = np.linalg.norm(vec)
        if length == 0:
             return center, 0.0, 0.0, 0.0
        angle_rad = np.arctan2(vec[1], vec[0])
        return center, length / 2.0, 1e-9, angle_rad
    U, S_diag, Vt = np.linalg.svd(M)
    a = S_diag[0] * r_r
    b = S_diag[1] * r_r
    angle_rad = np.arctan2(U[1, 0], U[0, 0])
    C_ellipse = (p0 + p1 + p2) / 3.0
    return C_ellipse, a, b, angle_rad
# --- End function definition ---
"""

# Statement to be timed
# This string is executed 'number' times.
# We generate new random points for each call to make the benchmark more realistic.
stmt_code = """
P0_rand = np.random.rand(2) * 100.0
P1_rand = np.random.rand(2) * 100.0
P2_rand = np.random.rand(2) * 100.0

# Crude way to ensure points are not identical, reducing degenerate cases
# A very small chance random points are perfectly collinear for 2D.
while np.allclose(P0_rand, P1_rand) or np.allclose(P1_rand, P2_rand) or np.allclose(P0_rand, P2_rand):
    P0_rand = np.random.rand(2) * 100.0
    P1_rand = np.random.rand(2) * 100.0
    P2_rand = np.random.rand(2) * 100.0

get_steiner_inellipse_params(P0_rand, P1_rand, P2_rand)
"""

num_runs = 10000

# Run the benchmark
# timeit.timeit executes stmt_code 'number' times and returns the total time for these executions.
print(f"Starting benchmark for {num_runs} runs...")
total_time = timeit.timeit(stmt_code, setup=setup_code, number=num_runs)

avg_time_per_run = total_time / num_runs

print(f"\n--- Benchmark Results ---")
print(f"Total time for {num_runs} runs: {total_time:.4f} seconds")
print(f"Average time per run: {avg_time_per_run * 1e6:.2f} microseconds") # microseconds = seconds * 1,000,000
print(f"Average time per run: {avg_time_per_run * 1e3:.4f} milliseconds")  # milliseconds = seconds * 1,000

# For verification, run one instance:
P0_test = np.random.rand(2) * 100
P1_test = np.random.rand(2) * 100
P2_test = np.random.rand(2) * 100
# Ensure non-identical for the test run too
while np.allclose(P0_test, P1_test) or np.allclose(P1_test, P2_test) or np.allclose(P0_test, P2_test):
    P0_test = np.random.rand(2) * 100
    P1_test = np.random.rand(2) * 100
    P2_test = np.random.rand(2) * 100

result = get_steiner_inellipse_params(P0_test, P1_test, P2_test)
print(f"\nSample output for one run (P0={P0_test}, P1={P1_test}, P2={P2_test}):")
print(f"Center: {result[0]}, a: {result[1]:.2f}, b: {result[2]:.2f}, angle: {np.degrees(result[3]):.2f} deg")
