import numpy as np
import cv2
import math
import time

# --- Constants for Pixel Values ---
EMPTY_SPACE = 0
MATERIAL = 255 # Use 255 for uint8 image representation (easier to visualize)
TOOL_COLOR_VIS = 150 # For visualizing the tool's current path
PLUNGE_HOLE_COLOR = 0 # Plunge hole is just empty space

# --- Simulation Parameters ---
WORKSPACE_SIZE = (500, 500) # Pixels (height, width)
TOOL_RADIUS = 20          # Pixels
STEP_SIZE = 1             # Pixels - How far the tool moves in one step (delta move)
SEARCH_ANGLE_RANGE_DEG = 120 # Degrees (+/- from the previous direction)
SEARCH_ANGLE_STEP_DEG = 1   # Degrees - Granularity of direction search
MIN_ENGAGEMENT_PIXELS = 1  # Min pixels to cut for a valid move
MAX_ENGAGEMENT_PIXELS = (2 * TOOL_RADIUS * STEP_SIZE) * 0.8 # Heuristic max (e.g., 80% of rectangle)

# --- Helper Functions ---

def create_pocket_workspace(size, pocket_poly_coords):
    """Creates the initial workspace with the pocket material."""
    workspace = np.full(size, EMPTY_SPACE, dtype=np.uint8)
    # Convert polygon coords to OpenCV format (list of [[x,y]] arrays)
    pts = np.array(pocket_poly_coords, dtype=np.int32)
    pts = pts.reshape((-1, 1, 2))
    cv2.fillPoly(workspace, [pts], MATERIAL)
    return workspace

def draw_tool(image, center_px, radius, color, thickness=-1):
    """Draws a circle representing the tool (for visualization or cutting)."""
    center_int = (int(round(center_px[0])), int(round(center_px[1])))
    radius_int = int(round(radius))
    cv2.circle(image, center_int, radius_int, color, thickness) # thickness=-1 fills

def get_tool_mask(image_shape, center_px, radius):
    """Creates a boolean mask for the area covered by the tool."""
    # 1. Create mask as uint8
    mask_uint8 = np.zeros(image_shape, dtype=np.uint8)
    center_int = (int(round(center_px[0])), int(round(center_px[1])))
    radius_int = int(round(radius))

    # 2. Draw onto the uint8 mask using 1 (or 255) as the color
    cv2.circle(mask_uint8, center_int, radius_int, 1, -1) # Use 1 (or 255)

    # 3. Convert back to boolean
    mask_bool = mask_uint8.astype(bool)
    # Alternatively: mask_bool = (mask_uint8 > 0)

    return mask_bool

def check_engagement(workspace, next_pos_px, tool_radius):
    """
    Calculates the tool engagement for moving to next_pos_px.
    Engagement is defined as the number of MATERIAL pixels
    that the tool will cover at the next position.
    """
    # Ensure position is within bounds (simplistic check)
    h, w = workspace.shape
    x, y = next_pos_px
    r = tool_radius
    if not (r <= x < w - r and r <= y < h - r):
         return 0 # Treat out-of-bounds as zero engagement (or invalid)

    tool_mask = get_tool_mask(workspace.shape, next_pos_px, tool_radius)

    # Get the pixels from the workspace under the tool's potential new position
    pixels_under_tool = workspace[tool_mask]

    # Count how many of those pixels are material
    engagement = np.count_nonzero(pixels_under_tool == MATERIAL)
    return engagement

def cut_material(workspace, pos_px, tool_radius):
    """Permanently removes material from the workspace at the given tool position."""
    draw_tool(workspace, pos_px, tool_radius, EMPTY_SPACE, thickness=-1)

def visualize_state(workspace, toolpath, current_pos, tool_radius):
    """Displays the current state of the simulation."""
    vis_img = cv2.cvtColor(workspace, cv2.COLOR_GRAY2BGR)

    # Draw the toolpath so far
    if len(toolpath) > 1:
        pts = np.array(toolpath, dtype=np.int32).reshape((-1, 1, 2))
        cv2.polylines(vis_img, [pts], isClosed=False, color=(0, 255, 0), thickness=1) # Green path

    # Draw the current tool position
    draw_tool(vis_img, current_pos, tool_radius, (255, 0, 0), thickness=1) # Blue outline

    cv2.imshow("Simulation", vis_img)
    key = cv2.waitKey(10) # Pause for visualization (adjust delay)
    if key == 27: # ESC key
        raise KeyboardInterrupt("ESC pressed")


# --- Core Simulation Logic (Based on Algorithm 1/2 ideas) ---

def find_next_move_greedy(workspace, current_pos, last_direction_rad, tool_radius,
                           step_size, angle_range_deg, angle_step_deg,
                           min_engagement, max_engagement):
    """
    Finds the next tool position using a brute-force angle search.
    Greedy: Returns the first valid move found within the angle range,
            starting from 0 deviation.
    """
    best_move = None
    min_deviation_rad = math.radians(angle_range_deg) + 0.1 # Initialize higher

    angles_to_check_deg = [0] # Start with moving straight
    for dev_deg in range(angle_step_deg, angle_range_deg + 1, angle_step_deg):
        angles_to_check_deg.extend([dev_deg, -dev_deg])

    for angle_dev_deg in angles_to_check_deg:
        current_angle_rad = last_direction_rad + math.radians(angle_dev_deg)

        # Calculate potential next position
        dx = step_size * math.cos(current_angle_rad)
        dy = step_size * math.sin(current_angle_rad)
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)

        # Check engagement at this potential position
        engagement = check_engagement(workspace, next_pos, tool_radius)

        # Check if engagement is within the desired range
        if min_engagement <= engagement <= max_engagement:
             # Found a valid move, return it (greedy approach)
             print(f"  Found valid move: angle_dev={angle_dev_deg:.1f} deg, eng={engagement}")
             return next_pos, current_angle_rad, engagement

    # If no valid move was found in the range
    print("  No valid move found within engagement limits.")
    return None, last_direction_rad, 0


# --- Main Simulation ---
if __name__ == "__main__":    
    # Define a rectangular pocket
    pocket_rect = [
        (100, 100), (400, 100), (400, 400), (100, 400)
    ]

    # --- Choose Pocket and Start ---
    pocket_coords = pocket_rect # Or pocket_L_shape
    workspace = create_pocket_workspace(WORKSPACE_SIZE, pocket_coords)

    # --- Plunge Hole ---
    # Start near the center (calculate centroid or pick manually)
    # For rect:
    start_pos = (250.0, 250.0)
    # For L-shape: approximate center of a larger area
    # start_pos = (150.0, 150.0)

    print(f"Creating plunge hole at {start_pos} with radius {TOOL_RADIUS}")
    cut_material(workspace, start_pos, TOOL_RADIUS) # Pre-cut the start area

    # --- Initialize Simulation State ---
    toolpath = [start_pos]
    current_pos = start_pos
    # Initial direction (e.g., right)
    current_direction_rad = 0.0 # Radians (0 is along positive X-axis)
    max_steps = 100 # Safety break

    print(f"Starting simulation...")
    print(f"Tool Radius: {TOOL_RADIUS}, Step Size: {STEP_SIZE}")
    print(f"Engagement Range: [{MIN_ENGAGEMENT_PIXELS}, {MAX_ENGAGEMENT_PIXELS}] pixels")
    print(f"Search Angle: +/- {SEARCH_ANGLE_RANGE_DEG} deg, Step: {SEARCH_ANGLE_STEP_DEG} deg")
    start_time = time.time()

    try:
        for step in range(max_steps):
            print(f"\nStep {step+1}")
            visualize_state(workspace, toolpath, current_pos, TOOL_RADIUS)

            next_pos, next_direction_rad, engagement = find_next_move_greedy(
                workspace,
                current_pos,
                current_direction_rad,
                TOOL_RADIUS,
                STEP_SIZE,
                SEARCH_ANGLE_RANGE_DEG,
                SEARCH_ANGLE_STEP_DEG,
                MIN_ENGAGEMENT_PIXELS,
                MAX_ENGAGEMENT_PIXELS
            )

            print(f"Next position: {next_pos}, Engagement: {engagement}")

            if next_pos is None:
                print("Simulation stopped: No suitable next move found.")
                break

            # --- Update State ---
            current_pos = next_pos
            current_direction_rad = next_direction_rad
            toolpath.append(current_pos)

            # --- Perform the Cut ---
            cut_material(workspace, current_pos, TOOL_RADIUS)

        else: # Loop finished without break (max_steps reached)
            print(f"Simulation stopped: Reached max steps ({max_steps}).")

    except KeyboardInterrupt:
        print("\nSimulation interrupted by user.")
    finally:
        end_time = time.time()
        print(f"\nSimulation finished in {end_time - start_time:.2f} seconds.")
        print(f"Toolpath length: {len(toolpath)} points.")

        # --- Final Visualization ---
        visualize_state(workspace, toolpath, current_pos, TOOL_RADIUS)
        print("Showing final state. Press ESC or close window to exit.")
        while True:
            key = cv2.waitKey(0)
            if key == 27: # ESC
                break
        cv2.destroyAllWindows()