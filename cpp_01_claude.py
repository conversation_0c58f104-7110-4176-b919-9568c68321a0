import networkx as nx
from itertools import combinations

def chinese_postman(G, start_node):
    # Check if graph is connected
    if not nx.is_connected(G):
        raise nx.NetworkXError("Graph must be connected")

    # Check if start_node exists in graph
    if start_node not in G.nodes():
        raise nx.NetworkXError(f"Start node {start_node} not in graph")

    # Find nodes with odd degree
    odd_nodes = [v for v, d in G.degree() if d % 2 == 1]

    # If graph is Eulerian (no odd nodes)
    if not odd_nodes:
        euler_circuit = list(nx.eulerian_circuit(G, source=start_node))
        # Don't close the circuit - just take the path
        path = [u for u, v in euler_circuit]
        path.append(euler_circuit[-1][1])
        # Calculate weight only for the path
        total_weight = sum(G[u][v]['weight'] for u, v in zip(path[:-1], path[1:]))
        return path, total_weight

    # For non-Eulerian graphs
    odd_graph = nx.Graph()
    for u, v in combinations(odd_nodes, 2):
        shortest_path = nx.shortest_path(G, u, v, weight='weight')
        path_weight = sum(G[shortest_path[i]][shortest_path[i+1]]['weight']
                        for i in range(len(shortest_path)-1))
        odd_graph.add_edge(u, v, weight=path_weight, path=shortest_path)

    matching = list(nx.algorithms.matching.min_weight_matching(odd_graph))

    additional_cost = 0
    augmented = nx.MultiGraph(G)
    for u, v in matching:
        path = odd_graph[u][v]['path']
        for i in range(len(path) - 1):
            augmented.add_edge(path[i], path[i+1], weight=G[path[i]][path[i+1]]['weight'])
            additional_cost += G[path[i]][path[i+1]]['weight']

    # Get Eulerian path instead of circuit
    path = list(nx.eulerian_path(augmented, source=start_node))
    # Convert edge list to node list
    final_path = [u for u, v in path]
    final_path.append(path[-1][1])  # Add the last node

    # Calculate total weight for the path
    original_weight = sum(G[u][v]['weight'] for u, v in zip(final_path[:-1], final_path[1:]))
    total_weight = original_weight

    return final_path, total_weight

def main():
    # Create sample graph
    G = nx.Graph()
    G.add_edge('A', 'B', weight=10)
    G.add_edge('B', 'C', weight=1)
    G.add_edge('C', 'D', weight=1)
    G.add_edge('D', 'A', weight=20)
    G.add_edge('B', 'D', weight=1)

    # Solve
    path, weight = chinese_postman(G, start_node='D')
    print(f"Path: {' -> '.join(path)}")
    print(f"Total weight: {weight}")

if __name__ == "__main__":
    main()
