import numpy as np

def relative_boundary_distance(points, center, theta, semi_major, semi_minor, front=True):
        """
        Compute the relative distance to the ellipse boundary (loop over ellipses, vectorized over points)
        
        Parameters:
        - points: array-like, shape (n_points, 2)
        - centers: array-like, shape (n_ellipses, 2)
        - axes: array-like, shape (n_ellipses, 2), semi-axes [a, b] per ellipse
        - angles: array-like, shape (n_ellipses,), rotation angles in radians    
        
        Returns:
        - float array, shape (n_ellipses,): minimum distance to ellipse boundary for each ellipse
        """
        translated = points - center    
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        rot_matrix = np.array([[cos_theta, sin_theta], [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T

        val = (translated[:, 0] ** 2 / semi_major ** 2) + (translated[:, 1] ** 2 / semi_minor ** 2)        

        if front:
            val = np.mean(np.abs(val - 1)**2)
        else:
            val = np.abs(1 - np.min(val)) **2
        return val


def optimized_objective_function(points, center, theta, semi_major, semi_minor, front=True):
    """
    Optimized ellipse fitting objective function with multiple performance improvements.
    
    Args:
        points: Nx2 array of points
        center: 2-element array of ellipse center
        theta: rotation angle in radians
        semi_major: semi-major axis length
        semi_minor: semi-minor axis length
        front: if True, use mean squared error; if False, use min-based error
    
    Returns:
        Objective function value
    """
    # Pre-compute trigonometric values (avoid recomputation)
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    
    # Vectorized translation and rotation in one step
    # Avoid creating intermediate arrays when possible
    dx = points[:, 0] - center[0]
    dy = points[:, 1] - center[1]
    
    # Apply rotation directly without matrix multiplication
    x_rot = dx * cos_theta + dy * sin_theta
    y_rot = -dx * sin_theta + dy * cos_theta
    
    # Pre-compute squared axis ratios to avoid repeated division
    inv_major_sq = 1.0 / (semi_major * semi_major)
    inv_minor_sq = 1.0 / (semi_minor * semi_minor)
    
    # Compute ellipse equation values
    val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
    
    if front:
        # Use more numerically stable computation
        diff = val - 1.0
        return np.mean(diff * diff)
    else:
        return (1.0 - np.min(val)) ** 2

# Alternative version using einsum for very large datasets
def optimized_objective_einsum(points, center, theta, semi_major, semi_minor, front=True):
    """
    Version optimized for very large point arrays using einsum.
    """
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    
    # Translation
    translated = points - center
    
    # Rotation using einsum (can be faster for large arrays)
    rot_matrix = np.array([[cos_theta, sin_theta], [-sin_theta, cos_theta]])
    rotated = np.einsum('ij,kj->ki', rot_matrix, translated)
    
    # Ellipse equation
    inv_axes_sq = np.array([1/(semi_major**2), 1/(semi_minor**2)])
    val = np.einsum('ij,j->i', rotated**2, inv_axes_sq)
    
    if front:
        diff = val - 1.0
        return np.mean(diff * diff)
    else:
        return (1.0 - np.min(val)) ** 2

# Memory-efficient version for streaming/chunked processing
def optimized_objective_chunked(points, center, theta, semi_major, semi_minor, front=True, chunk_size=10000):
    """
    Memory-efficient version that processes points in chunks.
    Useful for very large point arrays that don't fit in memory.
    """
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    inv_major_sq = 1.0 / (semi_major * semi_major)
    inv_minor_sq = 1.0 / (semi_minor * semi_minor)
    
    n_points = len(points)
    
    if front:
        total_error = 0.0
        for i in range(0, n_points, chunk_size):
            chunk = points[i:i+chunk_size]
            dx = chunk[:, 0] - center[0]
            dy = chunk[:, 1] - center[1]
            
            x_rot = dx * cos_theta + dy * sin_theta
            y_rot = -dx * sin_theta + dy * cos_theta
            
            val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
            diff = val - 1.0
            total_error += np.sum(diff * diff)
        
        return total_error / n_points
    else:
        min_val = float('inf')
        for i in range(0, n_points, chunk_size):
            chunk = points[i:i+chunk_size]
            dx = chunk[:, 0] - center[0]
            dy = chunk[:, 1] - center[1]
            
            x_rot = dx * cos_theta + dy * sin_theta
            y_rot = -dx * sin_theta + dy * cos_theta
            
            val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
            chunk_min = np.min(val)
            if chunk_min < min_val:
                min_val = chunk_min
        
        return (1.0 - min_val) ** 2

# Numba-optimized version (requires: pip install numba)
try:
    from numba import njit
    
    @njit
    def optimized_objective_numba(points, center, theta, semi_major, semi_minor, front=True):
        """
        Numba JIT-compiled version for maximum performance.
        First call will be slower due to compilation, subsequent calls very fast.
        """
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        inv_major_sq = 1.0 / (semi_major * semi_major)
        inv_minor_sq = 1.0 / (semi_minor * semi_minor)
        
        n_points = points.shape[0]
        
        if front:
            total_error = 0.0
            for i in range(n_points):
                dx = points[i, 0] - center[0]
                dy = points[i, 1] - center[1]
                
                x_rot = dx * cos_theta + dy * sin_theta
                y_rot = -dx * sin_theta + dy * cos_theta
                
                val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
                diff = val - 1.0
                total_error += diff * diff
            
            return total_error / n_points
        else:
            min_val = np.inf
            for i in range(n_points):
                dx = points[i, 0] - center[0]
                dy = points[i, 1] - center[1]
                
                x_rot = dx * cos_theta + dy * sin_theta
                y_rot = -dx * sin_theta + dy * cos_theta
                
                val = x_rot * x_rot * inv_major_sq + y_rot * y_rot * inv_minor_sq
                if val < min_val:
                    min_val = val
            
            return (1.0 - min_val) ** 2

except ImportError:
    print("Numba not available. Install with 'pip install numba' for maximum performance.")
    optimized_objective_numba = None

# Benchmark function to test different versions
def benchmark_versions(points, center, theta, semi_major, semi_minor, front=True):
    """
    Benchmark different optimization versions.
    """
    import time
    
    versions = [
        ("original", relative_boundary_distance),
        ("Optimized", optimized_objective_function),
        ("Einsum", optimized_objective_einsum),
        ("Chunked", optimized_objective_chunked),
    ]
    
    if optimized_objective_numba is not None:
        versions.append(("Numba", optimized_objective_numba))
    
    results = {}
    for name, func in versions:
        start_time = time.time()
        result = func(points, center, theta, semi_major, semi_minor, front)
        end_time = time.time()
        
        results[name] = {
            'time': end_time - start_time,
            'result': result
        }
        print(f"{name}: {end_time - start_time:.6f}s, result: {result:.6f}")
    
    return results

# Example usage and testing
if __name__ == "__main__":
    # Generate test data
    np.random.seed(42)
    n_points = 2000
    points = np.random.randn(n_points, 2) * 10
    center = np.array([1.0, 2.0])
    theta = 0.5
    semi_major = 3.0
    semi_minor = 2.0
    
    print("Benchmarking different optimization versions:")
    print(f"Points: {n_points}, Front mode: True")
    benchmark_versions(points, center, theta, semi_major, semi_minor, front=True)