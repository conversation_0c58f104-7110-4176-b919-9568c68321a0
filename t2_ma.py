from shapely.geometry import LineString, Point
from shapely.ops import linemerge
import networkx as nx
import numpy as np  # Import numpy

def merge_linestrings_efficient(linestrings, tolerance=1e-6):
    """
    Merges Shapely LineStrings efficiently, avoiding "Y" junctions.

    Args:
        linestrings: A list of Shapely LineString objects.
        tolerance:  A tolerance value for considering points as identical.

    Returns:
        A list of merged LineStrings.
    """

    graph = nx.Graph()
    point_to_lines = {}  # Map points to LineString indices

    # Build the graph and point-to-line mapping
    for i, line in enumerate(linestrings):
        start, end = line.coords[0], line.coords[-1]

        # Use tuples as keys, more reliable than Point objects for hashing
        start_key = tuple(np.round(start, int(-np.log10(tolerance))))
        end_key = tuple(np.round(end, int(-np.log10(tolerance))))

        # Add nodes and edges to the graph
        graph.add_node(start_key, line_index=i)
        graph.add_node(end_key, line_index=i)
        graph.add_edge(start_key, end_key, line_index=i)

        # Update the point-to-line mapping
        point_to_lines.setdefault(start_key, []).append(i)
        point_to_lines.setdefault(end_key, []).append(i)

    # Remove "Y" junctions (nodes with degree > 2)
    nodes_to_remove = [node for node, degree in graph.degree() if degree > 2]
    for node in nodes_to_remove:
        graph.remove_node(node)

    # Merge connected components
    merged_lines = []
    for component in nx.connected_components(graph):
        subgraph = graph.subgraph(component)
        if len(subgraph.edges) == 0:
            continue  # Skip isolated nodes

        # Extract LineStrings from the subgraph edges
        lines_in_component = []
        for u, v, data in subgraph.edges(data=True):
            lines_in_component.append(linestrings[data['line_index']])

        # Use shapely.ops.linemerge to handle merging
        if len(lines_in_component) == 1:
            merged_lines.append(lines_in_component[0])  # No merging needed
        else:
            merged = linemerge(lines_in_component)
            if merged.geom_type == 'MultiLineString':
                merged_lines.extend(list(merged.geoms)) #if not fully merged
            elif merged.geom_type == 'LineString':
                merged_lines.append(merged)


    return merged_lines


# Example Usage (and test cases)
linestrings = [
    LineString([(0, 0), (1, 1)]),  # Simple merge
    LineString([(1, 1), (2, 0)]),
    LineString([(2, 0), (3, 1)]),
    LineString([(3, 1), (4, 0)]),
    LineString([(1, 1), (1, 2)]),  # "Y" junction - should not be merged
    LineString([(1, 2), (1, 3)]),
    LineString([(5,5), (6,6)]), #isolated
    LineString([(6,6),(7,7)]),
    LineString([(10,10), (11, 10.0000001)]) #test tolerance
]

merged_result = merge_linestrings_efficient(linestrings)
for line in merged_result:
    print(line)

# --- Larger Test Case ---
def create_test_linestrings(n):
    linestrings = []
    for i in range(n - 1):
        linestrings.append(LineString([(i, i), (i + 1, i + 1)]))
    return linestrings

large_linestrings = create_test_linestrings(20000)
#add some Y
large_linestrings.append(LineString([(500,500), (500, 501)]))
large_linestrings.append(LineString([(500,501), (500, 502)]))

merged_large = merge_linestrings_efficient(large_linestrings)
print(f"Merged {len(large_linestrings)} linestrings into {len(merged_large)} linestrings.")