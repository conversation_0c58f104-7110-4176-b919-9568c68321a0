# --- START OF FILE sim_pixel_milling_sdf_01.py ---

import numpy as np
import time
from scipy.ndimage import map_coordinates

class MillingSimulationSDF:
    def __init__(self, tool_diameter, workpiece_dimensions, grid_spacing=None):
        """
        Initialize the SDF-based milling simulation.

        Parameters:
        - tool_diameter: diameter of the milling tool in mm.
        - workpiece_dimensions: tuple (width, height) in mm.
        - grid_spacing: distance between grid points for SDF evaluation in mm.
                        If None, calculated based on tool diameter (e.g., tool_diameter / 50).
        """
        self.tool_diameter = tool_diameter
        self.tool_radius = tool_diameter / 2.0
        self.workpiece_width_mm, self.workpiece_height_mm = workpiece_dimensions

        if grid_spacing is None:
            # Use a reasonable default grid spacing based on tool size
            self.grid_spacing = tool_diameter / 50.0
        else:
            self.grid_spacing = grid_spacing

        # Calculate grid dimensions (add padding for boundary checks)
        padding_px = int(np.ceil(self.tool_radius / self.grid_spacing)) + 5 # Add some buffer
        self.grid_width_px = int(np.ceil(self.workpiece_width_mm / self.grid_spacing)) + 2 * padding_px
        self.grid_height_px = int(np.ceil(self.workpiece_height_mm / self.grid_spacing)) + 2 * padding_px

        # Origin offset in pixels due to padding
        self.origin_offset_px = (padding_px, padding_px)

        # Create the grid coordinates
        # Note: Grid origin (0,0) corresponds to the bottom-left corner of the padded area
        x_coords = np.arange(self.grid_width_px) * self.grid_spacing
        y_coords = np.arange(self.grid_height_px) * self.grid_spacing
        self.grid_x, self.grid_y = np.meshgrid(x_coords, y_coords)

        # Initialize workpiece SDF
        self.workpiece_sdf = self._create_initial_workpiece_sdf()

        # Store engagement angle results
        self.engagement_angles = []
        print(f"SDF Grid: {self.grid_width_px}x{self.grid_height_px} cells, Spacing: {self.grid_spacing:.4f} mm")


    def _mm_to_grid_coords(self, x_mm, y_mm):
        """Convert mm coordinates to fractional grid coordinates."""
        # Account for the origin offset due to padding
        grid_x = (x_mm / self.grid_spacing) + self.origin_offset_px[0]
        grid_y = (y_mm / self.grid_spacing) + self.origin_offset_px[1]
        return grid_x, grid_y

    def _grid_coords_to_mm(self, grid_x, grid_y):
        """Convert grid coordinates back to mm."""
        x_mm = (grid_x - self.origin_offset_px[0]) * self.grid_spacing
        y_mm = (grid_y - self.origin_offset_px[1]) * self.grid_spacing
        return x_mm, y_mm

    def _sdf_rectangle(self, x, y, center_x, center_y, half_width, half_height):
        """Calculate SDF for a rectangle centered at (center_x, center_y)."""
        dx = np.abs(x - center_x) - half_width
        dy = np.abs(y - center_y) - half_height
        
        # Distance from outside points to the nearest edge
        outside_dist = np.sqrt(np.maximum(dx, 0)**2 + np.maximum(dy, 0)**2)
        
        # Negative distance for inside points (max component distance to edge)
        inside_dist = np.minimum(np.maximum(dx, dy), 0)
        
        return outside_dist + inside_dist

    def _create_initial_workpiece_sdf(self):
        """Creates the initial SDF for the rectangular workpiece."""
        center_x = self.workpiece_width_mm / 2.0
        center_y = self.workpiece_height_mm / 2.0
        half_width = self.workpiece_width_mm / 2.0
        half_height = self.workpiece_height_mm / 2.0

        # Calculate workpiece center in grid coordinates (mm space)
        grid_center_x_mm = (self.grid_width_px / 2.0) * self.grid_spacing
        grid_center_y_mm = (self.grid_height_px / 2.0) * self.grid_spacing
        
        # Adjust workpiece center relative to the padded grid origin
        wp_center_x_mm = self.origin_offset_px[0] * self.grid_spacing + center_x
        wp_center_y_mm = self.origin_offset_px[1] * self.grid_spacing + center_y

        # Calculate SDF over the entire grid
        sdf = self._sdf_rectangle(
            self.grid_x, self.grid_y,
            wp_center_x_mm, wp_center_y_mm,
            half_width, half_height
        )
        return sdf.astype(np.float32)

    def _sdf_circle(self, x, y, center_x, center_y, radius):
        """Calculate SDF for a circle."""
        return np.sqrt((x - center_x)**2 + (y - center_y)**2) - radius

    def _get_tool_sdf_at_pos(self, tool_center_x_mm, tool_center_y_mm):
        """Calculate the tool's SDF centered at the given position (in mm)."""
        return self._sdf_circle(self.grid_x, self.grid_y,
                                tool_center_x_mm, tool_center_y_mm,
                                self.tool_radius).astype(np.float32)

    def _update_workpiece_sdf(self, tool_center_x_mm, tool_center_y_mm):
        """
        Update the workpiece SDF by removing material (SDF subtraction).
        Uses the formula: max(workpiece_sdf, -tool_sdf)
        """
        # Calculate tool SDF centered at the current position
        tool_sdf = self._get_tool_sdf_at_pos(tool_center_x_mm, tool_center_y_mm)

        # Update the workpiece SDF - only update where tool is potentially interacting
        # Optimization: Only compute max in the vicinity of the tool
        tool_center_gx, tool_center_gy = self._mm_to_grid_coords(tool_center_x_mm, tool_center_y_mm)
        radius_px = int(np.ceil(self.tool_radius / self.grid_spacing)) + 2 # a bit extra

        min_gx = max(0, int(tool_center_gx - radius_px))
        max_gx = min(self.grid_width_px, int(tool_center_gx + radius_px) + 1)
        min_gy = max(0, int(tool_center_gy - radius_px))
        max_gy = min(self.grid_height_px, int(tool_center_gy + radius_px) + 1)

        if min_gx < max_gx and min_gy < max_gy:
            local_wp_sdf = self.workpiece_sdf[min_gy:max_gy, min_gx:max_gx]
            local_neg_tool_sdf = -tool_sdf[min_gy:max_gy, min_gx:max_gx]
            
            updated_local_sdf = np.maximum(local_wp_sdf, local_neg_tool_sdf)
            self.workpiece_sdf[min_gy:max_gy, min_gx:max_gx] = updated_local_sdf


    def _calculate_engagement_angle_sdf(self, tool_center_x_mm, tool_center_y_mm, num_samples=72):
        """
        Calculate the engagement angle by sampling the workpiece SDF
        around the tool's cutting edge.
        """
        angles = np.linspace(0, 2 * np.pi, num_samples, endpoint=False)

        # Calculate sample point coordinates in mm
        sample_x_mm = tool_center_x_mm + self.tool_radius * np.cos(angles)
        sample_y_mm = tool_center_y_mm + self.tool_radius * np.sin(angles)

        # Convert sample point coordinates to fractional grid coordinates
        sample_gx, sample_gy = self._mm_to_grid_coords(sample_x_mm, sample_y_mm)

        # Coordinates must be in (y, x) format for map_coordinates
        coords = np.vstack((sample_gy, sample_gx))

        # Sample the workpiece SDF at these points using bilinear interpolation
        sampled_sdf_values = map_coordinates(self.workpiece_sdf, coords, order=1, mode='nearest')

        # Find where the tool edge is inside the material (SDF < 0)
        engaged = sampled_sdf_values < -1e-6 # Small tolerance

        if not np.any(engaged):
            return 0.0 # No engagement

        # --- Start Modification ---
        total_angle_robust = 0
        if engaged[0] and engaged[-1]: # Potential wrap around engagement OR fully engaged
            # Check for FULL engagement FIRST
            if np.all(engaged):
                total_angle_robust = 2 * np.pi
            else:
                # Wrap-around case (some points are NOT engaged)
                # Find first non-engaged angle index (end of first segment)
                # This line caused the error if all were engaged
                end_idx = np.where(~engaged)[0][0]
                # Find last non-engaged angle index (leading into the start of the last segment)
                start_idx = np.where(~engaged)[0][-1]

                angle_segment1 = angles[end_idx] # Angle *at* the first non-engaged point
                angle_segment2 = (2 * np.pi) - angles[start_idx+1] # Angle from start of last engagement segment to 2*pi
                total_angle_robust = angle_segment1 + angle_segment2

        elif np.any(engaged): # One or more segments, no wrap around
            # Find start and end of contiguous blocks
            padded_engaged = np.concatenate(([False], engaged, [False]))
            diff_engaged = np.diff(padded_engaged.astype(int))
            starts = np.where(diff_engaged == 1)[0] # Indices in original 'angles' array where engagement starts
            ends = np.where(diff_engaged == -1)[0]   # Indices in original 'angles' array where engagement ends

            for i in range(len(starts)):
                # Angle span is from the start angle up to (but not including) the angle
                # at the index *after* the segment ends. angles[ends[i]] is the first angle *not* engaged.
                 total_angle_robust += angles[ends[i]] - angles[starts[i]]
        # --- End Modification ---

        # Return the calculated angle
        return total_angle_robust


    def simulate_toolpath(self, toolpath, feed_per_tooth, spindle_speed, axial_depth, num_teeth):
        """
        Simulate the toolpath using SDF, calculating engagement angles.

        Parameters are the same as the pixel-based version, but internal calculations differ.

        Returns:
        - engagement_angles: list of engagement angles in radians at each toolpath point.
        """
        start_time = time.time()

        # Clear previous results
        self.engagement_angles = np.zeros(len(toolpath))

        # Simulate each position in the toolpath
        for i, position in enumerate(toolpath):
            tool_x_mm, tool_y_mm = position

            # 1. Calculate engagement angle BEFORE updating the SDF
            #    This represents the state as the tool enters this position.
            current_engagement_angle = self._calculate_engagement_angle_sdf(tool_x_mm, tool_y_mm)
            self.engagement_angles[i] = current_engagement_angle

            # 2. Update the workpiece SDF to reflect material removal at this position
            self._update_workpiece_sdf(tool_x_mm, tool_y_mm)

            # Progress indicator
            if i % 1000 == 0 and i > 0: # Reduced frequency for potentially slower SDF steps
                elapsed = time.time() - start_time
                points_per_sec = i / elapsed if elapsed > 0 else 0
                remaining_points = len(toolpath) - i
                if points_per_sec > 0:
                   est_remaining_time = remaining_points / points_per_sec
                   print(f"Processed {i}/{len(toolpath)} points ({i/len(toolpath)*100:.1f}%). "
                         f"Rate: {points_per_sec:.1f} pts/s. Est. remaining: {est_remaining_time:.1f}s")
                else:
                   print(f"Processed {i}/{len(toolpath)} points ({i/len(toolpath)*100:.1f}%). Calculating rate...")


        end_time = time.time()
        print(f"SDF Simulation completed in {end_time - start_time:.2f} seconds")

        return self.engagement_angles

    def get_workpiece_image(self, threshold=0):
        """
        Generate a boolean image representing the material based on SDF sign.
        Useful for visualization.
        """
        return self.workpiece_sdf <= threshold

# --- END OF FILE sim_pixel_milling_sdf_01.py ---

# --- Example Usage ---
def generate_large_toolpath(num_points=10000): # Reduced points for faster SDF testing
    """Generate a complex toolpath (reduced points for SDF)."""
    toolpath = []
    center_x, center_y = 50, 40
    max_radius = 35
    min_radius = 5
    num_turns = 10 # Reduced turns

    angles = np.linspace(0, num_turns * 2 * np.pi, num_points // 2)
    radii = np.linspace(max_radius, min_radius, num_points // 2)

    for i in range(len(angles)):
        x = center_x + radii[i] * np.cos(angles[i])
        y = center_y + radii[i] * np.sin(angles[i])
        toolpath.append((x, y))

    angles = np.linspace(num_turns * 2 * np.pi, 0, num_points - len(toolpath))
    radii = np.linspace(min_radius, max_radius, num_points - len(toolpath))

    for i in range(len(angles)):
        x = center_x + radii[i] * np.cos(angles[i])
        y = center_y + radii[i] * np.sin(angles[i])
        toolpath.append((x, y))

    return toolpath

# Run the simulation
if __name__ == "__main__":
    # Set parameters
    tool_diameter = 10       # mm
    workpiece_dimensions = (100, 80) # mm
    # grid_spacing = 0.2     # mm - Controls SDF resolution. Smaller = more accurate but much slower.
    grid_spacing = tool_diameter / 20 # Try relative spacing

    # Process parameters (less relevant for SDF engagement angle calculation itself)
    feed_per_tooth = 0.05  # mm
    spindle_speed = 4000   # rpm
    axial_depth = 5        # mm
    num_teeth = 4

    # Create simulation
    sim_sdf = MillingSimulationSDF(tool_diameter, workpiece_dimensions, grid_spacing)

    # Generate toolpath (use fewer points for faster testing initially)
    # toolpath = [(10.0, 10.0),(11.0, 10.0),(12.0, 10.0), (13.0, 10.0), (14.0, 10.0), # Straight line entry
    #             (15.0, 10.0), (16.0, 10.0), (17.0, 10.0), (18.0, 10.0), (19.0, 10.0),
    #             (20.0, 10.0), (20.0, 11.0), (20.0, 12.0)] # Partial turn
    toolpath = generate_large_toolpath(10000) # Use 10k points for reasonable run time

    # Run simulation
    results_sdf = sim_sdf.simulate_toolpath(toolpath, feed_per_tooth, spindle_speed, axial_depth, num_teeth)
    print("Sample Engagement Angles (degrees):")
    print(np.rad2deg(results_sdf[::len(results_sdf)//20])) # Print every ~20th angle

    # Optional: Visualize the final workpiece SDF
    try:
        import matplotlib.pyplot as plt
        plt.figure(figsize=(8, 6))
        # Display SDF values, coolwarm cmap shows negative (inside) vs positive (outside)
        plt.imshow(sim_sdf.workpiece_sdf, cmap='coolwarm', origin='lower',
                   extent=[0, sim_sdf.grid_width_px * sim_sdf.grid_spacing,
                           0, sim_sdf.grid_height_px * sim_sdf.grid_spacing])
        plt.colorbar(label='Signed Distance (mm)')
        plt.title('Final Workpiece SDF')
        plt.xlabel('X (mm)')
        plt.ylabel('Y (mm)')
        
        # Overlay toolpath
        tp_arr = np.array(toolpath)
        # Adjust toolpath coords to match SDF grid's extent (including padding)
        tp_plot_x = tp_arr[:,0] + sim_sdf.origin_offset_px[0] * sim_sdf.grid_spacing
        tp_plot_y = tp_arr[:,1] + sim_sdf.origin_offset_px[1] * sim_sdf.grid_spacing
        plt.plot(tp_plot_x, tp_plot_y, 'k-', linewidth=0.5, alpha=0.5)
        
        # Plot workpiece boundaries (approximate in the grid space)
        wp_x0 = sim_sdf.origin_offset_px[0] * sim_sdf.grid_spacing
        wp_y0 = sim_sdf.origin_offset_px[1] * sim_sdf.grid_spacing
        wp_x1 = wp_x0 + sim_sdf.workpiece_width_mm
        wp_y1 = wp_y0 + sim_sdf.workpiece_height_mm
        plt.gca().add_patch(plt.Rectangle((wp_x0, wp_y0), sim_sdf.workpiece_width_mm, sim_sdf.workpiece_height_mm, fill=False, color='green', linestyle='--'))
        
        plt.axis('equal')
        plt.show()

        # Also show the material boundary image
        plt.figure(figsize=(8, 6))
        material_img = sim_sdf.get_workpiece_image()
        plt.imshow(material_img, cmap='gray', origin='lower',
                   extent=[0, sim_sdf.grid_width_px * sim_sdf.grid_spacing,
                           0, sim_sdf.grid_height_px * sim_sdf.grid_spacing])
        plt.title('Final Workpiece Material (SDF <= 0)')
        plt.xlabel('X (mm)')
        plt.ylabel('Y (mm)')
        plt.plot(tp_plot_x, tp_plot_y, 'r-', linewidth=0.5, alpha=0.5) # Toolpath in red
        plt.gca().add_patch(plt.Rectangle((wp_x0, wp_y0), sim_sdf.workpiece_width_mm, sim_sdf.workpiece_height_mm, fill=False, color='green', linestyle='--'))
        plt.axis('equal')
        plt.show()


    except ImportError:
        print("Matplotlib not found. Skipping visualization.")