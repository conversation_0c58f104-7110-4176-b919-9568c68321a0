import numpy as np
import time
from collections import defaultdict

def find_intersecting_lines_spatial(lines, cell_size=None):
    N = lines.shape[0]
    if N == 0:
        return np.zeros(N, dtype=bool)
    
    # Calculate cell size if not provided
    if cell_size is None:
        deltas = lines[:, 1, :] - lines[:, 0, :]
        lengths = np.hypot(deltas[:, 0], deltas[:, 1])
        cell_size = np.max(lengths) if np.any(lengths > 0) else 1.0
    
    all_points = lines.reshape(-1, 2)
    min_coords = np.min(all_points, axis=0)
    max_coords = np.max(all_points, axis=0)
    x0, y0 = min_coords
    x1, y1 = max_coords
    
    # Assign each line to grid cells
    cell_assignments = []
    for line_idx in range(N):
        p = lines[line_idx]
        px = p[:, 0]
        py = p[:, 1]
        min_x, max_x = np.min(px), np.max(px)
        min_y, max_y = np.min(py), np.max(py)
        
        i_min = int((min_x - x0) // cell_size)
        i_max = int((max_x - x0) // cell_size) + 1
        j_min = int((min_y - y0) // cell_size)
        j_max = int((max_y - y0) // cell_size) + 1
        
        for i in range(i_min, i_max):
            for j in range(j_min, j_max):
                cell_assignments.append((i, j, line_idx))
    
    # Build cell dictionary
    cell_dict = defaultdict(list)
    for i, j, line_idx in cell_assignments:
        cell_dict[(i, j)].append(line_idx)
    
    # Generate candidate pairs
    candidate_pairs = set()
    for (i, j), lines_in_cell in cell_dict.items():
        # Intra-cell pairs (upper triangle to avoid duplicates)
        n = len(lines_in_cell)
        if n >= 2:
            triu = np.triu_indices(n, k=1)
            for a, b in zip(triu[0], triu[1]):
                pair = (lines_in_cell[a], lines_in_cell[b])
                candidate_pairs.add(pair)
        
        # Inter-cell pairs with neighbors
        for di in [-1, 0, 1]:
            for dj in [-1, 0, 1]:
                if di == 0 and dj == 0:
                    continue
                neighbor_key = (i + di, j + dj)
                if neighbor_key in cell_dict:
                    for a in lines_in_cell:
                        for b in cell_dict[neighbor_key]:
                            if a < b:
                                candidate_pairs.add((a, b))
    
    if not candidate_pairs:
        return np.zeros(N, dtype=bool)
    
    # Convert pairs to arrays
    candidate_pairs = np.array(list(candidate_pairs))
    i_idx = candidate_pairs[:, 0]
    j_idx = candidate_pairs[:, 1]
    
    # Vectorized intersection check for candidate pairs
    p1 = lines[i_idx, 0, :]
    p2 = lines[i_idx, 1, :]
    p3 = lines[j_idx, 0, :]
    p4 = lines[j_idx, 1, :]
    
    dx1 = p2[:, 0] - p1[:, 0]
    dy1 = p2[:, 1] - p1[:, 1]
    o1 = dx1 * (p3[:, 1] - p1[:, 1]) - dy1 * (p3[:, 0] - p1[:, 0])
    o2 = dx1 * (p4[:, 1] - p1[:, 1]) - dy1 * (p4[:, 0] - p1[:, 0])
    
    dx2 = p4[:, 0] - p3[:, 0]
    dy2 = p4[:, 1] - p3[:, 1]
    o3 = dx2 * (p1[:, 1] - p3[:, 1]) - dy2 * (p1[:, 0] - p3[:, 0])
    o4 = dx2 * (p2[:, 1] - p3[:, 1]) - dy2 * (p2[:, 0] - p3[:, 0])
    
    cond1 = (o1 * o2) < 0
    cond2 = (o3 * o4) < 0
    intersects = cond1 & cond2
    
    # Colinear overlap check
    colinear = (o1 == 0) & (o2 == 0) & (o3 == 0) & (o4 == 0)
    if np.any(colinear):
        col_ia = i_idx[colinear]
        col_ib = j_idx[colinear]
        
        p1_col = lines[col_ia, 0, :]
        p2_col = lines[col_ia, 1, :]
        p3_col = lines[col_ib, 0, :]
        p4_col = lines[col_ib, 1, :]
        
        dir_col = p2_col - p1_col
        dir_dot = np.sum(dir_col**2, axis=1)
        valid_dot = dir_dot != 0
        
        vec3 = p3_col[valid_dot] - p1_col[valid_dot]
        vec4 = p4_col[valid_dot] - p1_col[valid_dot]
        dir_col_valid = dir_col[valid_dot]
        
        t3 = np.sum(vec3 * dir_col_valid, axis=1) / dir_dot[valid_dot]
        t4 = np.sum(vec4 * dir_col_valid, axis=1) / dir_dot[valid_dot]
        
        tmin = np.minimum(t3, t4)
        tmax = np.maximum(t3, t4)
        overlap = (tmax >= 0) & (tmin <= 1)
        
        col_ia_valid = col_ia[valid_dot]
        col_ib_valid = col_ib[valid_dot]
        intersects_colinear = np.zeros_like(intersects)
        intersects_colinear[colinear] = np.isin(np.arange(len(colinear)), np.where(overlap)[0])
        intersects |= intersects_colinear
    
    # Mark intersecting lines
    result = np.zeros(N, dtype=bool)
    intersecting_pairs = np.where(intersects)[0]
    np.logical_or.at(result, i_idx[intersecting_pairs], True)
    np.logical_or.at(result, j_idx[intersecting_pairs], True)
    
    return result

def find_intersecting_lines(lines):
    N = lines.shape[0]
    i_idx, j_idx = np.triu_indices(N, k=1)
    
    # Extract points for all pairs
    p1 = lines[i_idx, 0, :]
    p2 = lines[i_idx, 1, :]
    p3 = lines[j_idx, 0, :]
    p4 = lines[j_idx, 1, :]
    
    # Compute orientations for intersection checks
    dx1 = p2[:, 0] - p1[:, 0]
    dy1 = p2[:, 1] - p1[:, 1]
    o1 = dx1 * (p3[:, 1] - p1[:, 1]) - dy1 * (p3[:, 0] - p1[:, 0])
    o2 = dx1 * (p4[:, 1] - p1[:, 1]) - dy1 * (p4[:, 0] - p1[:, 0])
    
    dx2 = p4[:, 0] - p3[:, 0]
    dy2 = p4[:, 1] - p3[:, 1]
    o3 = dx2 * (p1[:, 1] - p3[:, 1]) - dy2 * (p1[:, 0] - p3[:, 0])
    o4 = dx2 * (p2[:, 1] - p3[:, 1]) - dy2 * (p2[:, 0] - p3[:, 0])
    
    # Check standard intersection condition
    cond1 = (o1 * o2) < 0
    cond2 = (o3 * o4) < 0
    intersects = cond1 & cond2
    
    # Check for colinear overlapping segments
    colinear = (o1 == 0) & (o2 == 0) & (o3 == 0) & (o4 == 0)
    if np.any(colinear):
        col_i = i_idx[colinear]
        col_j = j_idx[colinear]
        
        p1_col = lines[col_i, 0, :]
        p2_col = lines[col_i, 1, :]
        p3_col = lines[col_j, 0, :]
        p4_col = lines[col_j, 1, :]
        
        dir_col = p2_col - p1_col
        dir_dot = np.sum(dir_col**2, axis=1)
        
        vec3 = p3_col - p1_col
        vec4 = p4_col - p1_col
        
        t3 = np.sum(vec3 * dir_col, axis=1) / dir_dot
        t4 = np.sum(vec4 * dir_col, axis=1) / dir_dot
        
        tmin = np.minimum(t3, t4)
        tmax = np.maximum(t3, t4)
        overlap = (tmax >= 0) & (tmin <= 1)
        
        intersects_colinear = np.zeros_like(intersects)
        intersects_colinear[colinear] = overlap
        intersects |= intersects_colinear
    
    # Mark intersecting lines
    result = np.zeros(N, dtype=bool)
    valid_pairs = np.where(intersects)[0]
    result[i_idx[valid_pairs]] = True
    result[j_idx[valid_pairs]] = True
    
    return result


# Function to generate random lines
def generate_random_lines(num_lines):
    return np.random.rand(num_lines, 2, 2) * 10


if __name__ == "__main__":
    # lines = generate_random_lines(4000)
    lines = np.random.rand(12000, 2, 2)  # Random endpoints for demonstration
    print(lines.shape)
    start_time = time.time()
    result = find_intersecting_lines(lines)
    print(result)
    # result = find_intersecting_lines_spatial(lines)
    # print(result)    
    print(time.time() - start_time)