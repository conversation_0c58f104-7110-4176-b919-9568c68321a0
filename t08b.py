
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from scipy.optimize import minimize, brentq
from scipy.integrate import quad

# A helper function to plot ellipses for visualization
def plot_ellipse(ax, center, width, height, angle_deg, **kwargs):
    """Plots a single ellipse on a given matplotlib axis."""
    e = Ellipse(xy=center, width=width, height=height, 
                angle=angle_deg, **kwargs)
    ax.add_patch(e)


def solve_for_circle(R, t0, t1):
    """
    Finds the inscribed ellipse for the simple circle case using a pure
    algebraic solution.
    
    Args:
        R (float): Radius of the main circle.
        t0 (float): Perimeter fraction for the start of line 0 (0.0 to 1.0).
        t1 (float): Perimeter fraction for the start of line 1 (0.0 to 1.0).
        
    Returns:
        dict: Parameters of the inscribed ellipse (center, width, height, angle).
    """
    # 1. Find the angles of the normal lines
    theta0 = 2 * np.pi * t0
    theta1 = 2 * np.pi * t1

    # 2. Find the angle of the bisector and the half-angle of the wedge
    # This defines the rotation needed to simplify the problem
    rotation_angle_rad = (theta0 + theta1) / 2.0
    alpha = (theta1 - theta0) / 2.0
    
    # Ensure alpha is the smaller angle
    if alpha > np.pi / 2:
        alpha = np.pi - alpha
        rotation_angle_rad += np.pi
        
    # Tangent of the half-angle, which is the slope of the line in rotated coords
    m = np.tan(alpha)

    # 3. Solve the system of equations derived from tangency and osculating conditions
    # See derivation in thought process: a = (R * tan^2(alpha)) / (1 + 2*tan^2(alpha))
    tan_sq_alpha = m**2
    a = (R * tan_sq_alpha) / (1 + 2 * tan_sq_alpha)
    
    # The other parameters follow from 'a'
    # b^2 = R*a (osculating condition)
    b_sq = R * a
    b = np.sqrt(b_sq)
    
    # cx' = R - a (tangency at vertex)
    cx_rotated = R - a
    
    # 4. The ellipse is defined in the rotated frame. Now rotate it back.
    center_rotated = np.array([cx_rotated, 0])
    
    # Rotation matrix to go back to the original frame
    cos_rot = np.cos(rotation_angle_rad)
    sin_rot = np.sin(rotation_angle_rad)
    rot_matrix = np.array([[cos_rot, -sin_rot], 
                           [sin_rot,  cos_rot]])
    
    center_original = rot_matrix @ center_rotated

    return {
        "center": center_original,
        "width": 2 * a,
        "height": 2 * b,
        "angle_deg": np.rad2deg(rotation_angle_rad)
    }


# --- Helper functions for the General Ellipse case ---

def get_ellipse_point_from_angle(A, B, angle_rad):
    """Gets (x,y) point on ellipse from parametric angle."""
    return np.array([A * np.cos(angle_rad), B * np.sin(angle_rad)])

def get_ellipse_perimeter_point(A, B, t_fraction):
    """
    Finds the point (x,y) on an ellipse at a given fraction of its perimeter.
    This requires numerical integration as there's no closed-form solution.
    """
    # Integrand for arc length
    perimeter_integrand = lambda angle: np.sqrt((A * np.sin(angle))**2 + (B * np.cos(angle))**2)
    
    # Total perimeter
    total_perimeter, _ = quad(perimeter_integrand, 0, 2 * np.pi)
    target_length = t_fraction * total_perimeter
    
    # Find the parametric angle 'theta' that corresponds to this arc length
    # We want to find theta such that integral from 0 to theta equals target_length
    error_func = lambda angle: quad(perimeter_integrand, 0, angle)[0] - target_length
    
    # Use a root-finding algorithm to find the angle
    try:
        theta, r = brentq(error_func, 0, 2 * np.pi, full_output=True)
        if not r.converged:
            raise RuntimeError("Root finding for perimeter point did not converge.")
    except ValueError: # Happens if target_length is 0 or total_perimeter
        theta = 0 if t_fraction == 0 else 2*np.pi
        
    return get_ellipse_point_from_angle(A, B, theta)

def get_ellipse_normal_line(A, B, point):
    """
    Gets the normal line Ax+By+C=0 at a point (x0, y0) on the ellipse.
    The normal vector (A, B) points "outward". We will flip it to point inward.
    """
    x0, y0 = point
    if abs(x0) < 1e-9: # Point is on the y-axis
        return np.array([1.0, 0.0, -x0])
    if abs(y0) < 1e-9: # Point is on the x-axis
        return np.array([0.0, 1.0, -y0])

    # Gradient of F(x,y)=x^2/A^2 + y^2/B^2 - 1 is the normal vector
    # n = (2x0/A^2, 2y0/B^2). We can simplify by dividing by 2.
    normal_vec = np.array([x0 / A**2, y0 / B**2])
    
    # We want the normal to point "inward". The gradient points outward.
    # So we flip the sign.
    normal_vec = -normal_vec
    
    # Line equation: normal_vec[0]*(x-x0) + normal_vec[1]*(y-y0) = 0
    # A*x + B*y - (A*x0 + B*y0) = 0
    A_line, B_line = normal_vec
    C_line = -np.dot(normal_vec, point)
    
    return np.array([A_line, B_line, C_line])

def solve_for_general_ellipse(A, B, t0, t1, num_points=30):
    """
    Finds the inscribed ellipse for the general case using numerical optimization.
    
    Args:
        A (float): Semi-major axis of the main ellipse.
        B (float): Semi-minor axis of the main ellipse.
        t0 (float): Perimeter fraction for the start of line 0.
        t1 (float): Perimeter fraction for the start of line 1.
        num_points (int): Number of points to discretize the new ellipse for constraints.

    Returns:
        dict: Parameters of the inscribed ellipse from the optimizer.
    """
    # 1. Define the geometry of the container
    p0 = get_ellipse_perimeter_point(A, B, t0)
    p1 = get_ellipse_perimeter_point(A, B, t1)
    
    line0_params = get_ellipse_normal_line(A, B, p0)
    line1_params = get_ellipse_normal_line(A, B, p1)
    
    # 2. Define objective function (we want to MAXIMIZE area, so MINIMIZE -area)
    def objective(params):
        # params: [cx, cy, a, b, phi_rad]
        a, b = params[2], params[3]
        return -a * b

    # 3. Define the constraints
    def constraints(params):
        cx, cy, a, b, phi_rad = params
        
        # Avoid non-physical solutions
        if a <= 0 or b <= 0:
            return -np.inf

        # Generate points on the new ellipse's boundary
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        
        # Rotate and translate points to world coordinates
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        
        # Constraint values must be >= 0 for the solution to be valid.
        
        # Constraint from Line 0: A0*x + B0*y + C0 >= 0
        l0 = line0_params
        dist_to_line0 = ellipse_pts_world @ l0[:2] + l0[2]
        
        # Constraint from Line 1: A1*x + B1*y + C1 >= 0
        l1 = line1_params
        dist_to_line1 = ellipse_pts_world @ l1[:2] + l1[2]

        # Constraint from Main Ellipse: (x/A)^2 + (y/B)^2 - 1 <= 0
        # We flip the sign so that inside is positive, matching the lines
        x_pts, y_pts = ellipse_pts_world.T
        dist_to_main_ellipse = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        
        # Return all constraint values concatenated
        return np.concatenate([dist_to_line0, dist_to_line1, dist_to_main_ellipse])

    # 4. Create an initial guess (x0) for the optimizer
    # Midpoint of the perimeter arc is a good reference
    p_mid = get_ellipse_perimeter_point(A, B, (t0 + t1) / 2.0)
    # A point slightly inward from the midpoint
    initial_center = p_mid * 0.9
    initial_a = A * 0.1
    initial_b = B * 0.1
    # Guess the angle based on the orientation of the midpoint
    initial_phi = np.arctan2(p_mid[1], p_mid[0])
    
    x0 = np.array([initial_center[0], initial_center[1], initial_a, initial_b, initial_phi])

    # 5. Run the optimizer
    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 500, 'disp': False})

    if not res.success:
        print(f"Warning: Optimizer may not have converged. Message: {res.message}")

    final_params = res.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        "success": res.success
    }

# We will reuse the helper functions from the previous answer:
# plot_ellipse, get_ellipse_point_from_angle, get_ellipse_perimeter_point
# So make sure they are defined in your script.

def polyline_to_line_equations(polyline, ref_point):
    """
    Converts a polyline into a list of oriented line equations.
    
    Args:
        polyline (np.array): A list of vertices, shape (num_verts, 2).
        ref_point (np.array): A 2D point known to be on the "inside".
        
    Returns:
        list: A list of tuples, where each tuple is (A, B, C, sign)
              representing an oriented line segment equation.
    """
    line_eqs = []
    if len(polyline) < 2:
        return []
        
    for i in range(len(polyline) - 1):
        p1 = polyline[i]
        p2 = polyline[i+1]
        
        # Line equation from two points: (y1-y2)x + (x2-x1)y + (x1y2 - x2y1) = 0
        A = p1[1] - p2[1]
        B = p2[0] - p1[0]
        C = p1[0] * p2[1] - p2[0] * p1[1]
        
        # Normalize to prevent very large/small values
        norm = np.sqrt(A**2 + B**2)
        if norm < 1e-9: continue # Points are identical, skip segment
        A, B, C = A/norm, B/norm, C/norm

        # Determine the "inside" sign using the reference point
        ref_val = A * ref_point[0] + B * ref_point[1] + C
        # We want the constraint to be '>= 0', so we store the sign
        # that makes the reference point satisfy this.
        sign = 1.0 if ref_val >= 0 else -1.0
        
        line_eqs.append((A, B, C, sign))
        
    return line_eqs

def solve_for_polyline_boundaries(A, B, polyline0, polyline1, num_points=30):
    """
    Finds the inscribed ellipse for general polyline boundaries using
    numerical optimization.
    
    Args:
        A (float): Semi-major axis of the main ellipse.
        B (float): Semi-minor axis of the main ellipse.
        polyline0 (np.array): Vertices of the first boundary polyline.
        polyline1 (np.array): Vertices of the second boundary polyline.
        num_points (int): Discretization points for the new ellipse.

    Returns:
        dict: Parameters of the inscribed ellipse.
    """
    # 1. Define a reference point for orienting the line segments
    # A point between the start of the polylines, pushed inward.
    p0_start = polyline0[0]
    p1_start = polyline1[0]
    ref_point = (p0_start + p1_start) / 2.0 * 0.95 # Push slightly towards origin
    
    # 2. Pre-calculate the line equations for the polylines
    lines0 = polyline_to_line_equations(polyline0, ref_point)
    lines1 = polyline_to_line_equations(polyline1, ref_point)
    
    # 3. Objective function (maximize area by minimizing -area)
    def objective(params):
        # params: [cx, cy, a, b, phi_rad]
        return -params[2] * params[3]

    # 4. Constraints function
    def constraints(params):
        cx, cy, a, b, phi_rad = params
        if a <= 1e-4 or b <= 1e-4: return -np.inf

        # Generate points on the new ellipse's boundary
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        x_pts, y_pts = ellipse_pts_world.T

        all_constraints = []

        # Constraint 1: Inside the main ellipse
        # (x/A)^2 + (y/B)^2 <= 1  -->  1 - ((x/A)^2 + (y/B)^2) >= 0
        main_ellipse_constraint = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        all_constraints.append(main_ellipse_constraint)
        
        # Constraint 2: Inside Polyline 0
        for A_l, B_l, C_l, sign in lines0:
            constraint = sign * (A_l * x_pts + B_l * y_pts + C_l)
            all_constraints.append(constraint)

        # Constraint 3: Inside Polyline 1
        for A_l, B_l, C_l, sign in lines1:
            constraint = sign * (A_l * x_pts + B_l * y_pts + C_l)
            all_constraints.append(constraint)
            
        return np.concatenate(all_constraints)

    # 5. Initial Guess
    initial_center = ref_point
    dist_between_starts = np.linalg.norm(p0_start - p1_start)
    initial_a = dist_between_starts * 0.2
    initial_b = dist_between_starts * 0.1
    vec = p1_start - p0_start
    initial_phi = np.arctan2(vec[1], vec[0])

    x0 = np.array([initial_center[0], initial_center[1], initial_a, initial_b, initial_phi])

    # 6. Run Optimizer
    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 1000, 'disp': False, 'ftol': 1e-9})
    
    if not res.success:
        print(f"Warning: Optimizer may not have converged. Message: {res.message}")

    final_params = res.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        "success": res.success
    }
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from scipy.optimize import minimize, brentq
from scipy.integrate import quad

# Keep all the helper functions from the previous answer:
# plot_ellipse, get_ellipse_point_from_angle, get_ellipse_perimeter_point,
# polyline_to_line_equations

# (Assuming the helper functions from the previous response are already defined here)
# ...

def solve_for_tangent_ellipse(A, B, polyline0, polyline1, tangency_weight=100.0, num_points=40):
    """
    Finds an inscribed ellipse that is TANGENT to the boundaries using a 
    hybrid objective function.
    
    Args:
        A (float): Semi-major axis of the main ellipse.
        B (float): Semi-minor axis of the main ellipse.
        polyline0 (np.array): Vertices of the first boundary polyline.
        polyline1 (np.array): Vertices of the second boundary polyline.
        tangency_weight (float): How strongly to enforce tangency. Higher is stronger.
        num_points (int): Discretization points for the new ellipse.

    Returns:
        dict: Parameters of the inscribed ellipse.
    """
    # 1. Define reference point and pre-calculate line equations
    p0_start = polyline0[0]
    p1_start = polyline1[0]
    ref_point = (p0_start + p1_start) / 2.0 * 0.95
    
    lines0 = polyline_to_line_equations(polyline0, ref_point)
    lines1 = polyline_to_line_equations(polyline1, ref_point)
    
    # Memoization cache to avoid recomputing distances in objective and constraints
    memo = {}

    def _calculate_distances(params):
        """Helper to calculate all distances, used by objective and constraints."""
        # Use a tuple of params as a key for the cache
        params_key = tuple(params)
        if params_key in memo:
            return memo[params_key]

        cx, cy, a, b, phi_rad = params
        if a <= 1e-4 or b <= 1e-4: return None

        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        x_pts, y_pts = ellipse_pts_world.T

        # Calculate distances to main ellipse boundary
        dist_main = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        
        # Calculate distances to polyline 0 segments
        dist_poly0_segments = []
        for A_l, B_l, C_l, sign in lines0:
            dist_poly0_segments.append(sign * (A_l * x_pts + B_l * y_pts + C_l))
        
        # Calculate distances to polyline 1 segments
        dist_poly1_segments = []
        for A_l, B_l, C_l, sign in lines1:
            dist_poly1_segments.append(sign * (A_l * x_pts + B_l * y_pts + C_l))
            
        result = (dist_main, dist_poly0_segments, dist_poly1_segments)
        memo[params_key] = result
        return result

    # 2. Define the new HYBRID objective function
    def objective(params):
        cx, cy, a, b, phi_rad = params
        area = a * b
        
        distances = _calculate_distances(params)
        if distances is None: return np.inf

        dist_main, dist_poly0_segments, dist_poly1_segments = distances

        # Find the minimum distance to each of the three boundaries
        min_dist_main = np.min(dist_main)
        # For polylines, the minimum distance is the minimum over all its segments
        min_dist_poly0 = np.min(dist_poly0_segments) if dist_poly0_segments else 0
        min_dist_poly1 = np.min(dist_poly1_segments) if dist_poly1_segments else 0

        # The penalty is the sum of the minimum distances. We want this to be zero.
        tangency_penalty = min_dist_main + min_dist_poly0 + min_dist_poly1
        
        return tangency_weight * tangency_penalty - area

    # 3. Define the constraints function (this remains the same logic)
    def constraints(params):
        distances = _calculate_distances(params)
        if distances is None: return -np.inf
        
        dist_main, dist_poly0_segments, dist_poly1_segments = distances
        
        # Concatenate all distance checks. All must be >= 0.
        all_constraints = [dist_main]
        if dist_poly0_segments: all_constraints.extend(dist_poly0_segments)
        if dist_poly1_segments: all_constraints.extend(dist_poly1_segments)

        return np.concatenate(all_constraints)

    # 4. Initial Guess and Optimization
    initial_center = ref_point
    dist_between_starts = np.linalg.norm(p0_start - p1_start)
    initial_a = dist_between_starts * 0.2
    initial_b = dist_between_starts * 0.1
    vec = p1_start - p0_start
    initial_phi = np.arctan2(vec[1], vec[0])

    x0 = np.array([initial_center[0], initial_center[1], initial_a, initial_b, initial_phi])

    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 1000, 'disp': False, 'ftol': 1e-9})
    
    memo.clear() # Clear cache after run

    if not res.success:
        print(f"Warning: Optimizer may not have converged. Message: {res.message}")

    final_params = res.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        "success": res.success
    }
# (Assuming all helper functions from the previous response are defined)
# plot_ellipse, get_ellipse_point_from_angle, get_ellipse_perimeter_point,
# polyline_to_line_equations

def find_robust_initial_guess(A, B, t0_frac, t1_frac, polyline0, polyline1):
    """
    Calculates a robust initial guess for the ellipse parameters by finding
    the centroid of the bounding region.
    """
    # 1. Sample points along the main ellipse arc
    # Ensure t1_frac > t0_frac for linspace
    if t1_frac < t0_frac:
        t1_frac += 1.0 # Handle wrap-around
        
    arc_fractions = np.linspace(t0_frac, t1_frac, 10)
    arc_points = np.array([get_ellipse_perimeter_point(A, B, t) for t in arc_fractions])

    # 2. Collect all vertices defining the container
    all_vertices = np.vstack([polyline0, polyline1, arc_points])
    
    # 3. Calculate the centroid
    centroid = np.mean(all_vertices, axis=0)
    
    # 4. Define a safe, small initial size and neutral angle
    initial_a = A * 0.05 # Small fraction of main axis
    initial_b = B * 0.05
    initial_phi = 0.0 # Neutral angle

    return np.array([centroid[0], centroid[1], initial_a, initial_b, initial_phi])


# --- The main solver function is updated to use the new guess ---
def solve_for_tangent_ellipse(A, B, t0_frac, t1_frac, polyline0, polyline1, tangency_weight=200.0, num_points=40):
    """
    Finds a TANGENT inscribed ellipse. This version uses the robust initial
    guess and slightly adjusted parameters for better convergence.
    """
    # (The internal logic of this function remains the same as the previous response)
    # ...
    # 1. Define reference point and pre-calculate line equations
    p0_start = polyline0[0]
    p1_start = polyline1[0]
    ref_point = (p0_start + p1_start) / 2.0 * 0.95
    
    lines0 = polyline_to_line_equations(polyline0, ref_point)
    lines1 = polyline_to_line_equations(polyline1, ref_point)
    
    memo = {}

    def _calculate_distances(params):
        params_key = tuple(params)
        if params_key in memo: return memo[params_key]
        cx, cy, a, b, phi_rad = params
        if a <= 1e-4 or b <= 1e-4: return None
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        x_pts, y_pts = ellipse_pts_world.T
        dist_main = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        dist_poly0_segments = [sign * (A_l * x_pts + B_l * y_pts + C_l) for A_l, B_l, C_l, sign in lines0]
        dist_poly1_segments = [sign * (A_l * x_pts + B_l * y_pts + C_l) for A_l, B_l, C_l, sign in lines1]
        result = (dist_main, dist_poly0_segments, dist_poly1_segments)
        memo[params_key] = result
        return result

    def objective(params):
        a, b = params[2], params[3]
        area = a * b
        distances = _calculate_distances(params)
        if distances is None: return np.inf
        min_dist_main = np.min(distances[0])
        min_dist_poly0 = np.min(distances[1]) if distances[1] else 0
        min_dist_poly1 = np.min(distances[2]) if distances[2] else 0
        tangency_penalty = min_dist_main + min_dist_poly0 + min_dist_poly1
        return tangency_weight * tangency_penalty - area

    def constraints(params):
        distances = _calculate_distances(params)
        if distances is None: return -np.inf
        all_constraints = [distances[0]]
        if distances[1]: all_constraints.extend(distances[1])
        if distances[2]: all_constraints.extend(distances[2])
        return np.concatenate(all_constraints)

# (Assuming all helper functions from the previous responses are defined)
# plot_ellipse, get_ellipse_point_from_angle, get_ellipse_perimeter_point,
# polyline_to_line_equations

def solve_for_tangent_ellipse_robust(A, B, polyline0, polyline1, tangency_weight=500.0, num_points=40):
    """
    Finds a tangent inscribed ellipse using a robust two-stage optimization.
    
    Stage 1: Maximize area to find a good starting candidate.
    Stage 2: Refine the candidate to enforce tangency on all boundaries.
    """
    # --- Shared components for both stages ---
    p0_start = polyline0[0]
    p1_start = polyline1[0]
    ref_point = (p0_start + p1_start) / 2.0 * 0.95
    lines0 = polyline_to_line_equations(polyline0, ref_point)
    lines1 = polyline_to_line_equations(polyline1, ref_point)

    memo = {}
    def _calculate_distances(params):
        # (This helper function is identical to the previous version)
        params_key = tuple(params)
        if params_key in memo: return memo[params_key]
        cx, cy, a, b, phi_rad = params
        if a <= 1e-4 or b <= 1e-4: return None
        t_space = np.linspace(0, 2 * np.pi, num_points)
        ellipse_pts_local = np.vstack([a * np.cos(t_space), b * np.sin(t_space)])
        cos_phi, sin_phi = np.cos(phi_rad), np.sin(phi_rad)
        rot_mat = np.array([[cos_phi, -sin_phi], [sin_phi, cos_phi]])
        ellipse_pts_world = (rot_mat @ ellipse_pts_local).T + np.array([cx, cy])
        x_pts, y_pts = ellipse_pts_world.T
        dist_main = 1.0 - ((x_pts / A)**2 + (y_pts / B)**2)
        dist_poly0 = [sign * (A_l * x_pts + B_l * y_pts + C_l) for A_l, B_l, C_l, sign in lines0]
        dist_poly1 = [sign * (A_l * x_pts + B_l * y_pts + C_l) for A_l, B_l, C_l, sign in lines1]
        result = (dist_main, dist_poly0, dist_poly1)
        memo[params_key] = result
        return result

    def constraints(params):
        # (This constraint function is also identical)
        distances = _calculate_distances(params)
        if distances is None: return -np.inf
        all_constraints = [distances[0]]
        if distances[1]: all_constraints.extend(distances[1])
        if distances[2]: all_constraints.extend(distances[2])
        return np.concatenate(all_constraints)

    # --- Stage 1: Maximize Area ---
    print("--- Running Stage 1: Maximizing Area ---")
    def objective_area(params):
        a, b = params[2], params[3]
        return -a * b # Minimize negative area

    # Use a safe, simple initial guess for this stage
    initial_center = ref_point
    x0_stage1 = np.array([initial_center[0], initial_center[1], A*0.1, B*0.1, 0.0])
    
    cons = {'type': 'ineq', 'fun': constraints}
    bounds = [(-A, A), (-B, B), (1e-3, A), (1e-3, B), (-np.pi, np.pi)]

    res_stage1 = minimize(objective_area, x0_stage1, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 1000, 'ftol': 1e-8})
    memo.clear()

    if not res_stage1.success:
        print("Error: Stage 1 (Area Maximization) failed to converge.")
        print(f"Message: {res_stage1.message}")
        return {"success": False}

    print("Stage 1 successful. Found a good candidate ellipse.")
    
    # --- Stage 2: Refine for Tangency ---
    print("--- Running Stage 2: Refining for Tangency ---")
    def objective_tangency(params):
        a, b = params[2], params[3]
        area = a * b
        distances = _calculate_distances(params)
        if distances is None: return np.inf
        min_dist_main = np.min(distances[0])
        min_dist_poly0 = np.min(distances[1]) if distances[1] else 0
        min_dist_poly1 = np.min(distances[2]) if distances[2] else 0
        tangency_penalty = min_dist_main + min_dist_poly0 + min_dist_poly1
        return tangency_weight * tangency_penalty - area

    # CRITICAL: Use the result of Stage 1 as the starting guess for Stage 2
    x0_stage2 = res_stage1.x

    res_stage2 = minimize(objective_tangency, x0_stage2, method='SLSQP', bounds=bounds, constraints=cons, options={'maxiter': 1000, 'ftol': 1e-9})
    memo.clear()
    
    if not res_stage2.success:
        print(f"Warning: Stage 2 (Tangency) may not have converged. Message: {res_stage2.message}")

    print("Optimization finished.")
    final_params = res_stage2.x
    return {
        "center": final_params[0:2],
        "width": 2 * final_params[2],
        "height": 2 * final_params[3],
        "angle_deg": np.rad2deg(final_params[4]),
        # Report success if either stage produced a usable result, but ideally stage 2
        "success": res_stage1.success or res_stage2.success
    }

# --- Example Usage (call the new robust solver) ---
A, B = 10.0, 7.0
t0_frac, t1_frac = 0.95, 0.2

p0_start = get_ellipse_perimeter_point(A, B, t0_frac)
p1_start = get_ellipse_perimeter_point(A, B, t1_frac)

polyline0 = np.array([p0_start, p0_start * 0.8 + np.array([0.5, -0.5]), p0_start * 0.5 + np.array([-0.2, 0.3]), p0_start * 0.3])
polyline1 = np.array([p1_start, p1_start * 0.7 + np.array([-0.3, 0.6]), p1_start * 0.4])

inscribed_params = solve_for_tangent_ellipse_robust(A, B, polyline0, polyline1)

# --- Visualization ---
fig, ax = plt.subplots(figsize=(10, 8))
ax.set_aspect('equal')
plot_ellipse(ax, (0,0), 2*A, 2*B, 0, color='black', fill=False, lw=2, label='Main Ellipse')
ax.plot(polyline0[:, 0], polyline0[:, 1], 'o-', color='red', lw=2, label='Polyline 0')
ax.plot(polyline1[:, 0], polyline1[:, 1], 'o-', color='blue', lw=2, label='Polyline 1')

if inscribed_params["success"]:
    plot_ellipse(ax, inscribed_params['center'], inscribed_params['width'], inscribed_params['height'],
                 inscribed_params['angle_deg'], facecolor='cyan', edgecolor='green', alpha=0.7, lw=2, label='Inscribed Ellipse')

ax.set_xlim(-A-1, A+1)
ax.set_ylim(-B-1, B+1)
ax.grid(True)
ax.legend()
ax.set_title("Inscribed Ellipse (Robust Two-Stage Solution)")
plt.show()

print("\nFinal Inscribed Ellipse Parameters:")
print(inscribed_params)