import jax
import jax.numpy as jnp
import jaxopt

def fit_ellipse_jax(points, theta_min=-jnp.pi/2, theta_max=jnp.pi/2):
    """
    Fit an ellipse to 2D points using JAX and jaxopt for high-performance non-linear least squares with optional theta bounds.
    
    Parameters:
    points (jnp.ndarray): Array of shape (N, 2) where N >= 5, each row is (x, y).
    theta_min (float): Minimum bound for theta (default -pi/2).
    theta_max (float): Maximum bound for theta (default pi/2).
    
    Returns:
    dict: Fitted parameters {'h': center_x, 'k': center_y, 'a': semi-major, 'b': semi-minor, 'theta': rotation_angle}.
    """
    if points.shape[0] < 5:
        raise ValueError("At least 5 points are required to fit an ellipse.")
    
    # Initial guess: center at mean, axes from std dev, theta midpoint
    h0 = jnp.mean(points[:, 0])
    k0 = jnp.mean(points[:, 1])
    a0 = jnp.std(points[:, 0]) * 2  # Rough estimate
    b0 = jnp.std(points[:, 1]) * 2  # Rough estimate
    theta0 = (theta_min + theta_max) / 2
    initial_params = jnp.array([h0, k0, a0, b0, theta0])
    
    # Bounds as arrays for jaxopt
    lower_bounds = jnp.array([-jnp.inf, -jnp.inf, 0.001, 0.001, theta_min])
    upper_bounds = jnp.array([jnp.inf, jnp.inf, jnp.inf, jnp.inf, theta_max])
    
    # JIT-compiled residual sum of squares (loss function)
    @jax.jit
    def loss(params):
        h, k, a, b, theta = params
        c = jnp.cos(theta)
        s = jnp.sin(theta)
        x = points[:, 0] - h
        y = points[:, 1] - k
        xt = c * x + s * y
        yt = -s * x + c * y
        residuals = (xt**2 / a**2 + yt**2 / b**2) - 1
        return jnp.sum(residuals**2)  # Minimize sum of squared residuals
    
    # Define projection with bounds (ignore extra args for compatibility)
    projection = lambda params, *args, **kwargs: jaxopt.projection.projection_box(params, (lower_bounds, upper_bounds))
    
    # Use jaxopt's ProjectedGradient with LBFGS inner solver
    solver = jaxopt.ProjectedGradient(fun=loss, projection=projection, maxiter=500)
    result = solver.run(initial_params)
    
    h, k, a, b, theta = result.params
    
    # Ensure a >= b (swap if necessary, and adjust theta within bounds)
    if a < b:
        a, b = b, a
        theta += jnp.pi / 2
        theta = theta % jnp.pi  # Normalize to [0, pi)
        # Clamp back to bounds if swap pushes it out
        theta = jnp.maximum(theta_min, jnp.minimum(theta_max, theta))
    
    return {
        'h': float(h),
        'k': float(k),
        'a': float(a),
        'b': float(b),
        'theta': float(theta)
    }

# Example: Iterative fitting loop with your points and theta bounds
if __name__ == "__main__":
    # Your points as starting point (convert to JAX array)
    points = jnp.array([
        [-18107.85742188, -9668.421875],
        [-18109.07421875, -9649.95117188],
        [-18133.55859375, -9622.34765625],
        [-18161.0234375, -9615.94433594],
        [-18180.34570312, -9623.63476562]
    ])
    
    max_iters = 1
    tolerance = 1e-5  # Convergence threshold (e.g., change in params)
    prev_params = None
    
    for i in range(max_iters):
        # Example: Bound theta to [-pi/4, pi/4] (~ -45° to 45°)
        params = fit_ellipse_jax(points, theta_min=-jnp.pi/4, theta_max=jnp.pi/4)
        print(f"Iteration {i}: {params}")
        
        # Example modification: Perturb points toward the fitted center (dummy logic; replace with yours)
        center = jnp.array([params['h'], params['k']])
        directions = center - points
        points += 0.01 * directions  # Small step; adjust based on your "best shape" criterion
        
        # Check convergence (e.g., if params don't change much)
        if prev_params is not None:
            param_diff = jnp.linalg.norm(jnp.array(list(params.values())) - jnp.array(list(prev_params.values())))
            if param_diff < tolerance:
                print(f"Converged after {i} iterations.")
                break
        prev_params = params
    
    print("Final fitted parameters:")
    print(params)