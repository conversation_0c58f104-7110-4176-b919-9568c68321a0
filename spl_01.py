import numpy as np
import timeit
import matplotlib.pyplot as plt
from scipy.special import ellipe, ellipeinc
from scipy.optimize import brentq

# --- Accurate SciPy version (unchanged, it was correct) ---
def get_point_on_ellipse_scipy(a, b, fraction):
    if not (0.0 <= fraction <= 1.0):
        raise ValueError("Fraction must be between 0.0 and 1.0.")
    if a < b: # Ensure a is the semi-major axis for the formula
        # If b > a, we can swap them and adjust the final coordinates
        pt = get_point_on_ellipse_scipy(b, a, fraction)
        return np.array([pt[1], pt[0]])

    e_sq = 1.0 - b**2 / a**2
    
    # Handle edge cases to avoid solver
    if np.isclose(fraction, 0.0):
        return np.array([a, 0.0])
    if np.isclose(fraction, 1.0):
        # We return the start point, as 100% of the length is a full circle
        return np.array([a, 0.0])
        
    circumference = 4.0 * a * ellipe(e_sq)
    target_arc_length = fraction * circumference
    
    objective_func = lambda t: a * ellipeinc(t, e_sq) - target_arc_length
    
    # brentq requires the function to have different signs at the endpoints
    # f(0) is negative, f(2*pi) is positive, so this is safe.
    t_solution = brentq(objective_func, 0, 2 * np.pi)
    
    return np.array([a * np.cos(t_solution), b * np.sin(t_solution)])


# --- The CORRECTED Fast Discretized NumPy version ---
class FastEllipse:
    def __init__(self, a, b, num_points=2000):
        self.a = a
        self.b = b
        self.num_points = num_points
        
        t = np.linspace(0, 2 * np.pi, self.num_points)
        x = self.a * np.cos(t)
        y = self.b * np.sin(t)
        self.points = np.stack([x, y], axis=1)
        
        segment_lengths = np.sqrt(np.sum(np.diff(self.points, axis=0)**2, axis=1))
        self.cumulative_lengths = np.insert(np.cumsum(segment_lengths), 0, 0)
        self.total_length = self.cumulative_lengths[-1]

    def get_point(self, fraction):
        if not (0.0 <= fraction <= 1.0):
            raise ValueError("Fraction must be between 0.0 and 1.0.")

        target_length = fraction * self.total_length
        
        # Use np.searchsorted to find the correct segment
        idx = np.searchsorted(self.cumulative_lengths, target_length, side='right')
        
        # --- FIX: Add boundary checks ---
        if idx >= len(self.cumulative_lengths):
            # This happens if fraction is 1.0
            return self.points[-1]
        if idx == 0:
            # This happens if fraction is 0.0
            return self.points[0]
            
        # At this point, idx is guaranteed to be between 1 and num_points-1
        
        # Interpolate within the segment
        start_length = self.cumulative_lengths[idx - 1]
        segment_length = self.cumulative_lengths[idx] - start_length
        
        # Avoid division by zero if segment length is zero (highly unlikely)
        if segment_length == 0:
            return self.points[idx - 1]
            
        interp_fraction = (target_length - start_length) / segment_length
        
        p_start = self.points[idx - 1]
        p_end = self.points[idx]
        
        point = p_start + interp_fraction * (p_end - p_start)
        return point

# --- Verification with your fraction = 0.18 ---
a = 5.0
b = 2.0
fraction = 0.18

# Get the highly accurate answer
accurate_point = get_point_on_ellipse_scipy(a, b, fraction)

# Get the fast, discretized answer from the FIXED class
fast_ellipse = FastEllipse(a, b, num_points=2000)
discretized_point = fast_ellipse.get_point(fraction)

error = np.linalg.norm(accurate_point - discretized_point)

print(f"--- Accuracy Check for fraction = {fraction} (with corrected code) ---")
print(f"Accurate Point:   {accurate_point}")
print(f"Discretized Point:{discretized_point}")
print(f"Error (distance between points): {error:.8f}")

# Let's also check the boundary cases that caused the bug
print("\n--- Checking boundary cases ---")
print(f"Fraction 0.0: {fast_ellipse.get_point(0.0)}")
print(f"Fraction 1.0: {fast_ellipse.get_point(1.0)}")