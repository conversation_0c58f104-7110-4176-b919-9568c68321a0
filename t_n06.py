import numpy as np
from scipy.optimize import differential_evolution

def compute_ellipse_values(points, center, axes, angle):
    """
    Vectorized val computation for single ellipse (from previous code, simplified).
    """
    points = np.asarray(points)
    center = np.asarray(center)
    axes = np.asarray(axes)
    
    translated = points - center
    
    if angle != 0.0:
        cos_theta = np.cos(angle)
        sin_theta = np.sin(angle)
        rot_matrix = np.array([[cos_theta, sin_theta],
                               [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T
    
    return (translated[:, 0] ** 2 / axes[0] ** 2) + (translated[:, 1] ** 2 / axes[1] ** 2)

def objective(params, points):
    """
    Objective: Minimize mean |val - 1| to encourage touching.
    params: [h, k, a, b, theta]
    """
    h, k, a, b, theta = params
    if a <= 0 or b <= 0 or a < b:  # Constraints (a >= b > 0)
        return np.inf
    vals = compute_ellipse_values(points, [h, k], [a, b], theta)
    return np.mean(np.abs(vals - 1))  # Or np.sum((vals - 1)**2) for MSE

# Simulate geom boundary points (e.g., two clusters for "between geoms")
def sample_geom_points(n_points=1000):
    # Geom 1: points around a circle
    theta = np.linspace(0, 2*np.pi, n_points//2)
    geom1 = np.array([5 + 2*np.cos(theta), 0 + 2*np.sin(theta)]).T
    # Geom 2: points around another circle
    geom2 = np.array([-5 + 2*np.cos(theta), 0 + 2*np.sin(theta)]).T
    return np.vstack([geom1, geom2])

# Main fitting
points = sample_geom_points(1000)  # Replace with your geom samples
bounds = [(-10, 10), (-10, 10), (0.1, 5), (0.1, 5), (0, 2*np.pi)]  # Parameter bounds
result = differential_evolution(objective, bounds, args=(points,), popsize=15, maxiter=200, workers=1)

print("Optimized params [h, k, a, b, theta]:", result.x)
print("Minimized objective:", result.fun)  # Close to 0 means good touching fit