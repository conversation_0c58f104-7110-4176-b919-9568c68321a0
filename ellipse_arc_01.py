import numpy as np
from scipy.special import ellipe, ellipeinc
from scipy.optimize import brentq

def _find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution

def get_point_and_tangent_on_ellipse(a, b, fraction, start_angle_rad=0.0, normalize_tangent=True):
    """
    Calculates point and tangent on an ellipse, allowing for a custom start point.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_tangent (bool): If True, returns a unit tangent vector.

    Returns:
        tuple: A tuple containing (point_vector, tangent_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    t_arc = _find_t_for_arc_length_fraction(a, b, fraction)
    
    # 2. Apply the angular offset to get the effective parameter
    t_effective = t_arc + start_angle_rad
    
    # 3. Calculate point and tangent using the new effective angle
    point_vector = np.array([a * np.cos(t_effective), b * np.sin(t_effective)])
    tangent_vector = np.array([-a * np.sin(t_effective), b * np.cos(t_effective)])
    
    if normalize_tangent:
        norm = np.linalg.norm(tangent_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            tangent_vector /= norm
        else:
            tangent_vector = np.array([0., 0.])
            
    return point_vector, tangent_vector

# --- Test the new function ---
a = 20.0
b = 11.0
f = 0.24
# Define our desired start angle for 12 o'clock
start_at_12_oclock = np.pi / 2

results = get_point_and_tangent_on_ellipse(a, b, f, start_at_12_oclock)

print(f"Point: {results[0]}, Tangent: {results[1]}")