import numpy as np
from numpy.linalg import lstsq
from scipy.optimize import least_squares

# ------------------------------------------------------------------
# 1. Vectorised orthogonal residual + Jacobian (np.array only)
# ------------------------------------------------------------------
def sqrt_eps(dtype=np.float64):
    return np.finfo(dtype).eps ** 0.5

def ellipse_geometric_res_jac(p, XY):
    """
    Return (residual, Jacobian) w.r.t. *orthogonal* distances.
    p = [xc, yc, a, b, theta]
    XY = [[x,y],...] shape (N,2)
    Jacobian: (N,5)
    """
    xc, yc, a, b, θ = p
    C, S = np.cos(θ), np.sin(θ)
    dx = XY[:,0] - xc
    dy = XY[:,1] - yc

    # Cov into canonical frame
    u  =  +C*dx + S*dy
    v  =  -S*dx + C*dy

    # -----------------------------------------------------------
    # 1. Find φ such that point (u,v) projects orthogonally
    # -----------------------------------------------------------
    a2, b2 = a*a, b*b
    # Closed-form Newton step: cos/sin of the orthogonal foot point
    φ = np.arctan2(a*v, b*u)
    oldφ = φ - 1          # dummy for stopping
    maxIter = 4
    tol = 1e-8
    while np.max(np.abs(oldφ-φ)) > tol and maxIter > 0:
        oldφ = φ
        cosφ, sinφ = np.cos(φ), np.sin(φ)
        xp =  a*cosφ
        yp =  b*sinφ
        # Secant direction
        f  = a*u*sinφ - b*v*cosφ
        fp = a*u*cosφ + b*v*sinφ
        Δ  = f / (fp + (a2 - b2)*(cosφ**2 - sinφ**2) + 1e-12)
        φ  = φ - Δ
        maxIter -= 1
    # Later threshold is enough (<4 iterations)
    cosφ, sinφ = np.cos(φ), np.sin(φ)

    # -----------------------------------------------------------
    # 2. Orthogonal distance residuals
    # -----------------------------------------------------------
    xp = a * cosφ
    yp = b * sinφ
    res = np.sqrt(u*u + v*v) - np.sqrt(xp*xp + yp*yp)
    res[np.isnan(res)] = 0.0
    sign = np.sign(u*cosφ + v*sinφ)
    res = sign * np.abs(res)

    # -----------------------------------------------------------
    # 3. Jacobian (analytic w.r.t Taubin's routine, simplified here)
    # -----------------------------------------------------------
    denom = u*cosφ/a + v*sinφ/b
    denom = np.where(denom == 0.0, 1.0, denom)
    J = np.empty((XY.shape[0],5))

    xc_fact = -(C*cosφ/a - S*sinφ/b) / denom
    yc_fact = -(S*cosφ/a + C*sinφ/b) / denom

    J[:,0] =  xc_fact           # d(res)/dxc
    J[:,1] =  yc_fact           # d(res)/dyc
    J[:,2] =  cosφ               # d(res)/da
    J[:,3] =  sinφ               # d(res)/db

    # rotation: dq/dθ
    dres_dθ = ( (-S*dx + C*dy)*cosφ/a - (-C*dx - S*dy)*sinφ/b ) / denom
    J[:,4] = dres_dθ

    return res, J


# ------------------------------------------------------------------
# 2. Warp everything into SciPy + scaling
# ------------------------------------------------------------------
def fit_ellipse_geometric(points,
                          initial,
                          bounds,
                          max_nfev=50,
                          ftol=1e-9,
                          xtol=1e-9):
    """Geometric orthogonal-fit, returns un-scaled [xc,yc,a,b,θ]"""
    # --- scale once -------------------------------------------------
    centroid = points.mean(0)
    # span     = points.ptp(0)
    span = np.ptp(points, axis=0)
    s        = max(*span, 1e-6)
    pts_norm = (points - centroid) / s                 # centred + scaled
    # factor parameters
    inv_s    = 1.0/s

    init = initial.copy()
    init[:2] = (init[:2] - centroid) * inv_s
    init[2:4] *= inv_s

    lb = np.empty(5); ub = np.empty(5)
    lb[:2]  = (np.asarray(bounds[0][:2]) - centroid) * inv_s
    ub[:2]  = (np.asarray(bounds[1][:2]) - centroid) * inv_s
    lb[2:4] = np.asarray(bounds[0][2:4]) * inv_s
    ub[2:4] = np.asarray(bounds[1][2:4]) * inv_s
    lb[4]   = bounds[0][4]
    ub[4]   = bounds[1][4]

    fun = lambda p:  ellipse_geometric_res_jac(p, pts_norm)[0]
    jac = lambda p:  ellipse_geometric_res_jac(p, pts_norm)[1]

    result = least_squares(fun,
                           init,
                           jac=jac,
                           bounds=(lb, ub),
                           ftol=ftol,
                           xtol=xtol,
                           max_nfev=max_nfev,
                           method='trf',
                           verbose=0)

    p_out = result.x.copy()
    p_out[:2] = p_out[:2]*s + centroid
    p_out[2:4] = p_out[2:4]*s
    return p_out, {'status': result.status,
                   'cost': result.cost,
                #    'nit': result.nit,
                   'nfev': result.nfev}

# ----------------------------------------------------------
# Demo on provided 5 points
# ----------------------------------------------------------
if __name__ == "__main__":
    pts  = np.array([[-18107.85742188,  -9668.421875  ],
                     [-18109.07421875,  -9649.95117188],
                     [-18133.55859375,  -9622.34765625],
                     [-18161.0234375 ,  -9615.94433594],
                     [-18180.34570312,  -9623.63476562]], dtype=np.float64)

    initial = np.array([-18148, -9653, 45, 32, np.deg2rad(330)])
    bounds = ([-18170, -9679, 30, 20, np.deg2rad(320)],
              [-18130, -9625, 60, 40, np.deg2rad(340)])

    result, info = fit_ellipse_geometric(pts, initial, bounds)
    print("FIT :", result)
    print("INFO:", info)
