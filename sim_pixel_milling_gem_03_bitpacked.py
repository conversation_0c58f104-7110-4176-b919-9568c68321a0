import numpy as np
import math, time
import cv2                     # ← NEW: needed for cv2.distanceTransform

# ──────────────────────────────────────────────────────────────────────────────
# Simulation‑level constants (unchanged)
# ──────────────────────────────────────────────────────────────────────────────
tool_diameter          = 10.0
tool_radius            = tool_diameter / 2.0          # 5 mm
num_teeth              = 4
axial_depth_of_cut     = 5.0
spindle_speed_rpm      = 4000
feed_per_tooth         = 0.05
feed_rate_vf           = spindle_speed_rpm * num_teeth * feed_per_tooth

pixel_size             = 0.05                         # 50 µm per pixel
workpiece_width_mm     = 50.0
workpiece_height_mm    = 30.0
padding_mm             = tool_diameter                # 1‑diameter air on all sides
sim_width_mm           = workpiece_width_mm  + 2*padding_mm
sim_height_mm          = workpiece_height_mm + 2*padding_mm
sim_step_distance      = 0.1

print(f"Feed rate            : {feed_rate_vf:.1f} mm/min")
print(f"Pixel size Δ         : {pixel_size:.3f} mm/px")
print(f"Tool‑path increment  : {sim_step_distance:.2f} mm")

# ──────────────────────────────────────────────────────────────────────────────
# Helper – identical to your original, except indentation fixed where needed
# ──────────────────────────────────────────────────────────────────────────────
def world_to_pixel(xy_mm, origin_xy_mm, p_size):
    return ( int(round((xy_mm[0] - origin_xy_mm[0]) / p_size)),
             int(round((xy_mm[1] - origin_xy_mm[1]) / p_size)) )

def create_tool_matrices(diameter, p_size):
    r_px = diameter / (2*p_size)
    n    = int(math.ceil(diameter / p_size))
    if n % 2 == 0: n += 1
    c    = n // 2
    m    = np.zeros((n, n), np.uint8)
    for y in range(n):
        for x in range(n):
            if math.hypot(x-c, y-c) <= r_px + 1e-6:
                m[y, x] = 1
    return m, c

def create_workpiece_matrix(sim_w_mm, sim_h_mm,
                            wp_w_mm, wp_h_mm,
                            pad_mm, p_size):
    Wpx = int(round(sim_w_mm  / p_size))
    Hpx = int(round(sim_h_mm  / p_size))
    M   = np.zeros((Hpx, Wpx), np.uint8)

    wp_x0, wp_x1 = pad_mm, pad_mm + wp_w_mm
    wp_y0, wp_y1 = pad_mm, pad_mm + wp_h_mm
    px0, py0     = world_to_pixel((wp_x0, wp_y0), (0,0), p_size)
    px1, py1     = world_to_pixel((wp_x1, wp_y1), (0,0), p_size)
    M[py0:py1, px0:px1] = 1
    return M, (0.0, 0.0)

def generate_tool_path_straight(start_xy, end_xy, ds):
    a, b = np.asarray(start_xy), np.asarray(end_xy)
    L    = np.linalg.norm(b-a)
    if L < 1e-9:
        return [tuple(a)]
    u        = (b-a)/L
    n_steps  = int(math.floor(L/ds))
    return [tuple(a + i*ds*u) for i in range(n_steps+1)]

def generate_tool_path_slot(wp_x_start_mm, wp_y_center_mm,
                            wp_width_mm, tool_rad_mm,
                            pad_mm, ds):
    x0 = wp_x_start_mm - pad_mm/2.0
    x1 = wp_x_start_mm + wp_width_mm + pad_mm/2.0
    return generate_tool_path_straight((x0, wp_y_center_mm),
                                       (x1, wp_y_center_mm), ds)

# ──────────────────────────────────────────────────────────────────────────────
# 1)  Static geometry
# ──────────────────────────────────────────────────────────────────────────────
tool_mask, tool_c_off          = create_tool_matrices(tool_diameter, pixel_size)
workpiece, sim_origin          = create_workpiece_matrix(sim_width_mm, sim_height_mm,
                                                         workpiece_width_mm, workpiece_height_mm,
                                                         padding_mm, pixel_size)
Hpx, Wpx = workpiece.shape

print(f"Tool mask size       : {tool_mask.shape}")
print(f"Workpiece bitmap     : {Wpx}×{Hpx} px")

# ──────────────────────────────────────────────────────────────────────────────
# 2)  One‑shot signed‑distance field (in millimetres)
#     (+)  inside stock   /  (‑) outside (already air)
# ──────────────────────────────────────────────────────────────────────────────
def compute_sdf_mm(binary_matrix, px_size):
    inside = binary_matrix.astype(np.uint8)
    outside = 1 - inside
    din  = cv2.distanceTransform(inside , cv2.DIST_L2, 5)
    dout = cv2.distanceTransform(outside, cv2.DIST_L2, 5)
    return (din - dout) * px_size          # px → mm

print("Computing initial SDF …")
sdf_mm = compute_sdf_mm(workpiece, pixel_size)
print("…done.")

# ──────────────────────────────────────────────────────────────────────────────
# 3)  Tool path
# ──────────────────────────────────────────────────────────────────────────────
path_mm = generate_tool_path_slot(padding_mm,
                                  padding_mm + workpiece_height_mm/2.0,
                                  workpiece_width_mm, tool_radius,
                                  padding_mm, sim_step_distance)

print(f"Tool‑path length     : {len(path_mm)} positions")

# ──────────────────────────────────────────────────────────────────────────────
# 4)  Analytic engagement from a single signed distance
#     (front‑half only, i.e. what your “dot > 0” code was doing)
# ──────────────────────────────────────────────────────────────────────────────
def front_half_engagement(d, R):
    """
    d : signed distance (mm) at cutter centre ( +ve = stock, ‑ve = air )
    R : tool radius (mm)

    Returns arc angle in degrees (0–180) for the *leading* half of the cutter.
    """
    if d >= R:          # centre well inside stock
        return 180.0
    if d <= -R:         # centre more than a radius outside
        return 0.0
    if d >= 0.0:        # partially inside
        return 180.0 - 2.0*math.degrees(math.acos(d / R))
    else:               # partially outside
        return 2.0*math.degrees(math.acos(-d / R))

# ──────────────────────────────────────────────────────────────────────────────
# 5)  Simulation loop
# ──────────────────────────────────────────────────────────────────────────────
angles_deg  = []
path_s_mm   = []
s_acc       = 0.0
prev_xy     = None

t0 = time.time()

# path_mm = [(10.0, 10.0),                     
#                 (11.0, 10.0),
#                 (11.0, 11.0),
#                 (11.0, 12.0),
#                 (11.0, 12.1),
#                 ]


for i, centre in enumerate(path_mm):
    # running path length
    if prev_xy is not None:
        s_acc += math.dist(centre, prev_xy)
    path_s_mm.append(s_acc)
    prev_xy = centre

    # grid coordinates
    cx_px, cy_px = world_to_pixel(centre, sim_origin, pixel_size)
    cx_px = np.clip(cx_px, 0, Wpx-1)
    cy_px = np.clip(cy_px, 0, Hpx-1)

    d_mm   = sdf_mm[cy_px, cx_px]
    angle  = front_half_engagement(d_mm, tool_radius)
    angles_deg.append(angle)

    # --- material removal (bitmap AND) -------------------------------
    ys = cy_px - tool_c_off
    ye = ys + tool_mask.shape[0]
    xs = cx_px - tool_c_off
    xe = xs + tool_mask.shape[1]

    ys_c, ye_c = max(0, ys), min(Hpx, ye)
    xs_c, xe_c = max(0, xs), min(Wpx, xe)

    if ys_c < ye_c and xs_c < xe_c:
        ty0 = ys_c - ys;  ty1 = ty0 + (ye_c - ys_c)
        tx0 = xs_c - xs;  tx1 = tx0 + (xe_c - xs_c)
        workpiece_slice = workpiece[ys_c:ye_c, xs_c:xe_c]
        tool_slice      = tool_mask[ty0:ty1, tx0:tx1]
        workpiece_slice[tool_slice == 1] = 0

    # (OPTIONAL) re‑compute SDF every N steps
    # if (i+1) % 200 == 0:
    #     sdf_mm = compute_sdf_mm(workpiece, pixel_size)

    if (i+1) % 500 == 0 or i == len(path_mm)-1:
        print(f"Step {i+1:4d}/{len(path_mm)} | d = {d_mm:+6.2f} mm | θ = {angle:6.1f}°")

t1 = time.time()
print(f"\nSim run time         : {(t1-t0):.3f} s  "
      f"({1e3*(t1-t0)/len(path_mm):.2f} ms/step)")

# ──────────────────────────────────────────────────────────────────────────────
# 6)  Quick report  (same 10 samples you showed)
# ──────────────────────────────────────────────────────────────────────────────
# print("\nSample Engagement Angles:")
# sample_idx = np.linspace(0, len(angles_deg)-1, 10, dtype=int)
# for k in sample_idx:
#     print(f"Path {s_acc* k/ (len(angles_deg)-1):6.2f} mm :  "
#           f"Angle = {angles_deg[k]:6.2f} deg")
    
print(angles_deg[10:20])
print(path_mm[10:20])