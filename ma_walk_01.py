import bpy
from mathutils import Vector
import numpy as np
import trimesh
import shapely.geometry
from pyclothoids import Clothoid
import time
from shapely.geometry import <PERSON>y<PERSON>, Point, LineString
from shapely.strtree import STRtree

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry() -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []
    
    geometry_list = []
    for obj in selected_objects:
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str) -> bpy.types.Object:
    """Create a line object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    edges = [(i, i + 1) for i in range(len(vertices) - 1)]
    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def geometry_to_trocho_path(geometry: list[np.ndarray]) -> tuple[shapely.geometry.Polygon, shapely.geometry.LineString]:
    """Convert geometry to Shapely Polygon and LineString."""    
    if len(geometry) != 2:
        print("Please select exactly two mesh objects.")
        return None, None
    return shapely.geometry.Polygon(shell=geometry[0]), shapely.geometry.LineString(geometry[1])


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front(path, polygon, step, step_treshold=0.33, min_radius=5):
    """Generate advancing front points along path."""
    path = np.asarray(path)
    if path.shape[0] <= 1:
        return np.array([]), np.array([])

    # Sample path
    sampler = trimesh.path.traversal.PathSample(path)
    sample_step = step / 25.0
    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)
    points = sampler.sample(sample_distances)
    radii = boundary_distance(polygon, points)

    # Find end index where radius drops below min_radius
    decreasing_end = np.where(np.diff(radii) >= 0)[0]
    end_idx = decreasing_end[0] + 1 if decreasing_end.size > 0 else len(radii)
    drops_below = np.where(radii[:end_idx] < min_radius)[0]
    end_index = drops_below[0] if drops_below.size > 0 else len(radii) - 1
    end_distance = sample_distances[end_index]

    # Store results
    result_distances = [0.0]
    result_radii = [radii[0]]
    last_idx = 0

    for i in range(1, len(points)):
        if sample_distances[i] >= end_distance:
            vector = points[end_index] - points[last_idx]
            front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[end_index]            
            if front_distance >= (step_treshold * step):               
                result_distances.append(end_distance)
                result_radii.append(radii[end_index])
            break

        vector = points[i] - points[last_idx]
        front_distance = np.linalg.norm(vector) - radii[last_idx] + radii[i]
        if front_distance >= step:
            result_distances.append(sample_distances[i])
            result_radii.append(radii[i])
            last_idx = i

    return np.array(result_distances), np.array(result_radii)


def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, edge_length):
    """Generate points along an arc in counter-clockwise direction."""
    center = np.array(center)
    radius = max(1e-9, radius)
        
    arc_length = radius * (angle_start_rad -angle_end_rad)
    num_points = max(2, int(np.ceil(arc_length / edge_length)) + 1)
    angles = np.linspace(angle_end_rad, angle_start_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculate arc angles based on current and next centers/radii."""
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)
    angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if segment_len > 1e-9 else 0.0

    start_angle = angle_dir + np.pi / 2.0
    end_angle = angle_dir - np.pi / 2.0

    if R_next > R_current and segment_len > 1e-9:
        delta_angle = np.arcsin(np.clip((R_next - R_current) / segment_len, 0.0, 1.0))
        start_angle += delta_angle
        end_angle -= delta_angle

    return start_angle, end_angle


def find_nearby_edges(polygon: Polygon, point: Point, max_distance: float) -> list[LineString]:
    # Extract coordinates and create edges
    coords = list(polygon.exterior.coords)
    edges = [LineString([coords[i], coords[i + 1]]) 
            for i in range(len(coords) - 1)]
    
    # Create spatial index
    tree = STRtree(edges)
    
    # Create a search buffer around the point
    search_area = point.buffer(max_distance)
    
    # Query the spatial index for potential edges
    potential_edges = tree.query(search_area)
    
    # Final precise distance check
    nearby_edges = [
        edges[idx] for idx in potential_edges 
        if edges[idx].distance(point) <= max_distance
    ]
    
    return nearby_edges


def main():
    # Get geometry and validate
    geometry = get_geometry()
    if len(geometry) < 2:
        print("Please select exactly two objects: a boundary polygon (active) and a path.")
        return
    
    if len(geometry) > 2:
        # Create holes from all geometries except first (exterior) and last (path)
        holes = [geom for geom in geometry[1:-1]]
        # Create polygon with exterior and holes
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)
    else:
        polygon = shapely.geometry.Polygon(shell=geometry[0])

    path_shapely = shapely.geometry.LineString(geometry[-1])

    # Setup parameters
    buffer_dist = -10
    edge_step = 6
    edge_step_treshold = 0.4  # small value for debugging
    min_path_radius = 5
    edge_length = 2  

    path = np.array(path_shapely.coords)
    polygon_boundary = polygon.buffer(buffer_dist)
    create_ring_object(polygon_boundary.exterior.coords, "polygon_boundary")
    if isinstance(polygon_boundary, shapely.geometry.MultiPolygon):
        polygon_boundary = max(polygon_boundary.geoms, key=lambda p: p.area)

    # Generate points
    result_distances, result_radii = advancing_front(path, polygon_boundary, edge_step, 
                                                   edge_step_treshold, min_path_radius)
    if len(result_distances) < 2:
        print("Not enough points generated.")
        return

    path_sampler = trimesh.path.traversal.PathSample(path)
    selected_centers = path_sampler.sample(result_distances)

    # Generate final path
    final_path = []
    all_arc_data = {}

    # Process arcs and transitions
    for i in range(len(selected_centers)):
        C_i, R_i = selected_centers[i], max(1e-6, result_radii[i])
        
        if i < len(selected_centers) - 1:
            C_next, R_next = selected_centers[i+1], max(1e-6, result_radii[i+1])            
            start_angle, end_angle = calculate_arc_angles(C_i, R_i, C_next, R_next)            
        else: # Last arc - use previous arc for direction
            C_prev, R_prev = selected_centers[i-1], max(1e-6, result_radii[i-1])
            segment_vec = C_i - C_prev
            angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if np.linalg.norm(segment_vec) > 1e-9 else 0.0
            start_angle = angle_dir + np.pi/2
            end_angle = angle_dir - np.pi/2

            if R_i > R_prev and np.linalg.norm(segment_vec) > 1e-9:
                delta_angle = np.arcsin(np.clip((R_i - R_prev) / np.linalg.norm(segment_vec), 0.0, 1.0))
                start_angle += delta_angle
                end_angle -= delta_angle

        # Store arc data
        all_arc_data[i] = {
            "center": C_i,
            "radius": R_i,
            "start_angle": start_angle + np.pi * 2,
            "end_angle": end_angle + np.pi * 2
        }

        
        # Generate arc points
        arc_points = generate_arc_points(C_i, R_i, 
                                       all_arc_data[i]["start_angle"],
                                       all_arc_data[i]["end_angle"], 
                                       edge_length)

        # create_line_object(arc_points, f"arc_{i}")

        if i == 0:
            final_path.extend(arc_points)
        else:
            # Generate clothoid transition
            prev_arc = all_arc_data[i-1]
            x0 = float(prev_arc["center"][0] + prev_arc["radius"] * np.cos(prev_arc["start_angle"]))
            y0 = float(prev_arc["center"][1] + prev_arc["radius"] * np.sin(prev_arc["start_angle"]))            
            theta0 = float(prev_arc["start_angle"] + np.pi/2)
            
            x1 = float(C_i[0] + R_i * np.cos(all_arc_data[i]["end_angle"]))
            y1 = float(C_i[1] + R_i * np.sin(all_arc_data[i]["end_angle"]))
            theta1 = float(all_arc_data[i]["end_angle"] + np.pi/2)
            
            clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)
            num_points = max(2, int(np.ceil(clothoid.length / edge_length)) + 1)
            clothoid_points = np.array(clothoid.SampleXY(num_points)).T
            
            final_path.extend(clothoid_points[1:])
            final_path.extend(arc_points[1:])

        if i == len(selected_centers) - 1:
            # print('last arc')
            # print(f'center: {C_i}')
            # dis = polygon_boundary.boundary.distance(shapely.geometry.Point(C_i))
            # print(f'radius: {dis}')
            # nearby_edges = find_nearby_edges(polygon_boundary, C_i, dis)
            # print(nearby_edges)
            ...


    create_line_object(np.array(final_path), "merged_path")
    print(f'length: {shapely.LineString(final_path).length}')    


if __name__ == "__main__":    
    main()    
