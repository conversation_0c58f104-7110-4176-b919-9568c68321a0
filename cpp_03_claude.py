import networkx as nx
from itertools import combinations

def solve_cpp(G, start_node):
    """
    Solves the Chinese Postman Problem for an undirected, unweighted graph.

    Args:
        G: A NetworkX graph (undirected)
        start_node: The node to start the path from

    Returns:
        A list of nodes forming the CPP path
    """
    # Ensure the graph is connected
    if not nx.is_connected(G):
        raise ValueError("Graph must be connected")

    # Convert to multigraph to handle duplicate edges
    G_multi = nx.MultiGraph(G)

    # Find vertices with odd degree
    odd_vertices = [v for v, d in G.degree() if d % 2 == 1]

    # If odd vertices exist, add edges to make all vertices even degree
    if odd_vertices:
        # Create complete graph of odd vertices with shortest path weights
        odd_graph = nx.Graph()
        for u, v in combinations(odd_vertices, 2):
            weight = nx.shortest_path_length(G, u, v)
            odd_graph.add_edge(u, v, weight=weight)

        # Find minimum weight perfect matching
        matching = nx.algorithms.matching.min_weight_matching(odd_graph)

        # Add duplicate edges along shortest paths between matched pairs
        for u, v in matching:
            path = nx.shortest_path(G, u, v)
            for i in range(len(path) - 1):
                G_multi.add_edge(path[i], path[i+1])

    # Find Eulerian circuit starting at specified node
    edges = list(nx.eulerian_circuit(G_multi, source=start_node))

    # Convert edges to node path
    path = [start_node]
    for _, v in edges:
        path.append(v)

    return path
    

def main():
    # Create sample graph
    G = nx.Graph()
    # G.add_edge('A', 'B')
    # G.add_edge('B', 'C')
    # G.add_edge('C', 'D')
    # G.add_edge('D', 'A')
    # G.add_edge('B', 'D')
    G.add_edge('A', 'B')
    G.add_edge('B', 'C')
    G.add_edge('B', 'D')
    G.add_edge('D', 'E')
    G.add_edge('D', 'F')
    G.add_edge('F', 'G')
    G.add_edge('F', 'H')

    # Solve
    path = solve_cpp(G, start_node='A')
    print(path)

if __name__ == "__main__":
    main()
