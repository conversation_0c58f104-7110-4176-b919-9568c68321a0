import networkx as nx
from itertools import combinations

def solve_chinese_postman(graph, weight='weight', start_node=None):
    """
    Solves the Chinese Postman Problem for a connected undirected graph.

    Args:
        graph (nx.Graph or nx.MultiGraph): A connected NetworkX graph.
                                           Can be weighted or unweighted.
        weight (str): The edge attribute key corresponding to the weight
                      (default is 'weight'). If None or '', considers
                      the graph unweighted (weight=1 for all edges).
        start_node: The node from which the CPP tour should start. If None,
                    picks an arbitrary node (usually the smallest node index).

    Returns:
        tuple: A tuple containing:
            - list: The sequence of edges (u, v) representing the optimal CPP tour.
            - float: The total minimum cost (length) of the tour.
        Or raises ValueError if the graph is not connected.
    """
    if not nx.is_connected(graph):
        raise ValueError("Input graph must be connected.")

    # Use 1 as weight if 'weight' attribute is missing or specified as None/''
    for u, v, d in graph.edges(data=True):
        if weight not in d or d[weight] is None:
            d[weight] = 1

    # 1. Identify odd-degree nodes
    odd_degree_nodes = [n for n, d in graph.degree() if d % 2 != 0]

    # If no odd-degree nodes, the graph is Eulerian
    if not odd_degree_nodes:
        print("Graph is already Eulerian.")
        if start_node is None:
            start_node = list(graph.nodes())[0] # Pick a default start
        eulerian_circuit = list(nx.eulerian_circuit(graph, source=start_node, keys=False)) # keys=False for Graph
        total_cost = sum(graph[u][v][weight] for u, v in eulerian_circuit)
        return eulerian_circuit, total_cost

    print(f"Odd degree nodes identified: {odd_degree_nodes}")

    # 2. Compute all-pairs shortest paths between odd-degree nodes
    odd_pairs = list(combinations(odd_degree_nodes, 2))
    shortest_paths_costs = {}
    shortest_paths_nodes = {}

    print("Calculating shortest paths between odd nodes...")
    for u, v in odd_pairs:
        cost = nx.shortest_path_length(graph, source=u, target=v, weight=weight)
        path = nx.shortest_path(graph, source=u, target=v, weight=weight)
        shortest_paths_costs[(u, v)] = cost
        shortest_paths_nodes[(u, v)] = path
        shortest_paths_costs[(v, u)] = cost # Ensure symmetry for matching graph
        shortest_paths_nodes[(v, u)] = path[::-1]
        # print(f"  Path {u}-{v}: Cost={cost}, Path={path}")


    # 3. Create the complete graph K_odd for matching
    K_odd = nx.Graph()
    for u, v in odd_pairs:
        K_odd.add_edge(u, v, weight=shortest_paths_costs[(u, v)])

    # 4. Find Minimum Weight Perfect Matching
    print("Finding Minimum Weight Perfect Matching...")
    # Note: NetworkX matching minimizes weight. We want min cost paths.
    # For MWPM, higher weight usually means less desirable. But nx.min_weight_matching
    # finds the matching with the *minimum* sum of weights, which is exactly what we need.
    min_matching_edges = nx.min_weight_matching(K_odd, weight='weight')
    matching_cost = sum(K_odd[u][v]['weight'] for u, v in min_matching_edges)
    print(f"Matching found: {min_matching_edges}")
    print(f"Cost of matching (extra distance): {matching_cost}")


    # 5. Augment the graph: Create a MultiGraph
    G_augmented = nx.MultiGraph()
    G_augmented.add_nodes_from(graph.nodes())

    # Add original edges
    for u, v, d in graph.edges(data=True):
        G_augmented.add_edge(u, v, weight=d.get(weight, 1))

    # Add edges corresponding to the shortest paths in the matching
    print("Augmenting graph with shortest paths from matching...")
    for u_match, v_match in min_matching_edges:
        path = shortest_paths_nodes[(u_match, v_match)]
        print(f"  Adding path for match {u_match}-{v_match}: {path}")
        for i in range(len(path) - 1):
            u_path, v_path = path[i], path[i+1]
            edge_data = graph.get_edge_data(u_path, v_path)
            edge_weight = edge_data.get(weight, 1) if edge_data else 1
            G_augmented.add_edge(u_path, v_path, weight=edge_weight)


    # 6. Find Eulerian Circuit in the augmented graph
    # Check degrees in augmented graph (should all be even)
    # for n, d in G_augmented.degree():
    #     if d % 2 != 0: print(f"WARNING: Node {n} still has odd degree {d} in augmented graph!")

    if start_node is None:
       # If no start node specified, pick one of the originally odd nodes
       # or the smallest node index if graph was already Eulerian (handled above)
       start_node = odd_degree_nodes[0]
    elif start_node not in graph.nodes():       
       start_node = odd_degree_nodes[0]
       
    eulerian_circuit_augmented = list(nx.eulerian_circuit(G_augmented, source=start_node, keys=False))


    # 7. Calculate total cost
    original_total_weight = sum(d.get(weight, 1) for u, v, d in graph.edges(data=True))
    cpp_total_cost = original_total_weight + matching_cost

    # Verify cost from augmented circuit (optional sanity check)
    circuit_cost_check = sum(G_augmented.get_edge_data(u, v)[0]['weight'] for u, v in eulerian_circuit_augmented)
    # Note: Accessing weight in MultiGraph requires key, [0] assumes first edge if parallel
    # A better check sums weights directly from G_augmented edges
    circuit_cost_check_alt = sum(d['weight'] for u,v,d in G_augmented.edges(data=True)) / 2 # Each edge listed once, circuit traverses all

    return eulerian_circuit_augmented, cpp_total_cost


G = nx.Graph()
G.add_edge('A', 'B', weight=10)
G.add_edge('B', 'C', weight=1)
G.add_edge('C', 'D', weight=1)
G.add_edge('D', 'A', weight=20)
G.add_edge('B', 'D', weight=1)

try:
    cpp_tour, cpp_cost = solve_chinese_postman(G, weight='weight', start_node='D') # Start at A    
    print(f"Optimal CPP Tour Cost: {cpp_cost}")
    print(f"Optimal CPP Tour (Edges): {cpp_tour}")

except ValueError as e:
    print(f"Error: {e}")