import numpy as np
import time
from numba import njit

class MillingSimulation:
    def __init__(self, tool_diameter, workpiece_dimensions, pixel_size=None):
        """
        Initialize the milling simulation with tool and workpiece parameters
        
        Parameters:
        - tool_diameter: diameter of the milling tool in mm
        - workpiece_dimensions: tuple (width, height) in mm
        - pixel_size: size of each pixel in mm (if None, calculated based on tool diameter)
        """
        self.tool_diameter = tool_diameter
        self.tool_radius = tool_diameter / 2
        
        # Store workpiece dimensions
        self.workpiece_width_mm, self.workpiece_height_mm = workpiece_dimensions
        
        # If pixel_size is not specified, calculate it based on tool diameter
        if pixel_size is None:
            # Use 1/50 of tool diameter as recommended in the paper
            self.pixel_size = tool_diameter / 50
        else:
            self.pixel_size = pixel_size
            
        # Store only engagement angle results
        self.engagement_angles = []
    
    def _create_tool_matrix(self):
        """Create a circular tool matrix"""
        tool_size = 2 * self.tool_radius_px + 1
        tool = np.zeros((tool_size, tool_size), dtype=np.uint8)
        center = self.tool_radius_px
        
        # Create a circular tool using distance from center
        y, x = np.ogrid[:tool_size, :tool_size]
        dist = np.sqrt((x - center)**2 + (y - center)**2)
        tool[dist <= self.tool_radius_px] = 1
                    
        return tool
    
    def _mm_to_pixel(self, x_mm, y_mm):
        """Convert mm coordinates to pixel coordinates"""
        x_px = int(x_mm / self.pixel_size)
        y_px = int(y_mm / self.pixel_size)
        return x_px, y_px
    
    def _calculate_material_removal(self, tool_position_mm):
        """Calculate material removed at the given tool position"""
        # Convert tool position from mm to pixels
        tool_x_px, tool_y_px = self._mm_to_pixel(tool_position_mm[0], tool_position_mm[1])
        
        # Use the Numba-optimized function
        return _calculate_material_removal_njit(
            tool_x_px, tool_y_px,
            self.workpiece, self.tool,
            self.tool_radius_px, 
            self.workpiece_height_px, self.workpiece_width_px,
            self.pixel_size
        )
    
    def _calculate_engagement_angle(self, radial_immersion):
        """Calculate cutter engagement angle based on radial immersion"""
        return _calculate_engagement_angle_njit(radial_immersion, self.tool_radius)
    
    def simulate_toolpath(self, toolpath, feed_per_tooth, spindle_speed, axial_depth, num_teeth):
        """
        Optimized simulation for large toolpaths, focusing only on engagement angle
        
        Parameters:
        - toolpath: list of (x, y) positions in mm
        - feed_per_tooth: feed per tooth in mm
        - spindle_speed: spindle speed in rpm
        - axial_depth: axial depth of cut in mm
        - num_teeth: number of teeth on the tool
        
        Returns:
        - engagement_angles: list of engagement angles in radians
        """
        start_time = time.time()
        
        # Calculate feed rate in mm/min
        feed_rate = feed_per_tooth * num_teeth * spindle_speed
        
        # Initialize workpiece and tool
        self.workpiece_width_px = int(np.ceil(self.workpiece_width_mm / self.pixel_size))
        self.workpiece_height_px = int(np.ceil(self.workpiece_height_mm / self.pixel_size))
        self.workpiece = np.ones((self.workpiece_height_px, self.workpiece_width_px), dtype=np.uint8)
        
        self.tool_radius_px = int(np.ceil(self.tool_radius / self.pixel_size))
        self.tool = self._create_tool_matrix()
        
        # Clear previous results
        self.engagement_angles = np.zeros(len(toolpath))
        
        # Pre-allocate arrays for better performance
        positions = np.array(toolpath)
        
        # Compile Numba functions on first run
        print("Compiling Numba functions (first run only)...")
        _ = _calculate_material_removal_njit(
            0, 0, np.ones((2, 2), dtype=np.uint8), np.ones((2, 2), dtype=np.uint8),
            1, 2, 2, self.pixel_size
        )
        _ = _calculate_engagement_angle_njit(0.1, 1.0)
        _ = _simulate_toolpath_njit(
            positions, self.workpiece, self.tool, self.tool_radius_px,
            self.workpiece_height_px, self.workpiece_width_px,
            self.pixel_size, feed_rate, axial_depth, self.tool_radius
        )
        
        # Use the Numba-optimized simulation function
        self.engagement_angles = _simulate_toolpath_njit(
            positions, self.workpiece, self.tool, self.tool_radius_px,
            self.workpiece_height_px, self.workpiece_width_px,
            self.pixel_size, feed_rate, axial_depth, self.tool_radius
        )
        
        end_time = time.time()
        print(f"Simulation completed in {end_time - start_time:.2f} seconds")
        
        return self.engagement_angles


@njit(cache=True)
def _calculate_material_removal_njit(tool_x_px, tool_y_px, workpiece, tool, 
                                    tool_radius_px, workpiece_height_px, workpiece_width_px,
                                    pixel_size):
    """Numba-optimized function to calculate material removal"""
    # Calculate the region of the workpiece that overlaps with the tool
    tool_top = max(0, tool_y_px - tool_radius_px)
    tool_bottom = min(workpiece_height_px, tool_y_px + tool_radius_px + 1)
    tool_left = max(0, tool_x_px - tool_radius_px)
    tool_right = min(workpiece_width_px, tool_x_px + tool_radius_px + 1)
    
    # Calculate tool region offsets
    tool_region_top = max(0, tool_radius_px - tool_y_px)
    tool_region_left = max(0, tool_radius_px - tool_x_px)
    
    # Calculate dimensions
    height = tool_bottom - tool_top
    width = tool_right - tool_left
    
    # Count removed pixels
    removed_pixels = 0
    
    for i in range(height):
        for j in range(width):
            wp_i = tool_top + i
            wp_j = tool_left + j
            tool_i = tool_region_top + i
            tool_j = tool_region_left + j
            
            # Check bounds for both arrays
            if (wp_i < workpiece_height_px and wp_j < workpiece_width_px and 
                tool_i < tool.shape[0] and tool_j < tool.shape[1]):
                # If both workpiece and tool have material at this position
                if workpiece[wp_i, wp_j] == 1 and tool[tool_i, tool_j] == 1:
                    removed_pixels += 1
                    # Remove material
                    workpiece[wp_i, wp_j] = 0
    
    # Calculate removed area in mm²
    removed_area = removed_pixels * (pixel_size ** 2)
    
    return removed_area


@njit(cache=True)
def _calculate_engagement_angle_njit(radial_immersion, tool_radius):
    """Numba-optimized function to calculate engagement angle"""
    if radial_immersion >= tool_radius:
        return np.pi  # 180 degrees
    
    # Using formula from paper: θ = arccos((R-ae)/R)
    engagement_angle = np.arccos((tool_radius - radial_immersion) / tool_radius)
    return engagement_angle


@njit(cache=True)
def _simulate_toolpath_njit(positions, workpiece, tool, tool_radius_px,
                           workpiece_height_px, workpiece_width_px,
                           pixel_size, feed_rate, axial_depth, tool_radius):
    """Numba-optimized function to simulate the entire toolpath"""
    engagement_angles = np.zeros(len(positions))
    
    for i in range(len(positions)):
        position = positions[i]
        
        # Convert mm to pixels
        tool_x_px = int(position[0] / pixel_size)
        tool_y_px = int(position[1] / pixel_size)
        
        # Calculate material removal
        removed_area = _calculate_material_removal_njit(
            tool_x_px, tool_y_px, workpiece, tool, tool_radius_px,
            workpiece_height_px, workpiece_width_px, pixel_size
        )
        
        # Calculate radial immersion
        if feed_rate > 0:
            mrr = removed_area * axial_depth * feed_rate
            radial_immersion = mrr / (axial_depth * feed_rate)
        else:
            radial_immersion = 0
        
        # Calculate engagement angle
        engagement_angles[i] = _calculate_engagement_angle_njit(radial_immersion, tool_radius)
    
    return engagement_angles



# Example usage
def generate_sample_toolpath():
    """Generate a sample toolpath for demonstration"""
    # Create a simple rectangular pocket toolpath
    x_values = []
    y_values = []
    
    # Starting point
    x, y = 0, 20
    
    # First horizontal line
    for i in range(21):
        x_values.append(x + i)
        y_values.append(y)

    # second horizontal line

    x = 20
    for i in range(21):
        x_values.append(x - i)
        y_values.append(y+3)    
    
    # Convert to list of tuples
    return list(zip(x_values, y_values))

# Create a more complex toolpath with a circular arc
def generate_complex_toolpath():
    """Generate a more complex toolpath with straight lines and arcs"""
    toolpath = []
    
    # Starting point
    x, y = 0, 20
    toolpath.append((x, y))
    
    # First horizontal line
    for i in range(1, 41):
        toolpath.append((x + i, y))
    
    # Arc (quarter circle)
    center_x, center_y = 60, 20
    radius = 30
    for angle in np.linspace(0, np.pi/2, 20):
        arc_x = center_x + radius * np.cos(angle)
        arc_y = center_y + radius * np.sin(angle)
        toolpath.append((arc_x, arc_y))
    
    # Second horizontal line
    for i in range(1, 41):
        toolpath.append((90 - i, 50))
    
    # Second vertical line
    for i in range(1, 31):
        toolpath.append((20, 50 - i))
    
    return toolpath

def generate_large_toolpath(num_points=100000):
    """Generate a complex toolpath with 100,000 points"""
    toolpath = []
    
    # Parameters for spiral
    center_x, center_y = 50, 40  # Center of workpiece
    max_radius = 35  # Maximum radius of spiral
    min_radius = 5   # Minimum radius of spiral
    num_turns = 50   # Number of complete turns
    
    # Generate spiral points (inward spiral)
    angles = np.linspace(0, num_turns * 2 * np.pi, num_points // 2)
    radii = np.linspace(max_radius, min_radius, num_points // 2)
    
    # First half: inward spiral
    for i in range(len(angles)):
        x = center_x + radii[i] * np.cos(angles[i])
        y = center_y + radii[i] * np.sin(angles[i])
        toolpath.append((x, y))
    
    # Second half: outward spiral
    angles = np.linspace(num_turns * 2 * np.pi, 0, num_points - len(toolpath))
    radii = np.linspace(min_radius, max_radius, num_points - len(toolpath))
    
    for i in range(len(angles)):
        x = center_x + radii[i] * np.cos(angles[i])
        y = center_y + radii[i] * np.sin(angles[i])
        toolpath.append((x, y))
    
    return toolpath

# Run the simulation
if __name__ == "__main__":
    # Set parameters
    tool_diameter = 10  # mm
    workpiece_dimensions = (100, 80)  # mm
    pixel_size = 0.1  # mm
    
    # Process parameters
    feed_per_tooth = 0.05  # mm
    spindle_speed = 4000  # rpm
    axial_depth = 5  # mm
    num_teeth = 4
    
    # Create simulation
    sim = MillingSimulation(tool_diameter, workpiece_dimensions, pixel_size)
    
    # Generate toolpath
    toolpath = generate_sample_toolpath()
    toolpath = generate_large_toolpath(1000000)

    # toolpath = [(10.0, 10.0),                     
    #             (11.0, 10.0),
    #             (11.0, 11.0),
    #             (11.0, 12.0),
    #             (10.0, 11.0)
    #             ]

    # Run simulation
    results = sim.simulate_toolpath(toolpath, feed_per_tooth, spindle_speed, axial_depth, num_teeth)
    
    # print(f'Engagement angle: {results}')
    # print(f'point: {toolpath}')
