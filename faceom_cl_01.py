import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString, Polygon
import math

class FACEOM:
    """
    Fast Constant Engagement Offsetting Method for generating milling tool paths
    """
    def __init__(self, tool_radius, engagement_angle_deg):
        """
        Initialize the FACEOM algorithm

        Parameters:
        tool_radius (float): Radius of the milling tool
        engagement_angle_deg (float): Desired cutter engagement angle in degrees
        """
        self.tool_radius = tool_radius
        self.engagement_angle = math.radians(engagement_angle_deg)
        self.alpha = math.pi/2 - self.engagement_angle  # Calculate alpha angle (90° - θ)

    def find_intersection(self, P, v, C):
        """
        Find intersection of half-line from P along v with circle centered at C

        Parameters:
        P (array): Starting point of half-line
        v (array): Direction vector of half-line
        C (array): Center of circle

        Returns:
        array: Intersection point (or None if no intersection)
        """
        # Line equation: P + t*v
        # Circle equation: |X - C|^2 = r^2
        # Solve for t: |P + t*v - C|^2 = r^2

        PC = C - P
        a = np.dot(v, v)
        b = 2 * np.dot(v, PC)
        c = np.dot(PC, PC) - self.tool_radius**2

        # Solve quadratic equation at^2 + bt + c = 0
        discriminant = b**2 - 4*a*c

        if discriminant < 0:
            return None  # No intersection

        # Find the two solutions
        t1 = (-b + math.sqrt(discriminant)) / (2*a)
        t2 = (-b - math.sqrt(discriminant)) / (2*a)

        # We want the solution with t > 0 (forward direction)
        if t1 > 0 and t2 > 0:
            t = min(t1, t2)  # Take the closest intersection
        elif t1 > 0:
            t = t1
        elif t2 > 0:
            t = t2
        else:
            return None  # No forward intersection

        # Calculate intersection point
        return P + t * v

    def calculate_exit_point(self, P, C):
        """
        Calculate exit point Q based on tool position P and contact point C

        Parameters:
        P (array): Tool position
        C (array): Contact point

        Returns:
        array: Exit point Q
        """
        # Vector from P to C
        PC = C - P
        PC_norm = np.linalg.norm(PC)

        if PC_norm < 1e-10:  # Avoid division by zero
            return P

        PC_unit = PC / PC_norm

        # Rotate PC_vector by engagement angle
        cos_theta = math.cos(self.engagement_angle)
        sin_theta = math.sin(self.engagement_angle)

        rotated_x = PC_unit[0] * cos_theta - PC_unit[1] * sin_theta
        rotated_y = PC_unit[0] * sin_theta + PC_unit[1] * cos_theta

        # Calculate Q
        Q = P + self.tool_radius * np.array([rotated_x, rotated_y])

        return Q

    def generate_tool_path(self, contour_points, step_size=0.01):
        """
        Generate tool path with constant engagement

        Parameters:
        contour_points: List of (x,y) points defining the contour
        step_size: Step size for numerical solution (as a fraction of tool diameter)

        Returns:
        List of tool path points and exit points
        """
        # Initialize
        tool_path_points = []
        exit_points = []

        # Get starting point on contour
        C0 = np.array(contour_points[0])

        # Calculate tangent at starting point
        C1 = np.array(contour_points[1])
        tangent = C1 - C0
        tangent = tangent / np.linalg.norm(tangent)

        # Calculate omega angle
        omega = 90 + math.degrees(self.engagement_angle)
        omega_rad = math.radians(omega)

        # Rotate tangent vector by omega
        rot_x = tangent[0] * math.cos(omega_rad) - tangent[1] * math.sin(omega_rad)
        rot_y = tangent[0] * math.sin(omega_rad) + tangent[1] * math.cos(omega_rad)
        rot_tangent = np.array([rot_x, rot_y])

        # Calculate starting tool position P0
        P0 = C0 + self.tool_radius * rot_tangent

        # Calculate feed direction v0 (perpendicular to P0C0)
        PC_vector = C0 - P0
        v0 = np.array([-PC_vector[1], PC_vector[0]])  # Perpendicular vector
        v0 = v0 / np.linalg.norm(v0)  # Normalize

        # Add starting point to tool path
        tool_path_points.append(P0)

        # Calculate exit point Q0
        Q0 = self.calculate_exit_point(P0, C0)
        exit_points.append(Q0)

        # Current point and feed direction
        P_current = P0
        v_current = v0

        # Create a LineString from the contour points
        contour_line = LineString(contour_points)

        # Parameter along the contour
        param = 0
        step = step_size * self.tool_radius * 2  # Scale step size by tool diameter

        # Main loop for generating tool path
        while param < contour_line.length:
            # Step 1: Move along the contour by step
            param += step
            if param > contour_line.length:
                param = contour_line.length

            # Get next point on contour
            C_next = np.array(contour_line.interpolate(param).coords[0])

            # Step 2: Find intersection of half-line from P_current along v_current
            # with circle centered at C_next with radius tool_radius
            P_next = self.find_intersection(P_current, v_current, C_next)

            if P_next is None:
                # No valid intersection found, try smaller step
                param -= step
                step *= 0.5
                continue

            # Step 3: Calculate new feed direction
            PC_vector = C_next - P_next
            PC_norm = np.linalg.norm(PC_vector)

            if PC_norm < 1e-10:  # Avoid division by zero
                continue

            PC_unit = PC_vector / PC_norm

            # Rotate PC_vector by alpha
            cos_alpha = math.cos(self.alpha)
            sin_alpha = math.sin(self.alpha)

            v_next_x = PC_unit[0] * cos_alpha - PC_unit[1] * sin_alpha
            v_next_y = PC_unit[0] * sin_alpha + PC_unit[1] * cos_alpha
            v_next = np.array([v_next_x, v_next_y])

            # Add point to tool path
            tool_path_points.append(P_next)

            # Calculate exit point
            Q_next = self.calculate_exit_point(P_next, C_next)
            exit_points.append(Q_next)

            # Update current point and feed direction
            P_current = P_next
            v_current = v_next

            # Check if we've gone around the entire contour
            if param >= contour_line.length:
                break

        return tool_path_points, exit_points