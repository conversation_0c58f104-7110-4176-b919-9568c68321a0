# Need to install shapely and matplotlib if not already installed
# pip install shapely matplotlib numpy

import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString
import math

# --- Parameters (Based on Figure 7 and assumptions where not specified) ---
# Parameters needed for tool path and boundary geometry
r = 3.0          # Tool radius (mm)

# Workpiece boundaries and tool path (Interpretation based on Fig 2 and Sec 3.1 text)
# Cur1: Inner workpiece boundary (e.g., island fillet)
O1 = (0.0, 0.0)  # Center of Cur1 (mm) - Example coordinate
R1 = 5.0         # Radius of Cur1 (mm)

# Cur2: Tool center path (as labeled in Figure 2 for milling force prediction)
# The paper says O1O2 distance is l=0.5. Let's place O2 relative to O1.
O2 = (O1[0] + 0.5, O1[1]) # Center of Cur2 (mm) - Based on |O1O2|=0.5 and O1 at origin
R2 = 5.0         # Radius of Cur2 (mm) (Radius of tool center path around O2, R2 in Fig 7 text)

# Outer workpiece boundary - Assume Cur2 offset outwards by Ar for this pass's material
Ar = 1.0         # Radial depth of cut (mm) (Assumed as increment for outer boundary)

# Simulation parameters (needed to define time steps and path points)
n = 3000         # Spindle speed (r/min) - Used for time step calculation in paper
m = 3            # Number of teeth (assumed) - Used for time step calculation in paper
f = 960          # Path feedrate (mm/min) - Used for time step calculation based on path length
p = 100          # Number of time steps per tooth period - Used for dt

# Path simulation parameters
# Simulate tool movement along an arc of the tool path Cur2
path_start_angle_deg = 0   # Start angle on Cur2 for tool center (degrees) - Relative to O2->(+x)
path_end_angle_deg = 180   # End angle on Cur2 for tool center (degrees)
path_angle_sweep_rad = np.deg2rad(path_end_angle_deg - path_start_angle_deg)

# Calculate time steps based on path length and feedrate
feedrate_path = f # Use F=960 mm/min as the path feedrate
path_length = abs(path_angle_sweep_rad) * R2 # Length of the path segment
total_time = path_length / (feedrate_path / 60.0) # Total time for this path segment (seconds)

# Time step dt based on tooth period divided by p, as per paper for force sim
# This dt is used to get a consistent time basis for angle calculation, even if not for forces.
dt = 60.0 / (n * m * p)

# Number of time steps for the entire path segment
K1 = int(total_time / dt)
if K1 == 0:
    K1 = 1 # Ensure at least one step

# Recalculate total_time and dbeta1 based on integer K1 for consistency
total_time = K1 * dt
dbeta1 = path_angle_sweep_rad / K1

# Initial path angle on Cur2
beta1s = np.deg2rad(path_start_angle_deg)


# --- Helper Functions (Kept as they are essential for geometric calculations) ---

def normalize_angle_0_to_2pi(angle):
    """Normalize angle to [0, 2pi)"""
    return angle % (2 * np.pi)

def get_angle_relative_to_point(p1, center_pt):
    """Calculate angle of p1 relative to center_pt, measured from positive x-axis, in [-pi, pi)"""
    return np.atan2(p1.y - center_pt.y, p1.x - center_pt.x)

def is_angle_in_sector(angle, phis, phie, tol=1e-9):
    """
    Check if 'angle' is within the sector [phis, phie] (inclusive),
    handling wrapping around 0/2pi. Angles are assumed normalized [0, 2pi).
    """
    angle = normalize_angle_0_to_2pi(angle)
    phis = normalize_angle_0_to_2pi(phis)
    phie = normalize_angle_0_to_2pi(phie)

    if phis <= phie:
        # Standard sector [phis, phie]
        return phis - tol <= angle <= phie + tol
    else:
        # Wrapped sector [phis, 2pi) U [0, phie]
        return angle >= phis - tol or angle <= phie + tol

def get_engagement_angles(C_pt, r, O1_pt, R1, O2_pt, R2, Ar):
    """
    Find the start and end angles (global, [0, 2pi)) of the cutting arc
    on the tool boundary for the current tool center position C.
    Returns [phis, phie] or None if no engagement.
    Assumes a single continuous engagement sector.
    """
    tool_circle_boundary = C_pt.buffer(r).boundary
    inner_boundary_line = O1_pt.buffer(R1).boundary
    # Note: The material outer boundary is based on the *tool center path* Cur2 offset by Ar
    outer_boundary_line = O2_pt.buffer(R2 + Ar).boundary

    # Find intersections with both boundaries
    intersections_inner = tool_circle_boundary.intersection(inner_boundary_line)
    intersections_outer = tool_circle_boundary.intersection(outer_boundary_line)

    # Collect all intersection points from both boundaries
    intersection_points = []
    for intersections in [intersections_inner, intersections_outer]:
        if isinstance(intersections, Point):
            intersection_points.append(intersections)
        elif hasattr(intersections, 'geoms'): # MultiPoint, LineString, GeometryCollection
            for geom in intersections.geoms:
                if isinstance(geom, Point):
                    intersection_points.append(geom)
                # Handle LineStrings? In simple circle-circle intersection, LineString means tangential contact or overlap.
                # For simplicity here, we only use point intersections to define sector boundaries.

    if not intersection_points:
        # Check if the tool is entirely inside or outside the material band (between inner and outer)
        # Pick a test point on the tool boundary, e.g., C_pt + (r, 0)
        test_point_angle = 0 # Test point at angle 0 relative to tool center
        test_x = C_pt.x + r * np.cos(test_point_angle)
        test_y = C_pt.y + r * np.sin(test_point_angle)
        test_pt = Point(test_x, test_y)

        dist_to_O1 = test_pt.distance(O1_pt)
        dist_to_O2 = test_pt.distance(O2_pt)

        is_outside_cur1 = dist_to_O1 > R1 - 1e-9
        is_inside_cur2_outer = dist_to_O2 <= R2 + Ar + 1e-9

        if is_outside_cur1 and is_inside_cur2_outer:
             # Tool is fully engaged
             return [0.0, 2 * np.pi] # Full circle engagement
        else:
             return None # No engagement

    # Get angles of intersection points relative to tool center C_pt
    angles = [normalize_angle_0_to_2pi(get_angle_relative_to_point(p, C_pt)) for p in intersection_points]
    angles = sorted(list(set(angles))) # Unique and sorted angles

    # The angles divide the tool circle into segments. We need to check which segment(s) are cutting.
    # A segment is cutting if a test point within it is in the material zone.
    # Material zone: outside Cur1 (O1, R1) and inside Cur2_outer (O2, R2+Ar).
    cutting_segments = []
    num_angles = len(angles)
    for i in range(num_angles):
        start_angle = angles[i]
        end_angle = angles[(i + 1) % num_angles] # Wrap around

        # Calculate midpoint angle of the segment
        mid_angle = (start_angle + end_angle) / 2.0
        if start_angle > end_angle: # Segment wraps around 2pi/0
             mid_angle = (start_angle + end_angle + 2 * np.pi) / 2.0

        # Get a point on the tool boundary at the midpoint angle
        mid_x = C_pt.x + r * np.cos(mid_angle)
        mid_y = C_pt.y + r * np.sin(mid_angle)
        mid_point = Point(mid_x, mid_y)

        # Check if the midpoint is inside the material band
        dist_to_O1 = mid_point.distance(O1_pt)
        dist_to_O2 = mid_point.distance(O2_pt) # Distance to tool path center

        is_outside_cur1 = dist_to_O1 > R1 - 1e-9
        is_inside_cur2_outer = dist_to_O2 <= R2 + Ar + 1e-9


        if is_outside_cur1 and is_inside_cur2_outer:
             cutting_segments.append((start_angle, end_angle))

    # Assuming a single continuous cutting segment for simplicity in this implementation
    if cutting_segments:
        # If there are multiple segments, this picks the first one.
        # A more robust implementation would merge adjacent/overlapping segments.
        phis, phie = cutting_segments[0]
        # The engagement angle is the difference (phie - phis) possibly spanning 2pi.
        return [phis, phie]
    else:
        return None # No cutting segment found


# --- Simulation ---

O1_pt = Point(O1)
O2_pt = Point(O2)

# Store engagement angle over time
time_history = []
engagement_angle_history = [] # Store total engagement angle (in degrees)

print(f"Starting simulation with {K1} time steps...")
print(f"dt: {dt:.6f} s")
print(f"dbeta1: {np.rad2deg(dbeta1):.4f} deg")


for k in range(K1):
    # Current time
    current_time = k * dt
    time_history.append(current_time)

    # Current tool center position on Cur2 (path)
    current_path_angle = beta1s + k * dbeta1
    xc = O2[0] + R2 * np.cos(current_path_angle)
    yc = O2[1] + R2 * np.sin(current_path_angle)
    C_pt = Point(xc, yc)

    # Find radial engagement angles [phis_global, phie_global] relative to global +x
    engagement_sector = get_engagement_angles(C_pt, r, O1_pt, R1, O2_pt, R2, Ar)

    if engagement_sector is None:
        # No engagement at this time step
        engagement_angle_history.append(0.0)
        continue

    phis_global, phie_global = engagement_sector

    # Calculate the span of the engagement angle.
    # If phis > phie, it means the arc wraps around 0/2pi.
    total_engagement_angle_rad = (phie_global - phis_global + 2 * np.pi) % (2 * np.pi)

    engagement_angle_history.append(np.rad2deg(total_engagement_angle_rad))

# # --- Plotting Results ---

# plt.figure(figsize=(10, 6))

# # Plot Engagement Angle (Radial)
# plt.plot(time_history, engagement_angle_history, label='Engagement Angle (Radial)')
# plt.xlabel('Time (s)')
# plt.ylabel('Engagement Angle (degrees)')
# plt.title('Radial Engagement Angle Change Over Tool Path')
# plt.legend()
# plt.grid(True)

# plt.tight_layout()
# plt.show()

# Optional: Print some results
print("\nSample Results:")
for i in range(0, K1, max(1, K1 // 10)): # Print every 10% or at least 1 step
     print(f"Time: {time_history[i]:.4f} s, Engagement Angle: {engagement_angle_history[i]:.2f} deg")