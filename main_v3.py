import bpy
import numpy as np
import networkx as nx
import shapely
from mathutils import Vector

def has_selected_objects() -> bool:
    # Get the selected objects
    selected_objects = bpy.context.selected_objects

    # Check if there are any selected objects
    if len(selected_objects) >= 1:
        return True

    print("No objects are currently selected.")
    return False


def get_ordered_selection():
    # Get the active object
    active_obj = bpy.context.active_object
    # Get the list of selected objects
    selected_objects = bpy.context.selected_objects
    # Remove the active object from the list if it exists
    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        # Insert the active object at the front of the list
        selected_objects.insert(0, active_obj)
    else:
        return []

    return selected_objects


def get_geometry():
    selected_objects = get_ordered_selection()
    active_obj = selected_objects[0] if selected_objects else None

    geometry_list = []
    if active_obj and active_obj.type == 'MESH':
        for obj in selected_objects:
            data = obj.data
            length = len(data.vertices) * 3
            geometry = np.empty(length, dtype=np.float32)
            data.vertices.foreach_get('co', geometry)
            geometry = geometry.reshape((-1, 3))
            geometry = geometry[:, [0, 1]]  # Keep only x and y coordinates
            geometry_list.append(geometry)
        return geometry_list
    else:
        print("Please select a valid mesh object.")
        return None


def geometry_to_polygon(geometry):
    if len(geometry) > 0:
        exterior = geometry[0]
        if len(geometry) > 1:
            interiors = geometry[1:]
            return shapely.geometry.Polygon(shell=exterior, holes=interiors)
        else:
            return shapely.geometry.Polygon(shell=exterior)
    else:
        return None


def geometry_to_shapely(geometry):
    contour = shapely.geometry.Polygon(shell=geometry[0])
    if len(geometry) > 1:
        islands = shapely.geometry.MultiPolygon([shapely.geometry.Polygon(shell=geom) for geom in geometry[1:]])
    else:
        islands = False
    return contour, islands


def create_ring_object(coords, name):
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords]
    n = len(vertices)
    edges = [(i, (i+1)%n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def shapely_to_blender(shapely_geom, base_name="OffsetObject", interiors=True, exteriors=True):
    """
    Creates separate Blender objects for each ring in the geometry
    Returns: List of Blender objects
    """
    objects = []

    def process_rings(geometry):
        rings = []
        if geometry.geom_type == 'Polygon':
            if exteriors:
                rings.append(geometry.exterior)
            if interiors:
                rings.extend(geometry.interiors)
        elif geometry.geom_type == 'MultiPolygon':
            for poly in geometry.geoms:
                if exteriors:
                    rings.append(poly.exterior)
                if interiors:
                    rings.extend(poly.interiors)
        return rings

    for i, ring in enumerate(process_rings(shapely_geom)):
        obj = create_ring_object(
            list(ring.coords),
            f"{base_name}_{i:02d}"
        )
        objects.append(obj)

    return objects
    

def shapely_list_to_blender(polygons, base_name="OffsetObjects"):
    """Convert a list of Shapely Polygons to Blender mesh objects.
    
    Args:
        polygons: List of Shapely Polygon geometries
        base_name: Base name for generated Blender objects
    
    Returns:
        List of bpy.data.Object references
    """
    blender_objects = []
    
    for i, poly in enumerate(polygons):
        exterior = poly.exterior.coords
        obj_name = f"{base_name}_{i:03d}"
        blender_objects.append(create_ring_object(exterior, obj_name))
    
    return blender_objects


def decompose_to_polygons(geom):    
    exteriors = []
    interiors = []
    if geom.geom_type == 'Polygon':
        exteriors.append(shapely.geometry.Polygon(geom.exterior))
        interiors.extend([shapely.geometry.Polygon(interior) for interior in geom.interiors])
    elif geom.geom_type == 'MultiPolygon':
        for poly in geom.geoms:
            exteriors.append(shapely.geometry.Polygon(poly.exterior))
            interiors.extend([shapely.geometry.Polygon(interior) for interior in poly.interiors])
    return exteriors, interiors


def sort_containment(arr, exterior_list):
    # Initialize a directed graph
    G = nx.DiGraph()

    # Add edges from the array
    for container, contained in zip(arr[0], arr[1]):
        G.add_edge(exterior_list[container], exterior_list[contained])

    # Ensure the graph is a DAG and compute transitive reduction
    if nx.is_directed_acyclic_graph(G):
        containment_graph = nx.transitive_reduction(G)
    else:
        containment_graph = G.copy()
        containment_graph.remove_edges_from(nx.find_cycle(containment_graph))

    # Create a new graph for the containment (direct edges)
    directed_containment_graph = nx.DiGraph()
    directed_containment_graph.add_edges_from(containment_graph.edges())
    return directed_containment_graph


def create_graph_containment(polygons_list):
    tree = shapely.STRtree(polygons_list)
    contain_information = tree.query(polygons_list, predicate='contains_properly')            
    return sort_containment(contain_information, polygons_list)


def post_order_traversal(graph):
    # Identify root(s): nodes with no incoming edges
    roots = [node for node in graph.nodes() if graph.in_degree(node) == 0]
    visited = set()
    traversal_order = []

    def nearest_neighbor_order(parent_centroid, children):
        """Orders children using a nearest-neighbor heuristic from the parent's centroid."""
        if not children:
            return []
        ordered = []
        unvisited = children.copy()
        current_point = parent_centroid
        while unvisited:
            # Find the child whose centroid is closest to the current point
            closest = min(
                unvisited, 
                key=lambda child: current_point.distance(child.centroid)
            )
            ordered.append(closest)
            unvisited.remove(closest)
            current_point = closest.centroid  # Move to the next child's centroid
        return ordered

    def dfs(node):
        nonlocal visited, traversal_order
        children = list(graph.successors(node))
        if children:
            parent_centroid = node.centroid
            # Order children using TSP-like nearest neighbor
            ordered_children = nearest_neighbor_order(parent_centroid, children)
            for child in ordered_children:
                if child not in visited:
                    dfs(child)
        if node not in visited:
            visited.add(node)
            traversal_order.append(node)

    for root in roots:
        if root not in visited:
            dfs(root)

    return traversal_order


def global_post_order(DG, deep_nodes_order):
    visited = set()
    result = []
    # Create a dictionary to map each node to its index in deep_nodes_order for quick lookup
    node_order = {node: idx for idx, node in enumerate(deep_nodes_order)}
    
    for node in deep_nodes_order:
        if node not in visited:
            stack = [(node, False)]
            while stack:
                current, processed = stack.pop()
                if processed:
                    if current not in visited:
                        result.append(current)
                        visited.add(current)
                else:
                    if current in visited:
                        continue
                    # Push the current node back with processed=True
                    stack.append((current, True))
                    # Retrieve children and sort them
                    children = list(DG.successors(current))
                    # Split children into those in deep_nodes_order and those not
                    in_order = [n for n in children if n in node_order]
                    not_in_order = [n for n in children if n not in node_order]
                    # Sort in_order children based on their index in deep_nodes_order
                    in_order_sorted = sorted(in_order, key=lambda x: node_order[x])
                    # Sort not_in_order children using natural order (e.g., node name)
                    # Adjust the key function based on your node's type if necessary
                    not_in_order_sorted = sorted(not_in_order, key=lambda x: str(x))
                    # Combine the two sorted lists
                    children_ordered = in_order_sorted + not_in_order_sorted
                    # Push children in reverse order to process them in the correct order
                    for child in reversed(children_ordered):
                        if child not in visited:
                            stack.append((child, False))
    return result


def traverse_dag_postorder(graph: nx.DiGraph, leaf_order: list) -> list:
    """ Traverse the DAG starting from nodes in leaf_order (expected to be leaf nodes) and traverse upward (post-order: predecessors are visited before their parent).
    
    CopyInsert
    If a node is reached from multiple leaves, it will only appear once.
    """
    visited = set()
    result = []

    def dfs(node):
        if node in visited:
            return
        # First traverse all predecessors (i.e. nodes that feed into this node)
        for parent in graph.predecessors(node):
            dfs(parent)
        visited.add(node)
        result.append(node)

    for leaf in leaf_order:
        if leaf not in graph:
            raise ValueError(f"Leaf node {leaf} not found in graph")
        dfs(leaf)
    return reversed(result)


if __name__ == "__main__":
    
    offset = 0.005
    passes = 100
    island_passes = 1
    base_name = "OffsetObject"
    buffer_attributes = {
        "join_style": 'mitre',
        "mitre_limit": 1.1
        }

    geometry = get_geometry()
    contour, islands = geometry_to_shapely(geometry)
    polygon = geometry_to_polygon(geometry)
    exterior_list = []
    interior_list = []
    print(geometry)

    '''
    
    for pass_num in range(passes):
        buffer_offset = offset * (pass_num + 1)
        buffer = polygon.buffer(-buffer_offset, **buffer_attributes)
        if buffer.is_empty:
            print(f'{pass_num} passes completed')
            break

        exteriors, interiors = decompose_to_polygons(buffer)
        exterior_list.extend(exteriors)
        interior_list.extend(interiors)

    contour_graph = create_graph_containment(exterior_list)

    #Find deepest nodes
    deepest_nodes = [node for node in contour_graph.nodes if contour_graph.out_degree(node) == 0]    
    #Get centroids
    centroids = [node.centroid for node in deepest_nodes]    
    # Convert Shapely points to coordinate tuples
    coords = [(c.x, c.y) for c in centroids]

    # Create a complete graph with Euclidean distances as edge weights
    tsp_graph = nx.Graph()
    for i, (x1, y1) in enumerate(coords):
        for j, (x2, y2) in enumerate(coords[i+1:], start=i+1):
            distance = shapely.geometry.Point(x1, y1).distance(shapely.geometry.Point(x2, y2))  # Shapely distance
            tsp_graph.add_edge(i, j, weight=distance)    

    # Solve TSP using NetworkX's Christofides approximation
    tsp_route = nx.approximation.traveling_salesman_problem(tsp_graph, cycle=False)    
    tsp_route = [deepest_nodes[i] for i in tsp_route]
    # shapely_list_to_blender(reversed(tsp_route), "zpost")
    # shapely_list_to_blender(exterior_list, "t6t")
    # if islands:       
    #     islands_graph = create_graph_containment(interior_list)
    
    # post_order_contour = post_order_traversal(contour_graph)
    # # post_order_islands = post_order_traversal(islands_graph)
    # post_order = traverse_dag_postorder(contour_graph, tsp_route)
    # shapely_list_to_blender(post_order, "order")
    '''
