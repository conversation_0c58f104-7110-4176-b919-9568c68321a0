import numpy as np
from shapely.geometry import LineString, Point
from shapely import affinity, ops
import matplotlib.pyplot as plt

def parse_linestring_data(data_str):
    """Parses the string representation of coordinates into a list of tuples."""
    lines = data_str.strip().replace('[', '').replace(']', '').split('\n')
    coords = []
    for line in lines:
        if line.strip(): # Ensure line is not empty
            parts = line.split()
            coords.append((float(parts[0]), float(parts[1])))
    return coords

# Provided linestring data
data1_str = """
[[ -6744.70751953 -11817.34375   ]
 [ -6738.84423828 -11817.19921875]
 [ -6732.99560547 -11816.76855469]
 [ -6727.17480469 -11816.05078125]
 [ -6721.39648438 -11815.04785156]
 [ -6715.67382812 -11813.76269531]
 [ -6710.02148438 -11812.19824219]
 [ -6704.45263672 -11810.35839844]
 [ -6698.98095703 -11808.24804688]
 [ -6693.61914062 -11805.87109375]
 [ -6688.38085938 -11803.234375  ]
 [ -6683.27783203 -11800.34375   ]
 [ -6678.32275391 -11797.20605469]
 [ -6673.52783203 -11793.82910156]
 [ -6668.90429688 -11790.22070312]
 [ -6664.46337891 -11786.38964844]
 [ -6660.21582031 -11782.34570312]
 [ -6656.171875   -11778.09863281] # Corrected suspected typo from -656...
 [ -6652.34130859 -11773.65722656]
 [ -6648.73291016 -11769.03417969]
 [ -6645.35595703 -11764.23925781]
 [ -6642.21826172 -11759.28417969]
 [ -6639.32763672 -11754.18164062]
 [ -6636.69042969 -11748.94238281]
 [ -6634.31396484 -11743.58105469]
 [ -6632.203125   -11738.109375  ]
 [ -6630.36376953 -11732.54003906]
 [ -6628.79931641 -11726.88769531]
 [ -6627.51416016 -11721.16601562]
 [ -6626.51171875 -11715.38671875]
 [ -6625.79394531 -11709.56640625]
 [ -6625.36230469 -11703.71777344]
 [ -6625.21826172 -11697.85449219]
 [ -6625.36230469 -11691.99121094]
 [ -6625.79394531 -11686.14257812]
 [ -6626.51171875 -11680.32226562]
 [ -6627.51416016 -11674.54296875]
 [ -6628.79931641 -11668.82128906]
 [ -6630.36376953 -11663.16894531]]
"""

data2_str = """
[[ -6743.8984375  -11784.84375   ]
 [ -6735.81542969 -11784.4140625 ]
 [ -6727.58642578 -11783.14648438]
 [ -6719.28955078 -11781.05273438]
 [ -6711.00585938 -11778.15332031]
 [ -6702.81494141 -11774.4765625 ]
 [ -6694.79492188 -11770.05761719]
 [ -6687.0234375  -11764.93847656]
 [ -6679.57568359 -11759.16992188]
 [ -6672.52294922 -11752.80566406]
 [ -6665.93310547 -11745.90820312]
 [ -6659.87011719 -11738.54296875]
 [ -6654.39208984 -11730.78222656]
 [ -6649.55175781 -11722.70019531]
 [ -6645.39550781 -11714.37402344]
 [ -6641.96386719 -11705.88476562]
 [ -6639.2890625  -11697.31347656]
 [ -6637.39794922 -11688.74316406]
 [ -6636.30810547 -11680.25683594]
 [ -6636.02978516 -11671.93457031]
 [ -6636.56640625 -11663.85839844]]
"""

coords1 = parse_linestring_data(data1_str)
coords2 = parse_linestring_data(data2_str)

line1_orig = LineString(coords1)
line2_orig = LineString(coords2)

# --- Step 1: Orient line2_orig to match line1_orig's general direction ---
p1_start = np.array(line1_orig.coords[0])
p1_end = np.array(line1_orig.coords[-1])
dir1 = p1_end - p1_start
angle1_rad = np.arctan2(dir1[1], dir1[0]) if np.linalg.norm(dir1) > 0 else 0

p2_orig_start = np.array(line2_orig.coords[0])
p2_orig_end = np.array(line2_orig.coords[-1])
dir2_orig = p2_orig_end - p2_orig_start
angle2_orig_rad = np.arctan2(dir2_orig[1], dir2_orig[0]) if np.linalg.norm(dir2_orig) > 0 else 0

rotation_angle_rad = angle1_rad - angle2_orig_rad
rotation_angle_deg = np.degrees(rotation_angle_rad)

line2_oriented = affinity.rotate(line2_orig, rotation_angle_deg, origin='centroid')

# --- Step 2: Ensure initial separation (if line2_oriented intersects line1_orig) ---
current_line_to_move = line2_oriented
if line1_orig.intersects(current_line_to_move):
    print("Warning: Initial oriented line intersects line1. Attempting to separate.")
    c1 = np.array(line1_orig.centroid.coords[0])
    c2_oriented_centroid = np.array(current_line_to_move.centroid.coords[0])
    sep_vec = c2_oriented_centroid - c1 # Vector from c1 to c2_oriented
    
    if np.linalg.norm(sep_vec) < 1e-6: # Centroids are too close, pick arbitrary direction
        sep_vec = np.array([line1_orig.length * 0.1, 0.0]) # Arbitrary separation vector
    
    sep_vec_norm = sep_vec / np.linalg.norm(sep_vec)
    
    # Heuristic step size for separation
    # A small fraction of line length or a fixed small unit
    min_length = min(line1_orig.length, current_line_to_move.length) if current_line_to_move.length >0 else line1_orig.length
    dist_step = min_length / 100.0 if min_length > 0 else 1.0 
    if dist_step == 0: dist_step = 1.0 # Safety for zero length lines

    temp_line = current_line_to_move
    separated = False
    for _ in range(200): # Max separation steps
        if not line1_orig.intersects(temp_line):
            separated = True
            break
        # Move further away
        temp_line = affinity.translate(temp_line, xoff=sep_vec_norm[0]*dist_step, yoff=sep_vec_norm[1]*dist_step)
    
    if not separated:
        print("Error: Could not separate the lines after orientation. Result may be invalid.")
        # Proceeding with potentially intersecting line, which might lead to issues.
        # A more robust solution might try alternative separation or stop.
    current_line_to_move = temp_line


# --- Step 3: Determine "Snap" Translation ---
# `current_line_to_move` is now the (hopefully) disjoint, oriented line.
nearest_p_on_l1, nearest_p_on_l2_current = ops.nearest_points(line1_orig, current_line_to_move)

snap_dx = nearest_p_on_l1.x - nearest_p_on_l2_current.x
snap_dy = nearest_p_on_l1.y - nearest_p_on_l2_current.y

# --- Step 4: Apply Snap and Check ---
line_after_full_snap = affinity.translate(current_line_to_move, xoff=snap_dx, yoff=snap_dy)

final_line2 = None

if not line1_orig.intersects(line_after_full_snap):
    final_line2 = line_after_full_snap
    print("Full snap successful, no new intersections detected.")
else:
    # --- Step 5: Binary Search for Optimal Translation ---
    print("Full snap caused intersection. Using binary search to pull back.")
    low_s = 0.0  # Represents current_line_to_move (0% of snap vector)
    high_s = 1.0 # Represents line_after_full_snap (100% of snap vector)
    
    # `best_non_intersecting_line` must be a line known not to intersect.
    # `current_line_to_move` should be non-intersecting due to Step 2.
    if line1_orig.intersects(current_line_to_move):
        print("Critical Warning: 'current_line_to_move' for binary search is intersecting line1.")
        print("This indicates the separation step (Step 2) failed or was insufficient.")
        print("Binary search results might be unreliable.")
        # Initialize with current_line_to_move despite intersection, or handle error more strictly.
        best_non_intersecting_line = LineString(current_line_to_move.coords) # Make a copy
    else:
        best_non_intersecting_line = LineString(current_line_to_move.coords) # Make a copy

    N_ITERATIONS = 30 # Binary search iterations
    for i in range(N_ITERATIONS):
        mid_s = (low_s + high_s) / 2.0
        current_translation_dx = mid_s * snap_dx
        current_translation_dy = mid_s * snap_dy
        
        test_line = affinity.translate(current_line_to_move, xoff=current_translation_dx, yoff=current_translation_dy)
        
        if line1_orig.intersects(test_line):
            high_s = mid_s  # This translation scale is too large
        else:
            low_s = mid_s   # This scale is valid, try to get closer
            best_non_intersecting_line = test_line 
    final_line2 = best_non_intersecting_line

# # --- Plotting ---
# fig, ax = plt.subplots(1, 2, figsize=(15, 7), sharex=True, sharey=True)

# # Plot 1: Original and Intermediate
# ax[0].plot(*line1_orig.xy, label='Line 1 (Fixed)', marker='o', markersize=2, color='blue', zorder=1)
# ax[0].plot(*line2_orig.xy, label='Line 2 (Original)', marker='x', markersize=2, color='red', zorder=2)
# ax[0].plot(*line2_oriented.xy, label='Line 2 (Oriented)', marker='.', markersize=2, color='orange', linestyle=':', zorder=3)
# if current_line_to_move != line2_oriented : # if separation happened
#     ax[0].plot(*current_line_to_move.xy, label='Line 2 (Post-Separation)', marker='.', markersize=2, color='magenta', linestyle='--', zorder=4)
# ax[0].set_title('Original & Intermediate Lines')
# ax[0].set_xlabel('X')
# ax[0].set_ylabel('Y')
# ax[0].legend()
# ax[0].axis('equal')
# ax[0].grid(True)

# # Plot 2: Final Transformed
# ax[1].plot(*line1_orig.xy, label='Line 1 (Fixed)', marker='o', markersize=2, color='blue', zorder=1)
# if final_line2:
#     ax[1].plot(*final_line2.xy, label='Line 2 (Moved to Touch)', marker='x', markersize=2, color='green', zorder=2)
    
#     p_final_l1, p_final_l2 = ops.nearest_points(line1_orig, final_line2)
#     ax[1].plot(p_final_l1.x, p_final_l1.y, 'bo', markersize=6, label="Nearest Pt on L1", zorder=3)
#     ax[1].plot(p_final_l2.x, p_final_l2.y, 'gx', markersize=6, markeredgewidth=2, label="Nearest Pt on L2 (Final)", zorder=3)
    
#     dist_final = line1_orig.distance(final_line2)
#     touch_final = line1_orig.touches(final_line2)
#     intersect_final = line1_orig.intersects(final_line2)
#     ax[1].set_title(f'Final Position\nDist: {dist_final:.2e}, Touches: {touch_final}, Intersects: {intersect_final}')
# else:
#     ax[1].set_title('Final Position (Error in processing)')

# ax[1].set_xlabel('X')
# # ax[1].set_ylabel('Y') # Y-label is shared
# ax[1].legend()
# ax[1].axis('equal')
# ax[1].grid(True)

# plt.tight_layout()
# plt.show()

if final_line2:
    print(f"Final distance: {line1_orig.distance(final_line2)}")
    print(f"Final touches: {line1_orig.touches(final_line2)}")
    print(f"Final intersects: {line1_orig.intersects(final_line2)}")

    print(final_line2)