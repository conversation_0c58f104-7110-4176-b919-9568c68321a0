import numpy as np
import shapely.geometry as sg
from shapely.affinity import scale, translate
import matplotlib.pyplot as plt

# --- Helper Functions (to make the code runnable) ---

def get_ellipse_at_position(position: float, outer_ellipse_data: dict, inner_ellipse_data: dict) -> sg.Polygon:
    """
    Calculates the position of an inner ellipse moving along the perimeter of an outer ellipse.

    Args:
        position (float): A value from 0.0 to 1.0 representing the location on the outer ellipse's perimeter.
        outer_ellipse_data (dict): Defines the "track" ellipse.
        inner_ellipse_data (dict): Defines the ellipse we are placing.

    Returns:
        shapely.geometry.Polygon: The resulting inner ellipse.
    """
    # Map position (0-1) to an angle (0-2*pi)
    angle = position * 2 * np.pi

    # Get center of outer ellipse
    cx_outer, cy_outer = outer_ellipse_data['center']
    
    # Parametric equation for the outer ellipse to find the center of our inner ellipse
    inner_cx = cx_outer + outer_ellipse_data['major'] * np.cos(angle)
    inner_cy = cy_outer + outer_ellipse_data['minor'] * np.sin(angle)

    # Create the inner ellipse at the calculated center
    # 1. Create a unit circle at the origin
    circle = sg.Point(0, 0).buffer(1)
    # 2. Scale it to the desired ellipse dimensions
    ellipse = scale(circle, xfact=inner_ellipse_data['major'], yfact=inner_ellipse_data['minor'])
    # 3. Move it to the final position
    ellipse = translate(ellipse, xoff=inner_cx, yoff=inner_cy)
    
    return ellipse

def get_ellipse_distances(ellipse: sg.Polygon, sides: list) -> tuple[float, float]:
    """Calculates the minimum distance from the ellipse to each of the two side linestrings."""
    dist_left = ellipse.distance(sides[0])
    dist_right = ellipse.distance(sides[1])
    return (dist_left, dist_right)

def plot_scenario(sides, initial_ellipse, final_ellipse, final_position):
    """Visualizes the initial state and the final, centered result."""
    fig, ax = plt.subplots(figsize=(10, 8))
    for side in sides:
        x, y = side.xy
        ax.plot(x, y, 'k-', linewidth=2, label='Sides')
    
    if initial_ellipse:
        x_init, y_init = initial_ellipse.exterior.xy
        ax.plot(x_init, y_init, 'r--', label='Initial Ellipse (pos=0.0)')

    if final_ellipse:
        x_final, y_final = final_ellipse.exterior.xy
        ax.plot(x_final, y_final, 'g-', linewidth=2, label=f'Final Ellipse (pos={final_position:.4f})')

    # Remove duplicate labels
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    ax.legend(by_label.values(), by_label.keys())
    
    ax.set_title("Ellipse Centering Result")
    ax.set_aspect('equal', adjustable='box')
    ax.grid(True, linestyle=':')
    plt.show()


# --- Main Centering Logic ---

def center_ellipse(sides, outer_ellipse_data, inner_ellipse_data, tolerance, max_iterations):
    """
    Finds the position for an ellipse to be centered between two LineStrings.

    This function uses the Secant Method to efficiently find the root of the
    distance difference function, converging quickly on the solution.
    """
    print("--- Starting Ellipse Centering ---")
    
    # 1. INITIAL STATE (Iteration 0)
    # Start at position 0
    pos_prev = 0.0
    ellipse_prev = get_ellipse_at_position(pos_prev, outer_ellipse_data, inner_ellipse_data)
    distances_prev = get_ellipse_distances(ellipse_prev, sides)
    diff_prev = distances_prev[0] - distances_prev[1] # left - right
    
    print(f"Iter 0: pos={pos_prev:.4f}, dists=({distances_prev[0]:.4f}, {distances_prev[1]:.4f}), diff={diff_prev:+.4f}")

    if abs(diff_prev) <= tolerance:
        print("Ellipse is already centered.")
        return ellipse_prev, pos_prev

    # 2. FIRST GUESS (Iteration 1)
    # Use a simple proportional move for the first step to establish a second point for the Secant Method
    # Normalize the move distance by the perimeter to get a position delta.
    move_amount = abs(diff_prev) / outer_ellipse_data['perimeter']
    # If diff is positive (left is further), move in positive direction, else negative.
    move_direction = 1.0 if diff_prev > 0 else -1.0
    # Use modulo to handle circular space (e.g., -0.1 becomes 0.9)
    pos_current = (pos_prev + move_direction * move_amount) % 1.0
    
    ellipse_current = get_ellipse_at_position(pos_current, outer_ellipse_data, inner_ellipse_data)
    distances_current = get_ellipse_distances(ellipse_current, sides)
    diff_current = distances_current[0] - distances_current[1]
    
    print(f"Iter 1: pos={pos_current:.4f}, dists=({distances_current[0]:.4f}, {distances_current[1]:.4f}), diff={diff_current:+.4f}")
    
    # 3. ITERATIVE REFINEMENT (Secant Method)
    iteration = 2
    while abs(diff_current) > tolerance and iteration <= max_iterations:
        # Safety check to prevent division by zero if the difference doesn't change
        if (diff_current - diff_prev) == 0:
            print("Warning: Difference is not changing. Stopping to avoid division by zero.")
            break
            
        # SECANT METHOD FORMULA to find the next position guess
        # This is our highly effective 'move_correction_factor'
        # It predicts where the difference function will be zero (the root).
        pos_next = pos_current - diff_current * (pos_current - pos_prev) / (diff_current - diff_prev)
        
        # Keep position in the circular [0, 1) range
        pos_next = pos_next % 1.0

        # Update previous and current states for the next iteration
        pos_prev, diff_prev = pos_current, diff_current
        pos_current = pos_next
        
        ellipse_current = get_ellipse_at_position(pos_current, outer_ellipse_data, inner_ellipse_data)
        distances_current = get_ellipse_distances(ellipse_current, sides)
        diff_current = distances_current[0] - distances_current[1]

        print(f"Iter {iteration}: pos={pos_current:.4f}, dists=({distances_current[0]:.4f}, {distances_current[1]:.4f}), diff={diff_current:+.4f}")
        iteration += 1

    # 4. FINAL RESULT
    if abs(diff_current) <= tolerance:
        print(f"\nSuccess! Centered within tolerance in {iteration-1} iterations.")
    else:
        print(f"\nFailed to converge after {max_iterations} iterations.")
        
    print(f"Final Position: {pos_current:.6f}")
    print(f"Final Distances (L/R): ({distances_current[0]:.6f}, {distances_current[1]:.6f})")
    print(f"Final Difference: {diff_current:.6f}")
    
    return ellipse_current, pos_current


if __name__ == '__main__':
    # --- Configuration ---
    TOLERANCE = 0.01
    MAX_ITERATIONS = 4

    # The "track" the small ellipse moves on (a circle for simplicity)
    OUTER_ELLIPSE_DATA = {
        'major': 10,
        'minor': 10,
        'center': (0, 0),
        'perimeter': 2 * np.pi * 10
    }
    
    # The ellipse we are trying to center
    INNER_ELLIPSE_DATA = {
        'major': 3,
        'minor': 1.5
    }

    # Two linestrings that form the "sides" or "walls"
    SIDES = [
        sg.LineString([(-15, -20), (-15, 20)]),  # Left side
        sg.LineString([(15, -20), (15, 20)])     # Right side
    ]
    
    # Get the initial ellipse at position 0 for visualization
    initial_ellipse = get_ellipse_at_position(0.0, OUTER_ELLIPSE_DATA, INNER_ELLIPSE_DATA)

    # Run the centering algorithm
    final_ellipse, final_position = center_ellipse(
        sides=SIDES,
        outer_ellipse_data=OUTER_ELLIPSE_DATA,
        inner_ellipse_data=INNER_ELLIPSE_DATA,
        tolerance=TOLERANCE,
        max_iterations=MAX_ITERATIONS
    )

    # Plot the results
    plot_scenario(SIDES, initial_ellipse, final_ellipse, final_position)