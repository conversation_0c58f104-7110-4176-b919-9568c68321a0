import numpy as np

def _segment_vectors(poly):
    """
    Return the vectors of each segment of a polyline.
    poly shape: (N, 2)
    """
    return poly[1:] - poly[:-1]                     # (M, 2)

def _right_normals(seg_vec):
    """
    Right‑hand unit normals of each segment.
    Rotate clockwise by 90° and normalise.
    """
    # rotate (dx, dy) -> (dy, -dx)  (clockwise 90°)
    rot = np.stack([ seg_vec[:, 1], -seg_vec[:, 0] ], axis=1)
    norm = np.linalg.norm(rot, axis=1, keepdims=True)
    # avoid division by zero for degenerate segments
    norm[norm == 0] = 1.0
    return rot / norm                               # (M, 2)

def _point_to_segment_distances(points, seg_start, seg_end):
    """
    Vectorised point‑to‑segment distance.
    points : (K, 2)
    seg_start, seg_end : (M, 2)
    Returns: (K, M) array of distances.
    """
    # vectors
    v = seg_end - seg_start               # (M, 2)
    w = points[:, None, :] - seg_start[None, :, :]    # (K, M, 2)

    # projection factor t = (w·v) / (v·v)  (clamped to [0,1])
    vv = np.einsum('ij,ij->i', v, v)      # (M,)
    t = np.einsum('kmi,mi->km', w, v) / vv   # (K, M)
    t = np.clip(t, 0.0, 1.0)               # (K, M)

    # closest point on the segment
    proj = seg_start[None, :, :] + t[..., None] * v[None, :, :]   # (K, M, 2)

    # Euclidean distance
    diff = points[:, None, :] - proj      # (K, M, 2)
    return np.linalg.norm(diff, axis=2)   # (K, M)

def max_right_hausdorff(a0, a1):
    """
    Directed Hausdorff distance from a0 to a1,
    considering only the part of a1 that lies on the *right* side
    of its oriented polyline.

    Parameters
    ----------
    a0 : np.ndarray, shape (K, 2)
        Points of the first arc.
    a1 : np.ndarray, shape (M+1, 2)
        Ordered vertices of the second arc.

    Returns
    -------
    float
        The farthest distance of any point of a0 to the closest
        point/edge of a1 that is on its right side.
        If no point of a0 lies on the right side of any segment,
        ``np.nan`` is returned.
    """
    a0 = np.asarray(a0, dtype=float)
    a1 = np.asarray(a1, dtype=float)

    if a0.ndim != 2 or a0.shape[1] != 2:
        raise ValueError("a0 must be of shape (K,2)")
    if a1.ndim != 2 or a1.shape[1] != 2:
        raise ValueError("a1 must be of shape (M+1,2)")

    # ---- 1. Build segment data for a1 ---------------------------------
    seg_start = a1[:-1]                     # (M, 2)
    seg_end   = a1[1:]                      # (M, 2)
    seg_vec   = _segment_vectors(a1)        # (M, 2)
    right_n   = _right_normals(seg_vec)     # (M, 2)

    # ---- 2. Signed distance to the right‑hand normal --------------------
    # For each point p and each segment i compute (p - vi)·n_i
    # Positive → point is on the right side of that segment.
    # shape (K, M)
    signed = np.einsum('ki,mi->km', a0, right_n) - np.einsum('mi,mi->m', seg_start, right_n)

    # ---- 3. Point‑to‑segment Euclidean distances ------------------------
    dists = _point_to_segment_distances(a0, seg_start, seg_end)   # (K, M)

    # ---- 4. Keep only distances where the point is on the right side ----
    # Mask: True where signed > 0
    mask = signed > 0

    # If a point never satisfies the mask we set its distance to +inf
    # so that it will never be selected as the minimum.
    dists_masked = np.where(mask, dists, np.inf)   # (K, M)

    # ---- 5. Minimum distance for each point (closest right‑hand part) --
    min_per_point = np.min(dists_masked, axis=1)   # (K,)

    # Points that never had a right‑hand neighbour end up with +inf.
    # Remove them from the final max.
    finite = np.isfinite(min_per_point)
    if not np.any(finite):
        # No point of a0 lies on the right side of any segment of a1
        return np.nan

    # ---- 6. Directed Hausdorff distance (



# ----------------------------------------------------------------------
# Example usage
# ----------------------------------------------------------------------
if __name__ == "__main__":
    # a simple quarter‑circle (arc) sampled with 30 points
    t0 = np.linspace(0, np.pi/2, 30)
    a0 = np.column_stack([np.cos(t0), np.sin(t0)])          # arc 0

    # a larger quarter‑circle, shifted a bit to the right
    t1 = np.linspace(0, np.pi/2, 50)
    a1 = np.column_stack([1.5 + 0.8*np.cos(t1), 0.8*np.sin(t1)])  # arc 1

    # Compute the directed Hausdorff distance from a0 to the right side of a1
    d = max_right_hausdorff(a0, a1)
    print(f"Farthest right‑hand distance = {d}")