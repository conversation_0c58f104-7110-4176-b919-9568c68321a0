import scipy.special
import bpy
from mathutils import Vector
import numpy as np
import trimesh
import shapely.geometry
from pyclothoids import Clothoid, SolveG2
import pyclothoids
import time
from shapely.geometry import Polygon, Point, LineString
from shapely.strtree import STRtree
import math
import itertools
from shapely import affinity, ops, line_merge, line_locate_point, line_interpolate_point
from shapely import Point, LineString, Polygon, MultiLineString, GeometryCollection, MultiPoint
import time
import scipy.optimize


def counted(func):
    def wrapper(*args, **kwargs):
        wrapper.calls += 1
        return func(*args, **kwargs)
    wrapper.calls = 0  # Initialize the counter
    return wrapper

def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def create_ring_object(coords: list[tuple[float, float]], name: str) -> bpy.types.Object:
    """Create a ring object in Blender from coordinates."""
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)

    vertices = [Vector((x, y, 0)) for x, y in coords[:-1]]
    n = len(vertices)
    edges = [(i, (i + 1) % n) for i in range(n)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()
    return obj


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def expand_by_x(indices, x, total_elements):
  """
  Expands a sequence of circular indices by x elements at the beginning
  and x elements at the end.

  Args:
    indices: A NumPy array or list of integers representing the indices.
    x: The number of elements to expand by on each side (must be non-negative).
    total_elements: The total number of elements in the circle (default is 72).

  Returns:
    A NumPy array with the expanded sequence of indices.
  """
  if not isinstance(indices, np.ndarray):
    indices = np.array(indices)

  if indices.size == 0 or x < 0:
    return indices

  if x == 0:
    return indices

  # Calculate preceding and succeeding indices
  first = indices[0]
  last = indices[-1]

  # Create ranges and handle wrapping with modulo
  preceding = (first - np.arange(x, 0, -1)) % total_elements
  succeeding = (last + np.arange(1, x + 1)) % total_elements

  # Combine all indices
  return np.concatenate((preceding, indices, succeeding))


def max_front_engagement(sample_distances, radii, tool_radius, num_theta=36):
    """
    Compute maximum engagement angles for all pairs of consecutive circles using vectorized operations.

    Parameters:
    - sample_distances: Array of cumulative distances (shape: (N+1,))
    - radii: Array of circle radii (shape: (N+1,))
    - tool_radius: Radius of the tool (scalar)
    - num_theta: Number of angles to sample for each circle (default: 100)

    Returns:
    - Array of maximum engagement angles in radians (shape: (N,))
    """
    # Number of segments
    N = len(radii) - 1

    # Generate angle array for sampling
    theta = np.linspace(0, 2 * np.pi, num_theta, endpoint=False)

    # Compute segment lengths and radii for consecutive pairs
    # l = sample_distances[1:] - sample_distances[:-1]  # shape: (N,)
    l = sample_distances[1:]
    r1 = np.full_like(l, radii[0])  # shape: (N,)
    r2 = radii[1:]   # shape: (N,)

    # Offset radii by tool radius
    R1 = r1 + tool_radius  # shape: (N,)
    R2 = r2 + tool_radius  # shape: (N,)

    # Tool center coordinates (C) for all pairs and angles
    C_x = l[:, None] + r2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    C_y = r2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    C = np.stack((C_x, C_y), axis=-1)              # shape: (N, num_theta, 2)

    # Tangent points (N) on offset second circle
    N_x = l[:, None] + R2[:, None] * np.cos(theta)  # shape: (N, num_theta)
    N_y = R2[:, None] * np.sin(theta)               # shape: (N, num_theta)
    N = np.stack((N_x, N_y), axis=-1)              # shape: (N, num_theta, 2)

    # First circle center (O1) at origin for all pairs
    num_segments = len(r1)  # Use integer value instead of N array
    O1 = np.zeros((num_segments, 1, 2))  # shape: (num_segments, 1, 2)

    # Distance from O1 to C
    d = np.sqrt(np.sum((C - O1)**2, axis=-1))  # shape: (N, num_theta)

    # Intersection condition
    mask = (d >= np.abs(R1[:, None] - tool_radius)) & (d <= (R1[:, None] + tool_radius))

    # Compute gamma (angle for intersection points)
    gamma = np.arccos(np.clip((d**2 + R1[:, None]**2 - tool_radius**2) / (2 * d * R1[:, None]), -1.0, 1.0))
    gamma = np.nan_to_num(gamma, nan=0.0)  # shape: (N, num_theta)

    # Unit vector from O1 to C
    u = (C - O1) / d[..., None]  # shape: (N, num_theta, 2)
    u = np.nan_to_num(u, nan=0.0)

    # Perpendicular vector (rotated 90 degrees)
    v = np.stack((-u[..., 1], u[..., 0]), axis=-1)  # shape: (N, num_theta, 2)

    # Intersection points D1 and D2
    cos_gamma = np.cos(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    sin_gamma = np.sin(gamma)[:, :, None]  # shape: (N, num_theta, 1)
    D1 = O1 + R1[:, None, None] * (u * cos_gamma + v * sin_gamma)  # shape: (N, num_theta, 2)
    D2 = O1 + R1[:, None, None] * (u * cos_gamma - v * sin_gamma)  # shape: (N, num_theta, 2)

    # Distances from N to D1 and D2
    DN1 = np.sqrt(np.sum((D1 - N)**2, axis=-1))  # shape: (N, num_theta)
    DN2 = np.sqrt(np.sum((D2 - N)**2, axis=-1))  # shape: (N, num_theta)

    # Engagement angles
    alpha1 = np.arccos(np.clip(1 - (DN1**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)
    alpha2 = np.arccos(np.clip(1 - (DN2**2) / (2 * tool_radius**2), -1.0, 1.0))  # shape: (N, num_theta)

    # Apply mask and compute maximum
    alpha1 = np.where(mask, alpha1, 0.0)
    alpha2 = np.where(mask, alpha2, 0.0)
    max_alpha = np.max(np.maximum(alpha1, alpha2), axis=1)  # shape: (N,)

    return max_alpha


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def angle_engagement(next_center: np.ndarray, prev_center: np.ndarray, r: float, stock_poly: Polygon, arc_resolution=12) -> float | None:
    if not isinstance(stock_poly, (Polygon, shapely.geometry.MultiPolygon)) or not stock_poly.is_valid: return None
    if np.array_equal(next_center, prev_center): return 0.0
    vec = [next_center[0] - prev_center[0], next_center[1] - prev_center[1]]
    vec_mag = math.sqrt(vec[0]**2 + vec[1]**2)
    if vec_mag < 1e-9: return 0.0
    movement_angle = np.arctan2(vec[1], vec[0])
    start_angle = movement_angle + np.pi / 2
    end_angle = movement_angle - np.pi / 2
    arc_points = [(next_center[0] + r * np.cos(t), next_center[1] + r * np.sin(t))
                  for t in np.linspace(end_angle, start_angle, arc_resolution)]
    if len(arc_points) < 2: return None
    arc = shapely.LineString(arc_points)
    if not arc.is_valid: return None
    try:
        intersection_geom = shapely.intersection(arc, stock_poly)
        if not intersection_geom or intersection_geom.is_empty: return None
        total_length = 0.0
        if intersection_geom.geom_type == "LineString": total_length = intersection_geom.length
        elif intersection_geom.geom_type == "MultiLineString": total_length = sum(line.length for line in intersection_geom.geoms)
        elif intersection_geom.geom_type == "GeometryCollection":
             for geom in intersection_geom.geoms:
                  if geom.geom_type == "LineString": total_length += geom.length
                  elif geom.geom_type == "MultiLineString": total_length += sum(line.length for line in geom.geoms)
        engagement_angle = total_length / r
        return max(0.0, min(engagement_angle, np.pi))
    except Exception: return None


def advancing_front_ccwe(path, polygon, R, CWE, samples_per_milimeter = 10.0, min_radius=5):
    """Generate advancing front points along path with Cconstant cutter work-piece engagement."""
    path = np.asarray(path)
    if path.shape[0] <= 1:
        return np.array([]), np.array([])

    # Sample path
    sampler = trimesh.path.traversal.PathSample(path)
    sample_step = 1.0 / samples_per_milimeter

    sample_distances = np.arange(0, sampler.length + sample_step / 2.0, sample_step)
    points = sampler.sample(sample_distances)
    radii_full = boundary_distance(polygon, points)
    radii = radii_full

    # Find first index where radius drops below min_radius
    min_radius_idx = np.argmax(radii < min_radius) if np.any(radii < min_radius) else None
    if min_radius_idx is not None and min_radius_idx > 0:
        # We found a point where radius drops below min_radius
        sample_distances = sample_distances[:min_radius_idx]
        radii = radii[:min_radius_idx]
        points = points[:min_radius_idx]

    closest_indices = []
    cycle_offset = 0
    distance_offset = 0

    while True:
        slice_start = cycle_offset

        # Calculate engagement for current slice
        slice_distances = sample_distances[slice_start:] - distance_offset
        # print(f'slice_distances: {slice_distances}, slice_start: {slice_start}, distance_offset: {distance_offset}')
        slice_radii = radii[slice_start:]
        mfe = max_front_engagement(sample_distances=slice_distances, radii=slice_radii, tool_radius=R)

        # Find indices where values are greater than CWE
        indices = np.where(mfe > CWE)[0]

        # Get the first index if any exist
        if len(indices) > 0:
            local_idx = indices[0]-1
        else:
            local_idx = np.argmin(np.abs(mfe - CWE))

        global_idx = slice_start + local_idx

        # Update tracking variables
        cycle_offset += local_idx
        distance_offset += slice_distances[local_idx]

        if closest_indices and closest_indices[-1] == global_idx:
            break

        closest_indices.append(global_idx)
        if np.linalg.norm(points[-1] - points[global_idx]) < (radii[-1] + radii[global_idx]+(R*2)):
            break

    # Create buffer circles for remaining points
    last_idx = closest_indices[-1]

    # Store results
    result_points = points[closest_indices]
    result_radii = radii_full[closest_indices]
    remaining_points = points[last_idx:]
    remaining_radii = radii[last_idx:]

    return result_points, result_radii, remaining_points, remaining_radii


def clearing_front_ccwe(remaining_centers, remaining_radii, main_centers, main_radii, R, CWE, samples_per_milimeter = 10.0, min_radius=5):
    """Generate advancing front points along path with Cconstant cutter work-piece engagement."""

    # Create union of all circles along remaining path
    circles = [Point(p[0], p[1]).buffer(r+R) for p, r in zip(remaining_centers, remaining_radii)]
    circles_all = shapely.ops.unary_union(circles).simplify(0.05)

    # Create special buffer circles for the start and from the end points.
    buffer_factor = 1.05
    circle_start = Point(*main_centers[-1]).buffer(main_radii[-1] + (R * buffer_factor))
    circle_end = Point(*remaining_centers[-1]).buffer(remaining_radii[-1] + (R * buffer_factor))
    circle_ends = shapely.union(circle_start, circle_end)

    # Get the difference between all circles and the end circles
    stack_rest = shapely.difference(circles_all, circle_ends)

    circle_resolution = 72
    all_arc_data = {}
    clearing_centers = []
    clearing_radii = []

    last_biggest_engagement = 0.0
    last_biggest_arc_center = np.ndarray
    last_biggest_arc_radius = 0.0
    arc_number = 0
    speedup_offset = 20

    for idx, (c, r) in enumerate(zip(remaining_centers[::speedup_offset], remaining_radii[::speedup_offset])):
        # Handle the case when the next index would be out of bounds
        next_idx = min((idx+1)*speedup_offset, len(remaining_centers)-1)
        arc_vector = [c[0] - remaining_centers[next_idx][0], c[1] - remaining_centers[next_idx][1]]
        # if geom.geom_type == 'MultiPolygon':
        #     geom = max(geom.geoms, key=lambda p: p.area)
        stack_loop = stack_rest
        angles = []
        arc_points = find_valid_arc_points(c, r, stack_loop, arc_vector, R, circle_resolution, expand_by=1)

        if arc_points.size > 0:
            for i in range(len(arc_points)-1):
                prev_point = arc_points[i]
                next_point = arc_points[i+1]
                cutter = Point(*next_point).buffer(R)
                engagement = angle_engagement(next_point, prev_point, R, stack_loop, arc_resolution=24)
                if engagement:
                    angles.append(engagement)
                    # --- Update Stock ---
                    try:
                        if stack_loop.geom_type == 'MultiPolygon':
                            new_parts = []
                            for part in stack_loop.geoms:
                                if part.intersects(cutter):
                                    diff = shapely.difference(part, cutter)
                                    if not diff.is_empty:
                                            new_parts.append(diff)
                                else:
                                    new_parts.append(part)
                            new_stock = shapely.ops.unary_union(new_parts) # Reassemble
                        else: # Single Polygon
                            new_stock = shapely.difference(stack_loop, cutter)

                        # Validate and potentially fix new stock
                        if not new_stock.is_valid:
                            new_stock = new_stock.buffer(0)
                            if not new_stock.is_valid:
                                print(f"Error: Stock remains invalid after buffer(0) at step {i+1}. Stopping.")
                                break
                        stack_loop = new_stock
                        if stack_loop.is_empty:
                            print(f"Stopping toolpath generation at step {i+1}: Stock depleted.")
                            break
                    except Exception as e:
                        print(f"Error during stock update at step {i+1}: {e}")
                        break

            if len(angles) > 0:
                loop_max_engagement = np.max(angles)

                if loop_max_engagement >= CWE:
                    stack_rest = shapely.difference(stack_rest, shapely.LineString(arc_points).buffer(R))
                    last_biggest_engagement = 0.0
                    # Store arc data
                    clearing_centers.append(c)
                    clearing_radii.append(r)

                elif loop_max_engagement > last_biggest_engagement:
                        last_biggest_engagement = loop_max_engagement
                        last_biggest_arc_center = c
                        last_biggest_arc_radius = r

        # Check if we are in the last iteration
        if idx == len(remaining_centers[::20]) - 1:
            if len(angles) > 0 and loop_max_engagement >= CWE:
                continue
            else:
                # Store arc data
                clearing_centers.append(last_biggest_arc_center)
                clearing_radii.append(last_biggest_arc_radius)

    return clearing_centers, clearing_radii


def generate_arc_points(center, radius, angle_start_rad, angle_end_rad, edge_length):
    """Generate points along an arc in counter-clockwise direction."""
    center = np.array(center)
    radius = max(1e-9, radius)

    arc_length = radius * (angle_start_rad -angle_end_rad)
    num_points = max(2, int(np.ceil(arc_length / edge_length)) + 1)
    angles = np.linspace(angle_end_rad, angle_start_rad, num_points)
    return center + radius * np.array([np.cos(angles), np.sin(angles)]).T


def calculate_arc_angles(C_current, R_current, C_next, R_next):
    """Calculate arc angles based on current and next centers/radii."""
    segment_vec = C_next - C_current
    segment_len = np.linalg.norm(segment_vec)
    angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if segment_len > 1e-9 else 0.0

    start_angle = angle_dir + np.pi / 2.0
    end_angle = angle_dir - np.pi / 2.0

    if R_next > R_current and segment_len > 1e-9:
        delta_angle = np.arcsin(np.clip((R_next - R_current) / segment_len, 0.0, 1.0))
        start_angle += delta_angle
        end_angle -= delta_angle

    return start_angle, end_angle


def find_nearby_edges(polygon: Polygon, point: Point, max_distance: float) -> list[LineString]:
    # Extract coordinates and create edges
    coords = list(polygon.exterior.coords)
    edges = [LineString([coords[i], coords[i + 1]])
            for i in range(len(coords) - 1)]

    # Create spatial index
    tree = STRtree(edges)

    # Create a search buffer around the point
    search_area = point.buffer(max_distance)

    # Query the spatial index for potential edges
    potential_edges = tree.query(search_area)

    # Final precise distance check
    nearby_edges = [
        edges[idx] for idx in potential_edges
        if edges[idx].distance(point) <= max_distance
    ]

    return nearby_edges


def create_circle_points(center, radius, start_angle, resolution=72, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def find_valid_arc_points(center, radius, stock_poly, vector, tool_radius, circle_resolution=72, expand_by=1):
    """Find valid points on a circle that satisfy distance and containment criteria.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius
        stock_poly: Stock polygon to check against
        tool_radius: Tool radius for distance check
        circle_resolution: Number of points to sample on the circle

    Returns:
        Array of valid points on the circle
        start_angle: Starting angle of the valid arc in radians
        end_angle: Ending angle of the valid arc in radians
    """
    # Create circle points
    circle_points = create_circle_points(center, radius, vector, circle_resolution)

    # Check distance and containment
    p_dist = boundary_distance(stock_poly, circle_points)

    # Find points that satisfy both criteria
    if np.any(p_dist < tool_radius) and p_dist.size > 1:
        # Create a continuous range of indices from the first to the last index where p_dist < tool_radius
        # This ensures we get a complete arc segment rather than potentially disconnected points
        valid_indices = np.arange(np.min(np.where(p_dist < tool_radius)[0]), np.max(np.where(p_dist < tool_radius)[0]) + 1)
        valid_indices = expand_by_x(valid_indices, expand_by, circle_resolution)

        return circle_points[valid_indices]
    else:
        return np.array([])


def process_arcs_and_transitions(main_centers, main_radii, edge_length):
    """Process arcs and transitions to generate path segments and arc data.

    Args:
        main_centers: List of center points for arcs
        main_radii: List of radii for arcs
        edge_length: Length of elementary edge in the geometry

    Returns:
        Tuple containing arc points and arc data dictionary
    """
    all_arc_data = {}

    # Process arcs and transitions
    for i in range(len(main_centers)):
        C_i, R_i = main_centers[i], max(1e-6, main_radii[i])

        if i < len(main_centers) - 1:
            C_next, R_next = main_centers[i+1], max(1e-6, main_radii[i+1])
            start_angle, end_angle = calculate_arc_angles(C_i, R_i, C_next, R_next)
        else: # Last arc - use previous arc for direction
            C_prev, R_prev = main_centers[i-1], max(1e-6, main_radii[i-1])
            segment_vec = C_i - C_prev
            angle_dir = np.arctan2(segment_vec[1], segment_vec[0]) if np.linalg.norm(segment_vec) > 1e-9 else 0.0
            start_angle = angle_dir + np.pi/2
            end_angle = angle_dir - np.pi/2

            if R_i > R_prev and np.linalg.norm(segment_vec) > 1e-9:
                delta_angle = np.arcsin(np.clip((R_i - R_prev) / np.linalg.norm(segment_vec), 0.0, 1.0))
                start_angle += delta_angle
                end_angle -= delta_angle

        # Store arc data
        all_arc_data[i] = {
            "center": C_i,
            "radius": R_i,
            "start_angle": start_angle + np.pi * 2,
            "end_angle": end_angle + np.pi * 2
        }

        # Generate arc points
        arc_points = generate_arc_points(C_i, R_i,
                                       all_arc_data[i]["start_angle"],
                                       all_arc_data[i]["end_angle"],
                                       edge_length)

        all_arc_data[i]["points"] = arc_points

    return all_arc_data


def generate_final_path(arcs_data, edge_length):
    """Generate the final path by connecting arcs with clothoid transitions.

    Args:
        arcs_data: Dictionary containing arc data including centers, radii and points
        edge_length: Length of elementary edge in the geometry

    Returns:
        List of points forming the final path
    """
    final_path = []

    # Sort keys to ensure we process arcs in order
    arc_keys = sorted(arcs_data.keys())

    for i, key in enumerate(arc_keys):
        arc_data = arcs_data[key]
        arc_points = arc_data["points"]

        if i == 0:
            final_path.extend(arc_points)
        else:
            # Generate clothoid transition
            prev_key = arc_keys[i-1]
            prev_arc = arcs_data[prev_key]

            x0 = float(prev_arc["center"][0] + prev_arc["radius"] * np.cos(prev_arc["start_angle"]))
            y0 = float(prev_arc["center"][1] + prev_arc["radius"] * np.sin(prev_arc["start_angle"]))
            theta0 = float(prev_arc["start_angle"] + np.pi/2)

            x1 = float(arc_data["center"][0] + arc_data["radius"] * np.cos(arc_data["end_angle"]))
            y1 = float(arc_data["center"][1] + arc_data["radius"] * np.sin(arc_data["end_angle"]))
            theta1 = float(arc_data["end_angle"] + np.pi/2)

            clothoid = Clothoid.G1Hermite(x0, y0, theta0, x1, y1, theta1)
            num_points = max(2, int(np.ceil(clothoid.length / edge_length)) + 1)
            clothoid_points = np.array(clothoid.SampleXY(num_points)).T

            final_path.extend(clothoid_points[1:])
            final_path.extend(arc_points[1:])

    return final_path

def spiral_toolpath_snail(center      : tuple[float, float],
                          R           : float,
                          init_r      : float,
                          finish_r    : float,
                          cwe_start   : float,
                          cwe_finish  : float,
                          dtheta      : float = 0.02,
                          ccw         : bool  = True) -> np.ndarray:
    """
    Generate a 2-D toolpath whose cutter engagement (CWE) varies
    linearly from `cwe_start` to `cwe_finish`.

    Parameters
    ----------
    center      (cx, cy) : spiral origin [mm]
    R                   : cutter radius  [mm]
    init_r              : first centre-line radius  ≥ R   [mm]
    finish_r            : last  centre-line radius  > init_r   [mm]
    cwe_start           : CWE of the first revolution  (0 < … ≤ 2R) [mm]
    cwe_finish          : CWE of the last  revolution  (0 < … ≤ 2R) [mm]
    dtheta              : angular increment (rad) – smaller ⇒ smoother path
    ccw                 : True ⇒ counter-clockwise, False ⇒ clockwise

    Returns
    -------
    path : (N, 2) float32 numpy array of XY tool-centre coordinates
    """

    # ------------- basic sanity checks -----------------------------------
    # if init_r < R:
    #     raise ValueError("init_r must not be smaller than the cutter radius R")
    if finish_r <= init_r:
        raise ValueError("finish_r must be greater than init_r")
    for cwe, label in ((cwe_start, "cwe_start"), (cwe_finish, "cwe_finish")):
        if cwe <= 0 or cwe > 2 * R:
            raise ValueError(f"{label} must lie in the open interval (0 , 2·R]")

    # ------------- total revolutions needed ------------------------------
    # If CWE grows (or shrinks) linearly with θ, the mean engagement equals
    #            (cwe_start + cwe_finish) / 2
    # Every full revolution removes that radial thickness.
    mean_cwe = 0.5 * (cwe_start + cwe_finish)
    revs     = (finish_r - init_r) / mean_cwe          # total turns
    theta_max = revs * 2.0 * np.pi                     # final angle (rad)

    # ------------- discretise the spiral ---------------------------------
    sign   = 1.0 if ccw else -1.0
    theta  = np.arange(0.0, theta_max + dtheta, dtheta)
    n      = theta.size
    r      = np.empty(n, dtype=np.float32)
    r[0]   = init_r

    # incremental integration: r_{i+1} = r_i + dr
    # with dr = (CWE(θ) / 2π) · dθ  and  CWE(θ) linear in θ
    cwe_delta = cwe_finish - cwe_start
    two_pi    = 2.0 * np.pi

    for i in range(1, n):
        frac      = theta[i-1] / theta_max                       # progress 0…1
        cwe_now   = cwe_start + cwe_delta * frac
        dr_dtheta = cwe_now / two_pi
        r[i]      = r[i-1] + dr_dtheta * dtheta

    # ------------- Cartesian coordinates ---------------------------------
    theta *= sign
    cx, cy = center
    x = cx + r * np.cos(theta)
    y = cy + r * np.sin(theta)

    return np.column_stack((x, y)).astype(np.float32)


def distances_from_linestring_a_to_linestring_b_vectorized(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b using vectorized operations.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        return np.full(num_points_a, np.nan)

    # If linestring B is a single point
    if num_points_b == 1:
        # Distances from all points in ls_a to the single point in ls_b
        # ls_a is (N, 2), ls_b[0] is (2,), ls_b[0][np.newaxis, :] is (1,2)
        # Broadcasting (N,2) - (1,2) -> (N,2)
        diff = ls_a - ls_b[0] # or ls_a - ls_b[0][np.newaxis, :]
        distances = np.linalg.norm(diff, axis=1)
        return distances

    # Prepare segments of linestring B
    # b_starts are points p1 of segments, b_ends are points p2
    b_starts = ls_b[:-1]  # Shape (M-1, 2)
    b_ends = ls_b[1:]    # Shape (M-1, 2)

    num_segments_b = b_starts.shape[0]
    if num_segments_b == 0: # Should be caught by num_points_b == 1, but good check
        # This case implies ls_b had only one point, handled above.
        # If somehow reached, it means ls_b was malformed or empty.
        # For safety, if ls_b had points but no segments (e.g., ls_b = [[0,0]]),
        # we should have handled it with num_points_b == 1.
        # If ls_b was truly empty, already handled.
        # This path is unlikely if prior checks are robust.
        return np.full(num_points_a, np.nan)


    # Expand dimensions for broadcasting:
    # ls_a: (N, 1, 2) - for each point in A, we consider all segments of B
    # b_starts, b_ends: (1, M, 2) - for each segment in B, we consider all points of A
    p = ls_a[:, np.newaxis, :]     # (N, 1, 2)
    s1 = b_starts[np.newaxis, :, :] # (1, M, 2)
    s2 = b_ends[np.newaxis, :, :]   # (1, M, 2)

    # Vectors for segments and from segment start to points in ls_a
    seg_vectors = s2 - s1      # (1, M, 2), segment vectors (s1_s2)
    ap_vectors = p - s1        # (N, M, 2), vectors from s1 to p

    # Length squared of segments. Add epsilon to avoid division by zero for zero-length segments.
    # seg_lens_sq will be (1, M)
    seg_lens_sq = np.sum(seg_vectors**2, axis=2) + 1e-9 # Add epsilon for stability

    # Project ap_vectors onto seg_vectors
    # dot_product will be (N, M)
    dot_product = np.sum(ap_vectors * seg_vectors, axis=2)

    # t is the projection parameter
    # t will be (N, M)
    t = dot_product / seg_lens_sq

    # Clip t to be between 0 and 1.
    # If t < 0, closest point is s1.
    # If t > 1, closest point is s2.
    # Else, closest point is s1 + t * seg_vectors.
    t_clipped = np.clip(t, 0, 1)

    # Calculate the closest points on the segments (or their extensions if not clipped)
    # to each point in ls_a.
    # closest_points_on_lines will be (N, M, 2)
    # t_clipped needs to be (N, M, 1) for broadcasting with seg_vectors (1, M, 2)
    # or seg_vectors needs to be (N,M,2) (already is after broadcasting rules apply)
    closest_points_on_segments = s1 + t_clipped[:, :, np.newaxis] * seg_vectors

    # Calculate distances from points in p to these closest points
    # distances_to_segments will be (N, M)
    distances_to_segments = np.linalg.norm(p - closest_points_on_segments, axis=2)

    # For each point in ls_a, find the minimum distance to any segment in ls_b
    # min_distances will be (N,)
    min_distances = np.min(distances_to_segments, axis=1)

    return min_distances


def angle_between_points(center_point, point_a, point_b):
    """
    Calculates the angle (in radians) between two points
    relative to a center point.

    The angle is measured counter-clockwise from the vector (center -> point_a)
    to the vector (center -> point_b).

    Args:
        center_point (numpy.ndarray): The central reference point [x, y].
        point_a (numpy.ndarray): The first point [x, y].
        point_b (numpy.ndarray): The second point [x, y].

    Returns:
        float: The angle between the two vectors in radians.
    """

    # Calculate vectors from the center to point_a and point_b
    vec_a_x = point_a[0] - center_point[0]
    vec_a_y = point_a[1] - center_point[1]

    vec_b_x = point_b[0] - center_point[0]
    vec_b_y = point_b[1] - center_point[1]

    # Use math.atan2 to get the angle of each vector relative to the positive x-axis
    # atan2(y, x)
    angle_a_rad = math.atan2(vec_a_y, vec_a_x)
    angle_b_rad = math.atan2(vec_b_y, vec_b_x)

    # Calculate the difference between the angles
    # This gives the angle from vec_a to vec_b (counter-clockwise)
    angle_diff_rad = angle_b_rad - angle_a_rad

    # Normalize the angle to be between -pi and pi
    # This helps ensure consistent results for angles spanning across the 0/360 boundary
    angle_diff_rad = (angle_diff_rad + math.pi) % (2 * math.pi) - math.pi

    return angle_diff_rad


def compute_affine_coeffs(scale_x, scale_y, rotation, pivot_coords):
    """
    Compute affine transformation coefficients for scaling a geometry
    by scale_x along the x-axis and scale_y along the y-axis, 
    rotated around the pivot point.

    Parameters:
    - scale_x (float): Scaling factor along the rotated x-axis.
    - scale_y (float): Scaling factor along the rotated y-axis.
    - pivot (dict): Dictionary containing 'vector' and 'coords' keys for pivot information.

    Returns:
    - list: [a, b, d, e, xoff, yoff] coefficients for Shapely's affine_transform.
    """

    # Calculate angle between pivot vector and x-axis    
    theta = rotation - np.pi/2    

    # Rotation matrix for theta
    R_theta = np.array([
        [np.cos(theta), -np.sin(theta)],
        [np.sin(theta), np.cos(theta)]
    ])

    # Rotation matrix for -theta
    R_minus_theta = np.array([
        [np.cos(theta), np.sin(theta)],
        [-np.sin(theta), np.cos(theta)]
    ])

    # Scaling matrix: scale along both x and y axes
    S = np.array([
        [scale_x, 0],
        [0, scale_y]
    ])

    # Linear transformation matrix M = R_theta * S * R_minus_theta
    M = R_theta @ S @ R_minus_theta

    # Extract coefficients from M
    a, b = M[0, 0], M[0, 1]
    d, e = M[1, 0], M[1, 1]

    # Origin point
    O = pivot_coords

    # Compute M * O
    M_O = M @ O

    # Translation vector: O - M * O
    trans = O - M_O

    # Extract offsets
    xoff, yoff = trans[0], trans[1]

    return [a, b, d, e, xoff, yoff]


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def compare_distances(ls_a, ls_b):
    """
    Calculates the closest distance from each point in ls_a to ls_b using vectorized operations.
    ls_a: Nx2 numpy array representing linestring A.
    ls_b: Mx2 numpy array representing linestring B.
    Returns: A numpy array of N distances.
    """
    num_points_a = ls_a.shape[0]
    num_points_b = ls_b.shape[0]

    if num_points_a == 0:
        return np.array([])
    if num_points_b == 0:
        return np.full(num_points_a, np.nan)

    # If linestring B is a single point
    if num_points_b == 1:
        # Distances from all points in ls_a to the single point in ls_b
        # ls_a is (N, 2), ls_b[0] is (2,), ls_b[0][np.newaxis, :] is (1,2)
        # Broadcasting (N,2) - (1,2) -> (N,2)
        diff = ls_a - ls_b[0] # or ls_a - ls_b[0][np.newaxis, :]
        distances = np.linalg.norm(diff, axis=1)
        return distances

    # Prepare segments of linestring B
    # b_starts are points p1 of segments, b_ends are points p2
    b_starts = ls_b[:-1]  # Shape (M-1, 2)
    b_ends = ls_b[1:]    # Shape (M-1, 2)

    num_segments_b = b_starts.shape[0]
    if num_segments_b == 0: # Should be caught by num_points_b == 1, but good check
        # This case implies ls_b had only one point, handled above.
        # If somehow reached, it means ls_b was malformed or empty.
        # For safety, if ls_b had points but no segments (e.g., ls_b = [[0,0]]),
        # we should have handled it with num_points_b == 1.
        # If ls_b was truly empty, already handled.
        # This path is unlikely if prior checks are robust.
        return np.full(num_points_a, np.nan)


    # Expand dimensions for broadcasting:
    # ls_a: (N, 1, 2) - for each point in A, we consider all segments of B
    # b_starts, b_ends: (1, M, 2) - for each segment in B, we consider all points of A
    p = ls_a[:, np.newaxis, :]     # (N, 1, 2)
    s1 = b_starts[np.newaxis, :, :] # (1, M, 2)
    s2 = b_ends[np.newaxis, :, :]   # (1, M, 2)

    # Vectors for segments and from segment start to points in ls_a
    seg_vectors = s2 - s1      # (1, M, 2), segment vectors (s1_s2)
    ap_vectors = p - s1        # (N, M, 2), vectors from s1 to p

    # Length squared of segments. Add epsilon to avoid division by zero for zero-length segments.
    # seg_lens_sq will be (1, M)
    seg_lens_sq = np.sum(seg_vectors**2, axis=2) + 1e-9 # Add epsilon for stability

    # Project ap_vectors onto seg_vectors
    # dot_product will be (N, M)
    dot_product = np.sum(ap_vectors * seg_vectors, axis=2)

    # t is the projection parameter
    # t will be (N, M)
    t = dot_product / seg_lens_sq

    # Clip t to be between 0 and 1.
    # If t < 0, closest point is s1.
    # If t > 1, closest point is s2.
    # Else, closest point is s1 + t * seg_vectors.
    t_clipped = np.clip(t, 0, 1)

    # Calculate the closest points on the segments (or their extensions if not clipped)
    # to each point in ls_a.
    # closest_points_on_lines will be (N, M, 2)
    # t_clipped needs to be (N, M, 1) for broadcasting with seg_vectors (1, M, 2)
    # or seg_vectors needs to be (N,M,2) (already is after broadcasting rules apply)
    closest_points_on_segments = s1 + t_clipped[:, :, np.newaxis] * seg_vectors

    # Calculate distances from points in p to these closest points
    # distances_to_segments will be (N, M)
    distances_to_segments = np.linalg.norm(p - closest_points_on_segments, axis=2)

    # For each point in ls_a, find the minimum distance to any segment in ls_b
    # min_distances will be (N,)
    min_distances = np.min(distances_to_segments, axis=1)

    return min_distances


def max_distance_right_side(l1, l2):
    """
    Find maximum distance from points of l2 that are on the right side of l1.
    Returns: max_distance
    """    
    l1_coords = np.array(l1.coords)
    l2_coords = np.array(l2.coords)

    n_l2 = len(l2_coords)

    # Broadcast arrays for vectorized operations
    p_broadcast = l2_coords[:, np.newaxis, :]
    a_broadcast = l1_coords[:-1][np.newaxis, :, :]
    b_broadcast = l1_coords[1:][np.newaxis, :, :]

    # Calculate projections for all point-segment pairs
    ab = b_broadcast - a_broadcast
    ap = p_broadcast - a_broadcast

    # Dot products
    ab_dot_ab = np.sum(ab * ab, axis=2)
    ap_dot_ab = np.sum(ap * ab, axis=2)

    # Clamp t values
    t = np.clip(ap_dot_ab / ab_dot_ab, 0, 1)

    # Calculate closest points
    closest = a_broadcast + t[:, :, np.newaxis] * ab

    # Calculate distances
    distances = np.linalg.norm(p_broadcast - closest, axis=2)

    # Find closest segment for each point
    closest_seg_idx = np.argmin(distances, axis=1)
    min_distances = np.min(distances, axis=1)

    # Calculate cross products for closest segments
    on_right = np.zeros(n_l2, dtype=bool)
    for i in range(n_l2):
        seg_idx = closest_seg_idx[i]
        a = l1_coords[seg_idx]
        b = l1_coords[seg_idx + 1]
        p = l2_coords[i]

        cross_z = (b[0] - a[0]) * (p[1] - a[1]) - (b[1] - a[1]) * (p[0] - a[0])
        on_right[i] = cross_z < 0

    # Get distances for points on the right
    right_distances = min_distances[on_right]

    if len(right_distances) > 0:
        max_dist = np.max(right_distances)
    else:
        max_dist = None

    return max_dist

def minor_scale_down(inner_ellipse_data, front_data, scale_params):
    """
    Minor scale down.
    ^^
    ||
    ||
    VV
    Args:
    - ellipse: Ellipse to be scaled
    - front_line: Front line of the polygon
    - front_polygon: Front polygon of the polygon
    - pivot: Pivot point of the ellipse
    - params: Dictionary containing the following parameters:
        - vertical_sum_scale: Cumulative vertical scale factor
        - maximum_right_dist: Maximum allowed distance to the right side of the front line
        - min_aspect_ratio: Minimum aspect ratio of the ellipse
        - low: Lower bound for scale factor
        - high: Upper bound (starting scale factor)
        - tolerance: Precision tolerance

    Returns:
    - vertical_sum_scale * mid: Cumulative vertical scale factor (through all vertical scale downs)
    - mid: Actual scale down applied in this step
    - bool: True if minimum aspect ratio was reached, False otherwise
    """

    maximum_right_dist = scale_params['maximum_right_dist']
    low = scale_params['low'] # Lower bound for scale factor
    high = scale_params['high'] # Upper bound (starting scale factor)
    tolerance = scale_params['tolerance'] # Precision tolerance

    while high - low > tolerance:        
        mid = (low + high) / 2
        ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_ellipse_data['anchor'],
            semi_major_axis=inner_ellipse_data['major'],
            semi_minor_axis=inner_ellipse_data['minor'] * mid,
            rotation=inner_ellipse_data['rotation'],
            anchor_on_perimeter=True
            )
        
        # create_line_object(ellipse_geometry, "ellipse_minor_scaled", color=(1, 1, 0, 1))
        
        ellipse = shapely.geometry.LineString(ellipse_geometry)
        inter = ellipse.intersection(front_data['polygon'])       
        if inter.geom_type == "MultiLineString":
            inter = shapely.line_merge(inter)

        if inter.geom_type == "MultiLineString":
            longest_line = None
            max_length = -1
            for line in inter.geoms:
                if line.length > max_length:
                    max_length = line.length
                    longest_line = line
            inter = shapely.LineString(longest_line.coords)

        max_distance_right = max_distance_right_side(front_data['line'], inter)        

        if max_distance_right is not None and max_distance_right > maximum_right_dist:
            low = mid  # Too large, search in lower half
            # print("Too large.")
        else:
            high = mid  # Too small, search in upper half
            # print("Too small.")
    return mid * inner_ellipse_data['minor']


def create_perpendicular_segment(line, length=500.0):
    """Create a perpendicular line segment from the last segment of a line.

    Args:
        line: LineString to get the last segment from
        length: Length of the perpendicular segment

    Returns:
       numpy.ndarray: Perpendicular line segment coordinates
    """
    coords = np.array(line.coords)
    if len(coords) < 2:
        print("Line has fewer than 2 points, cannot create perpendicular")
        return

    # Get the last segment
    p1 = coords[-2]
    p2 = coords[-1]

    # Calculate segment vector and perpendicular vector
    segment_vector = p2 - p1

    # Normalize and rotate 90 degrees (perpendicular)
    # For 2D, perpendicular is (-y, x)
    perp_vector = np.array([-segment_vector[1], segment_vector[0]])
    perp_vector = perp_vector / np.linalg.norm(perp_vector) * length

    # Create perpendicular segment from the end point
    perp_segment = np.array([p2, p2 + perp_vector])

    # Return the coordinates of the perpendicular segment
    return perp_segment


def cut_linestring(line: LineString, max_length: float, move_to: float) -> LineString:
    """
    Cuts a line symmetrically around a middle point by a specified distance.

    Args:
        line: The LineString object to cut.
        max_length: The distance to cut in both directions from the middle point.
        move_to: The point around which to make the symmetrical cut.

    Returns:
        A LineString representing the merged segments from both sides of the cut,
        handling cases where the cuts wrap around the line's endpoints.
    """
    distance_normalized = max_length / line.length
    left_length = move_to
    right_length = 1 - move_to

    if distance_normalized > left_length or distance_normalized > right_length:
        max_dist = min(left_length, right_length)
    else:
        max_dist = distance_normalized

    start = move_to - max_dist
    end = move_to + max_dist

    seg = shapely.ops.substring(line, start, end, normalized=True)

    return seg


def get_orientation_angle(linestring: LineString) -> float:
    """
    Calculates the orientation angle of a LineString using pure NumPy
    by performing the steps of Principal Component Analysis (PCA) manually.

    Returns the angle in radians.
    """
    # 1. Get coordinates and center the data
    coords = np.array(linestring.coords)
    # Calculate the mean (centroid) of the points
    centroid = np.mean(coords, axis=0)
    # Subtract the centroid to center the data around the origin
    centered_coords = coords - centroid

    # 2. Compute the covariance matrix
    # `rowvar=False` tells np.cov that our variables (x and y) are in columns
    cov_matrix = np.cov(centered_coords, rowvar=False)

    # 3. Find eigenvalues and eigenvectors
    # `eigvals` contains the eigenvalues, `eigvecs` contains corresponding eigenvectors as columns
    eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

    # 4. Select the eigenvector for the largest eigenvalue
    # This vector is the first principal component, pointing in the direction of max variance.
    # `np.argmax` gives the index of the largest eigenvalue.
    principal_component = eigenvectors[:, np.argmax(eigenvalues)]

    # 5. Calculate the angle of this vector
    # Use atan2 for quadrant correctness.
    angle_rad = math.atan2(principal_component[1], principal_component[0])

    return angle_rad


def _find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, start_angle_rad=0.0, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation']
    outer_center = outer_ellipse_data['center']

    t_arc = _find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)
    
    # 2. Apply the angular offset to get the effective parameter
    t_effective = t_arc + start_angle_rad
    
    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_effective), outer_semi_minor_axis * np.sin(t_effective)])
    point_vector = rotate_vector(point_vector, outer_rotation)
    point_vector += outer_center
    
    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_effective), outer_semi_major_axis * np.sin(t_effective)])
    normal_vector = rotate_vector(normal_vector, outer_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])
    
    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])
            
    return point_vector, normal_angle


def project_on_basis(v1, pivot_vector):
    """
    Projects vector v1 onto the coordinate system defined by vector pivot_vector.

    Args:
        v1 (np.ndarray): The 2D vector to be projected.
        pivot_vector (np.ndarray): The 2D normalized vector that defines the new system's x-axis.

    Returns:
        np.ndarray: The coordinates of v1 in the new coordinate system.
    """
    # Ensure vectors are numpy arrays
    pivot_vector = np.asarray(pivot_vector)
    v1 = np.asarray(v1)

    # v1 is the new x-axis (u_x)
    # It must be a unit vector for this to be a valid basis
    u_x = pivot_vector / np.linalg.norm(pivot_vector)

    # The new y-axis (u_y) is perpendicular to u_x.
    # In 2D, a vector (x, y) has a perpendicular vector (-y, x).
    u_y = np.array([-u_x[1], u_x[0]])

    # The new coordinates are the dot products of v2 with the new basis vectors.
    new_x = np.dot(v1, u_x)
    new_y = np.dot(v1, u_y)

    return np.array([new_x, new_y])


def project_with_matrix(v2, v1):
    """Projects v2 onto the basis of v1 using a change of basis matrix."""
    v1 = np.asarray(v1)
    v2 = np.asarray(v2)
    
    # 1. Create the new orthonormal basis
    u_x = v1 / np.linalg.norm(v1)
    u_y = np.array([-u_x[1], u_x[0]])

    # 2. Create the transformation matrix from the new basis.
    # The columns of the matrix are the basis vectors.
    # This is the matrix that converts from the new basis TO the standard basis.
    P_transpose = np.array([u_x, u_y])

    # 3. The inverse of an orthonormal matrix is its transpose.
    # To convert FROM the standard basis TO the new basis, we use P_transpose.
    P = P_transpose.T 
    
    # In our construction, P_transpose is actually the inverse matrix (P⁻¹)
    # we need to transform v2.
    # new_coords = P⁻¹ @ v2
    v2_in_v1_coords = P_transpose @ v2
    
    return v2_in_v1_coords


def ellipse_find_center(inner_ellipse_data, outer_ellipse_data, sides, local_pos):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.
    
    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries
    
    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse
        
    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
    new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
        outer_ellipse_data = outer_ellipse_data,
        fraction=local_pos
        )
    
    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=new_anchor,
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=new_rotation,
        anchor_on_perimeter=True
        )
    
    # create_line_object(ellipse_geometry, "ellipse_new_anchor", color=(0, 1, 0, 1))
        
    # Calculate distances to sides
    ellipse = shapely.geometry.LineString(ellipse_geometry)    
    distances = [line.distance(ellipse) for line in sides.geoms]    

    return distances, new_anchor, new_rotation, ellipse_center


def optimize_ellipse_center(inner_ellipse_data, outer_ellipse_data, sides):
    """
    Optimize the center position of an ellipse using binary search.

    Args:
        ellipse: The ellipse to optimize
        pivot: Pivot information for the ellipse
        sides: Side geometries to avoid intersecting
        ellipse_act_pos: Current position of the front_line

    Returns:
        optimal_move: The optimal position along the front_line
    """    
    max_iterations = 500
    iteration = 0

    # Binary search to find optimal scale factor that avoids intersection    
    tolerance = 0.0001  # Precision tolerance

    distances, _, _, _ = ellipse_find_center(inner_ellipse_data, outer_ellipse_data, sides, 0.0)
    if sum(distances) == 0:
        return None
    elif distances[0] > distances[1]:
        low = 0.0  # Lower bound for scale factor
        high = outer_ellipse_data["front"]["fractions"][0]  # Upper bound (starting scale factor)
    else:
        low = 1 - outer_ellipse_data["front"]["fractions"][1]  # Lower bound for scale factor
        high = 1.0  # Upper bound (starting scale factor)

    while high - low > tolerance:
        iteration += 1
        if iteration > max_iterations:
            return None
        mid = (low + high) / 2
        distances, new_anchor, new_rotation, ellipse_center = ellipse_find_center(inner_ellipse_data, outer_ellipse_data, sides, mid)
        if sum(distances) == 0:
            return None
        elif distances[0] < distances[1]:
            # Too large, search in lower half
            high = mid
        else:
            # Valid scale, remember it and search in upper half                    
            low = mid

    ### Update the inner ellipse data
    inner_ellipse_data['anchor'] = new_anchor
    inner_ellipse_data['rotation'] = new_rotation
    inner_ellipse_data['center'] = ellipse_center
    # print(f'optimal move in: {iteration}')

    return distances


def apply_ellipse_transformation(ellipse, angle_diff, translation_vector):
    """
    Apply translation and rotation transformations to an ellipse based on optimal position.

    Args:
        ellipse: The ellipse to transform
        front_line: The front part of the outer ellipse
        pivot: Pivot information for the ellipse
        optimal_move: The optimal position along the front_line
        angle_diff_rad: The angle difference between the ellipse and the front line
        translation_vector: The translation vector between the ellipse and the front line

    Returns:
        transformed_ellipse: The transformed ellipse
    """
    # new_coords = np.array(front_line.interpolate(optimal_move, normalized=True).coords[0])
    # move_to_vector = new_coords - pivot['coords']

    # Apply final transformation
    transformed_ellipse = shapely.affinity.translate(ellipse,
                                            xoff=translation_vector[0],
                                            yoff=translation_vector[1])
    
    transformed_ellipse = shapely.affinity.rotate(transformed_ellipse, angle_diff,
                                        origin=transformed_ellipse.geoms[0].coords[0],
                                        use_radians=True)

    return transformed_ellipse


def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=72, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.
    
    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def rotate_around_pivot(polyline, pivot_point, angle_rad):
    """
    Rotates a 2D polyline around a specific pivot point.

    Args:
        polyline (np.ndarray): Shape (N, 2), the (x, y) coordinates.
        pivot_point (array-like): The (x, y) coordinate of the pivot.
        angle_rad (float): The rotation angle in radians.

    Returns:
        np.ndarray: The rotated polyline as an (N, 2) NumPy array.
    """
    pivot_point = np.asarray(pivot_point)
    
    # 1. Translate polyline to origin
    poly_at_origin = polyline - pivot_point

    # 2. Rotate
    c, s = np.cos(angle_rad), np.sin(angle_rad)
    rotation_matrix = np.array([[c, -s], [s, c]])
    rotated_at_origin = poly_at_origin @ rotation_matrix.T

    # 3. Translate back to original position
    rotated_polyline = rotated_at_origin + pivot_point

    return rotated_polyline


def intersect_ellipse(geometry, ellipse_center, semi_major_axis, semi_minor_axis, rotation):
    """
    Finds the intersection points between a polyline and an axis-aligned ellipse.

    Args:
        polyline (shapely.geometry.LineString): The polyline to intersect with the ellipse.

        ellipse_center (np.ndarray): Center point of the ellipse (h, k).
        semi_major_axis (float): Semi-major axis length of the ellipse.
        semi_minor_axis (float): Semi-minor axis length of the ellipse.

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
              Returns only the first intersection found when traversing the polyline.
    """
    if geometry.geom_type == "MultiLineString" and len(geometry.geoms) == 2:
        # Merge all line coordinates into a single numpy array
        all_coords = [np.array(line.coords) for line in geometry.geoms]
        coords = np.concatenate(all_coords, axis=0)
        skipping_element = len(geometry.geoms[0].coords)
    else:
        print(f'intersect_ellipse: {geometry.geom_type} not supported')
        return []

    # Rotate polyline to match ellipse's orientation    
    rotated_polyline = rotate_around_pivot(coords, ellipse_center, -rotation)
    # create_line_object(rotated_polyline, "rotated_polyline", color=(0, 1, 1, 1))

    intersections = []
    for i, _ in enumerate(rotated_polyline):
        if i > 0 and i != skipping_element:
            line_start = rotated_polyline[i-1]
            line_end = rotated_polyline[i]
            intersections = intersect_line_ellipse(line_start, line_end, ellipse_center, semi_major_axis, semi_minor_axis)
            if intersections:
                break

    return intersections


def get_ellipse_data(polygon, medial_axis):
        center = medial_axis.coords[0]
        center_point = shapely.geometry.Point(center)
        semi_axis_length = center_point.distance(polygon.boundary)

        ellipse_data = {
            'major': semi_axis_length,
            'minor': semi_axis_length,
            'center': center,            
            'length': 2 * np.pi * semi_axis_length
        }
        
        anchor = None
        for i, _ in enumerate(medial_axis.coords):
            if i > 0:
                line_start = np.array(medial_axis.coords[i-1])
                line_end = np.array(medial_axis.coords[i])
                anchor = intersect_line_ellipse(line_start, line_end, ellipse_data)
                if anchor is not None:
                    ellipse_data['anchor'] = anchor
                    break

        if anchor is None:            
            print("Problem with anchor")
            return None
        
        ### Initial rotation of the ellipse in radians ###        
        rotation_vector = anchor - np.array(center)
        ellipse_data['rotation'] = np.arctan2(rotation_vector[1], rotation_vector[0])

        return ellipse_data


def get_front_data(polygon, ellipse_data, cutter_data):

        ellipse_geometry, _ = create_ellipse(
            anchor_point=ellipse_data['anchor'],
            semi_major_axis=ellipse_data['major'],
            semi_minor_axis=ellipse_data['minor'],
            rotation=ellipse_data['rotation'],
            anchor_on_perimeter=True            
        )

        ### Front ####
        inter = shapely.geometry.LineString(ellipse_geometry).intersection(polygon)
        zero_point = shapely.geometry.Point(ellipse_geometry[0])
        front_lines = []
        tolerance = 0.01
        ellipse_length = ellipse_data['length']
        for geom in inter.geoms:
            if geom.distance(zero_point) < tolerance:
                front_lines.append(geom)                
        if len(front_lines) != 2:
            print("Problem with front lines")
            return
        else:
            # Merge front lines using NumPy for better precision control
            # Extract coordinates from both lines
            coords1 = np.array(front_lines[0].coords)
            coords2 = np.array(front_lines[1].coords)

            # if the first point of coordsX is in zero_point = the second part of the front line.
            if np.linalg.norm(coords1[0] - np.array(zero_point.coords[0])) < tolerance:
                c1 = coords2
                c2 = coords1[1:]
                fr1 = front_lines[0]
                fr2 = front_lines[1]
            else:
                c1 = coords1
                c2 = coords2[1:]
                fr1 = front_lines[1]
                fr2 = front_lines[0]

            front_fractions = [fr1.length/ellipse_length, fr2.length/ellipse_length]            
            merged_coords = np.vstack([c1, c2])

            # Create a new LineString from the merged coordinates
            front_line = shapely.geometry.LineString(merged_coords)

        front_line_back = front_line.parallel_offset(cutter_data['radius'], 'left')
        front_line_front = front_line.parallel_offset(-cutter_data['radius'], 'left')

        ## Create polygon from the two linestrings
        combined_lines = list(reversed(front_line_back.coords)) + list(front_line_front.coords)
        front_polygon = shapely.geometry.Polygon(combined_lines)

        front_data = {
            'line': front_line,
            'polygon': front_polygon,
            'fractions': front_fractions
        }

        return front_data


def get_sides(inner_ellipse_data, outer_ellipse_data, active_geoms):
    ### Base geometry ####
    outer_ellipse_geometry, _ = create_ellipse(
        anchor_point=outer_ellipse_data['center'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation']
        )
    
    inner_ellipse_geometry, _ = create_ellipse(
        anchor_point=inner_ellipse_data['center'],
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=inner_ellipse_data['rotation']        
        )
        
    outer_ellipse = shapely.geometry.Polygon(outer_ellipse_geometry)
    inner_ellipse = shapely.geometry.Polygon(inner_ellipse_geometry)
   
    sides = []
    for line in active_geoms:
        if line.intersects(outer_ellipse):            
            inter = line.intersection(outer_ellipse)
            if inter.geom_type == "MultiLineString":
                inter = shapely.line_merge(inter)

            inter = inter.difference(inner_ellipse)
            if inter.geom_type == "MultiLineString":
                for line in inter.geoms:                  
                    if line.distance(outer_ellipse_data['front']['line']) < 0.001:                        
                        sides.append(line)
            else:              
                if inter.distance(outer_ellipse_data['front']['line']) < 0.001:                    
                    sides.append(inter)

    if len(sides) == 2:
        # Sort sides by distance to the last point in front_line (it will be a ccw order from ellipse start)
        front_line_start = shapely.geometry.Point(outer_ellipse_data['front']['line'].coords[-1])
        sides.sort(key=lambda x: x.distance(front_line_start))
        sides = shapely.MultiLineString(sides)
    else:
        print("Problem with sides")
        return None

    # Add perpendicular segments to each side
    new_sides = []
    for line in sides.geoms:
        new_segment = create_perpendicular_segment(line)
        new_sides.append(shapely.geometry.LineString(np.vstack([line.coords, new_segment])))
    sides_perpendicular = shapely.geometry.MultiLineString(new_sides)
    return sides, sides_perpendicular


def fit_ellipse(outer_ellipse_data, active_geoms, polygon, cutter_data, min_radius_of_curvature=15.0, idx=0):
    ### Inital scale down ellipse of the cutter ###     
    minor_correction_factor = 0.95 ##TODO: its a quick fix for boolean operations.
    inner_major_axis = outer_ellipse_data['major'] * (1 - (cutter_data['radius']*minor_correction_factor/outer_ellipse_data['major']))
    inner_minor_axis = outer_ellipse_data['minor'] * (1 - (cutter_data['radius']*minor_correction_factor/outer_ellipse_data['minor']))

    inner_ellipse_data = {
        'major': inner_major_axis,
        'minor': inner_minor_axis,
        'center': outer_ellipse_data['center'],
        'anchor': outer_ellipse_data['anchor'],
        'rotation': outer_ellipse_data['rotation'],
    }    
    
    ### Helper data ###
    outer_ellipse_data['front'] = get_front_data(polygon, outer_ellipse_data, cutter_data)
    sides, sides_perpendicular = get_sides(inner_ellipse_data, outer_ellipse_data, active_geoms)
    if idx == 0:
        for line in sides.geoms:
            create_line_object(line.coords, "sides", color=(1, 0, 0, 1))
    if sides is None:
        print("Problem with sides")
        return
    
    # Finding ...
    maximum_right_dist: float = 0.3
    minimum_major = sides.geoms[0].distance(sides.geoms[1])/2
    maximum_major = shapely.hausdorff_distance(sides.geoms[0], sides.geoms[1])/2    
    # minimum_minor = min_radius_of_curvature / minimum_major  # Lower bound for scale factor
    # minimum_distance = 0.01 # mm
    minor_correction_factor = 0.05
    
    low = minimum_major
    high = maximum_major
    tolerance = 0.001

    outer_iteration = 0
    while high - low > tolerance:
        outer_iteration += 1
        print(f'outer iteration: {outer_iteration}')
        if outer_iteration > 100:
            print('max outer iterations reached')
            break
        mid = (low + high) / 2
        major = mid
        minor = (np.sqrt(min_radius_of_curvature * major))        

        minor_correction = 1.0
        side_intersection = False

        inside_iteration = 0
        while not side_intersection:
            if inside_iteration > 15:
                print('max inner iterations reached')
                side_intersection = True
                break        

            test_data = {
            'major': major,
            'minor': minor*minor_correction
            }
            test_inner_data = {**inner_ellipse_data, **test_data}
            distances = optimize_ellipse_center(test_inner_data, outer_ellipse_data, sides_perpendicular)

            if distances:
                print(f'inner iteration: {inside_iteration} distances {outer_iteration}: {distances}')

                ellipse_geometry, _ = create_ellipse(
                    anchor_point=test_inner_data['anchor'],
                    semi_major_axis=test_inner_data['major'],
                    semi_minor_axis=test_inner_data['minor'],
                    rotation=test_inner_data['rotation'],
                    anchor_on_perimeter=True
                    )
                ellipse = shapely.geometry.LineString(ellipse_geometry)
                # create_line_object(ellipse_geometry, f"ellipse_{outer_iteration}_{inside_iteration}", color=(1, 1, 0, 1))
                inter = ellipse.intersection(outer_ellipse_data['front']['polygon'])
                if inter.geom_type == "MultiLineString":
                    inter = shapely.line_merge(inter)

                if inter.geom_type == "MultiLineString":
                    longest_line = None
                    max_length = -1
                    for line in inter.geoms:
                        if line.length > max_length:
                            max_length = line.length
                            longest_line = line
                    inter = shapely.LineString(longest_line.coords)

                max_distance_right = max_distance_right_side(outer_ellipse_data['front']['line'], inter)
                if max_distance_right is not None and max_distance_right > maximum_right_dist:
                    minor_correction += minor_correction_factor
                    inside_iteration += 1
                else:
                    break
            else:
                side_intersection = True

        if side_intersection:
            high = mid
        else:
            low = mid
            new_inner_ellipse_data = test_inner_data

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=new_inner_ellipse_data['anchor'],
        semi_major_axis=new_inner_ellipse_data['major'],
        semi_minor_axis=new_inner_ellipse_data['minor'],
        rotation=new_inner_ellipse_data['rotation'],
        anchor_on_perimeter=True
        )
    create_line_object(ellipse_geometry, f"ellipse_outer", color=(1, 1, 0, 1))

    return
    # print(f'mid: {mid}')
    inner_ellipse_data = test_inner_data
    
    ### Lenght of the outer ellipse ###
    # Eccentricity of the ellipse
    e = np.sqrt(1 - ((inner_ellipse_data['minor']+cutter_data['radius'])**2 / (inner_ellipse_data['major']+cutter_data['radius'])**2))
    # Exact perimeter calculation using numerical integration
    perimeter = 4 * (inner_ellipse_data['major']+cutter_data['radius']) * scipy.special.ellipe(e)

    new_anchor = rotate_vector(np.array([cutter_data['radius'], 0]), inner_ellipse_data['rotation'])    
    
    outer_ellipse_data = {            
        'major': inner_ellipse_data['major']+cutter_data['radius'],
        'minor': inner_ellipse_data['minor']+cutter_data['radius'],
        'center': ellipse_center,
        'anchor': inner_ellipse_data['anchor']+new_anchor,
        'rotation': inner_ellipse_data['rotation'],
        'length': perimeter
    }

    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0.4, 0.1, 1))

    if idx == 8:
        ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_ellipse_data['anchor'],
            semi_major_axis=outer_ellipse_data['major'],
            semi_minor_axis=outer_ellipse_data['minor'],
            rotation=outer_ellipse_data['rotation'],
            anchor_on_perimeter=True
            )
        
        create_line_object(ellipse_geometry, "ellipse_outer", color=(0, 0.4, 0.8, 1))

    return outer_ellipse_data


    '''
    itr = 0    
    while itr < 10:        
        itr += 1
        print(f'itr: {itr}')
        actual_semi_minor = inner_ellipse_data['minor']
        minimum_semi_minor = np.sqrt(min_radius_of_curvature * inner_ellipse_data['major'])

        ## Minor scale down
        trial_semi_minor = minor_scale_down(inner_ellipse_data, outer_ellipse_data['front'], minor_scale_params)
        print(f'trial_semi_minor: {trial_semi_minor}')

        # Check if the trial semi-minor axis is below the minimum allowed value
        # If so, set it to the minimum value to maintain minimum radius of curvature
        if trial_semi_minor < minimum_semi_minor:
            inner_ellipse_data['minor'] = minimum_semi_minor            
            print(f'max aspect ratio reached')
        else:
            inner_ellipse_data['minor'] = trial_semi_minor
        
        if inner_ellipse_data['minor'] / actual_semi_minor > 0.95:
            print('last scaling')
            # break
            ## The last scaling, at the end to best fit
            # Binary search to find optimal scale factor
            tolerance = 0.001  # Precision tolerance
            low = 0.7
            high = 1.3

            # Binary search loop
            while high - low > tolerance:
                mid = (low + high) / 2
                minimum_minor_semi_axis = np.sqrt(min_radius_of_curvature * inner_ellipse_data['major']*mid)
                minor_mid = max(minimum_minor_semi_axis, inner_ellipse_data['minor']*mid)            
                ellipse_geometry, ellipse_center = create_ellipse(
                    anchor_point=inner_ellipse_data['anchor'],
                    semi_major_axis=inner_ellipse_data['major'] * mid,
                    semi_minor_axis=minor_mid,
                    rotation=inner_ellipse_data['rotation'],
                    anchor_on_perimeter=True
                    )
                ellipse = shapely.geometry.LineString(ellipse_geometry)                

                if ellipse.intersects(sides):
                    # Too large, search in lower half
                    high = mid
                else:
                    # Valid scale, remember it and search in upper half                    
                    low = mid

                ellipse_geometry, ellipse_center = create_ellipse(
                    anchor_point=inner_ellipse_data['anchor'],
                    semi_major_axis=inner_ellipse_data['major'] * mid,
                    semi_minor_axis=minor_mid,
                    rotation=inner_ellipse_data['rotation'],
                    anchor_on_perimeter=True
                    )
                create_line_object(ellipse_geometry, f"ellipse_{itr}_{mid}", color=(1, 1, 0, 1))

            # Use the largest valid scale factor            
            inner_ellipse_data['minor'] = minor_mid
            inner_ellipse_data['major'] *= mid
            inner_ellipse_data['center'] = ellipse_center

            ## Center optimization using binary search
            optimize_ellipse_center(inner_ellipse_data, outer_ellipse_data, sides)
            break

        ## Scale up/down
        # Binary search to find optimal scale factor that avoids intersection
        low = 0.5  # Lower bound for scale factor
        high = 1.0  # Upper bound (starting scale factor)
        tolerance = 0.001  # Precision tolerance
                
        while high - low > tolerance:
            mid = (low + high) / 2
            minimum_minor_semi_axis = np.sqrt(min_radius_of_curvature * inner_ellipse_data['major']*mid)
            minor_mid = max(minimum_minor_semi_axis, inner_ellipse_data['minor']*mid)            
            print(f'minimum: {minimum_minor_semi_axis}, mid: {inner_ellipse_data['minor']*mid, mid}')            
            
            ellipse_geometry, ellipse_center = create_ellipse(
                anchor_point=inner_ellipse_data['anchor'],
                semi_major_axis=inner_ellipse_data['major'] * mid,
                semi_minor_axis=minor_mid,
                rotation=inner_ellipse_data['rotation'],
                anchor_on_perimeter=True
            )
            ellipse = shapely.geometry.LineString(ellipse_geometry)
            # create_line_object(ellipse_geometry, f"ellipse_{itr}_{mid}", color=(1, 1, 0, 1))
            
            if ellipse.intersects(sides):                
                high = mid  # Too large, search in lower half
            else:
                low = mid  # Too small, search in upper half       
        
        ### Update inner_ellipse_data with the new scale factor ###
        inner_ellipse_data['major'] *= mid
        inner_ellipse_data['minor'] = minor_mid
        inner_ellipse_data['center'] = ellipse_center

        optimize_ellipse_center(inner_ellipse_data, outer_ellipse_data, sides)
           
    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=inner_ellipse_data['anchor'],
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=inner_ellipse_data['rotation'],
        anchor_on_perimeter=True
        )
    create_line_object(ellipse_geometry, "ellipse_outer", color=(1, 0, 0, 1))


    ### Lenght of the outer ellipse ###
    # Eccentricity of the ellipse
    e = np.sqrt(1 - ((inner_ellipse_data['minor']+cutter_data['radius'])**2 / (inner_ellipse_data['major']+cutter_data['radius'])**2))
    # Exact perimeter calculation using numerical integration
    perimeter = 4 * (inner_ellipse_data['major']+cutter_data['radius']) * scipy.special.ellipe(e)

    new_anchor = rotate_vector(np.array([cutter_data['radius'], 0]), inner_ellipse_data['rotation'])    
    
    outer_ellipse_data = {            
        'major': inner_ellipse_data['major']+cutter_data['radius'],
        'minor': inner_ellipse_data['minor']+cutter_data['radius'],
        'center': inner_ellipse_data['center'],
        'anchor': inner_ellipse_data['anchor']+new_anchor,
        'rotation': inner_ellipse_data['rotation'],
        'length': perimeter
    }
    ellipse_geometry, _ = create_ellipse(
        anchor_point=inner_ellipse_data['anchor'],
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=inner_ellipse_data['rotation'],
        anchor_on_perimeter=True
        )
    # create_line_object(ellipse_geometry, "ellipse_outer", color=(1, 0, 0, 1))
    return outer_ellipse_data
    '''


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None


def main():
    cutter_dim = 20 # in mm
    cutter_data = cutter_dim / 2

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_data
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])    
    medial_middle = medial_axis.interpolate(0.5, normalized=True)

    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]

    tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]
    ellipse_data = get_ellipse_data(polygon, medial_axis)

    # for geom in active_geoms:
    #     create_line_object(geom.coords, 'poly')
    time1 = time.time()

    min_radius_of_curvature = 10.0
    for i in range(1):
        ellipse_data = fit_ellipse(ellipse_data, active_geoms, polygon_buffered, cutter_data, min_radius_of_curvature, i)
        # ellipse_geometry, _ = create_ellipse(
        #     anchor_point=ellipse_data['anchor'],
        #     semi_major_axis=ellipse_data['major'],
        #     semi_minor_axis=ellipse_data['minor'],
        #     rotation=ellipse_data['rotation'],
        #     anchor_on_perimeter=True
        #     )
        # create_line_object(ellipse_geometry, "ellipse_outer", color=(0, 1, 0, 1))

    
    time2 = time.time()
    print(f'Time: {time2-time1}')


if __name__ == "__main__":
    main()    
